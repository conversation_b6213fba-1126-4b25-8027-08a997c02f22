﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="txtFrom" xml:space="preserve">
    <value> From </value>
  </data>
  <data name="txtFromDate" xml:space="preserve">
    <value> From Date </value>
  </data>
  <data name="Msg01" xml:space="preserve">
    <value>Sorry, you can't delete this account</value>
  </data>
  <data name="Msg02" xml:space="preserve">
    <value>You have to delete this account journals first</value>
  </data>
  <data name="Msg03" xml:space="preserve">
    <value>You have to delete sub-accounts first</value>
  </data>
  <data name="Msg04" xml:space="preserve">
    <value>Are you sure you want to delete this account</value>
  </data>
  <data name="Msg05" xml:space="preserve">
    <value>Sorry, you can't create sub-account</value>
  </data>
  <data name="Msg06" xml:space="preserve">
    <value>Please, enter account name</value>
  </data>
  <data name="MsgNameExist" xml:space="preserve">
    <value>This name already exists</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>some data are incorrect</value>
  </data>
  <data name="Msg09" xml:space="preserve">
    <value>you can't edit  this account</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>You made some changes, do you want to save</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>Sorry, you don't have privilege to edit record</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>Sorry, you don't have privilege to add new record</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>Saved Successfully</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="NoLossNoProfit" xml:space="preserve">
    <value>There is neither Loss nor Profit</value>
  </data>
  <data name="txtTo" xml:space="preserve">
    <value> To </value>
  </data>
  <data name="txtToDate" xml:space="preserve">
    <value> To Date </value>
  </data>
  <data name="TotalLoss" xml:space="preserve">
    <value>Total Loss</value>
  </data>
  <data name="TotalProfit" xml:space="preserve">
    <value>Total Profit</value>
  </data>
  <data name="Msg10" xml:space="preserve">
    <value>Sorry, you can't delete Default Drawer</value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>Deleted Successfully</value>
  </data>
  <data name="ValTxtName" xml:space="preserve">
    <value>Please enter name</value>
  </data>
  <data name="txt1" xml:space="preserve">
    <value>Please enter bank account number</value>
  </data>
  <data name="txtBank" xml:space="preserve">
    <value> Bank </value>
  </data>
  <data name="txtOpenBalance" xml:space="preserve">
    <value> Open Balance </value>
  </data>
  <data name="Msg11" xml:space="preserve">
    <value>Are you sure you want to delete this drawer</value>
  </data>
  <data name="txtDrawer" xml:space="preserve">
    <value> Drawer </value>
  </data>
  <data name="Msg12" xml:space="preserve">
    <value>Are you sure you want to delete this cash payment</value>
  </data>
  <data name="Msg13" xml:space="preserve">
    <value>Are you sure you want to delete this cash receive</value>
  </data>
  <data name="Msg14" xml:space="preserve">
    <value>Are you sure you want to delete this cash transfer</value>
  </data>
  <data name="txtBankAccount" xml:space="preserve">
    <value>Bank Account</value>
  </data>
  <data name="txtCashPay" xml:space="preserve">
    <value>Cash Payment</value>
  </data>
  <data name="txtCashPayList" xml:space="preserve">
    <value>Cash Payment List</value>
  </data>
  <data name="txtCashReceive" xml:space="preserve">
    <value>Cash Receive</value>
  </data>
  <data name="txtCashReceiveList" xml:space="preserve">
    <value>Cash Receive List</value>
  </data>
  <data name="txtCashTrnsfr" xml:space="preserve">
    <value>Cash Transfer</value>
  </data>
  <data name="txtCredit" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="txtCustomer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="txtDebit" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="txtPurDisc" xml:space="preserve">
    <value>Discount</value>
  </data>
  <data name="txtSelDisc" xml:space="preserve">
    <value>Discount</value>
  </data>
  <data name="txtVendor" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="ValtxtAcc" xml:space="preserve">
    <value>Please select account</value>
  </data>
  <data name="ValtxtAmount" xml:space="preserve">
    <value>Please enter Amount</value>
  </data>
  <data name="ValtxtDrwr" xml:space="preserve">
    <value>Please select drawer </value>
  </data>
  <data name="ValtxtMinus" xml:space="preserve">
    <value>Minus Values are not allowed</value>
  </data>
  <data name="Msg15" xml:space="preserve">
    <value>Sorry, you can't delete this journal, you have to delete journal source</value>
  </data>
  <data name="Msg16" xml:space="preserve">
    <value>Are you sure you want to delete this journal entry</value>
  </data>
  <data name="Msg17" xml:space="preserve">
    <value>Please enter filling number</value>
  </data>
  <data name="Msg18" xml:space="preserve">
    <value>Sorry, you can't edit this journal, you have to edit journal source</value>
  </data>
  <data name="Msg19" xml:space="preserve">
    <value>you have to enter journals</value>
  </data>
  <data name="Msg20" xml:space="preserve">
    <value>Debit side and Credit side should be equal</value>
  </data>
  <data name="Msg21" xml:space="preserve">
    <value>Closing Period Accounts can't be included in journals, it's calculated automatically</value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>delete row ?</value>
  </data>
  <data name="MsgNumExist" xml:space="preserve">
    <value>This number already exists</value>
  </data>
  <data name="txt2" xml:space="preserve">
    <value>Trade Statement - Total Loss</value>
  </data>
  <data name="txt3" xml:space="preserve">
    <value>Trade Statement - Total Profit</value>
  </data>
  <data name="txt4" xml:space="preserve">
    <value>Adjusting Entries</value>
  </data>
  <data name="ValtxtJrnl1" xml:space="preserve">
    <value>you have to enter a value in debit or credit columns</value>
  </data>
  <data name="ValtxtJrnl2" xml:space="preserve">
    <value>Debit or credit field should equal Zero</value>
  </data>
  <data name="Msg22" xml:space="preserve">
    <value>Are you sure you want to delete this pay notes</value>
  </data>
  <data name="Msg23" xml:space="preserve">
    <value>Are you sure you want to delete this receive notes</value>
  </data>
  <data name="txtBill" xml:space="preserve">
    <value>Bill </value>
  </data>
  <data name="txtBillNum" xml:space="preserve">
    <value>Bill Number</value>
  </data>
  <data name="txtCheck" xml:space="preserve">
    <value>Check</value>
  </data>
  <data name="txtCheckNum" xml:space="preserve">
    <value>Check Number</value>
  </data>
  <data name="txtPayNote" xml:space="preserve">
    <value>Pay Note</value>
  </data>
  <data name="txtReceiveNote" xml:space="preserve">
    <value>Receive Note</value>
  </data>
  <data name="Valtxt1" xml:space="preserve">
    <value>Please enter one note</value>
  </data>
  <data name="VAltxt2" xml:space="preserve">
    <value>Total amount should equal the sum of all notes</value>
  </data>
  <data name="valtxtbank" xml:space="preserve">
    <value>Please select bank</value>
  </data>
  <data name="ValtxtDueDate" xml:space="preserve">
    <value>Please enter maturity date</value>
  </data>
  <data name="ValtxtTotlAmnt" xml:space="preserve">
    <value>Please enter total amount</value>
  </data>
  <data name="ValtxtNoteNum" xml:space="preserve">
    <value>Please enter note number</value>
  </data>
  <data name="txt5" xml:space="preserve">
    <value>Trade Account is Debit in</value>
  </data>
  <data name="txt6" xml:space="preserve">
    <value>Trade Account is Credit in</value>
  </data>
  <data name="txt7" xml:space="preserve">
    <value>This Account is Debit in</value>
  </data>
  <data name="txt8" xml:space="preserve">
    <value>This Account is Credit in</value>
  </data>
  <data name="txt9" xml:space="preserve">
    <value>Sub-Accounts included</value>
  </data>
  <data name="MsgAskDel" xml:space="preserve">
    <value> Are you sure you want to delete </value>
  </data>
  <data name="txtBounceDate" xml:space="preserve">
    <value>Bounce Date</value>
  </data>
  <data name="txtPayDate" xml:space="preserve">
    <value>Pay Date</value>
  </data>
  <data name="ValtxtBounce" xml:space="preserve">
    <value>Please enter bounce date</value>
  </data>
  <data name="ValTxtMaturity" xml:space="preserve">
    <value>Please enter Maturity Date</value>
  </data>
  <data name="ValTxtPayDate" xml:space="preserve">
    <value>Please enter Payment Date</value>
  </data>
  <data name="txtEndorsed" xml:space="preserve">
    <value>Endorsed</value>
  </data>
  <data name="txtEndorseDate" xml:space="preserve">
    <value>Endorse Date</value>
  </data>
  <data name="txtNoteBounce" xml:space="preserve">
    <value> Bounced </value>
  </data>
  <data name="txtNoteEndorse" xml:space="preserve">
    <value> Endorse </value>
  </data>
  <data name="txtNotePay" xml:space="preserve">
    <value> Payment </value>
  </data>
  <data name="txtOutstanding" xml:space="preserve">
    <value>Outstanding</value>
  </data>
  <data name="txtPayedBank" xml:space="preserve">
    <value>Payed to Bank</value>
  </data>
  <data name="txtPayedDrwr" xml:space="preserve">
    <value>Payed to Drawer</value>
  </data>
  <data name="ValTxtBillDrwr" xml:space="preserve">
    <value>Please select Drawer to pay the bill to</value>
  </data>
  <data name="ValTxtCheckDrwr" xml:space="preserve">
    <value>Please select Pay Account to pay the paper to</value>
  </data>
  <data name="ValTxtEndorse" xml:space="preserve">
    <value>Please Select Dealer to endorse the paper</value>
  </data>
  <data name="txtDealerNm" xml:space="preserve">
    <value>Dealer Name</value>
  </data>
  <data name="Msg24" xml:space="preserve">
    <value>Are you sure you want to pay all selected pay notes</value>
  </data>
  <data name="Msg25" xml:space="preserve">
    <value>All selected pay notes are payed successfully</value>
  </data>
  <data name="Msg26" xml:space="preserve">
    <value>Please select payment drawer, then continue</value>
  </data>
  <data name="Msg27" xml:space="preserve">
    <value> Are you sure you want to update selected receive notes</value>
  </data>
  <data name="Msg28" xml:space="preserve">
    <value>Please select payment drawer, then continue</value>
  </data>
  <data name="Msg29" xml:space="preserve">
    <value>Receive notes data updated successfully</value>
  </data>
  <data name="Msg30" xml:space="preserve">
    <value>Are you sure you want to delete this revenue</value>
  </data>
  <data name="Msg31" xml:space="preserve">
    <value>Are you sure you want to delete this expense</value>
  </data>
  <data name="txt10" xml:space="preserve">
    <value>Account created by Revenue Screen</value>
  </data>
  <data name="txt11" xml:space="preserve">
    <value>Account created by Expenses Screen</value>
  </data>
  <data name="txtExp" xml:space="preserve">
    <value>Expenses</value>
  </data>
  <data name="txtExpList" xml:space="preserve">
    <value>Expenses List</value>
  </data>
  <data name="txtExpNum" xml:space="preserve">
    <value> Expense Number </value>
  </data>
  <data name="txtExpType" xml:space="preserve">
    <value>Expenses Type</value>
  </data>
  <data name="txtExpTypeAdd" xml:space="preserve">
    <value>Add Expenses Type</value>
  </data>
  <data name="txtRev" xml:space="preserve">
    <value>Revenues</value>
  </data>
  <data name="txtRevList" xml:space="preserve">
    <value>Revenue List</value>
  </data>
  <data name="txtRevNum" xml:space="preserve">
    <value>Revenue Number </value>
  </data>
  <data name="txtRevType" xml:space="preserve">
    <value>Revenue Type</value>
  </data>
  <data name="txtRevTypeAdd" xml:space="preserve">
    <value>Add Revenue Type</value>
  </data>
  <data name="ValTxt3" xml:space="preserve">
    <value>Please enter revenue name successfully</value>
  </data>
  <data name="ValTxt4" xml:space="preserve">
    <value>Please enter expense name successfully</value>
  </data>
  <data name="st_CompName" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="st_CompTel" xml:space="preserve">
    <value>Company Tel</value>
  </data>
  <data name="st_ItemCode" xml:space="preserve">
    <value>Item Code</value>
  </data>
  <data name="st_ItemName" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="st_ItemPrice" xml:space="preserve">
    <value>Item Price</value>
  </data>
  <data name="st_vendorCode" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="st_SetDaysCount" xml:space="preserve">
    <value>Please enter number of days</value>
  </data>
  <data name="NetLoss" xml:space="preserve">
    <value>Net Loss</value>
  </data>
  <data name="NetProfit" xml:space="preserve">
    <value>Net Profit</value>
  </data>
  <data name="MsgCCCode" xml:space="preserve">
    <value>Please enter cost center code</value>
  </data>
  <data name="MsgCCName" xml:space="preserve">
    <value>Please enter cost center name</value>
  </data>
  <data name="MsgDelCC" xml:space="preserve">
    <value>Are you sure you want to delete this cost center</value>
  </data>
  <data name="MsgDelCCDenied" xml:space="preserve">
    <value>You have to delete this cost center journals first</value>
  </data>
  <data name="MsgZeroCode" xml:space="preserve">
    <value>Code can't be zero</value>
  </data>
  <data name="ValTxtCstCntr" xml:space="preserve">
    <value>Please select cost center</value>
  </data>
  <data name="valCodeExist" xml:space="preserve">
    <value>This code already exists</value>
  </data>
  <data name="valtxtCode" xml:space="preserve">
    <value>Please enter Code</value>
  </data>
  <data name="Msg32" xml:space="preserve">
    <value>Are you sure you want to delete this custom accounts list</value>
  </data>
  <data name="Msg33" xml:space="preserve">
    <value>Please enter accounts</value>
  </data>
  <data name="Msg34" xml:space="preserve">
    <value>You have to delete this account from custom accounts lists first</value>
  </data>
  <data name="ValTxtAccExist" xml:space="preserve">
    <value>Account already exists</value>
  </data>
  <data name="ValTxtCstmLst" xml:space="preserve">
    <value>Please select custom accounts list</value>
  </data>
  <data name="Msg35" xml:space="preserve">
    <value>Please enter custom account list Name</value>
  </data>
  <data name="txt_Account" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="txt_JournalNumber" xml:space="preserve">
    <value>Journal Entry Number</value>
  </data>
  <data name="txt_incoming" xml:space="preserve">
    <value>Incoming</value>
  </data>
  <data name="txt_outgoing" xml:space="preserve">
    <value>Outgoing</value>
  </data>
  <data name="txt_Number" xml:space="preserve">
    <value>Number </value>
  </data>
  <data name="valtxtChildAcc" xml:space="preserve">
    <value>Please choose sub account</value>
  </data>
  <data name="txt_C" xml:space="preserve">
    <value>C</value>
  </data>
  <data name="txt_D" xml:space="preserve">
    <value>D</value>
  </data>
  <data name="msgUserDefault" xml:space="preserve">
    <value>This drawer is assigned as default drawer for some users</value>
  </data>
  <data name="emp" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="MsgHRAccount" xml:space="preserve">
    <value>Sorry, you can't delete this account, it is used by system. go to settings to change that</value>
  </data>
  <data name="txtExpense" xml:space="preserve">
    <value>Expenses Note</value>
  </data>
  <data name="txtRevenue" xml:space="preserve">
    <value>Revenue Note</value>
  </data>
  <data name="txtAcc" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="txtCostCenter" xml:space="preserve">
    <value>Cost Center</value>
  </data>
  <data name="txtCustomList" xml:space="preserve">
    <value>Custom List</value>
  </data>
  <data name="txtDiscount" xml:space="preserve">
    <value>Discount</value>
  </data>
  <data name="st_PurchaseInvoice" xml:space="preserve">
    <value>Purchase Invoice No.</value>
  </data>
  <data name="MsgBankAcc" xml:space="preserve">
    <value>Please select Banks, Receivable and Payable Notes accounts in settings screen</value>
  </data>
  <data name="MsgDrawerAcc" xml:space="preserve">
    <value>Please select Drawers main account in settings screen</value>
  </data>
  <data name="MsgNotesPayableAcc" xml:space="preserve">
    <value>Please select Payable Notes account in settings screen</value>
  </data>
  <data name="MsgNotesReceivableAcc" xml:space="preserve">
    <value>Please select Receivable Notes account in settings screen</value>
  </data>
  <data name="MsgMerchendaisingAcc" xml:space="preserve">
    <value>Please select Merchendaising account in settings screen</value>
  </data>
  <data name="MsgAccSettng" xml:space="preserve">
    <value>Sorry, you can't delete this account, it's used by accounts settings</value>
  </data>
  <data name="MsgDrwrBank" xml:space="preserve">
    <value>You have to register a drawer or bank account first</value>
  </data>
  <data name="excess" xml:space="preserve">
    <value>excess</value>
  </data>
  <data name="shortage" xml:space="preserve">
    <value>shortage</value>
  </data>
  <data name="MsgAccLevel" xml:space="preserve">
    <value>Sorry, we can't exceed Maximum number of accounts allowed in this level</value>
  </data>
  <data name="ValTxtCollectBankAcc" xml:space="preserve">
    <value>Please Select Bank portfolio</value>
  </data>
  <data name="MsgNotesReceivableUnderCollect" xml:space="preserve">
    <value>Please select Under Collection Notes account in settings screen</value>
  </data>
  <data name="ValTxtCollectDate" xml:space="preserve">
    <value>Please enter deposit date</value>
  </data>
  <data name="ValParentAcc" xml:space="preserve">
    <value>Sorry, you can't add a sub-account to a used account</value>
  </data>
  <data name="delCCChild" xml:space="preserve">
    <value>Sorry, you can't delete this cost center, you have to delete child cost centers first</value>
  </data>
  <data name="MsgCantAddCCC" xml:space="preserve">
    <value>Sorry, you can't create sub-cost center</value>
  </data>
  <data name="txtAccName" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="txtCashPayNotes" xml:space="preserve">
    <value>Cash Pay Notes</value>
  </data>
  <data name="txtCashReceiveNotes" xml:space="preserve">
    <value>Cash Receive Notes</value>
  </data>
  <data name="ExpireDate" xml:space="preserve">
    <value>Expire Date</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Credit Note</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Debit Note</value>
  </data>
  <data name="CreditNoteList" xml:space="preserve">
    <value>Credit Notes List</value>
  </data>
  <data name="DebitNoteList" xml:space="preserve">
    <value>Debit Notes List</value>
  </data>
  <data name="MsgDelCrdtNote" xml:space="preserve">
    <value>Are you sure you want to delete this Credit Note</value>
  </data>
  <data name="MsgDelDbtNote" xml:space="preserve">
    <value>Are you sure you want to delete this Debit Note</value>
  </data>
  <data name="ValSelectDbtCrdtAcc" xml:space="preserve">
    <value>Please select note account</value>
  </data>
  <data name="diff" xml:space="preserve">
    <value>Difference</value>
  </data>
  <data name="valprExp" xml:space="preserve">
    <value>Sorry, you can't delete this entry, it's used in purchas invoice No.</value>
  </data>
  <data name="valprExp1" xml:space="preserve">
    <value>Sorry, you can't add this entry, it's used in purchas invoice No.</value>
  </data>
  <data name="MsgIncomAccs" xml:space="preserve">
    <value>Please Set Income Statment Accounts in Settings Screen</value>
  </data>
  <data name="DelAccContinualStore" xml:space="preserve">
    <value>Sorry, you can't delete this account, its already used by Inventories</value>
  </data>
  <data name="DelFaGrp" xml:space="preserve">
    <value>Are you sure you want to delete this fixed assets group</value>
  </data>
  <data name="DelFaGrp2" xml:space="preserve">
    <value>Sorry, you can't delete this group there is some assets related to it</value>
  </data>
  <data name="FaAccs" xml:space="preserve">
    <value>Please select fixed assets account and depreciation account in settings screen</value>
  </data>
  <data name="DelFaDenied" xml:space="preserve">
    <value>Sorry, you can't delete this fixed asset, you've to delete it's journal entries first</value>
  </data>
  <data name="DelAccBom" xml:space="preserve">
    <value>Sorry, you can't delete this account, its already in use by Manufacturing module</value>
  </data>
  <data name="st_ItemCode2" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="MsgccDelDenie" xml:space="preserve">
    <value>Sorry you can't delete this cost center</value>
  </data>
  <data name="SelectPeriod" xml:space="preserve">
    <value>Please select period correctly</value>
  </data>
  <data name="valTxtUsedAccount" xml:space="preserve">
    <value>you can't move this account under a used account</value>
  </data>
  <data name="st_Batch" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="st_Qty" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="ValCrncRate" xml:space="preserve">
    <value>Currency rate must be greater than 0</value>
  </data>
  <data name="ValDelAccDenied" xml:space="preserve">
    <value>Sorry, you can't delete this account from accounts tree</value>
  </data>
  <data name="acTypeCredit" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="acTypeDebit" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="acTypeWithout" xml:space="preserve">
    <value>Undetermined</value>
  </data>
  <data name="ccTypeMandatory" xml:space="preserve">
    <value>Mandatory</value>
  </data>
  <data name="ccTypeOptional" xml:space="preserve">
    <value>Optional</value>
  </data>
  <data name="ccTypeWithout" xml:space="preserve">
    <value>Without</value>
  </data>
  <data name="valArchive" xml:space="preserve">
    <value>Sorry,Can't Show Archive In More Than Year</value>
  </data>
  <data name="MsgFiscalYearEndDate" xml:space="preserve">
    <value>Please select fiscal year end date in company information screen</value>
  </data>
  <data name="MsgVisaAcc" xml:space="preserve">
    <value>Please select Visa, Receivable and Payable Notes accounts in settings screen</value>
  </data>
  <data name="delManfExpWarn" xml:space="preserve">
    <value>You are about removing this expense!! Confirm?!</value>
  </data>
  <data name="valManfExp" xml:space="preserve">
    <value>Sorry, This Expeses is already used in another Manfucature.</value>
  </data>
  <data name="ValPayableNotes" xml:space="preserve">
    <value>Payable Notes</value>
  </data>
  <data name="txtPartialPayment" xml:space="preserve">
    <value>Partial Payment</value>
  </data>
  <data name="txtCashNote" xml:space="preserve">
    <value>Cash Note</value>
  </data>
  <data name="valstoreExp1" xml:space="preserve">
    <value>Sorry, you can't add this entry, it's used in strore movement No.</value>
  </data>
  <data name="valSourceNoteError" xml:space="preserve">
    <value>You should choose recieving note.</value>
  </data>
  <data name="valIntrmdtStorActId" xml:space="preserve">
    <value>Please make sure to set the intermediate account in settings</value>
  </data>
  <data name="capitalDisError" xml:space="preserve">
    <value>Distributed Amount must be equal to total capital amount.</value>
  </data>
  <data name="SalesAfterCost" xml:space="preserve">
    <value>Sales After Cost</value>
  </data>
  <data name="SalesNet" xml:space="preserve">
    <value>Sales Net</value>
  </data>
</root>