﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pharmacy.Properties {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "17.13.0.0")]
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase {
        
        private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));
        
        public static Settings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string ReceiptPrinterName {
            get {
                return ((string)(this["ReceiptPrinterName"]));
            }
            set {
                this["ReceiptPrinterName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("COM1")]
        public string SWPortName {
            get {
                return ((string)(this["SWPortName"]));
            }
            set {
                this["SWPortName"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2400")]
        public int SWBaudRate {
            get {
                return ((int)(this["SWBaudRate"]));
            }
            set {
                this["SWBaudRate"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("2")]
        public int SWParity {
            get {
                return ((int)(this["SWParity"]));
            }
            set {
                this["SWParity"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("7")]
        public int SWDataBits {
            get {
                return ((int)(this["SWDataBits"]));
            }
            set {
                this["SWDataBits"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("1")]
        public int SWStopBits {
            get {
                return ((int)(this["SWStopBits"]));
            }
            set {
                this["SWStopBits"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("4")]
        public int SWStartIndex {
            get {
                return ((int)(this["SWStartIndex"]));
            }
            set {
                this["SWStartIndex"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("8")]
        public int SWLength {
            get {
                return ((int)(this["SWLength"]));
            }
            set {
                this["SWLength"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("22")]
        public int SWPacketLength {
            get {
                return ((int)(this["SWPacketLength"]));
            }
            set {
                this["SWPacketLength"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("270")]
        public int SWTimerInterval {
            get {
                return ((int)(this["SWTimerInterval"]));
            }
            set {
                this["SWTimerInterval"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string fb {
            get {
                return ((string)(this["fb"]));
            }
            set {
                this["fb"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string Tw {
            get {
                return ((string)(this["Tw"]));
            }
            set {
                this["Tw"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string Lnkd {
            get {
                return ((string)(this["Lnkd"]));
            }
            set {
                this["Lnkd"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("http://localhost/")]
        public string BackEndPoint {
            get {
                return ((string)(this["BackEndPoint"]));
            }
            set {
                this["BackEndPoint"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("C:\\Users\\<USER>\\source\\repos\\centeromeer\\LinkIT-E-Invoice\\ERP\\Pharmacy\\bin\\McDRSig" +
            "niture.dll")]
        public string signuatuer_dll {
            get {
                return ((string)(this["signuatuer_dll"]));
            }
            set {
                this["signuatuer_dll"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("10")]
        public int RecievedDocumentsMinutesInterval {
            get {
                return ((int)(this["RecievedDocumentsMinutesInterval"]));
            }
            set {
                this["RecievedDocumentsMinutesInterval"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("")]
        public string RecievedDocumentsLastSyncDate {
            get {
                return ((string)(this["RecievedDocumentsLastSyncDate"]));
            }
            set {
                this["RecievedDocumentsLastSyncDate"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("57978")]
        public int BackEndPort {
            get {
                return ((int)(this["BackEndPort"]));
            }
            set {
                this["BackEndPort"] = value;
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.ConnectionString)]
        [global::System.Configuration.DefaultSettingValueAttribute("Data Source=DEV-RD\\SQLEXPRESS19;Initial Catalog=misrkheir;Integrated Security=Tru" +
            "e;Pooling=False;TrustServerCertificate=True")]
        public string ERPConnectionString {
            get {
                return ((string)(this["ERPConnectionString"]));
            }
        }
    }
}
