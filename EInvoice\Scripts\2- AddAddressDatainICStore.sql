﻿if COL_LENGTH('IC_Store','CountryId') is  NULL
BEGIN
alter table  [dbo].[IC_Store]
Add CountryId int not null DEFAULT(1)
end
go

ALTER TABLE [dbo].[IC_Store]  WITH NOCHECK ADD  CONSTRAINT [FK_IC_Store_HR_Country] FOREIGN KEY([CountryId])
REFERENCES [dbo].[HR_Country] ([CountryId])
GO

ALTER TABLE [dbo].[IC_Store] NOCHECK CONSTRAINT [FK_IC_Store_HR_Country]
GO

if COL_LENGTH('IC_Store','Governate') is  NULL
BEGIN
alter table  [dbo].[IC_Store]
Add Governate nvarchar(100) null
end
go

if COL_LENGTH('IC_Store','RegionCity') is  NULL
BEGIN
alter table  [dbo].[IC_Store]
Add RegionCity nvarchar(100) null
end
go

if COL_LENGTH('IC_Store','Street') is  NULL
BEGIN
alter table  [dbo].[IC_Store]
Add Street nvarchar(100) null
end
go

if COL_LENGTH('IC_Store','BuildingNumber') is  NULL
BEGIN
alter table  [dbo].[IC_Store]
Add BuildingNumber nvarchar(100) null
end
go
