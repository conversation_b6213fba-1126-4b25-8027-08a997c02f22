﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class SlAddDetail
    {
        public int SlAddDetailId { get; set; }
        public int SlAddId { get; set; }
        public int ItemId { get; set; }
        public byte Uomindex { get; set; }
        public int Uomid { get; set; }
        public decimal Qty { get; set; }
        public decimal SellPrice { get; set; }
        public decimal DiscountRatio { get; set; }
        public decimal DiscountValue { get; set; }
        public decimal PurchasePrice { get; set; }
        public int? VendorId { get; set; }
        public decimal TotalSellPrice { get; set; }
        public DateTime? Expire { get; set; }
        public string Batch { get; set; }
        public decimal? Height { get; set; }
        public decimal? Length { get; set; }
        public decimal? Width { get; set; }
        public decimal PiecesCount { get; set; }
        public string ItemDescription { get; set; }
        public string ItemDescriptionEn { get; set; }
        public decimal SalesTax { get; set; }
        public decimal DiscountRatio2 { get; set; }
        public decimal DiscountRatio3 { get; set; }
        public decimal SalesTaxRatio { get; set; }
        public string Serial { get; set; }
        public DateTime? ManufactureDate { get; set; }
        public decimal CustomTaxRatio { get; set; }
        public decimal CustomTax { get; set; }
        public string Serial2 { get; set; }
        public decimal? LibraQty { get; set; }
        public bool? IsOffer { get; set; }
        public bool? PricingWithSmall { get; set; }
        public bool? VariableWeight { get; set; }
        public decimal? KgWeightLibra { get; set; }
        public int? Pack { get; set; }
        public decimal? BonusDiscount { get; set; }

        public virtual SlAdd SlAdd { get; set; }
    }
}
