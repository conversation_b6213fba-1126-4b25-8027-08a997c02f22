﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraPrinting;
using DAL;
using DAL.Res;
using System.Collections;
using DevExpress.XtraReports.UI;
using System.Net.Http;
using System.Threading.Tasks;
using Models_1.ViewModels;
using Models_1.ViewModels.InvoiceVM;

namespace Pharmacy.Forms
{
    public partial class frm_SL_Invoice : DevExpress.XtraEditors.XtraForm
    {
        System.IO.MemoryStream rep_layout = new System.IO.MemoryStream();

        byte defaultRoundingPoints;
        DateTime lastTime = DateTime.Now;
        List<string> tableTax = new List<string>();
        UserPriv prvlg;
        bool DataModified;
        decimal taxValue = 0;
        decimal totalTaxRatio = 0;
        int rowhandle = 0;
        public static DataTable Dt_Rows = new DataTable();

        List<DAL.IC_UOM> uom_list;
        List<SL_Customer_Info> lst_Customers = new List<SL_Customer_Info>();
        List<IC_Store> stores_table;
        List<IC_Category> lst_Cat = new List<IC_Category>();
        List<ItemLkp> lstItems = new List<ItemLkp>();
        int JobOrderId = 0;
        List<JO_Status> lst_Status = new List<JO_Status>();
        List<JO_Priority> lst_Priority = new List<JO_Priority>();
        List<JO_Dept> lst_Dept = new List<JO_Dept>();

        DataTable dtSL_Details = new DataTable();
        DataTable dtUOM = new DataTable();
        DataTable dtPayAccounts = new DataTable();

        DataTable dtExpireQty = new DataTable();
        DataTable dtBatchQty = new DataTable();
        DataTable dt_Multi_CC = new DataTable();
        bool isCopy = false;
        // Adel:add Multiple Datatable
        public static DataTable Multiple_Data = new DataTable();

        DataTable dt_SubTax = new DataTable();
        DataTable dt_SalesEmps = new DataTable();
        List<InvBom> lst_invBom;

        DAL.ERPDataContext DB;

        public int invoiceId = 0;
        int userId = 0;
        int customerId = 0;

        int[] weights;

        DAL.ST_PrintInvoice printOptions;
        DAL.ST_CompanyInfo comp;

        DAL.IC_PriceLevel CustomerPriceLevel = null;
        bool? IsTaxable = null;

        bool Saved_Successfuly = false;
        IC_InTrn InTrns = null;

        int SrcProcessId, SlOrdrId;
        int SourceId;
        string sourceCode;
        List<IC_Item_Location> Item_Location_List;
        bool _isCopy;
        private string uUId;
        static int discountTaxId = 0;
        public frm_SL_Invoice()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        public frm_SL_Invoice(int invoiceId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.invoiceId = invoiceId;
        }

        public frm_SL_Invoice(int invoiceId, int CustomerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.invoiceId = invoiceId;
            this.customerId = CustomerId;
        }

        public frm_SL_Invoice(int SrcProcessId, int SourceId, string sourceCode, params int[] Weights)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            weights = Weights;
        }

        public frm_SL_Invoice(IC_InTrn _intrns)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            InTrns = _intrns;
        }

        private void frm_SL_Invoice_Load(object sender, EventArgs e)
        {
            Loadform();
        }

        public void Loadform()
        {
            DB = new DAL.ERPDataContext();
            discountTaxId = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault().E_TaxableTypeId;
            if (Shared.IsEnglish)
            {
                RTL.LTRLayout(this);
            }


            defaultRoundingPoints = Shared.st_Store.DefaultRoundingPoints ?? 4;
            grp_ExtraRevenues.Visible = Shared.LibraAvailabe;

            if (Shared.user.AccessType != (byte)AccessType.Admin)
                chk_IsPosted.Visible = txt_Post_Date.Visible = false;

            #region Available_Modules
            if (Shared.CurrencyAvailable == false)
                pnlCurrency.Visible = false;
            if (Shared.SalesManAvailable == false)
                pnlSalesEmp.Visible = false;
            if (Shared.JobOrderAvailable == false)
            {
                page_JobOrder.Visible = false;
                barBtnLoad_JO.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
            if (Shared.SalesOrderAvailable == false)
                barBtnLoad_SalesOrder.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;

            if (!Shared.ChecksAvailable)
                barBtnNotesReceivable.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            if (Shared.user.UserChangeCostCenterInInv == false)
                pnlCostCenter.Visible = false;

            if (Shared.ItemsPostingAvailable)
                txtExpenses.Enabled = txtExpensesR.Enabled = false;
            if (!Shared.RealEstateAvailable)
            {
                txt_AdvancePayV.Visible = txt_AdvancePayR.Visible = txt_retentionR.Visible = txt_RetentionV.Visible = labelControl37.Visible = labelControl38.Visible =
                    labelControl39.Visible = labelControl42.Visible = labelControl43.Visible = labelControl45.Visible = labelControl44.Visible = labelControl46.Visible = false;
            }

            if (Shared.LibraAvailabe)
            {
                gridView2.Columns["Qty"].OptionsColumn.ShowInCustomizationForm = false;
            }



            if (Shared.st_Store.UseQC != true)
                col_QC.OptionsColumn.ShowInCustomizationForm = col_QC.Visible = false;
            #endregion

            LoadPrivilege();

            grdPrInvoice.ProcessGridKey += new KeyEventHandler(grid_ProcessGridKey);
            lkp_Customers.Properties.View.Columns["CusNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;

            //ErpUtils.Allow_Incremental_Search(repItems);
            if (Shared.user.UseContainsToSearchItems)
                repItems.PopupFilterMode = PopupFilterMode.Contains;
            else
                repItems.PopupFilterMode = PopupFilterMode.StartsWith;

            //ErpUtils.Allow_Incremental_Search(repUOM);
            //ErpUtils.Allow_Incremental_Search(lkp_Customers);
            repItems.View.Columns["ItemNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemNameEn"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemCode1"].SortIndex = 0;

            lkp_Drawers.Properties.TextEditStyle = lkp_Drawers2.Properties.TextEditStyle =
                lkpStore.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            DB = new DAL.ERPDataContext();

            BindDataSources();

            //dtInvoiceDate.DateTime = MyHelper.Get_Server_DateTime();
            LoadInvoice();

            //printOptions = DB.ST_PrintInvoices.FirstOrDefault();
            comp = DB.ST_CompanyInfos.FirstOrDefault();

            #region hide and disable controls
            if (Shared.user.SL_Invoice_PayMethod == false)
            {
                groupControl1.Visible = false;
                cmbPayMethod.Properties.Items.RemoveAt(2);
                cmbPayMethod.Enabled = true;
                cmbPayMethod.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
                cmbPayMethod.Properties.Buttons[0].Visible = true;
                lbl_Paid.Visible = txt_paid.Visible = lbl_remains.Visible = txt_Remains.Visible = false;
            }

            if (Shared.user.HideDeliverDate)
                pnlDeliveryDate.Visible = false;
            if (Shared.user.HideAgedReceivables)
                pnlAgeDate.Visible = false;

            if (Shared.user.HideShipTo)
                lblShipTo.Visible = txt_Shipping.Visible = false;
            if (Shared.user.HidePO)
                pnlPO.Visible = false;
            if (Shared.user.HideSalesEmp)
                pnlSalesEmp.Visible = false;

            if (Shared.user.SL_Invoice_PayMethod == true &&
                Shared.user.UserEditTransactionDate == false)
                dtInvoiceDate.Enabled = false;
            #endregion

            repItems.CloseUp += new DevExpress.XtraEditors.Controls.CloseUpEventHandler(this.repItems_CloseUp);

            ErpUtils.Load_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""));
            ErpUtils.Load_MemoryStream_Layout(rep_layout, this.Name + ".repItems");
            ErpUtils.ColumnChooser(grdPrInvoice);
            ErpUtils.ColumnChooser(grdLastPrices);

            #region Hide_Show_Columns
            if (!Shared.st_Store.UseHeightDimension)
                col_Height.OptionsColumn.ShowInCustomizationForm = col_Height.Visible = false;
            if (!Shared.st_Store.UseWidthDimension)
                col_Width.OptionsColumn.ShowInCustomizationForm = col_Width.Visible = false;
            if (!Shared.st_Store.UseLengthDimension)
                col_Length.OptionsColumn.ShowInCustomizationForm = col_Length.Visible = false;
            if (!Shared.st_Store.UseHeightDimension && !Shared.st_Store.UseWidthDimension && !Shared.st_Store.UseLengthDimension)
                col_TotalQty.OptionsColumn.ShowInCustomizationForm = col_TotalQty.Visible = false;
            if (!Shared.st_Store.PiecesCount)
                col_PiecesCount.OptionsColumn.ShowInCustomizationForm = col_PiecesCount.Visible = false;
            if (!Shared.user.Sell_ShowCrntQty)
                col_CurrentQty.OptionsColumn.ShowInCustomizationForm = col_CurrentQty.Visible = false;
            if (!Shared.user.UserEditSalePrice)
                col_SellPrice.OptionsColumn.AllowEdit = col_SellPrice.OptionsColumn.AllowFocus = false;
            if (Shared.user.HidePurchasePrice)
                colPurchasePrice.OptionsColumn.ShowInCustomizationForm = colPurchasePrice.Visible = false;
            if (Shared.user.HideItemDiscount)
            {
                gridView2.Columns["DiscountRatio"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountRatio"].Visible = false;
                gridView2.Columns["DiscountRatio2"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountRatio2"].Visible = false;
                gridView2.Columns["DiscountRatio3"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountRatio3"].Visible = false;
                gridView2.Columns["DiscountValue"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountValue"].Visible = false;
            }
            if (!Shared.TaxAvailable)
            {
                col_SalesTax.OptionsColumn.ShowInCustomizationForm = col_SalesTax.Visible = false;
                col_CusTax.OptionsColumn.ShowInCustomizationForm = col_CusTax.Visible = false;
            }

            //batch and expire in settings and also user profile
            if (!Shared.st_Store.ExpireDate)
                col_Expire.OptionsColumn.ShowInCustomizationForm = col_Expire.Visible = false;
            if (!Shared.st_Store.Batch)
                col_Batch.OptionsColumn.ShowInCustomizationForm = col_Batch.Visible = false;

            if (Shared.user.HideBatchColumn4User == true)
                col_Expire.OptionsColumn.ShowInCustomizationForm = col_Expire.Visible =
                col_Batch.OptionsColumn.ShowInCustomizationForm = col_Batch.Visible = false;

            if (!Shared.user.UserCanEditQty)
            {
                colQty.OptionsColumn.AllowEdit = colQty.OptionsColumn.AllowFocus = false;
                col_PiecesCount.OptionsColumn.AllowEdit = col_PiecesCount.OptionsColumn.AllowFocus = false;
            }

            if (!Shared.st_Store.Serial)
                col_Serial.OptionsColumn.ShowInCustomizationForm = col_Serial.Visible = false;

            col_Serial.Caption = Shared.IsEnglish ? Shared.st_Store.SerialNameEn : Shared.st_Store.SerialNameAr;
            col_Serial2.Caption = Shared.IsEnglish ? Shared.st_Store.Serial2NameEn : Shared.st_Store.Serial2NameAr;
            col_Batch.Caption = Shared.IsEnglish ? Shared.st_Store.BatchNameEn : Shared.st_Store.BatchNameAr;
            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
            col_ActualPiecesCount.Caption = Shared.IsEnglish ? "Actual " + Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr + " الحالى";
            #endregion

            if (Shared.st_Store.SalesDeductTaxAccount == null || Shared.TaxAvailable == false)
                txt_DeductTaxR.Enabled = txt_DeductTaxV.Enabled = false;

            if (Shared.st_Store.SalesAddTaxAccount == null || Shared.TaxAvailable == false)
                txt_AddTaxR.Enabled = txt_AddTaxV.Enabled /*=txt_CusTaxV.Enabled=txt_CusTaxR.Enabled*/= false;

            txt_TaxValue.BackColor = Color.White;
            txt_TaxValue.ForeColor = Color.DimGray;

            txtInvoiceCode.Leave += new EventHandler(txtInvoiceCode_Leave);

            grdcol_branch.Visible = false;


            // if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)//CANNOT FIND OBJECT with IsStoreOnEachSellRecord value= false
            //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
            if (Shared.st_Store.IsStoreOnEachSellRecord)
            {
                //lkpStore.Enabled = false;
                //lkpStore.EditValue = null;

                grdcol_branch.Visible = true;
                gridView2.FocusedColumn = grdcol_branch;
                barbtnPrintStore.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barbtnPrintStore.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }

            //#region print using samples
            //var printSamples = (from b in DB.ST_PrintSamples
            //                    where b.ProcessId == (int)Process.SellInvoice
            //                    select new
            //                    {
            //                        SampleId = (int?)b.SampleId,
            //                        b.SampleName,
            //                        b.PrintFileName
            //                    }).ToList();

            //if (printSamples.Count() > 0)
            //{
            //    System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_Invoice));
            //    foreach (var samp in printSamples)
            //    {
            //        DevExpress.XtraBars.BarButtonItem barPrintSample = new DevExpress.XtraBars.BarButtonItem(barManager1, samp.SampleName);
            //        barPrintSample.Name = "printSample" + samp.SampleId;
            //        resources.ApplyResources(barPrintSample, "printSample" + samp.SampleId);
            //        barPrintSample.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            //        barbtnSamplesPr.ItemLinks.Add(barPrintSample);
            //    }
            //}
            //#endregion

            //btn_MultipleWeights.Visible = lbl_MultipleWeights.Visible = Shared.LibraAvailabe;
            Col_Company.Caption = Shared.IsEnglish ? Shared.st_Store.CompanyEn : Shared.st_Store.CompanyAr;
            Col_Category.Caption = Shared.IsEnglish ? Shared.st_Store.CategoryEn : Shared.st_Store.CategoryAr;
            col_CompanyNameAr.Caption = Shared.IsEnglish ? Shared.st_Store.CompanyEn : Shared.st_Store.CompanyAr;
            ColCompanyNameAr.Caption = Shared.IsEnglish ? Shared.st_Store.CompanyEn : Shared.st_Store.CompanyAr;
            ColCategoryNameAr.Caption = Shared.IsEnglish ? Shared.st_Store.CategoryEn : Shared.st_Store.CategoryAr;
            col_CategoryNameAr.Caption = Shared.IsEnglish ? Shared.st_Store.CategoryEn : Shared.st_Store.CategoryAr;
            //gridView2.OptionsView.ShowFooter = true;
            //col_PiecesCount.Summary.Add(DevExpress.Data.SummaryItemType.Sum);
            if (Shared.E_invoiceAvailable)
            {
                colETaxValue.Visible = false;
                colETaxRatio.Visible = false;
                Col_ETaxType.Visible = false;
                TotalTaxes.Visible = false;
                totalTaxesRatio.Visible = false;
                btn_AddTaxes.Visible = false;
                col_TotalSubDiscountTax.Visible =
                col_TotalSubCustomTax.Visible =
                col_TotalSubAddTax.Visible =
                col_TaxValue.Visible = col_TotalSubDiscountTax.OptionsColumn.ShowInCustomizationForm =
                col_TotalSubCustomTax.OptionsColumn.ShowInCustomizationForm =
                col_TotalSubAddTax.OptionsColumn.ShowInCustomizationForm =
                col_TaxValue.OptionsColumn.ShowInCustomizationForm = true;


                colETaxValue.OptionsColumn.ShowInCustomizationForm = false;
                colETaxRatio.OptionsColumn.ShowInCustomizationForm = false;
                Col_ETaxType.OptionsColumn.ShowInCustomizationForm = false;
                TotalTaxes.OptionsColumn.ShowInCustomizationForm = false;
                totalTaxesRatio.OptionsColumn.ShowInCustomizationForm = false;
                btn_AddTaxes.OptionsColumn.ShowInCustomizationForm = false;
            }
            else
            {
                if (Shared.st_Store.E_AllowMoreThanTax == true)
                {
                    TotalTaxes.Visible = true;
                    // totalTaxesRatio.Visible = true;
                    btn_AddTaxes.Visible = true;

                    colETaxValue.Visible = false;
                    colETaxRatio.Visible = false;
                    Col_ETaxType.Visible = false;
                    colETaxValue.OptionsColumn.ShowInCustomizationForm = false;
                    colETaxRatio.OptionsColumn.ShowInCustomizationForm = false;
                    Col_ETaxType.OptionsColumn.ShowInCustomizationForm = false;

                }
                else
                {
                    colETaxValue.Visible = true;
                    colETaxRatio.Visible = true;
                    Col_ETaxType.Visible = true;

                    TotalTaxes.Visible = false;
                    totalTaxesRatio.Visible = false;
                    btn_AddTaxes.Visible = false;
                    TotalTaxes.OptionsColumn.ShowInCustomizationForm = false;
                    totalTaxesRatio.OptionsColumn.ShowInCustomizationForm = false;
                    btn_AddTaxes.OptionsColumn.ShowInCustomizationForm = false;

                }
            }
            //===============//

        }
        private void frm_SL_Invoice_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrevious.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                lkp_Customers.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                txtNotes.Focus();
                FocusItemCode1(true);
            }
            if (e.KeyCode == Keys.End && e.Modifiers == Keys.Control)
            {
                txtDiscountRatio.Focus();
            }
            if (e.KeyCode == Keys.F10)
            {
                Saved_Successfuly = false;
                barBtnSave.PerformClick();
                if (Saved_Successfuly == true)
                {
                    barBtnNew.PerformClick();
                    txtNotes.Focus();
                    FocusItemCode1(Shared.user.FocusGridInInvoices);
                }
            }
        }

        private void frm_SL_Invoice_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
            else
                e.Cancel = false;

            if (timer1.Enabled) timer1.Enabled = false;
            ErpUtils.save_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""), true);
            ErpUtils.save_MemoryStream_Layout(rep_layout, this.Name + ".repItems");

        }

        public void gridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (e.Column.FieldName == "LargeUOMFactor"
                || e.Column.FieldName == "MediumUOMFactor" || e.Column.FieldName == "TotalSellPrice" || e.Column.FieldName == "CurrentQty")
                return;

            DB = new DAL.ERPDataContext();
            DAL.IC_Item item = null;

            GridView view = grdPrInvoice.FocusedView as GridView;
            DataRow row = view.GetDataRow(e.RowHandle);

            #region Barcode_Init_Detail_PR

            Detail_PR detail_PR = new Detail_PR
            {
                ItemId = (int)0,
                Batch = "",
                Expire = (DateTime?)DateTime.Now,
                Length = (decimal?)1,
                Width = (decimal?)1,
                Height = (decimal?)1,
                PiecesCount = (decimal)0,
                PurchasePrice = (decimal)0,
                TotalPurchasePrice = (decimal)0,
                SellPrice = (decimal)0,
                Qty = (decimal)0,
                UOMId = (int)0,
                UOMIndex = (byte)0,
                VendorId = (int?)0
            };
            detail_PR = null;
            #endregion

            int barcodeTemplateCode = 0;
            decimal barcodeTemplateQty = 0;
            string barcodeBatch = string.Empty;
            string code1 = string.Empty;

            #region Rounding values based on settings
            if (e.Column.FieldName == "SellPrice" && Shared.st_Store.DefaultRoundingPoints != null)
            {
                var sellPriceValue = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                var sellPriceDisplayValue = Convert.ToDecimal(view.GetFocusedRowCellDisplayText("SellPrice"));
                if (sellPriceValue != sellPriceDisplayValue)
                {
                    view.SetFocusedRowCellValue("SellPrice", sellPriceDisplayValue);
                }
            }

            if (e.Column.FieldName == "DiscountValue" && Shared.st_Store.DefaultRoundingPoints != null)
            {
                var DiscountValueValue = Convert.ToDecimal(view.GetFocusedRowCellValue("DiscountValue"));
                var DiscountValueDisplayValue = Convert.ToDecimal(view.GetFocusedRowCellDisplayText("DiscountValue"));
                if (DiscountValueValue != DiscountValueDisplayValue)
                {
                    view.SetFocusedRowCellValue("DiscountValue", DiscountValueDisplayValue);
                }
            }
            
            #endregion



            #region GetItem
            if (e.Column.FieldName == "ItemCode1")
            {
                if (view.GetFocusedRowCellValue("ItemCode1") != null && view.GetFocusedRowCellValue("ItemCode1").ToString() != string.Empty)
                {
                    code1 = view.GetFocusedRowCellValue("ItemCode1").ToString();

                    item = MyHelper.SearchItem(DB, code1, detail_PR, ref barcodeTemplateCode, ref barcodeTemplateQty, ref barcodeBatch, invoiceId,
                        Shared.st_Store);
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode2")
            {
                if (view.GetFocusedRowCellValue("ItemCode2").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)DAL.ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                  i.ItemType == (int)DAL.ItemType.Assembly : true
                            where i.ItemCode2 == view.GetFocusedRowCellValue("ItemCode2").ToString()
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).FirstOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemId")
            {
                if (view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                      i.ItemType == (int)ItemType.Assembly : true
                            where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).SingleOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "ItemId")
            {
                if (item != null && item.ItemId > 0)
                {
                    if (item.ItemType == (int)ItemType.Subtotal)
                    {
                        row["ItemId"] = item.ItemId;
                        row["ItemCode1"] = item.ItemCode1;
                        row["ItemCode2"] = item.ItemCode2;
                        row["ItemType"] = item.ItemType;
                        row["IsExpire"] = item.IsExpire;
                        row["AudiencePrice"] = item.AudiencePrice;
                        row["ItemDescription"] = item.Description;
                        row["ItemDescriptionEn"] = item.DescriptionEn;
                        row["CategoryId"] = item.Category;
                        row["bonusDiscount"] = 0;
                        row["ETaxRatio"] = 0;
                        row["EtaxValue"] = 0;
                        Get_SubTotal_RowData(row, view, e.RowHandle);


                    }
                    else
                    {
                        LoadItemRow(item, row);
                        if (Shared.st_Store.PrintBarcodePerInventory && detail_PR != null)
                        {
                            row["PurchasePrice"] = Decimal.ToDouble(detail_PR.TotalPurchasePrice / (detail_PR.Qty));
                            row["PiecesCount"] = detail_PR.PiecesCount;
                            row["Length"] = detail_PR.Length.HasValue ? decimal.ToDouble(detail_PR.Length.Value) : 1;
                            row["Width"] = detail_PR.Width.HasValue ? decimal.ToDouble(detail_PR.Width.Value) : 1;
                            row["Height"] = detail_PR.Height.HasValue ? decimal.ToDouble(detail_PR.Height.Value) : 1;
                            if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                                row["Qty"] = decimal.ToDouble(detail_PR.Qty);

                            row["Batch"] = detail_PR.Batch;

                            if (detail_PR.Expire.HasValue)
                            {
                                MyHelper.GetExpireQtyDataTable(dtExpireQty);
                                row["ExpireDate"] = detail_PR.Expire.Value;
                                row["Expire"] = detail_PR.Expire.Value + detail_PR.Batch;
                            }
                            else
                            {
                                row["ExpireDate"] = DBNull.Value;
                                row["Expire"] = DBNull.Value;
                            }
                        }

                        if (Shared.st_Store.PrintBarcodePerInventory == false && barcodeTemplateCode > 0 /*&& barcodeTemplateQty > 0*/)
                        {
                            row["Qty"] = decimal.ToDouble(barcodeTemplateQty);
                            row["Batch"] = barcodeBatch;
                            row["PiecesCount"] = 1;
                        }
                        if (Shared.E_invoiceAvailable == true &&
        ((IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable"))) ||
        IsTaxable == true))
                        {
                            view.SetRowCellValue(e.RowHandle, "TaxValue", 0);
                            calcSubTaxes(row, view);
                        }
                    }
                    Get_TotalAccount();

                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                    return;
                }
            }
            #endregion

            if (view.GetRowCellValue(e.RowHandle, "ItemType") != null && Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemType")) == (int)ItemType.Subtotal)
            {
                Update_First_SubTotal(view, e.RowHandle);
                return;
            }
            
            #region GetUomPrice
            if (e.Column.FieldName == "UOM")
            {
                if (view.GetFocusedRowCellValue("UOM").ToString() != string.Empty &&
                    view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                    int uomIndex = Convert.ToInt32(ErpUtils.GetGridLookUpValue(repUOM, view.GetRowCellValue(e.RowHandle, "UOM"), "Index"));

                    //get UOM and Factor, multiple by purchase and sell prices
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                      i.ItemType == (int)ItemType.Assembly : true
                            where i.ItemId == itmId
                            select i).SingleOrDefault();

                    ///GET IC_STORE PRICE LIST
                    /////MOHAMMAD 31-05-2020
                    decimal uom_price = 0;
                    if (Shared.LibraAvailabe && CustomerPriceLevel == null)
                    {
                        int _storeId = Shared.st_Store.IsStoreOnEachSellRecord ?
                        (view.GetFocusedRowCellValue("StoreId") != DBNull.Value ? Convert.ToInt32(view.GetFocusedRowCellValue("StoreId")) : 0) :
                                                                                 Convert.ToInt32(lkpStore.EditValue);
                        var StorePriceList = (from s in DB.IC_Stores
                                              join p in DB.IC_PriceLevels on s.pricelistId equals p.PriceLevelId
                                              where s.StoreId == _storeId
                                              select p).FirstOrDefault();
                        if (StorePriceList != null)
                            uom_price = MyHelper.GetPriceLevelSellPrice(StorePriceList, item, uomIndex);
                        else
                            uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, uomIndex);
                    }
                    else
                        //////////////////////////////////////////////
                        uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, uomIndex);

                    view.GetDataRow(e.RowHandle)["SellPrice"] = decimal.Round(uom_price,defaultRoundingPoints);

                    if (uomIndex == 0)//small                    
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.Round(item.PurchasePrice,defaultRoundingPoints);
                    if (uomIndex == 1)//medium                                           
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.Round(item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor),defaultRoundingPoints);
                    if (uomIndex == 2)//large
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.Round(item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor), defaultRoundingPoints);

                    view.GetDataRow(e.RowHandle)["UomIndex"] = uomIndex;

                }
            }
            #endregion

            #region ItemQtyEquation
            if (e.Column.FieldName == "Qty" || e.Column.FieldName == "UOM")
            {
                if (view.GetFocusedRowCellValue("UOM").ToString() != string.Empty &&
                    view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty &&
                    view.GetFocusedRowCellValue("Qty").ToString() != string.Empty)
                {
                    int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                    byte uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));
                    decimal medium = 1;
                    decimal large = 1;
                    medium = MyHelper.FractionToDouble(view.GetFocusedRowCellValue("MediumUOMFactor").ToString());
                    large = MyHelper.FractionToDouble(view.GetFocusedRowCellValue("LargeUOMFactor").ToString());
                    decimal Qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                    if (!(CustomerPriceLevel != null && (CustomerPriceLevel.IsRatio == true || (CustomerPriceLevel.IsRatio == false
                                                      && CustomerPriceLevel.IC_PriceLevelDetails.Where(x => x.ItemId == itmId).Count() > 0))))
                    {
                        //decimal sellprice = MyHelper.Get_ItemPricesPerQty(itmId, Qty, uomIndex, medium, large);
                        //if (sellprice > 0)
                        //    view.GetDataRow(e.RowHandle)["SellPrice"] = decimal.ToDouble(sellprice);
                    }
                }
            }
            #endregion

            #region Calculate Prices
            if (e.Column.FieldName == "bonusDiscount" || e.Column.FieldName == "DiscountValue" || e.Column.FieldName == "SellPrice"
                || e.Column.FieldName == "Qty" || e.Column.FieldName == "ItemId"
                || e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "UOM"
                || e.Column.FieldName == "ETaxRatio"
                || e.Column.FieldName == "totalTaxesRatio"
                || e.Column == col_Height || e.Column == col_Width || e.Column == col_Length || e.Column.FieldName == "Batch")
            {
                try
                {

                    if (!string.IsNullOrEmpty(view.GetFocusedRowCellValue("SellPrice").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("DiscountValue").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("Qty").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("ItemId").ToString()))
                    {
                        decimal Height = Convert.ToDecimal(view.GetFocusedRowCellValue("Height"));
                        decimal Length = Convert.ToDecimal(view.GetFocusedRowCellValue("Length"));
                        decimal Width = Convert.ToDecimal(view.GetFocusedRowCellValue("Width"));
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                            Height = Length = Width = 1;

                        decimal Qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                        decimal TotalQty = Qty * Height * Width * Length;
                        decimal SellPrice = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                        decimal PurchasePrice = Convert.ToDecimal(view.GetFocusedRowCellValue("PurchasePrice"));
                        int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                        byte uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));
                        decimal SalesTaxRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["SalesTaxRatio"]);
                        decimal CustomTaxRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["CustomTaxRatio"]);
                        bool calcTaxBeforeDisc = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);
                        
                        if ((Shared.E_invoiceAvailable == true &&
                            ((IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable"))) ||
                            IsTaxable == true)) || Shared.st_Store.E_AllowMoreThanTax == true)
                        {
                            var SubTaxes = DB.IC_ItemSubTaxes.Where(a => a.ItemId == itmId).ToList();
                            var SubTaxesIds = SubTaxes.Select(a => a.SubTaxId).ToList();
                            var dataRowsCountSubTax = Dt_Rows.AsEnumerable()
                                 .Where(a => a.RowState != DataRowState.Deleted)
                                 .Where(a => a["ItemId"].ToString() == itmId.ToString() && Convert.ToInt32(a["RowHandle"].ToString()) == rowhandle).Select(a => Convert.ToInt32(a["SubTax"].ToString())).ToList();
                            if (Enumerable.SequenceEqual(dataRowsCountSubTax.Intersect(SubTaxesIds).ToList().OrderBy(m => m), SubTaxesIds.OrderBy(m => m)) &&
                                dataRowsCountSubTax.Count == SubTaxesIds.Count)
                            {
                                var item1 = DB.IC_Items.FirstOrDefault(a => a.ItemId == itmId);
                                if (item1 != null && e.RowHandle >= 0)
                                    calcSubTaxes(view.GetDataRow(e.RowHandle), view);
                            }


                        }
                        decimal totalTaxesRatio = 0;
                        decimal DiscV = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountValue"]);
                        if (view.GetDataRow(e.RowHandle)["totalTaxesRatio"] != DBNull.Value && view.GetDataRow(e.RowHandle)["totalTaxesRatio"] != null)
                            totalTaxesRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["totalTaxesRatio"]);
                        decimal EtaxRatio = 0;
                        if (view.GetDataRow(e.RowHandle)["ETaxRatio"] != DBNull.Value && view.GetDataRow(e.RowHandle)["ETaxRatio"] != null)
                            EtaxRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["ETaxRatio"]);
                        decimal TaxType = 0;
                        if (view.GetDataRow(e.RowHandle)["TaxType"] != DBNull.Value && view.GetDataRow(e.RowHandle)["TaxType"] != null)
                            TaxType = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["TaxType"]);

                        //CalcTotalPrice(e.RowHandle, view, TotalQty, SellPrice, SalesTaxRatio, CustomTaxRatio, calcTaxBeforeDisc, DiscV, EtaxRatio, TaxType, totalTaxesRatio, defaultRoundingPoints);
                        view.GetDataRow(e.RowHandle)["ETaxValue"] = Math.Round(((TotalQty * SellPrice - DiscV) * EtaxRatio / 100),defaultRoundingPoints );
                        view.GetDataRow(e.RowHandle)["TotalTaxes"] = Math.Round(Convert.ToDecimal((TotalQty * SellPrice - DiscV) * totalTaxesRatio / 100), defaultRoundingPoints);

                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.DistinguishAndMultiply)
                            view.GetDataRow(e.RowHandle)["PiecesCount"] = Qty;

                        //if (totalTaxesRatio != 0 && DiscV!=0)
                        //    calcSubTaxes(row, view);

                        //get store qty
                        decimal medium = 1;
                        decimal large = 1;
                        if (view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString() != string.Empty)
                            medium = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString());
                        if (view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString() != string.Empty)
                            large = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString());
                        int store_id;

                        //  if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                        //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                        if (Shared.st_Store.IsStoreOnEachSellRecord)
                        {
                            if (gridView2.GetFocusedRowCellValue("StoreId") != DBNull.Value)
                                store_id = Convert.ToInt32(gridView2.GetFocusedRowCellValue("StoreId"));
                            else
                                store_id = 0;
                        }
                        else
                        {
                            store_id = Convert.ToInt32(lkpStore.EditValue);
                        }
                        Calculate_Qty(itmId, e, store_id, uomIndex, medium, large, view.GetDataRow(e.RowHandle)["Batch"].ToString());

                        if (!Shared.LibraAvailabe)
                            Get_TotalAccount();
                    }
                }
                catch (Exception f)
                {
                    MessageBox.Show(f.Message);
                }
            }
            #endregion

            #region DiscountRation_changed_by_User
            if (/*e.Column.FieldName == "bonusDiscount"*/ /*|| e.Column.FieldName == "ETaxRatio" ||*/
                e.Column.FieldName == "DiscountRatio" || e.Column.FieldName == "DiscountRatio2" || e.Column.FieldName == "DiscountRatio3"
                || e.Column.FieldName == "Qty" || e.Column.FieldName == "SellPrice" || e.Column.FieldName == "UOM")
            {
                try
                {
                    if (!string.IsNullOrEmpty(view.GetFocusedRowCellValue(e.Column.FieldName).ToString()))
                    {
                        decimal Height = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Height"));
                        decimal Length = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Length"));
                        decimal Width = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Width"));
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                            Height = Length = Width = 1;

                        decimal Qty = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Qty"));
                        decimal TotalQty = Qty * Height * Width * Length;

                        decimal SellPrice = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "SellPrice"));
                        decimal DiscR1 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio"]);
                        decimal DiscR2 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio2"]);
                        decimal DiscR3 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio3"]);

                        bool calcTaxBeforeDisc = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);
                        //========================================//
                        int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                        var SubTaxes = DB.IC_ItemSubTaxes.Where(a => a.ItemId == itmId).ToList();
                        var SubTaxesIds = SubTaxes.Select(a => a.SubTaxId).ToList();

                        //====================================//
                        decimal salesTax = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["SalesTax"]);
                        decimal CustomTax = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["CustomTax"]);
                        //decimal bonusDiscount = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["bonusDiscount"]);
                        decimal totalSellP = calcTaxBeforeDisc ? ((SellPrice * TotalQty) - salesTax) : SellPrice * TotalQty;
                        totalSellP = calcTaxBeforeDisc ? ((SellPrice * TotalQty) - CustomTax) : SellPrice * TotalQty;
                        decimal DiscountValue = Utilities.Calc_DiscountValue(DiscR1, DiscR2, DiscR3, totalSellP);
                        view.SetRowCellValue(e.RowHandle, "DiscountValue", decimal.ToDouble(DiscountValue).ToString($"F{defaultRoundingPoints}"));

                    }
                }
                catch { }
            }
            #endregion

            #region totalTableTaxes

            //decimal Qty1 = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Qty"));
            //decimal SellPrice1 = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "SellPrice"));
            //decimal DiscR11 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio"]);
            //decimal DiscR21 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio2"]);
            //decimal DiscR31 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio3"]);
            //bool calcTaxBeforeDisc1 = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);
            //var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId.ToString()).ToList();
            ////=======================//
            //var tableTaxes = Dt_Rows.AsEnumerable()
            //     .Where(a => a.RowState != DataRowState.Deleted)
            //     .Where(r => tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(row["RowHandle"])).ToList();
            //decimal tableTaxesType = tableTaxes.Sum(a => Convert.ToInt32(a["Percentage"]));
            ////=======================//
            //var addTaxes = Dt_Rows.AsEnumerable()
            //.Where(a => a.RowState != DataRowState.Deleted)
            //.Where(r => r["Tax"].ToString() == "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(row["RowHandle"])).ToList();
            //decimal addTaxesType = addTaxes.Sum(a => Convert.ToInt32(a["Percentage"]));
            //if (view.GetDataRow(e.RowHandle)["totalTableTaxes"] != DBNull.Value && view.GetDataRow(e.RowHandle)["totalTableTaxes"] != null)
            //{

            //    decimal totalTableTaxes = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["totalTableTaxes"]);
            //    CalcTableTax(e.RowHandle, view, DiscR11, DiscR21, DiscR31, SellPrice1, Qty1, calcTaxBeforeDisc1, totalTableTaxes);
            //}

            //if (view.GetDataRow(e.RowHandle)["tableTaxValue"] != DBNull.Value && view.GetDataRow(e.RowHandle)["tableTaxValue"] != null)
            //{

            //    CalcTableTaxType(e.RowHandle, view, DiscR11, DiscR21, DiscR31, SellPrice1, Qty1, calcTaxBeforeDisc1, tableTaxesType);
            //}


            //if (view.GetDataRow(e.RowHandle)["addTaxValue"] != DBNull.Value && view.GetDataRow(e.RowHandle)["addTaxValue"] != null)
            //{


            //    CalcAddTaxType(e.RowHandle, view, DiscR11, DiscR21, DiscR31, SellPrice1, Qty1, calcTaxBeforeDisc1, addTaxesType, tableTaxesType);
            //}
            #endregion
            calcSubTaxes(row, view);
            if (e.Column.FieldName == "Expire")
            {
                try
                {
                    view.SetRowCellValue(e.RowHandle, col_Batch, ErpUtils.GetGridLookUpValue(rep_expireDate, view.FocusedValue, "Batch"));
                    if (ErpUtils.GetGridLookUpValue(rep_expireDate, view.FocusedValue, "Expire") != null)
                        view.GetDataRow(e.RowHandle)["ExpireDate"] = ErpUtils.GetGridLookUpValue(rep_expireDate, view.FocusedValue, "Expire");
                    else
                        view.GetDataRow(e.RowHandle)["ExpireDate"] = DBNull.Value;
                }
                catch { }
            }

            if (e.Column.FieldName == "StoreId")
            {
                decimal medium = 1;
                decimal large = 1;
                int itmId;
                int store_id;
                byte uomIndex;

                if (view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString() != string.Empty)
                    medium = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString());
                if (view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString() != string.Empty)
                    large = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString());

                if (view.GetFocusedRowCellValue("ItemId") is DBNull)
                {
                    itmId = 0;
                    uomIndex = 0;
                }
                else
                {
                    itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                    uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));

                }
                // byte uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));

                if (view.GetFocusedRowCellValue("StoreId") is DBNull)
                {
                    store_id = 0;
                }
                else
                {
                    store_id = Convert.ToInt32(view.GetFocusedRowCellValue("StoreId"));
                }
                // store_id = Convert.ToInt32(view.GetFocusedRowCellValue("StoreId"));

                Calculate_Qty(itmId, e, store_id, uomIndex, medium, large, view.GetRowCellValue(e.RowHandle, "Batch").ToString());

            }

            //if (e.Column.FieldName == "Batch")
            //{
            //    int store_id;

            //    //  if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
            //    if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
            //    {
            //        if (gridView2.GetFocusedRowCellValue("StoreId") != DBNull.Value)
            //            store_id = Convert.ToInt32(gridView2.GetFocusedRowCellValue("StoreId"));
            //        else
            //            store_id = 0;
            //    }
            //    else
            //    {
            //        store_id = Convert.ToInt32(lkpStore.EditValue);
            //    }
            //    view.GetFocusedDataRow()["CurrentQty"] = MyHelper.GetItemQty(dtInvoiceDate.DateTime, Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"), store_id, view.GetDataRow(e.RowHandle)["Batch"]);//Convert.ToInt32(ErpUtils.GetGridLookUpValue(rep_Batch, view.GetDataRow(e.RowHandle)["Batch"], "Qty"));
            //}



            //specific for Libra settings 
            if (Shared.LibraAvailabe)
            {
                if (e.Column.FieldName == "StoreId")
                {
                    return;
                }
                var h = view.GetRowCellValue(e.RowHandle, "ItemId");
                if (item == null & view.GetRowCellValue(e.RowHandle, "ItemId") != DBNull.Value)
                    item = (from i in DB.IC_Items
                            where i.ItemId == Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"))
                            select i).FirstOrDefault();
                else if (item == null)
                    MessageBox.Show("item == null");
                decimal discountValue = 0;
                var _discount = view.GetFocusedRowCellValue("DiscountValue");
                if (_discount != null && _discount != DBNull.Value)
                    discountValue = Convert.ToDecimal(_discount);


                //var VariableWeight = view.GetFocusedRowCellValue("VariableWeight");
                int _PiecesCount = 0;
                //if (VariableWeight != null && VariableWeight != DBNull.Value && Convert.ToBoolean(VariableWeight))
                //{
                if (view.GetFocusedRowCellValue("PiecesCount") != DBNull.Value)
                    _PiecesCount = Convert.ToInt32(view.GetFocusedRowCellValue("PiecesCount"));
                if (_PiecesCount < 1)
                {
                    _PiecesCount = 1;
                    view.SetFocusedRowCellValue("PiecesCount", 1);
                }
                //}


                //في حالة ان الصنف وزنه متغير، هنخلي الكمية بتساوي الوزن بالكيلو
                if (e.Column == col_kg_Weight_libra && item != null && item.VariableWeight == true)
                {
                    view.SetFocusedRowCellValue("Qty", view.GetFocusedRowCellValue(col_kg_Weight_libra));
                }
                //في حالة ان الوزن ثابت، هنخلي الوزن بالكيلو بيساوي عدد القطع مضروب في معامل وحدة القياس
                //ونخلي الكمية بتساوي عدد القطع
                else if ((e.Column.FieldName == "PiecesCount" || e.Column.FieldName == "ItemId" || e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2" || e.Column.FieldName == "UOM") && item != null && item.PricingWithSmall == true)
                {
                    //var xxx = repUOM.GetRowByKeyValue(view.GetFocusedRowCellValue("UOM"));
                    //if (xxx != DBNull.Value)
                    //{
                    try
                    {
                        decimal f = 0;
                        ////if (xxx == null)
                        //{
                        var _uom = view.GetFocusedRowCellValue("UOM");
                        if (_uom != DBNull.Value)
                        {
                            byte uom = Convert.ToByte(_uom);
                            f = item.SmallUOM == uom ? 1 : (item.MediumUOM == uom ? item.MediumUOMFactorDecimal.Value : item.LargeUOMFactorDecimal.Value);
                        }
                        //}
                        //else
                        //f = Convert.ToDecimal(((DataRowView)xxx)["Factor"]);
                        var PiecesCount = Convert.ToDecimal(view.GetFocusedRowCellValue("PiecesCount"));

                        view.SetFocusedRowCellValue(col_kg_Weight_libra, Convert.ToDouble(f * PiecesCount));
                        view.SetFocusedRowCellValue("Qty", PiecesCount);
                    }
                    catch
                    { }
                    //}
                }
                else if (e.Column.FieldName == "PiecesCount" && item != null && item.VariableWeight != true &&
                   item.VariableWeight != true && item.PricingWithSmall != true && item.is_libra != true)
                {
                    var PiecesCount = Convert.ToDecimal(view.GetFocusedRowCellValue("PiecesCount"));
                    view.SetFocusedRowCellValue("Qty", PiecesCount);
                }
                else if (e.Column.FieldName == "Qty" && item != null && item.is_libra == true)
                {
                    view.SetFocusedRowCellValue(col_kg_Weight_libra, view.GetFocusedRowCellValue("Qty"));
                }
                var PricingWithSmall = view.GetFocusedRowCellValue("PricingWithSmall");
                if (PricingWithSmall != null && PricingWithSmall != DBNull.Value && Convert.ToBoolean(PricingWithSmall))
                {
                    //var xxx = repUOM.GetRowByKeyValue(view.GetFocusedRowCellValue("UOM"));
                    //if (xxx != DBNull.Value)
                    {
                        decimal _sp = 0, _qty = 0, _piecescount = 0;
                        try
                        {
                            decimal f = 0;
                            //if (xxx == null)
                            //{
                            var _uom = view.GetFocusedRowCellValue("UOM");
                            if (_uom != DBNull.Value)
                            {
                                byte uom = Convert.ToByte(_uom);
                                f = item.SmallUOM == uom ? 1 : (item.MediumUOM == uom ? item.MediumUOMFactorDecimal.Value : item.LargeUOMFactorDecimal.Value);
                            }
                            //}
                            //else
                            //    f = Convert.ToDecimal(((DataRowView)xxx)["Factor"]);
                            var sp = _sp = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice")) - discountValue;
                            var qty = _qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                            var PiecesCount = _piecescount = Convert.ToDecimal(view.GetFocusedRowCellValue("PiecesCount"));
                            var kg_Weight_libra = Convert.ToDecimal(view.GetFocusedRowCellValue(col_kg_Weight_libra));

                            view.SetFocusedRowCellValue("TotalSellPrice", Convert.ToDouble(kg_Weight_libra * sp)); /*Convert.ToDouble(f * sp * qty)/** (_PiecesCount > 1 ? PiecesCount : 1)*///);

                        }
                        catch (Exception exc)
                        {
                            Utilities.save_Log(exc.Message, exc);
                            Utilities.save_Log("sp= " + _sp, exc);
                            Utilities.save_Log("qty= " + _qty, exc);
                            Utilities.save_Log("PiecesCount= " + _PiecesCount, exc);
                        }
                    }
                }

                if (e.Column.FieldName == "ItemId" || e.Column.FieldName == "LibraQty" ||
                     (e.Column.FieldName == "PiecesCount" && item != null))
                {
                    if (item.is_libra != true) return;

                    var Large_price = view.GetRowCellValue(e.RowHandle, "LibraQty");
                    if (Large_price == DBNull.Value || Large_price == null) return;
                    view.SetRowCellValue(e.RowHandle, "Qty", Math.Round((Convert.ToDecimal(Large_price) / MyHelper.FractionToDouble(item.LargeUOMFactorDecimal.Value.ToString())), defaultRoundingPoints));

                }

                Get_TotalAccount();

            }

            Update_First_SubTotal(view, e.RowHandle);
            Get_TotalAccount();
        }
        public void CalcTableTax(int RowHandle, GridView view, decimal disc1, decimal disc2, decimal disc3, decimal SellPrice, decimal Qty, bool calcTaxBeforeDisc, decimal totalTableTaxes)
        {
            var TotalSellPrice = Convert.ToDecimal(Qty * SellPrice * (1 - disc1) * (1 - disc2) * (1 - disc3));
            totalTableTaxes = (totalTableTaxes / 100) * TotalSellPrice;
            view.GetDataRow(RowHandle)["salePriceWithTaxTable"] = decimal.ToDouble(TotalSellPrice + totalTableTaxes);
        }
        public void CalcTableTaxType(int RowHandle, GridView view, decimal disc1, decimal disc2, decimal disc3, decimal SellPrice, decimal Qty, bool calcTaxBeforeDisc, decimal totalTableTaxesType)
        {
            var TotalSellPrice = Convert.ToDecimal(Qty * SellPrice * (1 - disc1) * (1 - disc2) * (1 - disc3));
            totalTableTaxesType = (totalTableTaxesType / 100) * TotalSellPrice;
            view.GetDataRow(RowHandle)["tableTaxValue"] = decimal.ToDouble(totalTableTaxesType);
        }
        public void CalcAddTaxType(int RowHandle, GridView view, decimal disc1, decimal disc2, decimal disc3, decimal SellPrice, decimal Qty, bool calcTaxBeforeDisc, decimal totaladdTaxesTyp, decimal tableTaxesType)
        {
            var TotalSellPrice = Convert.ToDecimal(Qty * SellPrice * (1 - disc1) * (1 - disc2) * (1 - disc3));
            var tableTaxes = (tableTaxesType / 100) * TotalSellPrice;
            TotalSellPrice = tableTaxes + TotalSellPrice;
            var addTaxValue = (totaladdTaxesTyp / 100) * TotalSellPrice;
            view.GetDataRow(RowHandle)["addTaxValue"] = decimal.ToDouble(addTaxValue);
        }

        public static void CalcTotalPrice(int RowHandle, GridView view, decimal TotalQty, decimal SellPrice, decimal SalesTaxRatio, decimal CustomTaxRatio, bool calcTaxBeforeDisc, decimal DiscV, decimal EtaxRatio, decimal TaxType, decimal totalTaxesRatio, byte roundingDigits)
        {
            var DB = new DAL.ERPDataContext();
            int? taxID = 0;
            if (TaxType != 0)
                taxID = DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == TaxType).ParentTaxId;

            decimal TotalSellPrice = (TotalQty * SellPrice) - DiscV;
            decimal EtaxValue = TotalSellPrice * EtaxRatio / 100;
            decimal EtotalTaxesRatio = TotalSellPrice * totalTaxesRatio / 100;
            if (taxID.Value == discountTaxId && taxID.Value != 0)
            {
                TotalSellPrice -= EtaxValue + EtotalTaxesRatio;
            }
            else
            {
                TotalSellPrice += EtaxValue + EtotalTaxesRatio;
            }

            decimal bonusDiscount = Convert.ToDecimal(view.GetDataRow(RowHandle)["bonusDiscount"]);
            TotalSellPrice = TotalSellPrice - bonusDiscount;

            if (Shared.st_Store.PriceIncludeSalesTax)/*السعر شامل الضريبة*/
            {
                decimal salesTaxValue = 0;

                decimal customTaxValue = calcTaxBeforeDisc
                                               ? (CustomTaxRatio * TotalQty * SellPrice) / (1 + CustomTaxRatio)
                                               : (CustomTaxRatio * TotalSellPrice) / (1 + CustomTaxRatio);

                view.SetRowCellValue(RowHandle, "CustomTax", decimal.ToDouble(customTaxValue));

                if (CustomTaxRatio > 0)
                {


                    var salesTaxMask = TotalSellPrice + customTaxValue;
                    salesTaxValue = salesTaxMask * SalesTaxRatio;

                    view.SetRowCellValue(RowHandle, "SalesTax", decimal.ToDouble(salesTaxValue));

                }


                else
                {
                    salesTaxValue = calcTaxBeforeDisc
                    ? (SalesTaxRatio * TotalQty * SellPrice) / (1 + SalesTaxRatio)
                    : (SalesTaxRatio * TotalSellPrice) / (1 + SalesTaxRatio);


                    view.SetRowCellValue(RowHandle, "SalesTax", decimal.ToDouble(salesTaxValue));
                }


                view.GetDataRow(RowHandle)["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice - salesTaxValue - customTaxValue);// السعر الاجمالي شامل الضريبة                            

                //--------------------------------------------------------------------------------------//

            }

            else                                            /*السعر غير شامل الضريبة*/
            {
                if (CustomTaxRatio > 0)
                {
                    var customTaxValue = calcTaxBeforeDisc ? (CustomTaxRatio * TotalQty * SellPrice) : (CustomTaxRatio * TotalSellPrice);
                    view.SetRowCellValue(RowHandle, "CustomTax", decimal.ToDouble(customTaxValue));

                    var salesTaxMask = TotalSellPrice + customTaxValue;
                    var salesTaxValue = salesTaxMask * SalesTaxRatio;
                    view.SetRowCellValue(RowHandle, "SalesTax", decimal.ToDouble(salesTaxValue));

                }

                else
                {
                    decimal salesTaxValue = calcTaxBeforeDisc ? (SalesTaxRatio * TotalQty * SellPrice) : (SalesTaxRatio * TotalSellPrice);
                    view.SetRowCellValue(RowHandle, "SalesTax", decimal.ToDouble(salesTaxValue));
                    view.SetRowCellValue(RowHandle, "CustomTax", 0);

                }

                view.GetDataRow(RowHandle)["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);//ضيف الضريبة على السعر الاجمالي 

            }
        }

        private void gridView1_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "UOM")
            {
                DataRow row = view.GetFocusedDataRow();

                DB = new DAL.ERPDataContext();
                DAL.IC_Item item = new DAL.IC_Item();

                if (row != null && row["ItemId"].ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                      i.ItemType == (int)ItemType.Assembly : true
                            where i.ItemId == Convert.ToInt32(row["ItemId"])
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).SingleOrDefault();

                    MyHelper.GetUOMs(item, dtUOM, uom_list);

                    if (string.IsNullOrEmpty(row["UOM"].ToString()))
                        view.GetFocusedDataRow()["UOM"] = dtUOM.Rows[0]["UomId"];
                }
            }
            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "Expire")
            {
                DataRow row = view.GetFocusedDataRow();
                if (row != null && row["ItemId"].ToString() != string.Empty && Convert.ToBoolean(row["IsExpire"]))
                {
                    MyHelper.Get_Expire_Qtys(Convert.ToInt32(row["ItemId"]), Convert.ToInt32(lkpStore.EditValue), dtInvoiceDate.DateTime, dtExpireQty);

                    if (dtExpireQty.Rows.Count <= 0)
                    {
                        view.GetFocusedDataRow()["ExpireDate"] = DBNull.Value;
                        view.GetFocusedDataRow()["Expire"] = DBNull.Value;
                        view.GetFocusedDataRow()["Batch"] = DBNull.Value;
                    }
                }
            }
            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "Batch")
            {
                DataRow row = view.GetFocusedDataRow();
                //if (row != null && row["ItemId"].ToString() != string.Empty)
                //{
                //    MyHelper.Get_Batch_Qtys(Convert.ToInt32(row["ItemId"]), Convert.ToInt32(lkpStore.EditValue), dtInvoiceDate.DateTime, dtBatchQty);

                //}
            }

            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "StoreId")
            {
                if (stores_table.Count > 1)
                    e.FocusedColumn.OptionsColumn.ReadOnly = false;
                else
                    e.FocusedColumn.OptionsColumn.ReadOnly = true;

            }

        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            GridView view = sender as GridView;
            try
            {
                if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
                {
                    //if (MessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDelRow : ResSLAr.MsgDelRow,
                    //    Shared.IsEnglish ? ResSLEn.MsgTQues : ResSLAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                    //  DialogResult.Yes)
                    //    return;

                    var query = Dt_Rows.AsEnumerable()
                              .Where(
                                      r => r.Field<int>("ItemId") == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                                      && r.Field<decimal>("SellPrice") == Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"))
                                      && r.Field<decimal>("Qty") == Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"))
                                      )
                                      .Where(a => a.RowState != DataRowState.Deleted);

                    foreach (var row in query.ToList())
                        row.Delete();
                    view.DeleteRow(view.FocusedRowHandle);
                    dtSL_Details.AcceptChanges();
                    Dt_Rows.AcceptChanges();


                    Update_First_SubTotal(view, view.FocusedRowHandle);
                    taxValue = 0;
                    foreach (DataRow dr in dtSL_Details.Rows)
                    {

                        if (dr.RowState == DataRowState.Deleted)
                            continue;

                        calcSubTaxes(dr, view);

                    }

                    Get_TotalAccount();
                }
                if (e.KeyCode == Keys.Up && e.Control && e.Shift)
                {
                    ErpUtils.Move_Row_Up(view);
                    Update_First_SubTotal(view, view.FocusedRowHandle - 1);
                    Update_First_SubTotal(view, view.FocusedRowHandle + 2);
                }
                if (e.KeyCode == Keys.Down && e.Control && e.Shift)
                {
                    ErpUtils.Move_Row_Down(view);
                    Update_First_SubTotal(view, view.FocusedRowHandle - 1);
                    Update_First_SubTotal(view, view.FocusedRowHandle + 2);
                }
            }
            catch (Exception exc) { MessageBox.Show(exc.Message); }
        }

        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            try
            {
                ColumnView view = sender as ColumnView;

                if (Shared.LibraAvailabe)
                {
                    var itmm = view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]);
                    var item = DB.IC_Items.Where(x => x.ItemId == Convert.ToInt32(itmm)).FirstOrDefault();
                    if (itmm != null && itmm != DBNull.Value)
                    {
                        if (item != null && item.is_libra == true)
                        {
                            if ((view.GetRowCellValue(e.RowHandle, view.Columns["LibraQty"])).ToString() == string.Empty
                                || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["LibraQty"])) <= 0)
                            {
                                e.Valid = false;
                                view.SetColumnError(view.Columns["LibraQty"], Shared.IsEnglish ? ResPrEn.txtValidateQty : ResPrAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                            }
                            else
                            { e.Valid = true; }
                        }
                    }
                    try
                    {
                        decimal PiecesCount = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "PiecesCount"));
                        if (PiecesCount < 1)
                        {
                            e.Valid = false;
                            view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? "Pieces count must be larger than 0" : "عدد القطع يجب أن يكون أكبر من صفر");//"يجب ادخال عدد القطع";}
                        }
                    }
                    catch (Exception x)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? "Pieces count must be a number larger than 0" : "عدد القطع يجب أن يكون رقم أكبر من صفر");//"يجب ادخال عدد القطع";}
                    }


                    if ((item.VariableWeight == true || item.is_libra == true || item.PricingWithSmall == true) &&
                        view.GetRowCellValue(e.RowHandle, view.Columns["kg_Weight_libra"]).ToString() == string.Empty)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["kg_Weight_libra"], Shared.IsEnglish ? "You must enter the weight (kg)" : "يجب إدخال الوزن بالكيلو");
                    }

                    if (view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"]).ToString() == string.Empty)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? "You must enter the pieces count" : "يجب إدخال عدد القطع");
                    }

                }
                if (Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Subtotal)
                    return;

                int itemid = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"));
                byte uomIndex = Convert.ToByte(view.GetRowCellValue(e.RowHandle, "UomIndex"));

                if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["ItemId"], Shared.IsEnglish ? ResSLEn.txtValidateItem : ResSLAr.txtValidateItem);//"يجب اختيار الصنف";
                }
                //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                if (Shared.st_Store.IsStoreOnEachSellRecord)
                {
                    if (view.GetRowCellValue(e.RowHandle, view.Columns["StoreId"]).ToString() == string.Empty)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["StoreId"], Shared.IsEnglish ? ResPrEn.ValBranch : ResPrEn.ValBranch);//"يجب اختيار الفرع";
                    }
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["UOM"], Shared.IsEnglish ? ResSLEn.txtValidateUom : ResSLAr.txtValidateUom);//"يجب اختيار وحدة القياس");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])) <= 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Qty"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }



                #region Validate Height Length and Width
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Height"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Height"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Height"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Height"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Length"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Length"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Length"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Length"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Width"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Width"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Width"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Width"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"])) < 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }

                #endregion

                if (view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"])) < 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["SellPrice"], Shared.IsEnglish ? ResSLEn.txtValidateSPrice : ResSLAr.txtValidateSPrice);//"سعر البيع يجب أن يكون أكبر من الصفر");
                    return;
                }
                else
                {
                    decimal argument = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"]));
                    int count = BitConverter.GetBytes(decimal.GetBits(argument)[3])[2];
                    if (count > 5)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["SellPrice"], Shared.IsEnglish ? " Can not add more than 5 digits after decimal point" : " سعر البيع لا يمكن اضافة اكثر من 5 ارقام بعد العلامة العشرية");
                        return;
                    }
                }

                if ((Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["PurchasePrice"])) >=
                    Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"])))
                    && Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) != (int)ItemType.Service
                    && Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) != (int)ItemType.Subtotal)
                {
                    if (Shared.user.SellLessThanBuyPrice == true)
                        XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateSpriceLargerPprice : ResSLAr.txtValidateSpriceLargerPprice,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    else if (Shared.user.SellLessThanBuyPrice == false)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["PurchasePrice"], Shared.IsEnglish ? ResSLEn.txtValidateSpriceLargerPprice : ResSLAr.txtValidateSpriceLargerPprice);// "سعر الشراء يجب أن يكون أقل من سعر البيع");
                        view.SetColumnError(view.Columns["SellPrice"], Shared.IsEnglish ? ResSLEn.txtValidateSpriceLargerPprice : ResSLAr.txtValidateSpriceLargerPprice);//"سعر البيع يجب أن يكون أكبر من سعر الشراء");
                    }
                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountValue"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountValue"], Shared.IsEnglish ? ResSLEn.txtValidateDiscount : ResSLAr.txtValidateDiscount);//"يجب تحديد الخصم");
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio2"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio2"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio2"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio3"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio3"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio3"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["ETaxRatio"]) != DBNull.Value && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["ETaxRatio"])) > 0)
                {
                    if (view.GetRowCellValue(e.RowHandle, view.Columns["TaxType"]).ToString() == string.Empty)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["TaxType"], Shared.IsEnglish ? ResSLEn.txtvalidationTaxType : ResSLAr.txtvalidationTaxType);
                    }

                }


                if (Shared.InvoicePostToStore)
                {
                    int str_id;
                    int item_id = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"));
                    int uom_id = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "UOM"));
                    decimal factor = 1;
                    if (uomIndex == 1)
                        factor = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "MediumUOMFactor"));
                    else if (uomIndex == 2)
                        factor = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "LargeUOMFactor"));



                    //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                    //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                    if (Shared.st_Store.IsStoreOnEachSellRecord)
                        str_id = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "StoreId"));
                    else
                        str_id = Convert.ToInt32(lkpStore.EditValue);




                    decimal length = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Length"));
                    decimal width = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Width"));
                    decimal height = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Height"));

                    decimal Qty = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Qty")) * factor;
                    decimal CurrentQty = 0;
                    var isoffer = view.GetFocusedRowCellValue("IsOffer");
                    //if (isoffer == DBNull.Value || isoffer == null)
                    //    CurrentQty = 0;// MyHelper.GetItemQty(dtInvoiceDate.DateTime, item_id, str_id, SlOrdrId, view.GetRowCellValue(e.RowHandle, "Batch").ToString());
                    //else
                    //    CurrentQty = Convert.ToDecimal(view.GetFocusedRowCellValue("CurrentQty"));
                    //if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                    //    CurrentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item_id, str_id, length, width, height, view.GetRowCellValue(e.RowHandle, "Batch").ToString());

                    #region Can't Sell over current Qty
                    if (Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) != (int)ItemType.Service
                            && Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) != (int)ItemType.Subtotal
                            //&& invoiceId == 0
                            && Qty > CurrentQty)//validate when new invoice only
                    {
                        if (Shared.user.SellWithNoBalance == true)
                        {
                            XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateStoreQty : ResSLAr.txtValidateStoreQty,
                                   Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                            return;
                        }
                        else if (Shared.user.SellWithNoBalance == false)
                        {
                            e.Valid = false;
                            view.SetColumnError(view.Columns["Qty"], Shared.IsEnglish ? ResSLEn.txtValidateStoreQty : ResSLAr.txtValidateStoreQty);//"الكمية لا يمكن ان تكون اكبر من الرصيدالحالي");
                        }
                    }

                    if (Shared.st_Store.PiecesCount)
                    {
                        decimal ActualPiecesCount = 0;
                        decimal PiecesCount = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "PiecesCount"));
                        var ActualPiecesCountValue = view.GetRowCellValue(e.RowHandle, "ActualPiecesCount");
                        if (ActualPiecesCountValue != DBNull.Value && ActualPiecesCountValue != null)
                            ActualPiecesCount = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "ActualPiecesCount"));

                        if (PiecesCount > ActualPiecesCount)
                            if (Shared.user.SellWithNoBalance == true)
                            {
                                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateStoreQty : ResSLAr.txtValidateStoreQty,
                                       Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                                return;
                            }
                            else if (Shared.user.SellWithNoBalance == false)
                            {
                                e.Valid = false;
                                view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? ResSLEn.txtValidateStoreQty : ResSLAr.txtValidateStoreQty);//"الكمية لا يمكن ان تكون اكبر من الرصيدالحالي");
                            }
                    }
                    #endregion

                    #region Validate_Min_Qty_In_Store

                    if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString() != string.Empty
                        && view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() != string.Empty
                        && invoiceId == 0)//validate minimum qty only in new invoice
                    {
                        // Get max Qty can be in the store
                        int minqty = Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, itemid, "MinQty"));

                        if (minqty <= 0 || Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Service
                            || Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Subtotal
                            || Shared.user.SellUnderMinQty == null)
                        { }
                        else
                        {
                            // Check if store_qty and qty to buy is less than max qty can be in the store
                            if ((CurrentQty - Qty) < minqty)
                            {
                                if (Shared.user.SellUnderMinQty == true)
                                {
                                    XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateMinQty : ResSLAr.txtValidateMinQty,// "الكمية الموجودة حاليا في المخزن بعد خصم الكمية المباعة أقل من الحد الأدنى للكمية",
                                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                                    return;
                                }
                                else if (Shared.user.SellUnderMinQty == false)
                                {
                                    e.Valid = false;
                                    view.SetColumnError(view.Columns["Qty"], Shared.IsEnglish ? ResSLEn.txtValidateMinQty : ResSLAr.txtValidateMinQty);// "الكمية الموجودة حاليا في المخزن بعد خصم الكمية المباعة أقل من الحد الأدنى للكمية",
                                }
                            }
                        }
                    }
                    #endregion

                    #region Validate_Reorder_Level
                    if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString() != string.Empty
                        && view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() != string.Empty
                        && invoiceId == 0)//validate minimum qty only in new invoice
                    {
                        int reorder = Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, itemid, "ReorderLevel"));
                        if (reorder <= 0 || (Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Service
                            && Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Subtotal))
                        { }
                        else
                        {
                            if ((CurrentQty - Qty) < reorder)
                            {
                                if (Shared.user.SellReorder == true)
                                {
                                    XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateReorderQty : ResSLAr.txtValidateReorderQty,//الكمية الموجودة حاليا في المخزن بعد خصم الكمية المباعة أقل من حد الطلب
                                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                                }
                            }
                        }
                    }
                    #endregion
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["TotalTaxes"]) != DBNull.Value && view.GetRowCellValue(e.RowHandle, view.Columns["TotalTaxes"]).ToString() != string.Empty)
                {

                    updateSubTaxGrid();
                }

            }
            catch
            {
                e.Valid = false;
            }
        }

        private void gridView1_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            //dtSL_Details.AcceptChanges();
            //if ((e.Row as DataRowView).Row["ItemId"] == DBNull.Value && Shared.st_Store.IsStoreOnEachSellRecord)
            //{
            //    (grdPrInvoice.FocusedView as GridView).CancelUpdateCurrentRow();
            //    e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
            //    return;
            //}
            //else 


            if ((e.Row as DataRowView).Row["ItemId"] == DBNull.Value)
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore;
            else
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gridView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            Get_TotalAccount();
            grd_FocusOnItemId(Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1");

            //hide last prices grid
            GetLastPrices(false, false);
        }

        private void grid_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
                var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                if (e.KeyCode == Keys.Enter)
                {
                    var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
                    int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

                    if (view.FocusedColumn == view.Columns["ItemId"]
                        || view.FocusedColumn == view.Columns["ItemCode1"]
                        || view.FocusedColumn == view.Columns["ItemCode2"])
                    {
                        if (Shared.user.UseBarcodeScanner)
                        {
                            grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                        }
                        else
                        {
                            string temp = view.FocusedColumn.FieldName;
                            view.FocusedColumn = view.VisibleColumns[view.Columns["ItemId"].VisibleIndex - 1];
                            if (view.GetFocusedRowCellValue(temp) == null || string.IsNullOrEmpty(view.GetFocusedRowCellValue(temp).ToString()))
                                view.FocusedColumn = view.Columns[temp];
                            return;
                        }
                    }

                    if (view.FocusedColumn == view.Columns["UOM"]
                        || view.FocusedColumn == view.Columns["Qty"]
                        || view.FocusedColumn == view.Columns["Length"]
                        || view.FocusedColumn == view.Columns["Width"]
                        || view.FocusedColumn == view.Columns["Height"]
                        || view.FocusedColumn == view.Columns["Expire"]
                        || view.FocusedColumn == view.Columns["Batch"]
                        || view.FocusedColumn == view.Columns["AudiencePrice"]
                        || view.FocusedColumn == view.Columns["ItemDescription"]
                        || view.FocusedColumn == view.Columns["ItemDescriptionEn"]
                        || view.FocusedColumn == view.Columns["PiecesCount"]
                        || view.FocusedColumn == view.Columns["Serial"]
                        || view.FocusedColumn == view.Columns["Serial2"]
                        || view.FocusedColumn == view.Columns["LibraQty"]
                        || view.FocusedColumn == view.Columns["QC"]
                        || view.FocusedColumn == view.Columns["kg_Weight_libra"])
                    {
                        if (view.FocusedColumn.VisibleIndex - 1 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1].OptionsColumn.AllowFocus)
                        {
                            view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                            return;
                        }
                        else if (view.FocusedColumn.VisibleIndex - 2 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2].OptionsColumn.AllowFocus)
                        {
                            view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2];
                            return;
                        }
                        else
                            grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                    }

                    if (view.FocusedColumn == view.Columns["SellPrice"]
                        || view.FocusedColumn == view.Columns["PurchasePrice"]
                        || view.FocusedColumn == view.Columns["DiscountRatio"]
                        || view.FocusedColumn == view.Columns["DiscountRatio2"]
                        || view.FocusedColumn == view.Columns["DiscountRatio3"]
                        || view.FocusedColumn == view.Columns["DiscountValue"]
                        || view.FocusedColumn == view.Columns["Batch"]
                        )
                    {
                        grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                    }

                    if (view.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                    {
                        view.AddNewRow();
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud
                        //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                        //    view.FocusedColumn = view.Columns["StoreId"];

                    }
                    else
                    {
                        view.FocusedRowHandle = focused_row_handle + 1;
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];
                        //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                        //    view.FocusedColumn = view.Columns["StoreId"]; 

                    }

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Up
                   && view.GetFocusedRow() != null
                   && (view.GetFocusedRow() as DataRowView).IsNew == true
                   && (view.GetFocusedRowCellValue("ItemId") == null || view.GetFocusedRowCellValue("ItemId").ToString() == string.Empty))
                {
                    view.DeleteRow(view.FocusedRowHandle);
                }

            }
            catch
            { }
        }

        private void gridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "DiscountValue"
                || e.Column.FieldName == "TotalSellPrice" 
                || e.Column.FieldName == "SellPrice"
                || e.Column.FieldName == "PurchasePrice" 
                || e.Column.FieldName == "CurrentQty"
                || e.Column.FieldName == "SalesTax"
                || e.Column.FieldName == "CustomTax"
                ||e.Column.FieldName == "salePriceWithTaxTable" 
                || e.Column.FieldName == "TotalTaxes" 
                || e.Column.FieldName == "tableTaxValue"
                || e.Column.FieldName == "EtaxValue")
            {
                if (e.Value != DBNull.Value && e.Value != null)
                    e.DisplayText = decimal.Round(Convert.ToDecimal(Convert.ToDouble(e.Value)), defaultRoundingPoints).ToString($"F{defaultRoundingPoints}");
            }

            if (e.Column == col_Expire)
            {
                #region Expire
                if (gridView2.GetListSourceRowCellValue(e.ListSourceRowIndex, "ExpireDate") == null || gridView2.GetListSourceRowCellValue(e.ListSourceRowIndex, "ExpireDate") == DBNull.Value)
                {
                    e.DisplayText = "";
                    return;
                }

                DateTime date = Convert.ToDateTime(gridView2.GetListSourceRowCellValue(e.ListSourceRowIndex, "ExpireDate"));
                if (Shared.st_Store.ExpireDisplay == true)
                    e.DisplayText = date.Day + "-" + date.Month + "-" + date.Year;
                else
                    e.DisplayText = date.Month + "-" + date.Year;
                #endregion
            }
            if (e.Column == col_Batch)
            {
                e.DisplayText = e.Value + "";
            }
            else if (e.Column.FieldName == "ManufactureDate")
            {
                if (e.Value != null && e.Value != DBNull.Value)
                    e.DisplayText = Convert.ToDateTime(e.Value).ToShortDateString();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            DB = new DAL.ERPDataContext();
            int lastInvId = (from inv in DB.SL_Invoices
                             where inv.SL_InvoiceId > invoiceId
                             where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                      x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(inv.StoreId)// inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId
                             where Convert.ToByte(bookId.EditValue) == 0 ? true : true
                             orderby inv.SL_InvoiceId ascending
                             select inv.SL_InvoiceId).FirstOrDefault();

            if (lastInvId != 0)
            {
                invoiceId = lastInvId;
                LoadInvoice();
            }
            else
            {
                lastInvId = (from inv in DB.SL_Invoices
                             where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                      x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(inv.StoreId)// inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId
                             where Convert.ToByte(bookId.EditValue) == 0 ? true : true
                             orderby inv.SL_InvoiceId ascending
                             select inv.SL_InvoiceId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    invoiceId = lastInvId;
                    LoadInvoice();
                }
            }
        }

        private void btnPrevious_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            DB = new DAL.ERPDataContext();
            int lastInvId = (from inv in DB.SL_Invoices
                             where inv.SL_InvoiceId < invoiceId
                             where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                      x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(inv.StoreId)// inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId
                             where Convert.ToByte(bookId.EditValue) == 0 ? true : true
                             orderby inv.SL_InvoiceId descending
                             select inv.SL_InvoiceId).FirstOrDefault();

            if (lastInvId != 0)
            {
                invoiceId = lastInvId;
                LoadInvoice();
            }
            else
            {
                lastInvId = (from inv in DB.SL_Invoices
                             where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                      x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(inv.StoreId)// inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId
                             where Convert.ToByte(bookId.EditValue) == 0 ? true : true
                             orderby inv.SL_InvoiceId descending
                             select inv.SL_InvoiceId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    invoiceId = lastInvId;
                    LoadInvoice();
                }
            }
        }

        private void repUOM_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            if (e.Value == null || e.Value == DBNull.Value)
                return;
            try
            {
                e.DisplayText = uom_list.Where(x => x.UOMId == Convert.ToInt32(e.Value)).FirstOrDefault()?.UOM;
            }
            catch
            {
            }
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            invoiceId = userId = 0;

            invoice_remains = 0;
            LoadInvoice();
            lkp_Customers_EditValueChanged(null, EventArgs.Empty);
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);
            GetNextCode();

            //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
            //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
            //    lkpStore.EditValue = 0;
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if ((grdPrInvoice.FocusedView as GridView) != null)
                ++(grdPrInvoice.FocusedView as GridView).FocusedRowHandle;
            grdPrInvoice.DefaultView.PostEditor();
            grdPrInvoice.DefaultView.UpdateCurrentRow();

            //dtSL_Details.AcceptChanges();
            if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0)
            {
                foreach (DataRow item in dtSL_Details.Rows)
                {
                    try
                    {
                        if (item["StoreId"] == DBNull.Value)
                        {
                            XtraMessageBox.Show("Plase insert store in each row", "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }
                    }
                    catch { continue; }
                }
            }


            if (!ValidData())
                return;

            if (Shared.st_Store.LastEvaluationDate.HasValue && dtInvoiceDate.DateTime.Date > Shared.st_Store.LastEvaluationDate.Value.Date)
            {
                dtInvoiceDate.ErrorText = Shared.IsEnglish ? ResEn.LastEvaluationDateError : ResAr.LastEvaluationDateError;
                dtInvoiceDate.Focus();
                MessageBox.Show(Shared.IsEnglish ? ResEn.LastEvaluationDateError : ResAr.LastEvaluationDateError, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            #region validate_all_qts_Larger_Zero
            var Is_Qty_LessZero = (from DataRow dr in dtSL_Details.Rows
                                   where dr.RowState != DataRowState.Deleted
                                   where Convert.ToDecimal(dr["Qty"]) <= 0
                                   select dr).Count() > 0;

            if (Is_Qty_LessZero == true)
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgQtyLessZero : ResSLAr.MsgQtyLessZero, "",
                       MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return;
            }

            #endregion

            if (invoiceId == 0)
            {
                Save_Invoice();

                if (printOptions != null && comp != null && printOptions.PrintReceiptSLI == true)
                {
                    if (Convert.ToDecimal(txtNet.EditValue) >= printOptions.MinValue)
                    {
                        try
                        {
                            //new Reports.rpt_Printed_Receipt(dtSL_Details, Convert.ToDecimal(txtNet.EditValue), " رقم الفاتورة " + txtInvoiceCode.Text, printOptions, comp).ShowPreview();
                            Reports.rpt_Printed_Receipt r = new Reports.rpt_Printed_Receipt();

                            if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.repx"))
                                r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.repx");

                            r.Load_Receipt(dtSL_Details, lkp_Customers.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text, lkp_Drawers.Text,
                                txtNotes.Text, txtNet.Text, Shared.UserName, txt_Remains.Text, txtDiscountValue.Text, txtNet.Text, txt_paid.Text, txt_Total.Text);
                            r.Print(Shared.ReceiptPrinterName);
                        }
                        catch
                        { }
                    }
                }
                #region Ask_To_Create_OutTrnsBill
                //if (Shared.InvoicePostToStore == false)
                //{
                //    if (XtraMessageBox.Show(Shared.IsEnglish == true ? ResSLEn.MsgAskCreateOutTrns : ResSLAr.MsgAskCreateOutTrns,
                //    Shared.IsEnglish == true ? ResSLEn.MsgTQues : ResSLAr.MsgTQues,
                //    MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                //    {
                //        if (ErpUtils.IsFormOpen(typeof(frm_IC_OutTrns)))
                //            Application.OpenForms["frm_IC_OutTrns"].Close();

                //        if (ErpUtils.IsFormOpen(typeof(frm_IC_OutTrns)))
                //            Application.OpenForms["frm_IC_OutTrns"].BringToFront();
                //        else
                //        {
                //            new frm_IC_OutTrns((int)Process.SellInvoice, invoiceId, txtInvoiceCode.Text).Show();
                //        }
                //    }
                //}
                #endregion
            }
            else
                Save_Invoice();

            XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgSave : ResSLAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            //if (barbtnPrint.Enabled) barbtnPrint.PerformClick();

            Saved_Successfuly = true;
            invoice_remains = Convert.ToDouble(txt_Remains.EditValue);

            lkp_Customers_EditValueChanged(null, EventArgs.Empty);

            //Print_Prescriptions();
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void batBtnList_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_InvoiceList)))
            {
                frm_SL_InvoiceList frm = new frm_SL_InvoiceList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_SL_InvoiceList"].BringToFront();
        }

        private void barBtnCancel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            CancelInvoice(uUId).ConfigureAwait(false);
        }

        static async Task CancelInvoice(string UUId)
        {
            try
            {
                HttpClient client = new HttpClient();
                UriBuilder uriBuilder = new UriBuilder(Properties.Settings.Default.BackEndPoint);
                uriBuilder.Port = Properties.Settings.Default.BackEndPort;

                client.BaseAddress = uriBuilder.Uri;// new Uri(Properties.Settings.Default.BackEndPoint);
                client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                HttpResponseMessage response = await client.PutAsync(string.Format("api/Einvoice/ChangeDocumentStatus?UUID=" + UUId + "&status=4&reason=test&documentType=" + (int)DocumentType.I), null);

                if (response.IsSuccessStatusCode)
                    MessageBox.Show("تم إلغاء الفاتورة بنجاح");
                else
                    MessageBox.Show("Status Code: " + response.StatusCode + " Message: " + response.ReasonPhrase);
            }
            catch (Exception exc)
            { MessageBox.Show(exc.Message); }
        }


        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.CheckDocumentSaved(invoiceId, DataModified, dtSL_Details) == false)
                return;

            DataTable dt_PrintTable = new DataTable();
            dt_PrintTable.Columns.Add("ItemCode1");
            dt_PrintTable.Columns.Add("ItemCode2");
            dt_PrintTable.Columns.Add("DiscountValue");
            dt_PrintTable.Columns.Add("Expire");
            dt_PrintTable.Columns.Add("Batch");
            dt_PrintTable.Columns.Add("SellPrice");
            dt_PrintTable.Columns.Add("Qty");
            dt_PrintTable.Columns.Add("TotalSellPrice");
            dt_PrintTable.Columns.Add("ItemName");
            dt_PrintTable.Columns.Add("UOM");
            dt_PrintTable.Columns.Add("Height");
            dt_PrintTable.Columns.Add("Width");
            dt_PrintTable.Columns.Add("Length");
            dt_PrintTable.Columns.Add("TotalQty");
            dt_PrintTable.Columns.Add("Factor");
            dt_PrintTable.Columns.Add("MUOM");
            dt_PrintTable.Columns.Add("MUOM_Factor");
            dt_PrintTable.Columns.Add("ItemType");
            dt_PrintTable.Columns.Add("AudiencePrice");
            dt_PrintTable.Columns.Add("ItemDescription");
            dt_PrintTable.Columns.Add("ItemDescriptionEn");
            dt_PrintTable.Columns.Add("PiecesCount");
            dt_PrintTable.Columns.Add("Location");

            dt_PrintTable.Columns.Add("SalesTax");
            dt_PrintTable.Columns.Add("CustomTax");
            dt_PrintTable.Columns.Add("DiscountRatio");
            dt_PrintTable.Columns.Add("DiscountRatio2");
            dt_PrintTable.Columns.Add("DiscountRatio3");
            dt_PrintTable.Columns.Add("SalesTaxRatio");
            dt_PrintTable.Columns.Add("CustomTaxRatio");
            dt_PrintTable.Columns.Add("PicPath");
            dt_PrintTable.Columns.Add("SL_InvoiceDetailId");
            dt_PrintTable.Columns.Add("Serial");
            dt_PrintTable.Columns.Add("Serial2");
            dt_PrintTable.Columns.Add("ManufactureDate");
            dt_PrintTable.Columns.Add("DueDate");
            dt_PrintTable.Columns.Add("Index");
            dt_PrintTable.Columns.Add("kg_Weight_libra");
            dt_PrintTable.Columns.Add("IsOffer").DefaultValue = false;
            dt_PrintTable.Columns.Add("Pack");
            dt_PrintTable.Columns.Add("CategoryId");
            dt_PrintTable.Columns.Add("ItemIdF");
            dt_PrintTable.Columns.Add("EtaxValue");
            dt_PrintTable.Columns.Add("addTaxValue");
            dt_PrintTable.Columns.Add("tableTaxValue");
            dt_PrintTable.Columns.Add("bonusDiscount");


            DataTable dt_PrintTableSubTaxDetails = new DataTable();
            dt_PrintTableSubTaxDetails.Columns.Add("SubTaxId");
            dt_PrintTableSubTaxDetails.Columns.Add("Rate");
            dt_PrintTableSubTaxDetails.Columns.Add("Value");
            var viewSubTaxes = grd_SubTaxes.FocusedView as GridView;
            for (int i = 0; i < viewSubTaxes.RowCount; i++)
            {
                if (viewSubTaxes.GetRowCellDisplayText(i, "SubTaxId") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTableSubTaxDetails.NewRow();
                dr["SubTaxId"] = viewSubTaxes.GetRowCellDisplayText(i, "SubTaxId");
                dr["Rate"] = viewSubTaxes.GetRowCellDisplayText(i, "Rate");
                dr["Value"] = viewSubTaxes.GetRowCellDisplayText(i, "Value"); //mahmoud:18-12-2012, naser azemi issue                
                dt_PrintTableSubTaxDetails.Rows.Add(dr);

            }

            var itmslst = DB.IC_Items;
            var uomlst = DB.IC_UOMs;
            var view = grdPrInvoice.FocusedView as GridView;
            for (int i = 0; i < view.RowCount; i++)
            {
                if (view.GetRowCellDisplayText(i, "ItemCode1") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTable.NewRow();
                dr["ItemCode1"] = view.GetRowCellDisplayText(i, "ItemCode1");
                dr["ItemCode2"] = view.GetRowCellDisplayText(i, "ItemCode2");
                dr["DiscountValue"] = view.GetRowCellDisplayText(i, "DiscountValue") == "0" ? "" : view.GetRowCellDisplayText(i, "DiscountValue"); //mahmoud:18-12-2012, naser azemi issue                
                dr["Expire"] = view.GetRowCellDisplayText(i, "Expire");
                dr["Batch"] = view.GetRowCellDisplayText(i, "Batch");
                dr["SellPrice"] = view.GetRowCellDisplayText(i, "SellPrice");
                dr["Qty"] = view.GetRowCellDisplayText(i, "Qty");
                dr["TotalSellPrice"] = view.GetRowCellDisplayText(i, "TotalSellPrice");
                dr["ItemName"] = view.GetRowCellDisplayText(i, "ItemId");
                dr["UOM"] = view.GetRowCellDisplayText(i, "UOM");
                dr["Height"] = view.GetRowCellDisplayText(i, "Height");
                dr["Width"] = view.GetRowCellDisplayText(i, "Width");
                dr["Length"] = view.GetRowCellDisplayText(i, "Length");
                dr["TotalQty"] = view.GetRowCellDisplayText(i, "TotalQty");
                dr["Location"] = view.GetRowCellDisplayText(i, "Location");

                if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 0)
                    dr["Factor"] = 1;
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 1)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "MediumUOMFactor");
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 2)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "LargeUOMFactor");

                if (ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM") != null)
                {
                    dr["MUOM"] = uom_list.Where(x => x.UOMId == Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM"))).Select(x => x.UOM).FirstOrDefault();
                    dr["MUOM_Factor"] = (double)MyHelper.FractionToDouble(dr["Factor"].ToString()) * Convert.ToDouble(dr["Qty"]); /*ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOMFactor");*/
                }
                else
                {
                    dr["MUOM"] = dr["UOM"];
                    dr["MUOM_Factor"] = dr["Factor"];
                }

                dr["ItemType"] = view.GetRowCellValue(i, "ItemType");
                dr["AudiencePrice"] = view.GetRowCellValue(i, "AudiencePrice") == null ? string.Empty :
                    (view.GetRowCellValue(i, "AudiencePrice") == DBNull.Value ? "" :
                  Math.Round(Convert.ToDecimal(view.GetRowCellValue(i, "AudiencePrice")), defaultRoundingPoints).ToString());
                dr["ItemDescription"] = view.GetRowCellDisplayText(i, "ItemDescription");
                dr["ItemDescriptionEn"] = view.GetRowCellDisplayText(i, "ItemDescriptionEn");
                dr["PiecesCount"] = view.GetRowCellDisplayText(i, "PiecesCount");

                dr["SalesTax"] = view.GetRowCellDisplayText(i, "SalesTax");
                dr["CustomTax"] = view.GetRowCellDisplayText(i, "CustomTax");
                dr["DiscountRatio"] = view.GetRowCellDisplayText(i, "DiscountRatio");
                dr["DiscountRatio2"] = view.GetRowCellDisplayText(i, "DiscountRatio2");
                dr["DiscountRatio3"] = view.GetRowCellDisplayText(i, "DiscountRatio3");
                dr["SalesTaxRatio"] = view.GetDataRow(i)["SalesTaxRatio"];
                dr["CustomTaxRatio"] = view.GetDataRow(i)["CustomTaxRatio"];
                dr["SL_InvoiceDetailId"] = view.GetDataRow(i)["SL_InvoiceDetailId"];
                dr["PicPath"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "PicPath");
                dr["Serial"] = view.GetRowCellDisplayText(i, "Serial");
                dr["Serial2"] = view.GetRowCellDisplayText(i, "Serial2");

                //mohammad, 15/4/2018
                dr["ManufactureDate"] = view.GetRowCellDisplayText(i, "ManufactureDate");
                /////////////////////////
                dr["Index"] = i + 1;// dt_PrintTable.Rows.Count + 1;
                dr["PiecesCount"] = view.GetRowCellDisplayText(i, "PiecesCount");
                dr["kg_Weight_libra"] = view.GetRowCellDisplayText(i, "kg_Weight_libra");

                dr["Pack"] = view.GetRowCellDisplayText(i, "Pack");
                dr["CategoryId"] = view.GetRowCellValue(i, "CategoryId");

                dr["ItemIdF"] = view.GetRowCellDisplayText(i, "ItemIdF");
                dr["EtaxValue"] = view.GetRowCellDisplayText(i, "EtaxValue");


                dr["addTaxValue"] = view.GetRowCellDisplayText(i, "addTaxValue");
                dr["tableTaxValue"] = view.GetRowCellDisplayText(i, "tableTaxValue");
                dr["bonusDiscount"] = view.GetRowCellDisplayText(i, "bonusDiscount");

                var cc = view.GetRowCellDisplayText(i, "bonusDiscount");
                dt_PrintTable.Rows.Add(dr);

                //if (Shared.LibraAvailabe)
                //{
                //    bool isoffer = false;
                //    var _isOffer = view.GetRowCellValue(i, "IsOffer");
                //    if (_isOffer != DBNull.Value && _isOffer != null)
                //        isoffer = Convert.ToBoolean(_isOffer);
                //    if (Shared.st_Store.SellAsRestaurant && isoffer)
                //    {
                //        dr["IsOffer"] = true;
                //        //get item bom
                //        var itemid = Convert.ToInt32(view.GetRowCellValue(i, "ItemId"));
                //        List<InvBom> bom = lst_invBom.Where(b => b.ProductItemId == itemid).ToList();
                //        dr["PiecesCount"] = "";
                //        //subtract bom items
                //        if (bom.Count > 0)
                //        {
                //            foreach (InvBom b in bom)
                //            {
                //                DataRow dr_ = dt_PrintTable.NewRow();
                //                dr_["ItemName"] = itmslst.Where(x => x.ItemId == b.RawItemId).Select(x => x.ItemNameAr).FirstOrDefault();
                //                dr_["PiecesCount"] = (double)(b.RawQty * Convert.ToDecimal(dr["Qty"]));
                //                dr_["UOM"] = uomlst.Where(x => x.UOMId == b.UomId).Select(x => x.UOM).FirstOrDefault();
                //                //dr_["SellPrice"] = "";
                //                dr_["Index"] = "--";
                //                dr_["Qty"] = 0;
                //                dr_["kg_Weight_libra"] = Math.Round(b.Factor * b.RawQty * Convert.ToDecimal(dr["Qty"]), defaultRoundingPoints);
                //                dt_PrintTable.Rows.Add(dr_);

                //                //dr["PiecesCount"] =b.pi uomlst.Where(x => x.UOMId == b.UomId).Select(x => x.UOM);
                //            }
                //        }
                //    }
                //}
            }

            string Customer = lkp_Customers.Text;
            string TaxFileNumber = DB.SL_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).Select(x => x.TaxFileNumber).FirstOrDefault();
            string TaxCardNumber = DB.SL_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).Select(x => x.TradeRegistry).FirstOrDefault();
            string salesEmp_Job = lkp_SalesEmp.EditValue == null ? "" : lkp_SalesEmp.GetColumnValue("JobNameAr") + "";
            var branch = DB.IC_Stores.Where(a => a.StoreId == Convert.ToInt32(lkpStore.EditValue)).FirstOrDefault();
            if (branch.ParentId != null)
            {
                branch = DB.IC_Stores.Where(a => a.StoreId == branch.ParentId).FirstOrDefault();
            }
            string address = branch.Address;
            string tel = branch.Tel;
            string managerName = branch.ManagerName;



            if (printOptions != null && comp != null && printOptions.PrintReceiptSLI == true)
            {
                if (Convert.ToDecimal(txtNet.EditValue) >= printOptions.MinValue)
                {
                    try
                    {
                        //new Reports.rpt_Printed_Receipt(dtSL_Details, Convert.ToDecimal(txtNet.EditValue), " رقم الفاتورة " + txtInvoiceCode.Text, printOptions, comp).ShowPreview();
                        Reports.rpt_Printed_Receipt r = new Reports.rpt_Printed_Receipt();

                        if (Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.en-US.repx"))
                            r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.en-US.repx");

                        else if (!Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.ar-EG.repx"))
                            r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.ar-EG.repx");

                        else if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.repx"))
                            r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.repx");

                        r.Load_Receipt(dtSL_Details, lkp_Customers.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text, lkp_Drawers.Text,
                                txtNotes.Text, txtNet.Text,
                Shared.lst_Users.Where(x => x.UserId == userId).Select(x => x.Name).FirstOrDefault(), txt_Remains.Text, txtDiscountValue.Text, txtNet.Text, txt_paid.Text, txt_Total.Text);
                        r.Print(Shared.ReceiptPrinterName);
                        //r.ShowPreview();
                    }
                    catch (Exception ex)
                    { }
                }
            }
            else
            {
                var invoiceQty = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == invoiceId).Sum(a => a.Qty);
                Reports.rpt_SL_Invoice r = new Reports.rpt_SL_Invoice
                    (Customer, TaxCardNumber, TaxFileNumber, lkp_InvoiceBook.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text,
                    cmbPayMethod.Text, lkp_Drawers.Text, txtNotes.Text, txt_Total.Text, "", txt_TaxValue.Text,
                    txtDiscountRatio.Text, txtDiscountValue.Text, txtExpensesR.Text, txtExpenses.Text, txtNet.Text,
                    txt_paid.Text, txt_Remains.Text, dt_PrintTable,
                    Shared.lst_Users.Where(x => x.UserId == userId).Select(x => x.Name).FirstOrDefault()
                    , lkp_SalesEmp.Text,
                    txt_Shipping.Text, txt_PO_No.Text, dtDeliverDate.Text, salesEmp_Job, txt_DeductTaxV.Text, txt_AddTaxV.Text,
                    Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue), lbl_IsCredit_Before.Text + " " + txt_Balance_Before.Text,
                    lbl_IsCredit_After.Text + " " + txt_Balance_After.Text,
                    txtDriverName.Text, txtVehicleNumber.Text, txtDestination.Text, "",
                    "", txtScaleSerial.Text, txt_CusTaxR.Text, txt_CusTaxV.Text, lkpCostCenter.Text,
                    txt_RetentionV.Text, txt_AdvancePayV.Text, txt_DueDate.Text, Convert.ToInt32(lkp_Customers.EditValue), new decimal[] { txt_transportation.Value, txt_Handing.Value, txt_ShiftAdd.Value }
                    , col_PiecesCount.SummaryItem.SummaryValue == null ? string.Empty : col_PiecesCount.SummaryItem.SummaryValue.ToString(),
                    Multiple_Data, lkpDelivery.EditValue != null ? lkpDelivery.Text : "", address, tel, managerName, txt_AttnMr.Text,
                    txt_total_b4_Discounts.Text, txt_totalAfterCommercial_Disc.Text, txt_CommercialDiscounts.Text, txt_EtaxValue.Text, dt_PrintTableSubTaxDetails, txt_bounsDiscount.Text);

                string TemplateName = "rpt_SL_Invoice";

                if (e.Item.Name.Contains("printSample"))
                {
                    int sampleId = Convert.ToInt32(e.Item.Name.Substring(11));
                    TemplateName = DB.ST_PrintSamples.Where(x => x.SampleId == sampleId).Select(x => x.PrintFileName).FirstOrDefault();
                }
                else if (lkp_InvoiceBook.GetColumnValue("PrintFileName") != null && lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString().Trim() != string.Empty)
                    TemplateName = lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString();

                if (Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".en-US.repx"))
                    r.LoadLayout(Shared.ReportsPath + TemplateName + ".en-US.repx");
                else if (!Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".ar-EG.repx"))
                    r.LoadLayout(Shared.ReportsPath + TemplateName + ".ar-EG.repx");
                else if (System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".repx"))
                    r.LoadLayout(Shared.ReportsPath + TemplateName + ".repx");

                r.LoadData();
                if (Shared.user.ShowPrintPreview == false)
                    r.PrintDialog();
                else
                    r.ShowPreview();
            }

            if (Shared.st_Store.ch_Authorize == true)
            {
                HrHelper.UsePermission(FormAction.Print, (int)FormsNames.SL_Invoice, invoiceId);
                LoadPrivilege();
            }
        }

        private void barBtnPrint_F_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.CheckDocumentSaved(invoiceId, DataModified, dtSL_Details) == false)
                return;

            DataTable dt_PrintTable = new DataTable();
            dt_PrintTable.Columns.Add("ItemCode1");
            dt_PrintTable.Columns.Add("ItemCode2");
            dt_PrintTable.Columns.Add("DiscountValue");
            dt_PrintTable.Columns.Add("Expire");
            dt_PrintTable.Columns.Add("Batch");
            dt_PrintTable.Columns.Add("SellPrice");
            dt_PrintTable.Columns.Add("Qty");
            dt_PrintTable.Columns.Add("TotalSellPrice");
            dt_PrintTable.Columns.Add("ItemName");
            dt_PrintTable.Columns.Add("UOM");
            dt_PrintTable.Columns.Add("Height");
            dt_PrintTable.Columns.Add("Width");
            dt_PrintTable.Columns.Add("Length");
            dt_PrintTable.Columns.Add("TotalQty");
            dt_PrintTable.Columns.Add("Factor");
            dt_PrintTable.Columns.Add("MUOM");
            dt_PrintTable.Columns.Add("MUOM_Factor");
            dt_PrintTable.Columns.Add("ItemType");
            dt_PrintTable.Columns.Add("AudiencePrice");
            dt_PrintTable.Columns.Add("ItemDescription");
            dt_PrintTable.Columns.Add("ItemDescriptionEn");
            dt_PrintTable.Columns.Add("PiecesCount");

            dt_PrintTable.Columns.Add("SalesTax");
            dt_PrintTable.Columns.Add("CustomTax");
            dt_PrintTable.Columns.Add("DiscountRatio");
            dt_PrintTable.Columns.Add("DiscountRatio2");
            dt_PrintTable.Columns.Add("DiscountRatio3");
            dt_PrintTable.Columns.Add("SalesTaxRatio");
            dt_PrintTable.Columns.Add("CustomTaxRatio");
            dt_PrintTable.Columns.Add("PicPath");
            dt_PrintTable.Columns.Add("SL_InvoiceDetailId");
            dt_PrintTable.Columns.Add("Serial");
            dt_PrintTable.Columns.Add("Serial2");
            dt_PrintTable.Columns.Add("kg_Weight_libra");
            dt_PrintTable.Columns.Add("addTaxValue");
            dt_PrintTable.Columns.Add("tableTaxValue");
            dt_PrintTable.Columns.Add("bonusDiscount");



            // dt_PrintTable.Columns.Add("ItemIdF");
            DataTable dt_PrintTableSubTaxDetails = new DataTable();
            dt_PrintTableSubTaxDetails.Columns.Add("SubTaxId");
            dt_PrintTableSubTaxDetails.Columns.Add("Rate");
            dt_PrintTableSubTaxDetails.Columns.Add("Value");
            var viewSubTaxes = grd_SubTaxes.FocusedView as GridView;
            for (int i = 0; i < viewSubTaxes.RowCount; i++)
            {
                if (viewSubTaxes.GetRowCellDisplayText(i, "SubTaxId") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTableSubTaxDetails.NewRow();
                dr["SubTaxId"] = viewSubTaxes.GetRowCellDisplayText(i, "SubTaxId");
                dr["Rate"] = viewSubTaxes.GetRowCellDisplayText(i, "Rate");
                dr["Value"] = viewSubTaxes.GetRowCellDisplayText(i, "Value"); //mahmoud:18-12-2012, naser azemi issue                
                dt_PrintTableSubTaxDetails.Rows.Add(dr);

            }

            var view = grdPrInvoice.FocusedView as GridView;
            for (int i = 0; i < view.RowCount; i++)
            {
                if (view.GetRowCellDisplayText(i, "ItemCode1") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTable.NewRow();
                dr["ItemCode1"] = view.GetRowCellDisplayText(i, "ItemCode1");
                dr["ItemCode2"] = view.GetRowCellDisplayText(i, "ItemCode2");
                dr["DiscountValue"] = view.GetRowCellDisplayText(i, "DiscountValue") == "0" ? "" : view.GetRowCellDisplayText(i, "DiscountValue"); //mahmoud:18-12-2012, naser azemi issue                
                dr["Expire"] = view.GetRowCellDisplayText(i, "Expire");
                dr["Batch"] = view.GetRowCellDisplayText(i, "Batch");
                dr["SellPrice"] = view.GetRowCellDisplayText(i, "SellPrice");
                dr["Qty"] = view.GetRowCellDisplayText(i, "Qty");
                dr["TotalSellPrice"] = view.GetRowCellDisplayText(i, "TotalSellPrice");
                dr["ItemName"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "ItemNameEn");
                // dr["ItemIdF"] = view.GetRowCellDisplayText(i, "ItemIdF");
                dr["UOM"] = view.GetRowCellDisplayText(i, "UOM");
                dr["Height"] = view.GetRowCellDisplayText(i, "Height");
                dr["Width"] = view.GetRowCellDisplayText(i, "Width");
                dr["Length"] = view.GetRowCellDisplayText(i, "Length");
                dr["TotalQty"] = view.GetRowCellDisplayText(i, "TotalQty");

                if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 0)
                    dr["Factor"] = 1;
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 1)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "MediumUOMFactor");
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 2)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "LargeUOMFactor");

                if (ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM") != null)
                {
                    dr["MUOM"] = uom_list.Where(x => x.UOMId == Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM"))).Select(x => x.UOM).FirstOrDefault();
                    dr["MUOM_Factor"] = (double)MyHelper.FractionToDouble(dr["Factor"].ToString()) * Convert.ToDouble(dr["Qty"]); /*ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOMFactor");*/
                }
                else
                {
                    dr["MUOM"] = dr["UOM"];
                    dr["MUOM_Factor"] = dr["Factor"];
                }

                dr["ItemType"] = view.GetRowCellValue(i, "ItemType");
                dr["AudiencePrice"] = view.GetRowCellValue(i, "AudiencePrice");
                dr["ItemDescription"] = view.GetRowCellDisplayText(i, "ItemDescription");
                dr["ItemDescriptionEn"] = view.GetRowCellDisplayText(i, "ItemDescriptionEn");
                dr["PiecesCount"] = view.GetRowCellDisplayText(i, "PiecesCount");

                dr["SalesTax"] = view.GetRowCellDisplayText(i, "SalesTax");
                dr["CustomTax"] = view.GetRowCellDisplayText(i, "CustomTax");
                dr["DiscountRatio"] = view.GetRowCellDisplayText(i, "DiscountRatio");
                dr["DiscountRatio2"] = view.GetRowCellDisplayText(i, "DiscountRatio2");
                dr["DiscountRatio3"] = view.GetRowCellDisplayText(i, "DiscountRatio3");
                dr["SalesTaxRatio"] = view.GetDataRow(i)["SalesTaxRatio"];
                dr["CustomTaxRatio"] = view.GetDataRow(i)["CustomTaxRatio"];
                dr["SL_InvoiceDetailId"] = view.GetDataRow(i)["SL_InvoiceDetailId"];
                dr["PicPath"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "PicPath");
                dr["Serial"] = view.GetRowCellDisplayText(i, "Serial");
                dr["Serial2"] = view.GetRowCellDisplayText(i, "Serial2");
                dr["Index"] = dt_PrintTable.Rows.Count + 1;
                dr["PiecesCount"] = view.GetRowCellDisplayText(i, "PiecesCount");
                dr["kg_Weight_libra"] = view.GetRowCellDisplayText(i, "kg_Weight_libra");

                dr["addTaxValue"] = view.GetRowCellDisplayText(i, "addTaxValue");
                dr["tableTaxValue"] = view.GetRowCellDisplayText(i, "tableTaxValue");
                dr["bonusDiscount"] = view.GetRowCellDisplayText(i, "bonusDiscount");
                dt_PrintTable.Rows.Add(dr);
            }
            string Customer = ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "CusNameEn") + "";
            string Store = lkpStore.GetColumnValue("StoreNameEn") + "";
            string PayMethod = "";
            if (Shared.IsEnglish == false)
            {
                if (cmbPayMethod.EditValue == null)
                    PayMethod = "Cash / On Credit";
                else if (Convert.ToBoolean(cmbPayMethod.EditValue) == true)
                    PayMethod = "Cash ";
                else
                    PayMethod = "On Credit";
            }
            else
            {
                if (cmbPayMethod.EditValue == null)
                    PayMethod = "اجل/كاش";
                else if (Convert.ToBoolean(cmbPayMethod.EditValue) == true)
                    PayMethod = "كاش";
                else
                    PayMethod = "اجل";
            }
            string Drawer = DB.ACC_Accounts.Where(x => x.AccountId == Convert.ToInt32(lkp_Drawers.EditValue)).Select(x => x.AcNameEn).FirstOrDefault();
            string salesEmp = lkp_SalesEmp.EditValue == null ? "" : lkp_SalesEmp.GetColumnValue("EmpFName") + "";
            string salesEmp_Job = lkp_SalesEmp.EditValue == null ? "" : lkp_SalesEmp.GetColumnValue("JobNameEn") + "";
            var branch = DB.IC_Stores.Where(a => a.StoreId == Convert.ToInt32(lkpStore.EditValue)).FirstOrDefault();
            string address = branch.Address;
            string tel = branch.Tel;
            string managerName = branch.ManagerName;
            Shared.IsEnglish = !Shared.IsEnglish;
            Reports.rpt_SL_Invoice r = new Reports.rpt_SL_Invoice
                (Customer, lkp_InvoiceBook.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, Store,
                PayMethod, Drawer, txtNotes.Text, txt_Total.Text, "", txt_TaxValue.Text, txtDiscountRatio.Text,
                txtDiscountValue.Text, txtExpensesR.Text, txtExpenses.Text, txtNet.Text, txt_paid.Text, txt_Remains.Text,
                dt_PrintTable,
                Shared.lst_Users.Where(x => x.UserId == userId).Select(x => x.Name).FirstOrDefault()
                , salesEmp, txt_Shipping.Text, txt_PO_No.Text, dtDeliverDate.Text, salesEmp_Job,
                txt_DeductTaxV.Text, txt_AddTaxV.Text, Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue),
                lbl_IsCredit_Before.Text + " " + txt_Balance_Before.Text, lbl_IsCredit_After.Text + " " + txt_Balance_After.Text,
                txtDriverName.Text, txtVehicleNumber.Text, txtDestination.Text, "", "", txtScaleSerial.Text,
                txt_CusTaxR.Text, txt_CusTaxV.Text, lkpCostCenter.Text, txt_RetentionV.Text, txt_AdvancePayV.Text,
                txt_DueDate.Text, Convert.ToInt32(lkp_Customers.EditValue), new decimal[] { txt_transportation.Value, txt_Handing.Value }
                , col_PiecesCount.SummaryItem.SummaryValue == null ? string.Empty : col_PiecesCount.SummaryItem.SummaryValue.ToString(),
                address, tel, managerName, txt_AttnMr.Text, txt_total_b4_Discounts.Text, txt_totalAfterCommercial_Disc.Text,
                txt_CommercialDiscounts.Text, txt_EtaxValue.Text, dt_PrintTableSubTaxDetails, txt_bounsDiscount.Text);

            string TemplateName = "rpt_SL_Invoice";
            if (lkp_InvoiceBook.GetColumnValue("PrintFileName") != null && lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString().Trim() != string.Empty)
                TemplateName = lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString();

            if (System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".repx"))
                r.LoadLayout(Shared.ReportsPath + TemplateName + ".repx");

            r.LoadData();
            if (Shared.user.ShowPrintPreview == false)
                r.PrintDialog();
            else
                r.ShowPreview();
            Shared.IsEnglish = !Shared.IsEnglish;

            if (Shared.st_Store.ch_Authorize == true)
            {
                HrHelper.UsePermission(FormAction.Print, (int)FormsNames.SL_Invoice, invoiceId);
                LoadPrivilege();
            }
        }

        private void btnAddCustomer_Click(object sender, EventArgs e)
        {
            int customers_count = lkp_Customers.Properties.View.RowCount;
            int LastCustId = 0;
            new frm_SL_Customer().ShowDialog();
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);
            if (lst_Customers.Count > customers_count)
            {
                lkp_Customers.Properties.DataSource = lst_Customers;
                lkp_Customers.EditValue = LastCustId;
                //lkp_Customers.EditValue = lkp_Customers.Properties.GetKeyValue(lkp_Customers.Properties.View.RowCount - 1);
            }
        }

        private void txtDiscountRatio_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            if (e.IsSpinUp == false)
            {
                if (Convert.ToDecimal(((TextEdit)sender).Text) == 0)
                    e.Handled = true;
            }
        }

        private void txtDiscountRatio_KeyPress_1(object sender, KeyPressEventArgs e)
        {
            //accepts only integer values           
            if (char.IsNumber(e.KeyChar) || e.KeyChar == '.')
            {
            }
            else
            {
                e.Handled = e.KeyChar != (char)Keys.Back;
            }
        }

        private void gridView2_ShowingEditor(object sender, CancelEventArgs e)
        {
            try
            {
                GridView view = sender as GridView;

                if (Convert.ToBoolean(view.GetRowCellValue(view.FocusedRowHandle, "itemperoffer")) == true)
                {
                    e.Cancel = true;
                }
                if (view.FocusedColumn.FieldName == "kg_Weight_libra")
                {
                    try
                    {
                        var PricingWithSmall = (grdPrInvoice.FocusedView as GridView).GetFocusedRowCellValue("PricingWithSmall");
                        if (PricingWithSmall != null && PricingWithSmall != DBNull.Value && Convert.ToBoolean(PricingWithSmall))
                        {
                            e.Cancel = true;
                        }
                    }
                    catch (Exception ex)
                    {
                        Utilities.save_Log(ex.Message, ex);
                    }
                }
                #region Expire
                if (gridView2.FocusedColumn == col_Expire
                && gridView2.GetFocusedRowCellValue("IsExpire") != null
                && gridView2.GetFocusedRowCellValue("IsExpire") != DBNull.Value)
                {
                    bool IsExpire = Convert.ToBoolean(gridView2.GetFocusedRowCellValue("IsExpire"));
                    e.Cancel = !IsExpire;
                }
                #endregion
            }
            catch { }
        }

        bool cust_IsDebit = false;
        private void lkp_Customers_EditValueChanged(object sender, EventArgs e)
        {
            cust_IsDebit = false;
            int? accountId = null;
            double maxcredit = 0;
            double balance = 0;

            txt_MaxCredit.Text = string.Empty;

            var selected_customer = lst_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).FirstOrDefault();

            if (selected_customer != null)
            {
                accountId = selected_customer.AccountId;

                if (invoiceId == 0)
                {
                    txt_Shipping.Text = selected_customer.Shipping;
                    lkpDelivery.EditValue = selected_customer.Delivery;

                    if (selected_customer.SalesEmpId.HasValue)
                        lkp_SalesEmp.EditValue = selected_customer.SalesEmpId;
                    else
                        lkp_SalesEmp.EditValue = null;

                    txt_AttnMr.Text = selected_customer.Representative;

                    if (selected_customer.DueDaysCount.HasValue)
                    {
                        txt_DueDays.EditValue = selected_customer.DueDaysCount;
                        txt_DueDate.EditValue = dtInvoiceDate.DateTime.AddDays(selected_customer.DueDaysCount.Value);
                    }
                    else
                    {
                        txt_DueDays.EditValue = 0;
                        txt_DueDate.EditValue = dtInvoiceDate.EditValue;
                    }

                    lkpDelivery.EditValue = selected_customer.Delivery;
                }
                if (selected_customer.PriceLevelId.HasValue)
                    CustomerPriceLevel = DB.IC_PriceLevels.Where(x => x.PriceLevelId == selected_customer.PriceLevelId).FirstOrDefault();
                else
                    CustomerPriceLevel = null;

                txt_Phone.Text = selected_customer.Tel;
                txt_Mobile.Text = selected_customer.Mobile;
                txt_Address.Text = selected_customer.Address;
                txt_Sales.EditValue = selected_customer.SalesEmpId;


                #region Balance_Before_and_After
                DateTime? end_date = (dtInvoiceDate.DateTime == DateTime.MinValue) ? (DateTime?)null : dtInvoiceDate.DateTime;
                if (accountId != null)
                {
                    balance = decimal.ToDouble(HelperAcc.Get_account_balance(accountId.Value, Shared.minDate, end_date));
                    if (invoiceId == 0)
                    {
                        txt_Balance_Before.Text = Math.Abs(balance).ToString("0,0.00");
                        if (balance > 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if (balance < 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_Before.Text = "";

                        double balance_after = balance - Convert.ToDouble(txt_Remains.EditValue);
                        txt_Balance_After.Text = Math.Abs(balance_after).ToString("0,0.00");
                        if (balance_after > 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if (balance_after < 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_After.Text = "";
                    }
                    else
                    {
                        txt_Balance_Before.Text = Math.Abs(balance + invoice_remains).ToString("0,0.00");
                        if ((balance + invoice_remains) > 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if ((balance + invoice_remains) < 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_Before.Text = "";

                        double balance_after = (balance + invoice_remains) - Convert.ToDouble(txt_Remains.EditValue);
                        txt_Balance_After.Text = Math.Abs(balance_after).ToString("0,0.00");
                        if (balance_after > 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if (balance_after < 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_After.Text = "";
                    }
                }
                else
                {
                    txt_Balance_Before.Text = "0";
                    txt_Balance_After.Text = "0";
                }
                #endregion

                maxcredit = selected_customer.MaxCredit;

                if (invoiceId == 0)
                    txtDiscountRatio.EditValue = selected_customer.DiscountRatio * 100;

                if (maxcredit <= 0 || Shared.user.SellCustomerOverCredit == null)
                {
                    lbl_Validate_MaxLimit.Visible = false;
                }
                else
                {
                    lbl_Validate_MaxLimit.Visible = true;
                }

                txt_MaxCredit.Text = maxcredit.ToString("0,0.00");

                if (balance <= 0)
                    cust_IsDebit = true;
                else
                    cust_IsDebit = false;

                if (balance <= 0
                    && Convert.ToDecimal(txt_Balance_After.Text) > Convert.ToDecimal(txt_MaxCredit.Text)
                    && maxcredit > 0)
                {
                    lbl_Validate_MaxLimit.Text = Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebit : ResSLAr.txtValidateCustomerMaxDebit;
                    lbl_Validate_MaxLimit.ForeColor = Color.Red;
                }
                else
                {
                    lbl_Validate_MaxLimit.Text = "";
                }
            }
        }

        private void contextMenuStrip1_Opened(object sender, EventArgs e)
        {
            var view = grdPrInvoice.FocusedView as GridView;
            var item_id = view.GetFocusedRowCellValue("ItemId");
            if (item_id == null || item_id == DBNull.Value || Convert.ToInt32(item_id) <= 0)
                mi_frm_IC_Item.Enabled = false;
            else
            {
                if (Shared.LstUserPrvlg == null || Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() > 0)
                    mi_frm_IC_Item.Enabled = true;
            }
        }

        private void mi_frm_IC_Item_Click(object sender, EventArgs e)
        {
            var view = grdPrInvoice.FocusedView as GridView;
            var item_id = view.GetFocusedRowCellValue("ItemId");
            if (item_id == null || item_id == DBNull.Value || Convert.ToInt32(item_id) <= 0)
                return;

            new frm_IC_Item(Convert.ToInt32(item_id), FormAction.Edit).ShowDialog();

            //Item_Location_List = DB.IC_Item_Locations.ToList();
        }

        private void mi_CustLastPrices_Click(object sender, EventArgs e)
        {
            GetLastPrices(true, true);
        }

        private void mi_LastPrices_Click(object sender, EventArgs e)
        {
            GetLastPrices(false, true);
        }


        private void Get_Items()
        {
            #region Get Items
            DB = new ERPDataContext();
            //var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).ToList();
            //if (defaultCategories.Count > 0)
            //{
            //    lstItems = (from i in DB.IC_Items
            //                where i.ItemType != (int)ItemType.MatrixParent
            //                where Shared.st_Store.SellRawMaterial == false ? i.ItemType == (int)ItemType.Assembly : true
            //                where invoiceId == 0 ? i.IsDeleted == false : true
            //                join c in DB.IC_User_Categories
            //                on i.Category equals c.CategoryId
            //                where c.UserId == Shared.UserId
            //                select new ItemLkp
            //                {
            //                    ItemCode1 = i.ItemCode1,
            //                    ItemCode2 = i.ItemCode2,
            //                    ItemId = i.ItemId,
            //                    ItemNameAr = i.ItemNameAr,
            //                    ItemNameEn = i.ItemNameEn,
            //                    MaxQty = i.MaxQty,
            //                    MinQty = i.MinQty,
            //                    ReorderLevel = i.ReorderLevel,
            //                    IsExpire = i.IsExpire,
            //                    PurchasePrice = i.PurchasePrice,
            //                    SellPrice = i.SmallUOMPrice,
            //                    PicPath = i.PicPath,
            //                    MediumUOM = i.MediumUOM,
            //                    LargeUOM = i.LargeUOM,
            //                    CategoryNameAr = i.IC_Category.CategoryNameAr,
            //                    CompanyNameAr = i.IC_Company.CompanyNameAr,
            //                    SellDiscountRatio = i.SalesDiscRatio,
            //                    CategoryId = i.Category
            //                }).ToList();
            //}
            //else
            {
                lstItems = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ? i.ItemType == (int)ItemType.Assembly : true
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select new ItemLkp
                            {
                                ItemCode1 = i.ItemCode1,
                                ItemCode2 = i.ItemCode2,
                                ItemId = i.ItemId,
                                ItemNameAr = i.ItemNameAr,
                                ItemNameEn = i.ItemNameEn,
                                MaxQty = i.MaxQty,
                                MinQty = i.MinQty,
                                ReorderLevel = i.ReorderLevel,
                                IsExpire = i.IsExpire,
                                PurchasePrice = i.PurchasePrice,
                                SellPrice = i.SmallUOMPrice,
                                PicPath = i.PicPath,
                                MediumUOM = i.MediumUOM,
                                LargeUOM = i.LargeUOM,
                                CategoryNameAr = i.IC_Category.CategoryNameAr,
                                //CompanyNameAr = i.IC_Company.CompanyNameAr,
                                SellDiscountRatio = i.SalesDiscRatio,
                                CategoryId = i.Category
                            }).ToList();
            }
            repItems.DataSource = lstItems;
            repItems.DisplayMember = Shared.IsEnglish ? "ItemNameEn" : "ItemNameAr";
            repItems.ValueMember = "ItemId";
            #endregion
        }

        void grd_FocusOnItemId(string columnName)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            view.FocusedColumn = view.Columns[columnName];
        }

        private void Reset()
        {
            if (Shared.InvoicePostToStore)
                chk_IsPosted.Checked = true;
            else
                chk_IsPosted.Checked = false;


            chk_IsOutTrns.Checked = false;

            txtNotes.EditValue = null;
            txtExpenses.EditValue = txtDiscountRatio.EditValue = txtDiscountValue.EditValue = txt_AdvancePayR.EditValue = txt_AdvancePayV.EditValue = txt_retentionR.EditValue = txt_RetentionV.EditValue = 0;

            txt_TaxValue.EditValue = 0;
            txt_DeductTaxR.EditValue = txt_DeductTaxV.EditValue = 0;
            txt_AddTaxR.EditValue = txt_AddTaxV.EditValue = txt_CusTaxR.EditValue = txt_CusTaxV.EditValue = 0;

            txtExpensesR.EditValue = 0;

            txt_Total.EditValue = 0.0;
            txt_paid.EditValue = 0.0;
            txtNet.EditValue = 0.0;
            txt_PayAcc1_Paid.EditValue = 0.0;
            txt_PayAcc2_Paid.EditValue = 0.0;
            lkp_Drawers2.EditValue = lkp_Cars.EditValue = null;
            txt_Remains.EditValue = 0.0;
            txt_Handing.EditValue = txt_transportation.EditValue = txt_ShiftAdd.EditValue = 0;
            dtInvoiceDate.DateTime = MyHelper.Get_Server_DateTime();

            //user don't has the right to save invoice with date older than server's date
            if (Shared.user.CanSave_SL_WithOldDate == false)
                dtInvoiceDate.DateTime = MyHelper.Get_Server_DateTime();

            //user don't has the right to save invoice with a future date
            if (Shared.user.CanSave_SL_WithUpcomingDate == false)
                dtInvoiceDate.DateTime = MyHelper.Get_Server_DateTime();

            dtSL_Details.Rows.Clear();
            Page_LastPrices.PageVisible = false;

            //lkp_SalesEmp.EditValue = null;                        

            dtDeliverDate.EditValue = null;
            //txt_Shipping.Text = "";            
            txt_PO_No.Text = "";

            cmbPayMethod.EditValue = Shared.user.SL_Invoice_PayMethod;

            //SourceInvoiceName = "";
            //SourceInvoiceId = 0;

            //InTrns = null;
            //scaleweight = null;

            JobOrderId = 0;

            txtInvoiceCode.ResetBackColor();
            txtInvoiceCode.ToolTipIconType = DevExpress.Utils.ToolTipIconType.None;
            txtInvoiceCode.ToolTip = "";
            txtInvoiceCode.ErrorText = "";

            txtDriverName.Text = txtVehicleNumber.Text = txtDestination.Text = txtScaleSerial.Text = string.Empty;
            txt_EtaxValue.Text = txt_total_b4_Discounts.Text = txt_totalAfterCommercial_Disc.Text = txt_Subtotal.Text = string.Empty;
            Multiple_Data.Rows.Clear();
            gv_CostCenter.ClearSelection();
            dt_Multi_CC.Rows.Clear();
            int defaultStoreId = 0;
            rowhandle = 0;
            Dt_Rows.Rows.Clear();
            dt_SubTax.Rows.Clear();
        }

        public void BindDataSources()
        {
            DB = new DAL.ERPDataContext();

            if (Shared.ItemsPostingAvailable)
            {
                if (Shared.StockIsPeriodic)
                {
                    lst_Cat = MyHelper.GetChildCategoriesList_Periodic();
                    if (lst_Cat.Where(x => x.SellAcc.HasValue == false || x.SellReturnAcc.HasValue == false
                                   || x.PurchaseAcc.HasValue == false || x.PurchaseReturnAcc.HasValue == false
                                   || x.OpenInventoryAcc.HasValue == false || x.CloseInventoryAcc.HasValue == false).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResEn.ItemPosting : ResAr.ItemPosting,
                            Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.BeginInvoke(new MethodInvoker(this.Close));
                    }
                }
                else
                {
                    lst_Cat = MyHelper.GetChildCategoriesList();
                    if (lst_Cat.Where(x => x.SellAcc.HasValue == false || x.SellReturnAcc.HasValue == false || x.COGSAcc.HasValue == false ||
                        x.InvAcc.HasValue == false).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResEn.ItemPosting : ResAr.ItemPosting,
                            Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.BeginInvoke(new MethodInvoker(this.Close));
                    }
                }
            }

            #region Delivery
            lkpDelivery.Properties.ValueMember = "idDelivery";
            lkpDelivery.Properties.DisplayMember = Shared.IsEnglish ? "DeliveryF" : "Delivery";

            lkpDelivery.Properties.DataSource = DB.SL_Deliveries;
            lkpDelivery.EditValue = 0;

            #endregion

            #region Get Cost Centers
            lkpCostCenter.Properties.DataSource = null;// HelperAcc.GetCostCentersLst(true);
            lkpCostCenter.Properties.DisplayMember = Shared.IsEnglish ? "CostCenterNameEn" : "CostCenterName";
            lkpCostCenter.Properties.ValueMember = "CostCenterId";
            #endregion

            #region Get Stores
            int defaultStoreId = 0;
            dt_Multi_CC.Columns.Clear();
            dt_Multi_CC.Columns.Add("CostCenterId");
            dt_Multi_CC.Rows.Clear();

            if (Shared.InvoicePostToStore)
                //stores_table = MyHelper.Get_Stores(true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                //Shared.UserId);
                stores_table = MyHelper.Get_StoresNotStopped(0, true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);
            else
                //stores_table = MyHelper.Get_Stores(false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                //Shared.UserId);
                stores_table = MyHelper.Get_StoresNotStopped(0, false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);

            lkpStore.Properties.DataSource = stores_table;

            lkpStore.Properties.DisplayMember = Shared.IsEnglish ? "StoreNameEn" : "StoreNameAr";
            lkpStore.Properties.ValueMember = "StoreId";
            //lkpStore.EditValue = defaultStoreId;
            //=================================//

            lkp_storee.DataSource = stores_table;
            lkp_storee.DisplayMember = Shared.IsEnglish ? "StoreNameEn" : "StoreNameAr";
            lkp_storee.ValueMember = "StoreId";



            if (Shared.IsEnglish)
            {
                lkpStore.Properties.Columns["StoreNameEn"].Visible = true;
                lkpStore.Properties.Columns["StoreNameAr"].Visible = false;


            }
            else
            {
                lkpStore.Properties.Columns["StoreNameEn"].Visible = false;
                lkpStore.Properties.Columns["StoreNameAr"].Visible = true;

            }

            #endregion

            #region LoadInvoicesBooks
            var empty_book = new
            {
                InvoiceBookId = (int?)null,
                InvoiceBookName = "",
                IsTaxable = (bool?)null,
                PrintFileName = (string)null,
            };

            var books = (from b in DB.ST_InvoiceBooks
                         where b.ProcessId == (int)Process.SellInvoice
                         select new
                         {
                             InvoiceBookId = (int?)b.InvoiceBookId,
                             b.InvoiceBookName,
                             b.IsTaxable,
                             b.PrintFileName,
                         }).ToList();

            if (books.Count == 0)
                pnlBook.Visible = false;

            books.Insert(0, empty_book);
            lkp_InvoiceBook.Properties.DataSource = books;
            lkp_InvoiceBook.Properties.DisplayMember = "InvoiceBookName";
            lkp_InvoiceBook.Properties.ValueMember = "InvoiceBookId";
            lkp_InvoiceBook.EditValue = Shared.user.DefaultSLInv_InvBookId;


            var empty_sample = new
            {
                SampleId = (int?)null,
                SampleName = "",
                PrintFileName = (string)null
            };

            #endregion

            Get_Items();

            #region User Rights
            //disable dicount if user has no privilege
            if (Shared.user.UserCanWriteDiscount == false)
                txtDiscountRatio.Enabled = txtDiscountValue.Enabled = false;


            #endregion

            //#region Get_PayAccounts
            //int defaultAcc = HelperAcc.LoadPayAccounts(dtPayAccounts, Shared.user.UserChangeDrawer, Shared.user.DefaultDrawer,
            //    Shared.IsEnglish);

            //lkp_Drawers.Properties.ValueMember = "AccountId";
            //lkp_Drawers.Properties.DisplayMember = Shared.IsEnglish ? "AccountNameEn" : "AccountName";
            //lkp_Drawers.EditValue = defaultAcc;
            //lkp_Drawers.Properties.DataSource = dtPayAccounts;

            //lkp_Drawers2.Properties.ValueMember = "AccountId";
            //lkp_Drawers2.Properties.DisplayMember = Shared.IsEnglish ? "AccountNameEn" : "AccountName";
            //lkp_Drawers2.EditValue = null;
            //lkp_Drawers2.Properties.DataSource = dtPayAccounts;
            //#endregion

            #region dtSL_Details
            dtSL_Details.Columns.Clear();
            dtSL_Details.Columns.Add("SL_InvoiceDetailId");
            dtSL_Details.Columns.Add("SL_InvoiceId");
            dtSL_Details.Columns.Add("ItemId");
            dtSL_Details.Columns.Add("ItemIdF");
            dtSL_Details.Columns.Add("ItemCode1");
            dtSL_Details.Columns.Add("ItemCode2");
            dtSL_Details.Columns.Add("ItemType").DefaultValue = 0;
            dtSL_Details.Columns.Add("UOM");
            dtSL_Details.Columns.Add("Qty").DefaultValue = 1;
            dtSL_Details.Columns.Add("PurchasePrice").DefaultValue = 0;
            dtSL_Details.Columns.Add("SellPrice").DefaultValue = 0;
            dtSL_Details.Columns.Add("DiscountValue").DefaultValue = 0;
            dtSL_Details.Columns.Add("DiscountRatio").DefaultValue = 0;
            dtSL_Details.Columns.Add("DiscountRatio2").DefaultValue = 0;
            dtSL_Details.Columns.Add("DiscountRatio3").DefaultValue = 0;
            dtSL_Details.Columns.Add("TotalSellPrice").DefaultValue = 0;
            dtSL_Details.Columns.Add("CurrentQty").DefaultValue = 0;
            dtSL_Details.Columns.Add("MediumUOMFactor");
            dtSL_Details.Columns.Add("LargeUOMFactor");
            dtSL_Details.Columns.Add("TotalTaxes", typeof(decimal));
            dtSL_Details.Columns.Add("totalTaxesRatio", typeof(decimal));



            dtSL_Details.Columns.Add("UomIndex");

            dtSL_Details.Columns.Add("Length").DefaultValue = 1;
            dtSL_Details.Columns.Add("Width").DefaultValue = 1;
            dtSL_Details.Columns.Add("Height").DefaultValue = 1;
            dtSL_Details.Columns.Add("PiecesCount").DefaultValue = 0;

            dtSL_Details.Columns.Add("SalesTax").DefaultValue = 0;
            dtSL_Details.Columns.Add("SalesTaxRatio").DefaultValue = 0;

            dtSL_Details.Columns.Add("AudiencePrice");

            dtSL_Details.Columns.Add("ItemDescription");
            dtSL_Details.Columns.Add("ItemDescriptionEn");

            dtSL_Details.Columns.Add("ParentItemId");
            dtSL_Details.Columns.Add("M1");
            dtSL_Details.Columns.Add("M2");
            dtSL_Details.Columns.Add("M3");

            dtSL_Details.Columns.Add("Expire");
            dtSL_Details.Columns.Add("ExpireDate");
            dtSL_Details.Columns.Add("Batch");
            dtSL_Details.Columns.Add("IsExpire");

            //used for xml export, to insert items to importer database
            dtSL_Details.Columns.Add("ItemNameAr");
            dtSL_Details.Columns.Add("ItemNameEn");

            dtSL_Details.Columns.Add("SmallUOM");
            dtSL_Details.Columns.Add("SmallUOMPrice");

            dtSL_Details.Columns.Add("MediumUOM");
            dtSL_Details.Columns.Add("MediumUOMPrice");

            dtSL_Details.Columns.Add("LargeUOM");
            dtSL_Details.Columns.Add("LargeUOMPrice");

            dtSL_Details.Columns.Add("ReorderLevel");
            dtSL_Details.Columns.Add("MaxQty");
            dtSL_Details.Columns.Add("MinQty");
            dtSL_Details.Columns.Add("calcTaxBeforeDisc");
            dtSL_Details.Columns.Add("Serial");
            dtSL_Details.Columns.Add("Serial2");
            dtSL_Details.Columns.Add("CategoryId");
            dtSL_Details.Columns.Add("StoreId");

            //for Elm Dawa2..Doesn't affect Qty, Mohammad 17-03-2018 
            dtSL_Details.Columns.Add("ManufactureDate");


            //fayza
            dtSL_Details.Columns.Add("CustomTaxRatio").DefaultValue = 0;
            dtSL_Details.Columns.Add("CustomTax").DefaultValue = 0;

            dtSL_Details.Columns.Add("Index");
            dtSL_Details.Columns.Add("Location");
            dtSL_Details.Columns.Add("LibraQty");
            dtSL_Details.Columns.Add("IsOffer");
            dtSL_Details.Columns.Add("PricingWithSmall");
            dtSL_Details.Columns.Add("VariableWeight");
            dtSL_Details.Columns.Add("kg_Weight_libra");
            dtSL_Details.Columns.Add("Is_Libra");
            dtSL_Details.Columns.Add("QC");
            dtSL_Details.Columns.Add("RowHandle", typeof(int));
            dtSL_Details.Columns.Add("ActualPiecesCount");
            dtSL_Details.Columns.Add("itemperoffer", typeof(bool)).DefaultValue = false;

            dtSL_Details.Columns.Add("Pack");


            dtSL_Details.Columns.Add("Category");
            dtSL_Details.Columns.Add("Company");

            dtSL_Details.Columns.Add("bonusDiscount");

            dtSL_Details.Columns.Add("TaxType");
            dtSL_Details.Columns.Add("ETaxRatio");
            dtSL_Details.Columns.Add("EtaxValue");
            dtSL_Details.Columns.Add("salePriceWithTaxTable");
            dtSL_Details.Columns.Add("totalTableTaxes");

            dtSL_Details.Columns.Add("addTaxValue");
            dtSL_Details.Columns.Add("tableTaxValue");
            dtSL_Details.Columns.Add("TotalSubDiscountTax");
            dtSL_Details.Columns.Add("TotalSubCustomTax");
            dtSL_Details.Columns.Add("TotalSubAddTax");
            dtSL_Details.Columns.Add("TaxValue");



            dtSL_Details.TableNewRow += new DataTableNewRowEventHandler(dt_TableNewRow);
            dtSL_Details.RowDeleted += new DataRowChangeEventHandler(dt_RowChanged);
            dtSL_Details.RowChanged += new DataRowChangeEventHandler(dt_RowChanged);
            grdPrInvoice.DataSource = dtSL_Details;


            if (Multiple_Data.Columns.Count < 1)
            {
                Multiple_Data.Columns.Add("Code1", typeof(int));
                Multiple_Data.Columns.Add("item");
                Multiple_Data.Columns.Add("Count", typeof(int));
                Multiple_Data.Columns.Add("Weight", typeof(decimal));
                Multiple_Data.Columns.Add("Libra", typeof(decimal));
            }

            #endregion

            //#region Categories & Companies
            //var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).ToList();
            //if (defaultCategories.Count > 0)
            //{
            //    repCategory.DataSource = (from m in DB.IC_Categories
            //                              join d in DB.IC_User_Categories
            //                              on m.CategoryId equals d.CategoryId
            //                              where d.UserId == Shared.UserId
            //                              select new
            //                              {
            //                                  m.CategoryId,
            //                                  m.CategoryNameAr
            //                              }).ToList();
            //}
            //else
            //{
            //     repCategory.DataSource = DB.IC_Categories;
            //}

            //repCategory.ValueMember = "CategoryId";
            //repCategory.DisplayMember = "CategoryNameAr";

            //repCompany.DataSource = DB.IC_Companies;
            //repCompany.ValueMember = "CompanyId";
            //repCompany.DisplayMember = "CompanyNameAr";
            //#endregion

            #region GetCustomers
            int LastCustId = 0;
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId, true);

            lkp_Customers.Properties.DisplayMember = Shared.IsEnglish ? "CusNameEn" : "CusNameAr";
            lkp_Customers.Properties.ValueMember = "CustomerId";
            lkp_Customers.Properties.DataSource = lst_Customers;
            lkp_Customers.EditValue = goldencustomer;
            lkp_Customers_EditValueChanged(lkp_Customers, EventArgs.Empty);
            #endregion

            #region UOM
            uom_list = DB.IC_UOMs.ToList();
            repUOM.DisplayMember = "Uom";
            repUOM.ValueMember = "UomId";
            repUOM.DataSource = MyHelper.GetUomDataTable(dtUOM);
            #endregion

            #region Expire_Qty
            rep_expireDate.DisplayMember = "Expire";
            rep_expireDate.ValueMember = "ExpireId";
            rep_expireDate.DataSource = MyHelper.GetExpireQtyDataTable(dtExpireQty);
            #endregion

            #region Batch
            dtBatchQty = new DataTable();
            dtBatchQty.Columns.Add("Batch");
            dtBatchQty.Columns.Add("Qty");

            rep_Batch.DisplayMember = "Batch";
            rep_Batch.ValueMember = "Batch";
            rep_Batch.DataSource = dtBatchQty;
            #endregion

            //#region SalesEmp
            //int? defaultEmp = MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            //txt_Sales.Properties.ValueMember = lkp_SalesEmp.Properties.ValueMember = "EmpId";
            //txt_Sales.Properties.DisplayMember = lkp_SalesEmp.Properties.DisplayMember = Shared.IsEnglish ? "EmpFName" : "EmpName";
            //txt_Sales.Properties.DataSource = lkp_SalesEmp.Properties.DataSource = dt_SalesEmps;
            //if (lkp_SalesEmp.EditValue == null)
            //    lkp_SalesEmp.EditValue = defaultEmp;

            //#endregion

            //#region JobOrder
            //lst_Status = DB.JO_Status.ToList();
            //lkp_JOStatus.Properties.DisplayMember = "Status";
            //lkp_JOStatus.Properties.ValueMember = "StatusId";
            //lkp_JOStatus.Properties.DataSource = lst_Status;

            //lst_Dept = DB.JO_Depts.ToList();
            //lkp_JODept.Properties.DisplayMember = "Department";
            //lkp_JODept.Properties.ValueMember = "DeptId";
            //lkp_JODept.Properties.DataSource = lst_Dept;

            //lst_Priority = DB.JO_Priorities.ToList();
            //lkp_JOPriority.Properties.DisplayMember = "Priority";
            //lkp_JOPriority.Properties.ValueMember = "PriorityId";
            //lkp_JOPriority.Properties.DataSource = lst_Priority;


            //lkp_JOSalesEmp.Properties.ValueMember = "EmpId";
            //lkp_JOSalesEmp.Properties.DisplayMember = "EmpName";
            //lkp_JOSalesEmp.Properties.DataSource = dt_SalesEmps;
            //#endregion

            //if (Shared.st_Store.SellAsRestaurant)
            //{
            //    lst_invBom = new List<InvBom>();
            //    lst_invBom = (from m in DB.IC_BOMs
            //                  join d in DB.IC_BOMDetails
            //                  on m.BOMId equals d.BOMId
            //                  join i in DB.IC_Items
            //                  on d.RawItemId equals i.ItemId
            //                  select new InvBom
            //                  {
            //                      BOMId = m.BOMId,
            //                      ProductItemId = m.ProductItemId,
            //                      ProductQty = m.Qty,
            //                      RawItemId = d.RawItemId,
            //                      UomId = d.UomId,
            //                      RawQty = d.Qty,
            //                      UomIndex = d.UomId == i.SmallUOM ? (byte)0 : (d.UomId == i.MediumUOM.Value ? (byte)1 : (byte)2),
            //                      Factor = d.UomId == i.SmallUOM ? 1 :
            //                            (d.UomId == i.MediumUOM.Value ? (i.MediumUOMFactorDecimal.HasValue ? i.MediumUOMFactorDecimal.Value : 0) :
            //                                                           (i.LargeUOMFactorDecimal.HasValue ? i.LargeUOMFactorDecimal.Value : 0)),
            //                      RawItemType = i.ItemType,
            //                      mtrxAttribute1 = i.mtrxAttribute1,
            //                      mtrxAttribute2 = i.mtrxAttribute2,
            //                      mtrxAttribute3 = i.mtrxAttribute3,
            //                      mtrxParentItem = i.mtrxParentItem,
            //                      MediumUOMFactorDecimal = i.MediumUOMFactorDecimal.HasValue ? i.MediumUOMFactorDecimal.Value : 1,
            //                      LargeUOMFactorDecimal = i.LargeUOMFactorDecimal.HasValue ? i.LargeUOMFactorDecimal.Value : 1
            //                  }).ToList();
            //}
            //var cars = DB.ST_Cars.ToList();
            //lkp_Cars.Properties.DataSource = cars;
            //lkp_Cars.Properties.DisplayMember = "Name";
            //lkp_Cars.Properties.ValueMember = "PlateNo";

            //lkp_Cars.Visible = cars.Count > 0;

            //Item_Location_List = DB.IC_Item_Locations.ToList();

            #region Get SubTax Types
            var taxTypes = DB.E_TaxableTypes.Where(x => x.ParentTaxId != null);
            repTaxTypes.DataSource = taxTypes;
            repTaxTypes.ValueMember = "E_TaxableTypeId";
            repTaxTypes.DisplayMember = "Code";
            #endregion
            #region Taxes
            if (Dt_Rows.Columns.Count < 1)
            {
                Dt_Rows.Columns.Add("ItemId", typeof(int));
                Dt_Rows.Columns.Add("SellPrice", typeof(decimal));
                Dt_Rows.Columns.Add("Qty", typeof(decimal));
                Dt_Rows.Columns.Add("Tax");
                Dt_Rows.Columns.Add("SubTax");
                Dt_Rows.Columns.Add("Percentage");
                Dt_Rows.Columns.Add("TaxValue");
                Dt_Rows.Columns.Add("RowHandle", typeof(int));
            }
            #endregion
            #region SubTax
            lkp_SubTaxes.DataSource = DB.E_TaxableTypes.Where(x => x.ParentTaxId != null).ToList();
            lkp_SubTaxes.ValueMember = "E_TaxableTypeId";
            lkp_SubTaxes.DisplayMember = "DescriptionAr";
            dt_SubTax.Columns.Add("SubTaxId");
            dt_SubTax.Columns.Add("ItemId");
            dt_SubTax.Columns.Add("Rate");
            dt_SubTax.Columns.Add("Value");
            grd_SubTaxes.DataSource = dt_SubTax;
            #endregion
            tableTax.Add("T2");
            tableTax.Add("T3");
            tableTax.Add("T5");
            tableTax.Add("T6");
            tableTax.Add("T7");
            tableTax.Add("T8");
            tableTax.Add("T9");
            tableTax.Add("T10");
            tableTax.Add("T11");
            tableTax.Add("T12");
        }


        private void LoadItemRow(DAL.IC_Item item, DataRow row)
        {


            if (item != null && item.ItemId > 0)
            {
                row["ItemId"] = item.ItemId;
                row["ItemIdF"] = Shared.IsEnglish ? item.ItemNameAr : item.ItemNameEn;
                row["CategoryId"] = item.Category;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["Category"] = item.Category;
                row["Company"] = item.Company;
                row["Category"] = item.Category;
                row["Company"] = item.Company;
                row["ItemType"] = item.ItemType;
                row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);

                MyHelper.GetUOMs(item, dtUOM, uom_list);

                row["UOM"] = dtUOM.Rows[item.DfltSellUomIndx]["UomId"];
                row["UomIndex"] = item.DfltSellUomIndx;

                ///GET IC_STORE PRICE LIST
                /////MOHAMMAD 31-05-2020
                decimal uom_price = 0;
                try
                {
                    if (Shared.LibraAvailabe && CustomerPriceLevel == null)
                    {
                        GridView view = grdPrInvoice.FocusedView as GridView;
                        int _storeId = Shared.st_Store.IsStoreOnEachSellRecord ?
                            (view.GetFocusedRowCellValue("StoreId") != DBNull.Value ? Convert.ToInt32(view.GetFocusedRowCellValue("StoreId")) : 0) :
                                                                                     Convert.ToInt32(lkpStore.EditValue);
                        var StorePriceList = (from s in DB.IC_Stores
                                              join p in DB.IC_PriceLevels on s.pricelistId equals p.PriceLevelId
                                              where s.StoreId == _storeId
                                              select p).FirstOrDefault();
                        if (StorePriceList != null)
                            uom_price = MyHelper.GetPriceLevelSellPrice(StorePriceList, item, item.DfltSellUomIndx);
                        else
                            uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, item.DfltSellUomIndx);

                    }
                    else if (Shared.user.ShowLast_Sell_Prices == true)
                    {
                        var _uom_price = (decimal)MyHelper.GetLastPrices(DB, true, item.ItemId, Convert.ToInt32(lkp_Customers.EditValue));
                        if (_uom_price == 0)
                            uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, item.DfltSellUomIndx);

                        else
                            uom_price = _uom_price;
                    }
                    else
                        //////////////////////////////////////////////
                        uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, item.DfltSellUomIndx);
                }
                catch (Exception ex)
                {
                    Utilities.save_Log("GET IC_STORE PRICE LIST - LoadItemRow", null);
                    Utilities.save_Log(ex.Message, ex);
                }
                row["SellPrice"] = decimal.ToDouble(uom_price);

                if (frm_InvoiceDiscs.SLInv_DiscR1 > 0 ||
                    frm_InvoiceDiscs.SLInv_DiscR2 > 0 ||
                    frm_InvoiceDiscs.SLInv_DiscR3 > 0)
                {
                    if (frm_InvoiceDiscs.SLInv_DiscR1 > 0)
                        row["DiscountRatio"] = decimal.ToDouble(frm_InvoiceDiscs.SLInv_DiscR1);
                    if (frm_InvoiceDiscs.SLInv_DiscR2 > 0)
                        row["DiscountRatio2"] = decimal.ToDouble(frm_InvoiceDiscs.SLInv_DiscR2);
                    if (frm_InvoiceDiscs.SLInv_DiscR3 > 0)
                        row["DiscountRatio3"] = decimal.ToDouble(frm_InvoiceDiscs.SLInv_DiscR3);
                }
                else
                {
                    row["DiscountRatio"] = decimal.ToDouble(item.SalesDiscRatio / 100);
                    row["DiscountRatio2"] = decimal.ToDouble(0);
                    row["DiscountRatio3"] = decimal.ToDouble(0);
                }

                row["DiscountValue"] = decimal.ToDouble(Utilities.Calc_DiscountValue(Convert.ToDecimal(row["DiscountRatio"]),
                    Convert.ToDecimal(row["DiscountRatio2"]), Convert.ToDecimal(row["DiscountRatio3"]), Convert.ToDecimal(row["SellPrice"])));

                if (IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable")))
                {
                    row["SalesTaxRatio"] = Math.Round(item.SalesTaxRatio / 100, defaultRoundingPoints);
                    row["CustomTaxRatio"] = Math.Round(item.CustomSalesTaxRatio / 100, defaultRoundingPoints);
                }
                else if (IsTaxable == true)
                {
                    row["SalesTaxRatio"] = Math.Round(item.SalesTaxRatio / 100, defaultRoundingPoints);
                    row["CustomTaxRatio"] = Math.Round(item.CustomSalesTaxRatio / 100, defaultRoundingPoints);
                }
                else
                {
                    row["SalesTaxRatio"] = 0;
                    row["CustomTaxRatio"] = 0;
                }

                row["calcTaxBeforeDisc"] = item.calcTaxBeforeDisc;

                row["Qty"] = "1";

                row["Length"] = Decimal.ToDouble(item.Length);
                row["Width"] = Decimal.ToDouble(item.Width);
                row["Height"] = Decimal.ToDouble(item.Height);

                if (frm_InvoiceDimenstions.SLInv_Height > 0)
                    row["Height"] = decimal.ToDouble(frm_InvoiceDimenstions.SLInv_Height);
                if (frm_InvoiceDimenstions.SLInv_Width > 0)
                    row["Width"] = decimal.ToDouble(frm_InvoiceDimenstions.SLInv_Width);
                if (frm_InvoiceDimenstions.SLInv_Length > 0)
                    row["Length"] = decimal.ToDouble(frm_InvoiceDimenstions.SLInv_Length);


                row["TotalSellPrice"] = "0";

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);

                //row["ActualPiecesCount"] = MyHelper.GetItemPieces(dtInvoiceDate.DateTime, item.ItemId, Convert.ToInt32(lkpStore.EditValue), SlOrdrId);

                if (item.DfltSellUomIndx == 0)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);
                else if (item.DfltSellUomIndx == 1)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice) * decimal.ToDouble(MyHelper.FractionToDouble(item.MediumUOMFactor));
                else if (item.DfltSellUomIndx == 2)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice) * decimal.ToDouble(MyHelper.FractionToDouble(item.LargeUOMFactor));

                row["IsExpire"] = item.IsExpire;
                if (item.IsExpire == true)
                {
                    MyHelper.Get_Expire_Qtys(item.ItemId, Convert.ToInt32(lkpStore.EditValue), dtInvoiceDate.DateTime, dtExpireQty);
                    if (dtExpireQty.Rows.Count == 0)
                    {
                        row["ExpireDate"] = DBNull.Value;
                        row["Expire"] = DBNull.Value;
                        row["Batch"] = DBNull.Value;
                    }
                }

                //MyHelper.Get_Batch_Qtys(item.ItemId, Convert.ToInt32(lkpStore.EditValue), dtInvoiceDate.DateTime, dtBatchQty);


                //if (Shared.user.Sell_ShowCrntQty == true)
                //{
                //    decimal currentQty;
                //    //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                //    if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                //        if (row["StoreId"] is DBNull)
                //            currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item.ItemId, 0, SlOrdrId, row["Batch"].ToString());

                //        else
                //            currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item.ItemId, Convert.ToInt32(row["StoreId"]), SlOrdrId, row["Batch"].ToString());

                //    else

                //        currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item.ItemId, Convert.ToInt32(lkpStore.EditValue), SlOrdrId, row["Batch"].ToString());

                //    currentQty = MyHelper.getCalculatedUomQty(currentQty, Convert.ToByte(row["UomIndex"]), MyHelper.FractionToDouble(item.MediumUOMFactor), MyHelper.FractionToDouble(item.LargeUOMFactor));
                //    row["CurrentQty"] = decimal.ToDouble(currentQty);
                //}
                //else
                {
                    row["CurrentQty"] = decimal.ToDouble(0);
                }

                if (item.AudiencePrice.HasValue)
                    row["AudiencePrice"] = Math.Round(item.AudiencePrice.Value, defaultRoundingPoints);

                row["ItemDescription"] = item.Description;
                row["ItemDescriptionEn"] = item.DescriptionEn;

                row["ParentItemId"] = item.mtrxParentItem;
                row["M1"] = item.mtrxAttribute1;
                row["M2"] = item.mtrxAttribute2;
                row["M3"] = item.mtrxAttribute3;
                row["RowHandle"] = ++rowhandle;
                row["IsOffer"] = item.IsOffer;
                row["PricingWithSmall"] = item.PricingWithSmall;
                row["VariableWeight"] = item.VariableWeight;
                row["Is_Libra"] = item.is_libra;
                row["bonusDiscount"] = 0;


                #region data for xml export
                row["ItemNameAr"] = item.ItemNameAr;
                row["ItemNameEn"] = item.ItemNameEn;

                row["SmallUOM"] = item.SmallUOM;
                row["SmallUOMPrice"] = item.SmallUOMPrice;

                if (item.MediumUOM.HasValue)
                {
                    row["MediumUOM"] = item.MediumUOM;
                    row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                    row["MediumUOMPrice"] = item.MediumUOMPrice;
                }
                if (item.LargeUOM.HasValue)
                {
                    row["LargeUOM"] = item.LargeUOM;
                    row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);
                    row["LargeUOMPrice"] = item.LargeUOMPrice;
                }

                row["ReorderLevel"] = item.ReorderLevel;
                row["MaxQty"] = item.MaxQty;
                row["MinQty"] = item.MinQty;
                var SubTaxes = DB.IC_ItemSubTaxes.Where(a => a.ItemId == item.ItemId).ToList();
                if (Shared.E_invoiceAvailable == true || Shared.st_Store.E_AllowMoreThanTax == true)
                {

                    setETaxE_Invoice(SubTaxes, row, item);

                }
                else
                {
                    if (SubTaxes.Count > 0)
                    {
                        var subTaxItem = SubTaxes.FirstOrDefault();
                        row["ETaxRatio"] = subTaxItem.Rate;
                        row["TaxType"] = subTaxItem.SubTaxId;
                    }

                }


                #endregion
            }
        }

        double invoice_remains = 0;
        private void GetInvoice(int invId)
        {
            grdcol_branch.Visible = false;

            DB = new DAL.ERPDataContext();


            var inv = DB.SL_Invoices.Where(v => v.SL_InvoiceId == invId).SingleOrDefault();
            if (inv != null)
            {
                if (inv.IsOffer.HasValue)
                    chk_Offer.Checked = inv.IsOffer.Value;
                else
                    chk_Offer.Checked = false;

                userId = inv.UserId;
                lkp_InvoiceBook.EditValue = inv.InvoiceBookId;
                invoice_remains = decimal.ToDouble(inv.Remains);

                lkp_Drawers.EditValue = inv.DrawerAccountId.ToString();

                lkp_Customers.EditValue = inv.CustomerId;
                lkpDelivery.EditValue = inv.Delivery;

                uUId = inv.uuid;

                if (!isCopy)
                {
                    dtInvoiceDate.EditValue = inv.InvoiceDate;
                    txt_DueDate.EditValue = inv.DueDate;
                    dtDeliverDate.EditValue = inv.DeliverDate;
                    txt_Post_Date.EditValue = inv.PostDate;
                    txtInvoiceCode.EditValue = inv.InvoiceCode;
                }


                //if (inv.Is_StoreForEachRow == true)
                if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0)
                {
                    grdcol_branch.Visible = true;
                    //lkpStore.EditValue = null;
                    //lkpStore.Enabled = false;
                }
                else
                {
                    grdcol_branch.Visible = false;

                    //lkpStore.EditValue = inv.StoreId;
                    //lkpStore.Enabled = true;
                }



                dt_Multi_CC.Rows.Clear();
                if (inv.CostCenterId != null)
                {
                    dt_Multi_CC.Rows.Add(inv.CostCenterId);
                }
                //MyHelper.FillMultiCC(ref dt_Multi_CC, invId, (int)Process.SellInvoice);

                lkpStore.EditValue = inv.StoreId;
                txtNotes.Text = inv.Notes;
                if (!isCopy)
                    chk_IsOutTrns.Checked = (inv.Is_OutTrans == true);

                uc_Currency1.lkp_Crnc.EditValue = inv.CrncId;
                uc_Currency1.txtRate.EditValue = inv.CrncRate;

                GetInvoiceDetails(invId);

                txtDiscountRatio.EditValue = decimal.ToDouble(decimal.Round(inv.DiscountRatio * 100, defaultRoundingPoints));
                txt_DeductTaxR.EditValue = decimal.ToDouble(decimal.Round(inv.DeductTaxRatio * 100, defaultRoundingPoints));
                txt_AddTaxR.EditValue = decimal.ToDouble(decimal.Round(inv.AddTaxRatio * 100, defaultRoundingPoints));
                txtExpensesR.EditValue = decimal.ToDouble(decimal.Round(inv.ExpensesRatio * 100, defaultRoundingPoints));

                txtDiscountValue.EditValue = decimal.ToDouble(inv.DiscountValue);
                txt_TaxValue.EditValue = decimal.ToDouble(inv.TaxValue);
                txt_DeductTaxV.EditValue = decimal.ToDouble(inv.DeductTaxValue);
                txt_CusTaxR.EditValue = decimal.ToDouble(inv.CustomTaxRatio);
                txt_CusTaxV.EditValue = decimal.ToDouble(inv.CustomTaxValue);
                txt_AddTaxV.EditValue = decimal.ToDouble(inv.AddTaxValue);

                txt_retentionR.EditValue = decimal.ToDouble(decimal.Round(inv.RetentionRatio * 100, defaultRoundingPoints));
                txt_RetentionV.EditValue = decimal.ToDouble(inv.RetentionValue);

                txt_AdvancePayR.EditValue = decimal.ToDouble(decimal.Round(inv.AdvancePaymentRatio * 100, defaultRoundingPoints));
                txt_AdvancePayV.EditValue = decimal.ToDouble(inv.AdvancePaymentValue);

                if (inv.TransportationValue.HasValue)
                    txt_transportation.EditValue = decimal.ToDouble(decimal.Round(inv.TransportationValue.Value, defaultRoundingPoints));
                if (inv.HandingValue.HasValue)
                    txt_Handing.EditValue = decimal.ToDouble(decimal.Round(inv.HandingValue.Value, defaultRoundingPoints));

                if (inv.ShiftAdd.HasValue)
                    txt_ShiftAdd.EditValue = decimal.ToDouble(decimal.Round(inv.ShiftAdd.Value, defaultRoundingPoints));

                txtExpenses.EditValue = decimal.ToDouble(inv.Expenses).ToString($"F{defaultRoundingPoints}");

                txtNet.EditValue = decimal.ToDouble(inv.Net).ToString($"F{defaultRoundingPoints}");
                txt_PayAcc1_Paid.EditValue = decimal.ToDouble(inv.Paid).ToString($"F{defaultRoundingPoints}");

                if (inv.PayAccountId2.HasValue)
                    lkp_Drawers2.EditValue = inv.PayAccountId2.ToString();
                else
                    lkp_Drawers2.EditValue = null;

                if (inv.PayAcc2_Paid.HasValue)
                    txt_PayAcc2_Paid.EditValue = decimal.ToDouble(inv.PayAcc2_Paid.Value).ToString($"F{defaultRoundingPoints}");
                else
                    txt_PayAcc2_Paid.EditValue = 0;

                txt_Remains.EditValue = decimal.ToDouble(inv.Remains).ToString($"F{defaultRoundingPoints}");
                cmbPayMethod.EditValue = inv.PayMethod;
                chk_IsPosted.Checked = inv.Is_Posted;
                if (!isCopy)
                    txt_PO_No.Text = inv.PurchaseOrderNo;
                txt_Shipping.Text = inv.Shipping;
                lkp_Customers_EditValueChanged(null, EventArgs.Empty);
                txt_AttnMr.Text = inv.AttnMr;

                txtScaleSerial.Text = inv.ScaleWeightSerial;
                txtDriverName.Text = inv.DriverName;
                lkp_Cars.EditValue = txtVehicleNumber.Text = inv.VehicleNumber;
                txtDestination.Text = inv.Destination;

                if (inv.IsArchived != null)//posted offline before
                {
                    barBtnSave.Enabled = false;
                    barBtnCancel.Enabled = false;
                    (grdPrInvoice.FocusedView as GridView).OptionsBehavior.ReadOnly = true;
                }
                else
                {
                    barBtnSave.Enabled = true;
                    if (Shared.LstUserPrvlg == null)
                        barBtnCancel.Enabled = true;
                    else
                    {
                        prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();

                        if ((Shared.st_Store.ch_Authorize.HasValue ? Shared.st_Store.ch_Authorize.Value : false) && (HrHelper.CheckPermission(FormAction.Delete, (int)FormsNames.SL_Invoice, invoiceId)))
                        {
                            barBtnCancel.Enabled = true;
                        }
                        else
                        {
                            barBtnCancel.Enabled = prvlg.CanDel;
                        }

                        if ((Shared.st_Store.ch_Authorize.HasValue ? Shared.st_Store.ch_Authorize.Value : false) && (HrHelper.CheckPermission(FormAction.Print, (int)FormsNames.SL_Invoice, invoiceId)))
                        {
                            barbtnPrint.Enabled = true;
                            barbtnPrintF.Enabled = true;
                            barSubItemPrint.Enabled = true;
                        }
                        else
                        {
                            barbtnPrint.Enabled = prvlg.CanPrint;
                            barbtnPrintF.Enabled = prvlg.CanPrint;
                            barSubItemPrint.Enabled = prvlg.CanPrint;
                        }
                    }
                    (grdPrInvoice.FocusedView as GridView).OptionsBehavior.ReadOnly = false;
                }
                lkp_SalesEmp.EditValue = inv.SalesEmpId;

                if (inv.Estatus == "Valid")
                    barBtnSave.Enabled = false;
                else
                    barBtnSave.Enabled = true;

                if (Shared.E_invoiceAvailable)
                    barBtnSave.Enabled = false;
            }

            else
                invoice_remains = 0;
            Get_TotalAccount();

        }
        private void GetInvoiceDetails(int invoiceId)
        {
            dtSL_Details.Rows.Clear();
            DB = new DAL.ERPDataContext();
            var details = (from d in DB.SL_InvoiceDetails
                           where d.SL_InvoiceId == invoiceId
                           join i in DB.IC_Items on d.ItemId equals i.ItemId
                           orderby d.SL_InvoiceDetailId
                           select new { detail = d, item = i }).ToList();
            Dt_Rows.Rows.Clear();
            if (Shared.E_invoiceAvailable)
            {

                //barBtnSave.Enabled = false;
                getTaxColumns(details.Select(a => a.detail.SL_InvoiceDetailId).ToList());

            }

            int defaultStoreId = 0;
            foreach (var d in details)
            {
                DataRow row = dtSL_Details.NewRow();

                row["SL_InvoiceDetailId"] = d.detail.SL_InvoiceDetailId;
                row["SL_InvoiceId"] = d.detail.SL_InvoiceId;
                row["ItemId"] = d.detail.ItemId;
                row["ItemIdF"] = Shared.IsEnglish ? d.item.ItemNameAr : d.item.ItemNameEn;
                row["CategoryId"] = d.item.Category;
                row["ItemCode1"] = d.item.ItemCode1;
                row["ItemCode2"] = d.item.ItemCode2;
                row["Category"] = d.item.Category;
                row["Company"] = d.item.Company;
                row["ItemType"] = d.item.ItemType;
                row["AudiencePrice"] = Math.Round(d.item.AudiencePrice.HasValue ? d.item.AudiencePrice.Value : 0, defaultRoundingPoints);
                row["UOM"] = d.detail.UOMId;
                row["UomIndex"] = d.detail.UOMIndex;
                row["Qty"] = decimal.ToDouble(d.detail.Qty);
                row["RowHandle"] = ++rowhandle;
                if (d.detail.StoreId != null)
                {
                    lkp_storee.DataSource = MyHelper.Get_StoresNotStopped(d.detail.StoreId.Value, true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,

                        Shared.UserId).Select(s => new { s.StoreNameAr, s.StoreId, s.StoreNameEn }).ToList();
                }

                else
                {
                    lkp_storee.DataSource = MyHelper.Get_StoresNotStopped(0, true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                 Shared.UserId).Select(s => new { s.StoreNameAr, s.StoreId, s.StoreNameEn }).ToList();

                }

                row["StoreId"] = d.detail.StoreId;
                if (d.detail.Height != null)
                    row["Height"] = decimal.ToDouble(d.detail.Height.Value);
                if (d.detail.Length != null)
                    row["Length"] = decimal.ToDouble(d.detail.Length.Value);
                if (d.detail.Width != null)
                    row["Width"] = decimal.ToDouble(d.detail.Width.Value);
                row["PiecesCount"] = decimal.ToDouble(d.detail.PiecesCount);

                if (d.detail.UOMIndex == 0)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice);
                else if (d.detail.UOMIndex == 1)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice * MyHelper.FractionToDouble(d.item.MediumUOMFactor));
                else if (d.detail.UOMIndex == 2)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice * MyHelper.FractionToDouble(d.item.LargeUOMFactor));

                row["SellPrice"] = decimal.ToDouble(d.detail.SellPrice);

                row["SalesTaxRatio"] = Math.Round(d.detail.SalesTaxRatio, defaultRoundingPoints);
                row["SalesTax"] = decimal.ToDouble(d.detail.SalesTax);
                //CustomTaxRatio
                //row["CustomTaxRatio"] = d.detail.CustomTaxRatio;
                //row["CustomTax"] = decimal.ToDouble(d.detail.CustomTax);
                row["CustomTaxRatio"] = decimal.ToDouble(d.detail.CustomTaxRatio);
                row["CustomTax"] = decimal.ToDouble(d.detail.CustomTax);


                row["calcTaxBeforeDisc"] = d.item.calcTaxBeforeDisc;

                row["DiscountValue"] = decimal.ToDouble(d.detail.DiscountValue);
                row["DiscountRatio"] = decimal.ToDouble(d.detail.DiscountRatio);
                row["DiscountRatio2"] = decimal.ToDouble(d.detail.DiscountRatio2);
                row["DiscountRatio3"] = decimal.ToDouble(d.detail.DiscountRatio3);

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(d.item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(d.item.LargeUOMFactor);
                if (d.detail.Expire.HasValue)
                    row["ExpireDate"] = d.detail.Expire;
                row["Batch"] = d.detail.Batch;
                row["Serial"] = d.detail.Serial;
                row["Serial2"] = d.detail.Serial2;
                row["IsExpire"] = d.item.IsExpire;
                row["bonusDiscount"] = decimal.ToDouble(d.detail.bonusDiscount.GetValueOrDefault(0));
                //if (Shared.E_invoiceAvailable)
                //    getETaxDetailForErp(d.detail.SL_InvoiceDetailId, row);
                //else
                //    getETaxE_Invoice(d.detail, d.detail.SL_InvoiceDetailId, row);
                var eTaxes = (from tax in DB.SL_InvoiceDetailSubTaxValues
                              from detail in DB.SL_InvoiceDetails
                              where tax.InvoiceDetailId == d.detail.SL_InvoiceDetailId && detail.ItemId == d.detail.ItemId
                              select new
                              {
                                  IC_ItemSubTaxesId = 0,
                                  ItemId = detail.ItemId,
                                  SubTaxId = tax.esubTypeId,
                                  Rate = tax.TaxRatio,
                              }).Distinct();

                var taxesForItem = eTaxes.AsEnumerable()
                                        .Select(item => new IC_ItemSubTax
                                        {
                                            IC_ItemSubTaxesId = item.IC_ItemSubTaxesId,
                                            ItemId = item.ItemId,
                                            SubTaxId = item.SubTaxId,
                                            Rate = item.Rate,
                                        }).ToList();
                //var SubTaxes = DB.IC_ItemSubTaxes.Where(a => a.ItemId == d.detail.ItemId).ToList();
                if (Shared.st_Store.E_AllowMoreThanTax == true || Shared.E_invoiceAvailable == true)
                {

                    setETaxE_Invoice(taxesForItem, row, d.item);

                }
                else
                {
                    if (taxesForItem.Count > 0)
                    {
                        var subTaxItem = taxesForItem.FirstOrDefault();
                        row["ETaxRatio"] = subTaxItem.Rate;
                        row["TaxType"] = subTaxItem.SubTaxId;
                    }

                }
                //get store qty   
                if (Shared.user.Sell_ShowCrntQty == true)
                {

                    decimal currentQty = 0;

                    //if (d.detail.SL_Invoice.StoreId == 0)

                    //update 30/8/2018 alaa
                    //load quantity according to invoice type
                    //if (d.detail.SL_Invoice.Is_StoreForEachRow == true)
                    //    currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.StoreId.HasValue ? d.detail.StoreId.Value : 0, SlOrdrId, d.detail.Batch);
                    //else
                    //    currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.SL_Invoice.StoreId, SlOrdrId, d.detail.Batch);
                    ////decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.SL_Invoice.StoreId);
                    //currentQty = MyHelper.getCalculatedUomQty(currentQty, d.detail.UOMIndex, MyHelper.FractionToDouble(d.item.MediumUOMFactor), MyHelper.FractionToDouble(d.item.LargeUOMFactor));
                    row["CurrentQty"] = decimal.ToDouble(currentQty);
                }
                else
                {
                    row["CurrentQty"] = decimal.ToDouble(0);
                }
                row["TotalSellPrice"] = decimal.ToDouble(d.detail.TotalSellPrice);

                row["ItemDescription"] = d.detail.ItemDescription;
                row["ItemDescriptionEn"] = d.detail.ItemDescriptionEn;

                row["ParentItemId"] = d.item.mtrxParentItem;
                row["M1"] = d.item.mtrxAttribute1;
                row["M2"] = d.item.mtrxAttribute2;
                row["M3"] = d.item.mtrxAttribute3;

                row["ManufactureDate"] = d.detail.ManufactureDate;
                row["Index"] = dtSL_Details.Rows.Count + 1;
                row["LibraQty"] = Math.Round(d.detail.LibraQty.HasValue ? d.detail.LibraQty.Value : 0, defaultRoundingPoints);
                row["PricingWithSmall"] = d.item.PricingWithSmall;
                row["kg_Weight_libra"] = decimal.Round(d.detail.kg_Weight_libra.HasValue ? d.detail.kg_Weight_libra.Value : 0, defaultRoundingPoints);
                row["IsOffer"] = d.item.IsOffer;
                row["QC"] = d.detail.QC;
                //row["ActualPiecesCount"] = MyHelper.GetItemPieces(dtInvoiceDate.DateTime, d.detail.ItemId, Convert.ToInt32(lkpStore.EditValue), SlOrdrId);
                row["Pack"] = d.detail.Pack;
                row["AudiencePrice"] = d.detail.AudiencePrice;

                dtSL_Details.Rows.Add(row);
            }


            dtSL_Details.AcceptChanges();
            DataModified = false;
            Get_TotalAccount();

        }

        public void getETaxE_Invoice(SL_InvoiceDetail detail, int detailId, DataRow row)
        {

            var etaxes = DB.SL_InvoiceDetailSubTaxValues.Where(x => x.InvoiceDetailId == detailId).ToList();

            if (Shared.st_Store.E_AllowMoreThanTax == false || Shared.st_Store.E_AllowMoreThanTax == null)
            {
                if (etaxes.Count > 0)
                {
                    row["TaxType"] = etaxes.FirstOrDefault().esubTypeId;
                    row["ETaxRatio"] = decimal.ToDouble(etaxes.FirstOrDefault().TaxRatio.GetValueOrDefault(0));
                    row["EtaxValue"] = decimal.ToDouble(etaxes.FirstOrDefault().value);
                }
            }
            else
            {
                if (etaxes.Count > 0)
                {
                    taxValue = 0;
                    decimal totalTaxValue = 0;
                    decimal totalTaxRatio = 0;
                    decimal ratio = 0;
                    decimal tableTaxValue = 0;
                    decimal TotalSellPrice = 0;
                    decimal totalTableTaxes = 0;

                    foreach (var tax in etaxes)
                    {
                        bool isT1 = false;
                        DataRow dTax = Dt_Rows.NewRow();
                        dTax["SubTax"] = tax.esubTypeId;
                        dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.esubTypeId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                        dTax["Percentage"] = decimal.ToDouble(tax.TaxRatio.GetValueOrDefault(0));
                        var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId).ToList();
                        //decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
                        //totalTaxRatio += taxvalueRatio;
                        decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                        decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                        decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

                        TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                        bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);

                        decimal taxvalue = calcTaxBeforeDisc ?
                            Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints)
                            : Math.Round(TotalSellPrice * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints);


                        if (dTax["Tax"].ToString() == "1")
                        {
                            isT1 = true;
                            taxvalue = tax.value;
                            ratio = Math.Round((tax.TaxRatio.GetValueOrDefault(0) / TotalSellPrice) * 100, defaultRoundingPoints);


                        }
                        if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
                        {

                            decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.TaxRatio.GetValueOrDefault(0)), defaultRoundingPoints);
                            totalTaxValue -= taxvalue;
                            totalTaxRatio -= taxvalueRatio;
                            taxValue -= taxvalue;
                        }

                        else
                        {


                            decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.TaxRatio.GetValueOrDefault(0)), defaultRoundingPoints);
                            if (isT1 == true)
                            {
                                taxvalueRatio = ratio;
                                row["addTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);
                            }


                            totalTaxValue += taxvalue;
                            totalTaxRatio += taxvalueRatio;
                            taxValue += taxvalue;
                            if (tableTaxIds.Contains(Convert.ToInt32(dTax["Tax"])))
                            {
                                tableTaxValue += taxvalue;
                                totalTableTaxes += taxvalueRatio;
                                row["tableTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);

                            }


                        }
                        //totalTaxValue += taxvalue;
                        //taxValue += taxvalue;

                        dTax["ItemId"] = detail.ItemId;
                        dTax["SellPrice"] = detail.SellPrice;
                        dTax["Qty"] = detail.Qty;
                        dTax["TaxValue"] = decimal.ToDouble(taxvalue);
                        dTax["RowHandle"] = rowhandle;
                        Dt_Rows.Rows.Add(dTax);

                        if (dt_SubTax.AsEnumerable()
                          .Where(a => a.RowState != DataRowState.Deleted)
                          .Any(r => Convert.ToInt32(r["SubTaxId"].ToString()) == Convert.ToInt32(tax.esubTypeId)))
                        {
                            var subTax = dt_SubTax.AsEnumerable()
                           .Where(a => a.RowState != DataRowState.Deleted)
                           .FirstOrDefault(r => Convert.ToInt32(r["SubTaxId"].ToString()) == Convert.ToInt32(tax.esubTypeId));
                            subTax["Rate"] = Convert.ToDouble(subTax["Rate"]) + Convert.ToDouble(dTax["Percentage"]);
                            subTax["Value"] = Convert.ToDouble(subTax["Value"]) + Convert.ToDouble(dTax["TaxValue"]);

                        }
                        else
                        {
                            DataRow dtTax = dt_SubTax.NewRow();
                            dtTax["SubTaxId"] = Convert.ToInt32(tax.esubTypeId);
                            dtTax["Rate"] = Convert.ToDouble(dTax["Percentage"]);
                            dtTax["Value"] = Convert.ToDouble(dTax["TaxValue"]);
                            dt_SubTax.Rows.Add(dtTax);
                        }

                    }

                    row["salePriceWithTaxTable"] = Math.Round(Convert.ToDecimal(TotalSellPrice + tableTaxValue), defaultRoundingPoints);
                    row["totalTableTaxes"] = Math.Round(Convert.ToDecimal(totalTableTaxes), defaultRoundingPoints);
                    row["TotalTaxes"] = Math.Round(Convert.ToDecimal(totalTaxValue), defaultRoundingPoints);
                    row["totalTaxesRatio"] = Math.Round(Convert.ToDecimal(totalTaxRatio), defaultRoundingPoints);
                }
            }

        }
        decimal totalSubDiscountTax = 0;

        //public void setETaxE_Invoice(List<IC_ItemSubTax> detail, DataRow row, IC_Item item)
        //{

        //    if (detail.Count > 0)
        //    {
        //        taxValue = 0;
        //        decimal totalTaxValue = 0;
        //        decimal totalTaxRatio = 0;
        //        decimal TotalSellPrice = 0;
        //        decimal totalTableTaxRatio = 0;
        //        decimal tableTaxValue = 0;
        //        decimal tableTaxratio = 0;
        //        row["TotalTaxes"] = 0;
        //        row["totalTaxesRatio"] = 0;

        //        var customTaxT1 = DB.E_TaxableTypes.Where(a => a.Code == "T2").FirstOrDefault()?.E_TaxableTypeId;
        //        var customTaxT2 = DB.E_TaxableTypes.Where(a => a.Code == "T3").FirstOrDefault()?.E_TaxableTypeId;
        //        var salesTaxId = DB.E_TaxableTypes.Where(a => a.Code == "T1").FirstOrDefault()?.E_TaxableTypeId;

        //        decimal totalCustomTaxRatio = 0;

        //        var eTaxesWithParentId = from t in detail
        //                                 join etax in DB.E_TaxableTypes
        //                                 on t.SubTaxId equals etax.E_TaxableTypeId
        //                                 select new
        //                                 {
        //                                     t.IC_ItemSubTaxesId,
        //                                     t.Rate,
        //                                     etax.ParentTaxId
        //                                 };

        //        foreach (var r in eTaxesWithParentId)
        //        {
        //            if (r.ParentTaxId == customTaxT1 || r.ParentTaxId == customTaxT2)
        //            {
        //                totalCustomTaxRatio += Convert.ToDecimal(r.Rate) / 100;
        //            }
        //        }

        //        //  Dt_Rows.Clear();
        //        var detailTaxes = DB.E_TaxableTypes.Where(a => detail.Select(c => c.SubTaxId).Contains(a.E_TaxableTypeId)).Select(a => new { ParentTaxId = (int)a.ParentTaxId, E_TaxableTypeId = a.E_TaxableTypeId }).ToList();
        //        var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId).ToList();
        //        var detailTablesTaxes = detailTaxes.Where(a => tableTaxIds.Contains(a.ParentTaxId)).Select(a => a.E_TaxableTypeId);
        //        foreach (var tax in detail.Where(a => detailTablesTaxes.Contains(a.SubTaxId)))
        //        {
        //            DataRow dTax = Dt_Rows.NewRow();
        //            dTax["SubTax"] = tax.SubTaxId;
        //            dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
        //            dTax["Percentage"] = decimal.ToDouble(tax.Rate.GetValueOrDefault(0));

        //            //decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
        //            //totalTaxRatio += taxvalueRatio;
        //            decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

        //            TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

        //            bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);

        //            decimal taxvalue = calcTaxBeforeDisc ?
        //              Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints)
        //              : Math.Round(TotalSellPrice * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints);
        //            if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
        //            {

        //                decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
        //                totalTaxValue -= taxvalue;
        //                totalTaxRatio -= taxvalueRatio;
        //                taxValue -= taxvalue;
        //            }

        //            else
        //            {


        //                decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
        //                totalTaxValue += taxvalue;
        //                totalTaxRatio += taxvalueRatio;
        //                taxValue += taxvalue;
        //                totalTableTaxRatio += taxvalueRatio;
        //                tableTaxValue += taxvalue;
        //                tableTaxratio += taxvalueRatio;
        //            }
        //            dTax["ItemId"] = item.ItemId;
        //            dTax["SellPrice"] = 0;
        //            dTax["Qty"] = 0;
        //            dTax["TaxValue"] = decimal.ToDouble(taxvalue);
        //            dTax["RowHandle"] = rowhandle;
        //            Dt_Rows.Rows.Add(dTax);
        //            row["tableTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);

        //        }
        //        foreach (var tax in detail.Where(a => !detailTablesTaxes.Contains(a.SubTaxId)))
        //        {
        //            bool isT1 = false;
        //            DataRow dTax = Dt_Rows.NewRow();
        //            dTax["SubTax"] = tax.SubTaxId;
        //            dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
        //            dTax["Percentage"] = decimal.ToDouble(tax.Rate.GetValueOrDefault(0));

        //            //decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
        //            //totalTaxRatio += taxvalueRatio;
        //            decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

        //            TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

        //            bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);
        //            if (dTax["Tax"].ToString() == "1")
        //            {
        //                var totalSellPriceWithTableTaxes = Convert.ToDecimal(totalTableTaxRatio / 100) * TotalSellPrice;
        //                TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;
        //                isT1 = true;
        //            }

        //            decimal taxvalue = calcTaxBeforeDisc ?
        //              Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints)
        //              : Math.Round(TotalSellPrice * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints);
        //            if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
        //            {

        //                decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
        //                totalTaxValue -= taxvalue;
        //                totalTaxRatio -= taxvalueRatio;
        //                taxValue -= taxvalue;
        //            }

        //            else
        //            {


        //                decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
        //                if (isT1)
        //                {
        //                    TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //                    taxvalueRatio = TotalSellPrice != 0 ? Math.Round((taxvalue / TotalSellPrice) * 100, defaultRoundingPoints) : 0;

        //                    row["addTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);
        //                }
        //                totalTaxValue += taxvalue;
        //                totalTaxRatio += taxvalueRatio;
        //                taxValue += taxvalue;

        //            }
        //            dTax["ItemId"] = item.ItemId;
        //            dTax["SellPrice"] = 0;
        //            dTax["Qty"] = 0;
        //            dTax["TaxValue"] = decimal.ToDouble(taxvalue);
        //            dTax["RowHandle"] = rowhandle;
        //            Dt_Rows.Rows.Add(dTax);

        //        }

        //        row["totalTableTaxes"] = Math.Round(Convert.ToDecimal(tableTaxratio), defaultRoundingPoints);
        //        row["salePriceWithTaxTable"] = Math.Round(Convert.ToDecimal(TotalSellPrice + tableTaxValue), defaultRoundingPoints);
        //        row["TotalTaxes"] = Math.Round(Convert.ToDecimal(totalTaxValue), defaultRoundingPoints);
        //        row["totalTaxesRatio"] = Math.Round(Convert.ToDecimal(totalTaxRatio), defaultRoundingPoints);



        //    }

        //}
        public void setETaxE_Invoice(List<IC_ItemSubTax> detail, DataRow row, IC_Item item)
        {
            MyHelper.TaxService.setETaxE_Invoice(detail, row, ref Dt_Rows, rowhandle, item, discountTaxId);
            if (Dt_Rows.Rows.Count > 0)
            {
                taxValue = dtSL_Details.AsEnumerable()
                    .Where(r => r.RowState != DataRowState.Deleted)
                    .Sum(x => Convert.ToDecimal(x["TaxValue"] is DBNull ? 0 : Convert.ToDecimal(x["TaxValue"])));
                totalSubDiscountTax = dtSL_Details.AsEnumerable()
                    .Where(r => r.RowState != DataRowState.Deleted)
                    .Sum(x => Convert.ToDecimal(x["TotalSubDiscountTax"] is DBNull ? 0 : Convert.ToDecimal(x["TotalSubDiscountTax"])));
                // Call the method to get total account
                Get_TotalAccount();
                AssignTaxestoDGV();
            }
        }
        void AssignTaxestoDGV()
        {
                foreach (DataRow dTax in Dt_Rows.Rows)
                {
                    if (dt_SubTax.AsEnumerable()
                      .Where(a => a.RowState != DataRowState.Deleted)
                      .Any(r => Convert.ToInt32(r["SubTaxId"].ToString()) == Convert.ToInt32(dTax["SubTax"])))
                    {
                        var subTax = dt_SubTax.AsEnumerable()
                       .Where(a => a.RowState != DataRowState.Deleted)
                       .FirstOrDefault(r => Convert.ToInt32(r["SubTaxId"].ToString()) == Convert.ToInt32(dTax["SubTax"]));
                        subTax["Rate"] = Convert.ToDecimal(dTax["Percentage"]);
                        subTax["Value"] = Convert.ToDecimal(dTax["TaxValue"]);

                    }
                    else
                    {
                        DataRow dtTax = dt_SubTax.NewRow();
                        dtTax["SubTaxId"] = Convert.ToInt32(dTax["SubTax"]);
                        dtTax["Rate"] = Convert.ToDecimal(dTax["Percentage"]);
                        dtTax["Value"] = Convert.ToDecimal(dTax["TaxValue"]);
                        dt_SubTax.Rows.Add(dtTax);
                    }
                }
        }
        private void calcSubTaxes(DataRow detail, GridView view)
        {
            MyHelper.TaxService.CalcSubTaxes(detail,ref Dt_Rows, discountTaxId);
            // Update grid view
            if (Dt_Rows.Rows.Count > 0)
            {
                view.UpdateCurrentRow();

                taxValue = dtSL_Details.AsEnumerable()
                    .Where(r => r.RowState != DataRowState.Deleted)
                    .Sum(x => Convert.ToDecimal(x["TaxValue"] is DBNull ? 0 : Convert.ToDecimal(x["TaxValue"])));
                totalSubDiscountTax = dtSL_Details.AsEnumerable()
                    .Where(r => r.RowState != DataRowState.Deleted)
                    .Sum(x => Convert.ToDecimal(x["TotalSubDiscountTax"] is DBNull ? 0 : Convert.ToDecimal(x["TotalSubDiscountTax"])));
                // Call the method to get total account
                Get_TotalAccount();
                AssignTaxestoDGV();
            }
        }
        public void setETaxE_InvoiceAfterSellInvoice(List<IC_ItemSubTax> detail, DataRow row, IC_Item item)
        {

            if (detail.Count > 0)
            {
                taxValue = 0;
                decimal totalTaxValue = 0;
                decimal totalTaxRatio = 0;
                decimal TotalSellPrice = 0;
                decimal totalTableTaxRatio = 0;
                decimal tableTaxValue = 0;
                decimal tableTaxratio = 0;

                row["TotalTaxes"] = 0;
                row["totalTaxesRatio"] = 0;

                var detailTaxes = DB.E_TaxableTypes.Where(a => detail.Select(c => c.SubTaxId).Contains(a.E_TaxableTypeId)).Select(a => new { ParentTaxId = (int)a.ParentTaxId, E_TaxableTypeId = a.E_TaxableTypeId }).ToList();
                var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId).ToList();
                var detailTablesTaxes = detailTaxes.Where(a => tableTaxIds.Contains(a.ParentTaxId)).Select(a => a.E_TaxableTypeId);
                var Dt_RowsTaxes = Dt_Rows.AsEnumerable()
                          .Where(a => a.RowState != DataRowState.Deleted)
                          .Where(r => Convert.ToInt32(r["RowHandle"].ToString()) == rowhandle).ToList();
                //======================Table Taxes Calculation=============//
                foreach (var tax in detail.Where(a => detailTablesTaxes.Contains(a.SubTaxId)))
                {
                    var rowExist = Dt_RowsTaxes.Where(r => Convert.ToInt32(r["SubTax"].ToString()) == tax.SubTaxId).FirstOrDefault();
                    var Tax = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                    var Percentage = decimal.ToDouble(tax.Rate.GetValueOrDefault(0));


                    decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

                    TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                    bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);

                    decimal taxvalue = calcTaxBeforeDisc ?
                       Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(Percentage) / 100, defaultRoundingPoints)
                       : Math.Round(TotalSellPrice * Convert.ToDecimal(Percentage) / 100, defaultRoundingPoints);
                    if (Tax == discountTaxId)
                    {

                        decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
                        totalTaxValue -= taxvalue;
                        totalTaxRatio -= taxvalueRatio;
                        taxValue -= taxvalue;
                    }

                    else
                    {
                        decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
                        totalTaxValue += taxvalue;
                        totalTaxRatio += taxvalueRatio;
                        taxValue += taxvalue;
                        totalTableTaxRatio += taxvalueRatio;
                        tableTaxValue += taxvalue;
                        tableTaxratio += taxvalueRatio;
                    }

                    if (rowExist != null)
                    {
                        rowExist["TaxValue"] = taxvalue;
                    }
                    row["tableTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);
                }
                //======================Other Taxes Calculation(Add Or Discout Taxes)=============//
                foreach (var tax in detail.Where(a => !detailTablesTaxes.Contains(a.SubTaxId)))
                {
                    bool isT1 = false;
                    var rowExist = Dt_RowsTaxes.Where(r => Convert.ToInt32(r["SubTax"].ToString()) == tax.SubTaxId).FirstOrDefault();
                    var Tax = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                    var Percentage = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);

                    decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

                    TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                    bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);
                    if (Tax == 1)
                    {
                        var totalSellPriceWithTableTaxes = Convert.ToDecimal(totalTableTaxRatio / 100) * TotalSellPrice;
                        TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;
                        isT1 = true;
                    }

                    decimal taxvalue = calcTaxBeforeDisc ?
                      Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(Percentage) / 100, defaultRoundingPoints)
                      : Math.Round(TotalSellPrice * Percentage / 100, defaultRoundingPoints);
                    if (Tax == discountTaxId)
                    {

                        decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
                        totalTaxValue -= taxvalue;
                        totalTaxRatio -= taxvalueRatio;
                        taxValue -= taxvalue;
                    }

                    else
                    {


                        decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
                        if (isT1)
                        {
                            TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                            taxvalueRatio = TotalSellPrice != 0 ? Math.Round(Convert.ToDecimal(taxvalue / TotalSellPrice) * 100, defaultRoundingPoints) : 0;
                            row["addTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);
                        }
                        totalTaxValue += taxvalue;
                        totalTaxRatio += taxvalueRatio;
                        taxValue += taxvalue;

                    }

                    if (rowExist != null)
                    {
                        rowExist["TaxValue"] = taxvalue;
                    }

                }
                row["totalTableTaxes"] = Math.Round(Convert.ToDecimal(tableTaxratio), defaultRoundingPoints);
                row["salePriceWithTaxTable"] = Math.Round(Convert.ToDecimal(TotalSellPrice + tableTaxValue), defaultRoundingPoints);
                row["TotalTaxes"] = Math.Round(Convert.ToDecimal(totalTaxValue), defaultRoundingPoints);
                row["totalTaxesRatio"] = Math.Round(Convert.ToDecimal(totalTaxRatio), defaultRoundingPoints);

            }

        }

        public void getTaxColumns(List<int> details)
        {
            var subTaxesList = DB.SL_InvoiceDetailSubTaxValues.Where(a => details.Contains(a.InvoiceDetailId)).Select(a => a.esubTypeId).Distinct().ToList();
            var count = 1;
            foreach (var item in subTaxesList)
            {
                var view = (grdPrInvoice.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                string ETaxType = "ETaxType" + item.ToString();
                dtSL_Details.Columns.Add(ETaxType);
                GridColumn columnETaxType = new GridColumn();
                columnETaxType.FieldName = ETaxType;
                columnETaxType.Visible = true;
                columnETaxType.OptionsColumn.ReadOnly = true;
                columnETaxType.Caption = "نوع الضريبة" + count.ToString();
                view.Columns.Add(columnETaxType);
                //==================================
                string ETaxRatio = "ETaxRatio" + item.ToString();
                dtSL_Details.Columns.Add(ETaxRatio);
                GridColumn columnETaxRatio = new GridColumn();
                columnETaxRatio.FieldName = ETaxRatio;
                columnETaxRatio.Visible = true;
                columnETaxRatio.OptionsColumn.ReadOnly = true;
                columnETaxRatio.Caption = "نسبة الضريبة" + count.ToString();
                view.Columns.Add(columnETaxRatio);
                //==================================
                string ETaxValue = "ETaxValue" + item.ToString();
                dtSL_Details.Columns.Add(ETaxValue);
                GridColumn columnETaxValue = new GridColumn();
                columnETaxValue.FieldName = ETaxValue;
                columnETaxValue.Visible = true;
                columnETaxValue.OptionsColumn.ReadOnly = true;
                columnETaxValue.Caption = "قيمة الضريبة" + count.ToString();
                view.Columns.Add(columnETaxValue);

                count++;
                // colETaxValue.Visible = false;
                //colETaxRatio.Visible = false;
                //Col_ETaxType.Visible = false;
                //TotalTaxes.Visible = false;
                //totalTaxesRatio.Visible = false;
                //btn_AddTaxes.Visible = false;

            }
        }
        public void getETaxDetailForErp(int detailId, DataRow row)
        {
            //int index = dtSL_Details.Rows.IndexOf(row);
            var etax = DB.SL_InvoiceDetailSubTaxValues.Where(x => x.InvoiceDetailId == detailId).ToList();
            if (etax.Count != 0)
            {
                //var EtaxValueTotal = 0;
                foreach (var item in etax)
                {
                    row["ETaxType" + item.esubTypeId.ToString()] = DB.E_TaxableTypes.Where(a => a.E_TaxableTypeId == item.esubTypeId).Select(a => a.Code).FirstOrDefault();
                    row["ETaxRatio" + item.esubTypeId.ToString()] = item.TaxRatio != null ? decimal.ToDouble(item.TaxRatio.Value) : 0;
                    row["ETaxValue" + item.esubTypeId.ToString()] = decimal.ToDouble(item.value);
                    //EtaxValueTotal += item.TaxRatio != null ? decimal.ToInt32(item.TaxRatio.Value) : 0;
                }
                //colETaxValue.View.SetRowCellValue(index, "EtaxValue", EtaxValueTotal);
            }
        }
        public void LoadInvoice()
        {
            if (DB == null)
                DB = new ERPDataContext();
            Reset();
            int defaultStoreId = 0;

            if (invoiceId > 0)
            {
                //DB.Refresh(System.Data.Linq.RefreshMode.OverwriteCurrentValues, DB.SL_Invoices);
                var storeId = DB.SL_Invoices.Where(a => a.SL_InvoiceId == invoiceId).Select(x => x.StoreId).FirstOrDefault();

                if (Shared.InvoicePostToStore)

                    stores_table = MyHelper.Get_StoresNotStopped(storeId, true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                    Shared.UserId);
                else

                    stores_table = MyHelper.Get_StoresNotStopped(storeId, false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                    Shared.UserId);
                lkpStore.Properties.DataSource = stores_table;
                GetInvoice(invoiceId);
            }
            else
            {
                stores_table = MyHelper.Get_StoresNotStopped(0, false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);

                lkpStore.Properties.DataSource = stores_table;
                lkpStore.EditValue = defaultStoreId;
                //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                {
                    //lkpStore.Enabled = false;
                    //lkpStore.EditValue = null;
                    grdcol_branch.Visible = true;

                }
                else
                {
                    //lkpStore.Enabled = true;
                    //lkpStore.ItemIndex = 0;

                    //lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);
                    grdcol_branch.Visible = false;



                }

                txtNotes.Text = Shared.user.InvoicesNotes;
                //lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);

                barBtnSave.Enabled = true;
                if (Shared.LstUserPrvlg == null)
                    barBtnCancel.Enabled = true;
                else
                {
                    prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();

                    if ((Shared.st_Store.ch_Authorize.HasValue ? Shared.st_Store.ch_Authorize.Value : false) && (HrHelper.CheckPermission(FormAction.Delete, (int)FormsNames.SL_Invoice, invoiceId)))
                    {
                        barBtnCancel.Enabled = true;
                    }
                    else
                    {
                        barBtnCancel.Enabled = prvlg.CanDel;
                    }

                    if ((Shared.st_Store.ch_Authorize.HasValue ? Shared.st_Store.ch_Authorize.Value : false) && (HrHelper.CheckPermission(FormAction.Print, (int)FormsNames.SL_Invoice, invoiceId)))
                    {
                        barBtnCancel.Enabled = true;
                    }
                    else
                    {
                        barbtnPrint.Enabled = prvlg.CanPrint;
                        barbtnPrintF.Enabled = prvlg.CanPrint;
                        barSubItemPrint.Enabled = prvlg.CanPrint;
                    }
                }
                (grdPrInvoice.FocusedView as GridView).OptionsBehavior.ReadOnly = false;

                FocusItemCode1(Shared.user.FocusGridInInvoices);

                if (customerId != 0)
                {
                    lkp_Customers.EditValue = customerId;

                    lkpDelivery.EditValue = DB.SL_Customers.Where(x => x.CustomerId == customerId).FirstOrDefault().Delivery;
                }
            }


            bool canApprove = false;
            if (invoiceId > 0)
            {
                var validateUser = (from i in DB.SL_Invoices
                                    where i.SL_InvoiceId == invoiceId
                                    join u in DB.HR_Users
                                    on i.UserId equals u.UserId
                                    select u.MaxUserId).FirstOrDefault();
                if (validateUser != null)
                {
                    canApprove = true;
                }
            }

            if (Shared.st_Store.IsMaxSalesInvoice == true &&
                ((Shared.user.MaxUserId == null && Shared.user.MaxSalesValue > 0)
                || canApprove))
            {
                chk_Approved.Visible = true;
            }
            else
            {
                if (Shared.st_Store.IsMaxSalesOrder != true) chk_Approved.Checked = true;
                chk_Approved.Visible = false;
            }

            DataModified = false;
            // isCopy = false;
        }

        private void FocusItemCode1(bool focusGrid)
        {
            if (focusGrid)
            {
                grdPrInvoice.Focus();
                var view = (grdPrInvoice.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud            
            }
            else
                lkp_Customers.Focus();
        }


        public void Save_Invoice()
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            grdPrInvoice.RefreshDataSource();
            view.RefreshData();
            Get_TotalAccount();

            //save invoice
            DB = new DAL.ERPDataContext();
            DAL.SL_Invoice sl;
            DAL.IC_MultipleWeight mul;
            if (invoiceId > 0)
            {
                sl = DB.SL_Invoices.Where(x => x.SL_InvoiceId == invoiceId).FirstOrDefault();
                sl.LastUpdateUserId = Shared.UserId;
                sl.LastUpdateDate = DateTime.Now;

                MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                (int)FormAction.Edit, (int)FormsNames.SL_Invoice);

            }
            else
            {
                sl = new SL_Invoice();
                sl.UserId = Shared.UserId;
                sl.JornalId = 0;
                if (chk_IsOutTrns.Checked)//when just loaded from invoice
                    sl.Is_OutTrans = true;
                DB.SL_Invoices.InsertOnSubmit(sl);

                MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                    (int)FormAction.Add, (int)FormsNames.SL_Invoice);
            }


            #region SL Inv



            //update 29/8/2017 alaa
            //save invoice type
            if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0)
            {
                sl.Is_StoreForEachRow = true;
            }
            else
            {
                sl.Is_StoreForEachRow = false;

            }


            if (lkp_InvoiceBook.EditValue == null || Convert.ToInt32(lkp_InvoiceBook.EditValue) == 0)
                sl.InvoiceBookId = null;
            else
                sl.InvoiceBookId = Convert.ToInt32(lkp_InvoiceBook.EditValue);

            /*if (lkpCostCenter.EditValue == null || lkpCostCenter.EditValue.ToString() == string.Empty || Convert.ToInt32(lkpCostCenter.EditValue) == 0)
                sl.CostCenterId = null;
            else
                sl.CostCenterId = Convert.ToInt32(lkpCostCenter.EditValue);*/


            sl.CustomerId = Convert.ToInt32(lkp_Customers.EditValue);
            if (lkpDelivery.EditValue != null)
                sl.Delivery = Convert.ToInt32(lkpDelivery.EditValue);

            sl.InvoiceCode = txtInvoiceCode.Text.Trim();
            sl.InvoiceDate = dtInvoiceDate.DateTime;

            sl.StoreId = Convert.ToInt32(lkpStore.EditValue);

            sl.Notes = txtNotes.Text;

            //if (Shared.InvoicePostToStore)
            //    sl.Is_OutTrans = true;
            //else
            //    sl.Is_OutTrans = chk_IsOutTrns.Checked;

            sl.ProcessId = null;
            sl.SourceId = null;

            sl.PayMethod = (bool?)cmbPayMethod.EditValue;
            sl.DiscountRatio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
            sl.DiscountValue = Convert.ToDecimal(txtDiscountValue.EditValue);

            sl.ExpensesRatio = Convert.ToDecimal(txtExpensesR.EditValue) / 100;
            sl.Expenses = Convert.ToDecimal(txtExpenses.EditValue);

            sl.TaxValue = Convert.ToDecimal(txt_TaxValue.EditValue);
            sl.DeductTaxValue = Convert.ToDecimal(txt_DeductTaxV.EditValue);
            sl.DeductTaxRatio = Convert.ToDecimal(txt_DeductTaxR.EditValue) / 100;

            sl.AddTaxValue = Convert.ToDecimal(txt_AddTaxV.EditValue);
            sl.AddTaxRatio = Convert.ToDecimal(txt_AddTaxR.EditValue) / 100;

            sl.CustomTaxValue = Convert.ToDecimal(txt_CusTaxV.EditValue);
            sl.CustomTaxRatio = Convert.ToDecimal(txt_CusTaxR.EditValue) / 100;

            sl.Net = Convert.ToDecimal(txtNet.EditValue);
            sl.Paid = Convert.ToDecimal(txt_PayAcc1_Paid.EditValue);

            sl.RetentionValue = Convert.ToDecimal(txt_RetentionV.EditValue);
            sl.RetentionRatio = Convert.ToDecimal(txt_retentionR.EditValue) / 100;
            sl.AdvancePaymentValue = Convert.ToDecimal(txt_AdvancePayV.EditValue);
            sl.AdvancePaymentRatio = Convert.ToDecimal(txt_AdvancePayR.EditValue) / 100;

            sl.HandingValue = Convert.ToDecimal(txt_Handing.EditValue);
            sl.TransportationValue = Convert.ToDecimal(txt_transportation.EditValue);
            sl.ShiftAdd = Convert.ToDecimal(txt_ShiftAdd.EditValue);

            if (lkp_Drawers2.EditValue != null)
                sl.PayAccountId2 = Convert.ToInt32(lkp_Drawers2.EditValue);
            else
                sl.PayAccountId2 = null;
            sl.PayAcc2_Paid = Convert.ToDecimal(txt_PayAcc2_Paid.EditValue);

            sl.Remains = Convert.ToDecimal(txt_Remains.EditValue);
            if (lkp_Drawers.EditValue != null && lkp_Drawers.Text != "")
                sl.DrawerAccountId = Convert.ToInt32(lkp_Drawers.EditValue);
            if (lkp_SalesEmp.EditValue == null)
            {
                sl.SalesEmpId = null;
            }
            else
            {
                sl.SalesEmpId = Convert.ToInt32(lkp_SalesEmp.EditValue);
            }

            if (dtDeliverDate.EditValue != null)
                sl.DeliverDate = dtDeliverDate.DateTime;
            else
                sl.DeliverDate = null;
            sl.Shipping = txt_Shipping.Text;
            sl.PurchaseOrderNo = txt_PO_No.Text;



            sl.Is_Posted = chk_IsPosted.Checked;
            if (txt_Post_Date.EditValue == null)
                sl.PostDate = null;
            else
                sl.PostDate = txt_Post_Date.DateTime;



            if (txt_DueDate.EditValue == null || txt_DueDate.DateTime == DateTime.MinValue)
                sl.DueDate = dtInvoiceDate.DateTime;
            else
                sl.DueDate = txt_DueDate.DateTime;

            sl.AttnMr = txt_AttnMr.Text;

            sl.CrncId = Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue);
            sl.CrncRate = Convert.ToDecimal(uc_Currency1.txtRate.EditValue);

            sl.ScaleWeightSerial = txtScaleSerial.Text;
            sl.DriverName = txtDriverName.Text;
            sl.VehicleNumber = txtVehicleNumber.Text;
            sl.Destination = txtDestination.Text;
            #endregion

            userId = sl.UserId;

            sl.IsOffer = chk_Offer.Checked;
            try
            {
                DB.SubmitChanges();
            }
            catch (Exception exc)
            {
                Utilities.save_Log(exc.Message, exc);
                Utilities.save_Log(sl.ToString(), null);
            }

            if (chk_Approved.Visible == true && chk_Approved.Checked == true)
            {
                sl.ValidateUser = Shared.UserId;
            }
            else if (DataModified == true)
            {
                if (Shared.st_Store.IsMaxSalesOrder == true) chk_Approved.Checked = false;
                sl.ValidateUser = null;
            }

            #region Delete ItemStore & SL Detail

            //var invoice_itemstores = (from i in DB.IC_ItemStores
            //                          join s in DB.SL_InvoiceDetails
            //                          on i.SourceId equals s.SL_InvoiceDetailId
            //                          where i.ProcessId == (int)Process.SellInvoice &&
            //                          s.SL_InvoiceId == invoiceId
            //                          select i).ToList();

            var taxDetails = DB.SL_InvoiceDetailSubTaxValues.Where(x => sl.SL_InvoiceDetails.Select(d => d.SL_InvoiceDetailId).Contains(x.InvoiceDetailId));
            //DB.IC_ItemStores.DeleteAllOnSubmit(invoice_itemstores);
            DB.SL_InvoiceDetailSubTaxValues.DeleteAllOnSubmit(taxDetails);
            DB.SL_InvoiceDetails.DeleteAllOnSubmit(sl.SL_InvoiceDetails);
            #endregion

            decimal CostOfSoldGoods = 0;//used for continual inventory
            byte costmethod = Convert.ToByte(lkpStore.Properties.GetDataSourceValue("CostMethod", lkpStore.ItemIndex));
            List<StoreItem> lst_outitems = new List<StoreItem>();
            var lst = new List<IC_ItemStore>();

            for (int x = 0; x < dtSL_Details.Rows.Count; x++)
            {
                #region SL Detail
                if (dtSL_Details.Rows[x].RowState == DataRowState.Deleted)
                    continue;

                decimal MediumUOMFactor = 1;
                decimal LargeUOMFactor = 1;

                DAL.SL_InvoiceDetail detail = new DAL.SL_InvoiceDetail();
                detail.SL_InvoiceId = sl.SL_InvoiceId;
                detail.ItemId = Convert.ToInt32(dtSL_Details.Rows[x]["ItemId"]);
                detail.UOMId = Convert.ToInt32(dtSL_Details.Rows[x]["UOM"]);
                detail.UOMIndex = Convert.ToByte(dtSL_Details.Rows[x]["UomIndex"]);
                detail.Qty = Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]);


                #region check
                // if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null) 

                //if ( DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null/*lkpStore.EditValue == null*/)

                //update 30/8/2018 alaa
                //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0 /*|| sl.Is_StoreForEachRow == true*/)
                if (Shared.st_Store.IsStoreOnEachSellRecord)
                {
                    detail.StoreId = Convert.ToInt32(dtSL_Details.Rows[x]["StoreId"]);
                }
                #endregion



                detail.Height = Convert.ToDecimal(dtSL_Details.Rows[x]["Height"]);
                detail.Length = Convert.ToDecimal(dtSL_Details.Rows[x]["Length"]);
                detail.Width = Convert.ToDecimal(dtSL_Details.Rows[x]["Width"]);
                detail.PiecesCount = Convert.ToDecimal(dtSL_Details.Rows[x]["PiecesCount"]);

                #region Expire
                if (dtSL_Details.Rows[x]["ExpireDate"] == DBNull.Value || dtSL_Details.Rows[x]["ExpireDate"] == null)
                    detail.Expire = null;
                else
                {
                    DateTime temp = Convert.ToDateTime(dtSL_Details.Rows[x]["ExpireDate"]);
                    temp = temp.AddDays(-temp.Day + 1);
                    detail.Expire = temp;
                }

                if (dtSL_Details.Rows[x]["Batch"] == DBNull.Value
                    || dtSL_Details.Rows[x]["Batch"].ToString().Trim() == string.Empty)
                    detail.Batch = null;
                else
                    detail.Batch = dtSL_Details.Rows[x]["Batch"].ToString();

                if (dtSL_Details.Rows[x]["Serial"] == DBNull.Value
                    || dtSL_Details.Rows[x]["Serial"].ToString().Trim() == string.Empty)
                    detail.Serial = null;
                else
                    detail.Serial = dtSL_Details.Rows[x]["Serial"].ToString();

                if (dtSL_Details.Rows[x]["Serial2"] == DBNull.Value
                    || dtSL_Details.Rows[x]["Serial2"].ToString().Trim() == string.Empty)
                    detail.Serial2 = null;
                else
                    detail.Serial2 = dtSL_Details.Rows[x]["Serial2"].ToString();
                #endregion

                detail.SellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]);
                detail.SalesTax = Convert.ToDecimal(dtSL_Details.Rows[x]["SalesTax"]);
                //CustomTaxRatio
                detail.CustomTaxRatio = Convert.ToDecimal(dtSL_Details.Rows[x]["CustomTaxRatio"]);
                detail.CustomTax = Convert.ToDecimal(dtSL_Details.Rows[x]["CustomTax"]);
                detail.SalesTaxRatio = Math.Round(Convert.ToDecimal(dtSL_Details.Rows[x]["SalesTaxRatio"]), defaultRoundingPoints);

                detail.DiscountValue = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountValue"]);
                detail.DiscountRatio = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio"]);
                detail.DiscountRatio2 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio2"]);
                detail.DiscountRatio3 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio3"]);
                detail.TotalSellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["TotalSellPrice"]);
                detail.ItemDescription = dtSL_Details.Rows[x]["ItemDescription"].ToString();
                detail.ItemDescriptionEn = dtSL_Details.Rows[x]["ItemDescriptionEn"].ToString();
                detail.QC = dtSL_Details.Rows[x]["QC"].ToString();

                detail.bonusDiscount = Convert.ToDecimal(dtSL_Details.Rows[x]["bonusDiscount"]);

                if (dtSL_Details.Rows[x]["MediumUOMFactor"].ToString() != string.Empty)
                    MediumUOMFactor = MyHelper.FractionToDouble(dtSL_Details.Rows[x]["MediumUOMFactor"].ToString());
                if (dtSL_Details.Rows[x]["LargeUOMFactor"].ToString() != string.Empty)
                    LargeUOMFactor = MyHelper.FractionToDouble(dtSL_Details.Rows[x]["LargeUOMFactor"].ToString());
                //if()// تسجيل المخزن على مستوى الصنف

                if (dtSL_Details.Rows[x]["ManufactureDate"] != null && dtSL_Details.Rows[x]["ManufactureDate"] != DBNull.Value)
                    detail.ManufactureDate = Convert.ToDateTime(dtSL_Details.Rows[x]["ManufactureDate"]);


                if (Shared.LibraAvailabe)
                {
                    if (dtSL_Details.Rows[x]["LibraQty"] != null && dtSL_Details.Rows[x]["LibraQty"] != DBNull.Value && dtSL_Details.Rows[x]["LibraQty"].ToString() != string.Empty)
                        detail.LibraQty = Convert.ToDecimal(dtSL_Details.Rows[x]["LibraQty"]);

                    if (dtSL_Details.Rows[x]["kg_Weight_libra"] != null && dtSL_Details.Rows[x]["kg_Weight_libra"] != DBNull.Value & dtSL_Details.Rows[x]["kg_Weight_libra"].ToString() != string.Empty)
                        detail.kg_Weight_libra = Convert.ToDecimal(dtSL_Details.Rows[x]["kg_Weight_libra"]);
                }

                detail.Pack = dtSL_Details.Rows[x]["Pack"] == null || dtSL_Details.Rows[x]["Pack"] == DBNull.Value ? 0 : Convert.ToInt32(dtSL_Details.Rows[x]["Pack"]);

                if (dtSL_Details.Rows[x]["AudiencePrice"] != DBNull.Value && dtSL_Details.Rows[x]["AudiencePrice"] != null)
                    detail.AudiencePrice = Convert.ToDecimal(dtSL_Details.Rows[x]["AudiencePrice"]);
                else
                    detail.AudiencePrice = 0;
                #endregion

                DB.SL_InvoiceDetails.InsertOnSubmit(detail);
                var subTaxes = DB.SL_InvoiceDetailSubTaxValues.Where(sub => sl.SL_InvoiceDetails.Select(d => d.SL_InvoiceDetailId).Contains(sub.InvoiceDetailId));
                if (subTaxes.Count() > 0)
                    DB.SL_InvoiceDetailSubTaxValues.DeleteAllOnSubmit(subTaxes);

                if ((chk_Approved.Checked == true || (Shared.user.MaxSalesValue > 0 && sl.Net < Shared.user.MaxSalesValue) || Shared.user.MaxSalesValue == null || Shared.user.MaxSalesValue == 0))
                    /*Subtract from strore*/
                    if (Shared.InvoicePostToStore == true && sl.Is_Posted)
                    {

                        if (sl.IsArchived == null)         //Autopost Mood
                        {
                            if (Shared.InvoicePostToStore == true || Shared.StockIsPeriodic == false)
                            {
                                int itemType = Convert.ToInt32(dtSL_Details.Rows[x]["ItemType"]);
                                if (itemType != (int)ItemType.Subtotal)
                                {

                                    if (dtSL_Details.Rows[x]["MediumUOMFactor"].ToString() != string.Empty)
                                        MediumUOMFactor = MyHelper.FractionToDouble(dtSL_Details.Rows[x]["MediumUOMFactor"].ToString());
                                    if (dtSL_Details.Rows[x]["LargeUOMFactor"].ToString() != string.Empty)
                                        LargeUOMFactor = MyHelper.FractionToDouble(dtSL_Details.Rows[x]["LargeUOMFactor"].ToString());

                                    int? ParentItemId = ErpUtils.GetMtrxVal(dtSL_Details.Rows[x]["ParentItemId"]);
                                    int? M1 = ErpUtils.GetMtrxVal(dtSL_Details.Rows[x]["M1"]);
                                    int? M2 = ErpUtils.GetMtrxVal(dtSL_Details.Rows[x]["M2"]);
                                    int? M3 = ErpUtils.GetMtrxVal(dtSL_Details.Rows[x]["M3"]);

                                    bool isoffer = false;
                                    var _isOffer = dtSL_Details.Rows[x]["IsOffer"];
                                    if (_isOffer != DBNull.Value && _isOffer != null)
                                        isoffer = Convert.ToBoolean(_isOffer);

                                    {
                                        lst_outitems.Add(new StoreItem(Shared.st_Store.MultiplyDimensions, detail.ItemId, itemType, detail.SL_InvoiceDetailId, detail.UOMIndex, detail.Qty,
                                            MediumUOMFactor, LargeUOMFactor, detail.Expire, detail.Batch, null, null, detail.Length.Value, detail.Width.Value, detail.Height.Value,
                                            detail.PiecesCount, ParentItemId, M1, M2, M3, detail.TotalSellPrice * sl.CrncRate, 0, detail, detail.Serial, detail.Serial2,
                                            Convert.ToInt32(dtSL_Details.Rows[x]["CategoryId"]), detail.StoreId, detail.Pack));
                                    }
                                }
                            }
                        }



                    }
                DB.SubmitChanges();
                #region Save Taxes
                if (Dt_Rows.Rows.Count > 0)
                {

                    foreach (DataRow dd in Dt_Rows.Rows)
                    {
                        if (dd.RowState == DataRowState.Deleted)
                            continue;
                        if (Convert.ToInt32(dd["RowHandle"]) == Convert.ToInt32(dtSL_Details.Rows[x]["RowHandle"]))
                        {
                            SL_InvoiceDetailSubTaxValue dTax = new SL_InvoiceDetailSubTaxValue();
                            dTax.esubTypeId = Convert.ToInt32(dd["SubTax"]);
                            dTax.TaxRatio = Convert.ToDecimal(dd["Percentage"]);
                            dTax.InvoiceDetailId = detail.SL_InvoiceDetailId;

                            dTax.value = Convert.ToDecimal(dtSL_Details.Rows[x]["TaxValue"]);
                            DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                        }
                    }
                }
                #endregion
                DB.SubmitChanges();
                //if (Shared.st_Store.E_AllowMoreThanTax == false || Shared.st_Store.E_AllowMoreThanTax == null)
                //{
                //    if (dtSL_Details.Rows[x]["TaxType"] != null && dtSL_Details.Rows[x]["TaxType"] != DBNull.Value)
                //    {
                //        DB.SubmitChanges();
                //        SL_InvoiceDetailSubTaxValue dTax = new SL_InvoiceDetailSubTaxValue();
                //        dTax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                //        dTax.esubTypeId = Convert.ToInt32(dtSL_Details.Rows[x]["TaxType"]);
                //        dTax.TaxRatio = Convert.ToDecimal(dtSL_Details.Rows[x]["ETaxRatio"]);
                //        dTax.value = Convert.ToDecimal(dtSL_Details.Rows[x]["EtaxValue"]);
                //        DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //    }
                //}

                //else
                //{
                //    if (Dt_Rows.Rows.Count > 0)
                //    {

                //        decimal totalTableTaxRatio = 0;
                //        var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId.ToString()).ToList();

                //        var tableTaxes = Dt_Rows.AsEnumerable()
                //            .Where(a => a.RowState != DataRowState.Deleted)
                //            .Where(r => tableTaxIds.Contains(r["Tax"].ToString())).ToList();

                //        var otherTaxes = Dt_Rows.AsEnumerable()
                //            .Where(a => a.RowState != DataRowState.Deleted)
                //            .Where(r => !tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1").ToList();
                //        //=================Case 1 Table Taxes===================//
                //        foreach (DataRow row in tableTaxes)
                //        {
                //            if (Convert.ToInt32(row["RowHandle"]) == Convert.ToInt32(dtSL_Details.Rows[x]["RowHandle"]))
                //            {
                //                SL_InvoiceDetailSubTaxValue dTax = new SL_InvoiceDetailSubTaxValue();
                //                decimal DiscR1 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio"]);
                //                decimal DiscR2 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio2"]);
                //                decimal DiscR3 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio3"]);

                //                decimal TotalSellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                //                bool calcTaxBeforeDisc = Convert.ToBoolean(dtSL_Details.Rows[x]["calcTaxBeforeDisc"]);

                //                decimal taxvalue = calcTaxBeforeDisc ?
                //                    Math.Round(Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]) * Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]) * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints)
                //                    : Math.Round(TotalSellPrice * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints);
                //                dTax.esubTypeId = Convert.ToInt32(row["SubTax"]);
                //                dTax.TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
                //                dTax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                //                dTax.value = taxvalue;
                //                totalTableTaxRatio += Convert.ToDecimal(row["Percentage"]);
                //                DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //            }
                //        }
                //        //=================Case 2 Add Tax===================//
                //        foreach (DataRow mm in Dt_Rows.AsEnumerable()
                //            .Where(a => a.RowState != DataRowState.Deleted)
                //            .Where(r => r["Tax"].ToString() == "1").ToList())
                //        {

                //            if (Convert.ToInt32(mm["RowHandle"]) == Convert.ToInt32(dtSL_Details.Rows[x]["RowHandle"]))
                //            {
                //                SL_InvoiceDetailSubTaxValue dTax = new SL_InvoiceDetailSubTaxValue();
                //                decimal DiscR1 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio"]);
                //                decimal DiscR2 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio2"]);
                //                decimal DiscR3 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio3"]);

                //                decimal TotalSellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                //                var totalSellPriceWithTableTaxes = Convert.ToDecimal((totalTableTaxRatio / 100)) * TotalSellPrice;
                //                TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;
                //                bool calcTaxBeforeDisc = Convert.ToBoolean(dtSL_Details.Rows[x]["calcTaxBeforeDisc"]);

                //                decimal taxvalue = calcTaxBeforeDisc ?
                //                    Math.Round(Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]) * Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]) * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints)
                //                    : Math.Round(TotalSellPrice * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints);
                //                dTax.esubTypeId = Convert.ToInt32(mm["SubTax"]);
                //                dTax.TaxRatio = Math.Round(Convert.ToDecimal(mm["Percentage"]), defaultRoundingPoints);
                //                dTax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                //                dTax.value = taxvalue;
                //                DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //            }
                //        }
                //        //==============Case 3  Other Taxes=================//
                //        foreach (DataRow m in otherTaxes)
                //        {
                //            if (Convert.ToInt32(m["RowHandle"]) == Convert.ToInt32(dtSL_Details.Rows[x]["RowHandle"]))
                //            {
                //                SL_InvoiceDetailSubTaxValue dTax = new SL_InvoiceDetailSubTaxValue();
                //                decimal DiscR1 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio"]);
                //                decimal DiscR2 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio2"]);
                //                decimal DiscR3 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio3"]);

                //                decimal TotalSellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                //                bool calcTaxBeforeDisc = Convert.ToBoolean(dtSL_Details.Rows[x]["calcTaxBeforeDisc"]);

                //                decimal taxvalue = calcTaxBeforeDisc ?
                //                    Math.Round(Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]) * Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]) * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints)
                //                    : Math.Round(TotalSellPrice * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints);
                //                dTax.esubTypeId = Convert.ToInt32(m["SubTax"]);
                //                dTax.TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
                //                dTax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                //                dTax.value = taxvalue;
                //                DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //            }
                //        }

                //        //foreach (DataRow dd in Dt_Rows.Rows)
                //        //{

                //        //    if (Convert.ToInt32(dd["RowHandle"]) == Convert.ToInt32(dtSL_Details.Rows[x]["RowHandle"]))
                //        //    {
                //        //        SL_InvoiceDetailSubTaxValue dTax = new SL_InvoiceDetailSubTaxValue();
                //        //        decimal DiscR1 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio"]);
                //        //        decimal DiscR2 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio2"]);
                //        //        decimal DiscR3 = Convert.ToDecimal(dtSL_Details.Rows[x]["DiscountRatio3"]);

                //        //        decimal TotalSellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                //        //        bool calcTaxBeforeDisc = Convert.ToBoolean(dtSL_Details.Rows[x]["calcTaxBeforeDisc"]);

                //        //        decimal taxvalue = calcTaxBeforeDisc ?
                //        //            Math.Round(Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]) * Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dd["Percentage"]) / 100, defaultRoundingPoints)
                //        //            : Math.Round(TotalSellPrice * Convert.ToDecimal(dd["Percentage"]) / 100, defaultRoundingPoints);
                //        //        dTax.esubTypeId = Convert.ToInt32(dd["SubTax"]);
                //        //        dTax.TaxRatio = Convert.ToDecimal(dd["Percentage"]);
                //        //        dTax.InvoiceDetailId = detail.SL_InvoiceDetailId;

                //        //        dTax.value = taxvalue;
                //        //        DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //        //    }
                //        //}
                //    }
                //}


            }
            DB.SubmitChanges();
            if ((chk_Approved.Checked == true || (Shared.user.MaxSalesValue > 0 && sl.Net < Shared.user.MaxSalesValue) || Shared.user.MaxSalesValue == 0 || Shared.user.MaxSalesValue == null))
                if (Shared.InvoicePostToStore == true && sl.Is_Posted && (Shared.st_Store.SlInvoice_mustB_Approved != true ||
                (Shared.st_Store.SlInvoice_mustB_Approved == true && sl.Approved_Invoice == true)))
                {
                    lst_outitems.ForEach(x => x.SourceId = ((SL_InvoiceDetail)x.Source).SL_InvoiceDetailId);
                    //lst = MyHelper.Subtract_from_store(sl.StoreId, costmethod, (int)Process.SellInvoice, sl.InvoiceDate, lst_outitems);
                    //CostOfSoldGoods = lst.Select(x => x.PurchasePrice).ToList().DefaultIfEmpty(0).Sum();
                    lst_outitems.ForEach(x => ((SL_InvoiceDetail)x.Source).CostPrice = x.TotalCost);

                    sl.TotalCostPrice = CostOfSoldGoods;

                    //if (Shared.InvoicePostToStore)
                    //    DB.IC_ItemStores.InsertAllOnSubmit(lst);

                    DB.SubmitChanges();
                }

            try
            {
                DB.SubmitChanges();
            }
            catch (Exception c)
            {
                Utilities.save_Log(c.Message, c);
                MessageBox.Show(c.Message);
            }




            invoiceId = sl.SL_InvoiceId;

            //if (sl.IsArchived == null /*Auto Post Mood*/ && (Shared.st_Store.SlInvoice_mustB_Approved != true ||
            //(Shared.st_Store.SlInvoice_mustB_Approved == true && sl.Approved_Invoice == true)))
            //    CreateJournal(DB, sl, CostOfSoldGoods,
            //        string.Empty,
            //        lst_Cat, lst_outitems, lst);

            DataModified = false;
            dtSL_Details.AcceptChanges();
        }

        public void Calculate_Qty(int itmId, CellValueChangedEventArgs e, int store_id, byte uomIndex, decimal medium, decimal large, string batch)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            DataRow row = view.GetDataRow(e.RowHandle);
            //if (Shared.user.Sell_ShowCrntQty == true)
            //{
            //    decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, itmId, store_id, SlOrdrId, batch);
            //    currentQty = MyHelper.getCalculatedUomQty(currentQty, uomIndex, medium, large);

            //    view.GetDataRow(e.RowHandle)["CurrentQty"] = decimal.ToDouble(currentQty);
            //}
            //else
            {
                view.GetDataRow(e.RowHandle)["CurrentQty"] = decimal.ToDouble(0);
            }
        }

        void LoadPrivilege()
        {
            DB = new ERPDataContext();
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    btnAddCustomer.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() < 1)
                {
                    //btn_add_Item.Enabled = false;
                    mi_frm_IC_Item.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.frm_JO_JobOrder).Count() < 1)
                {
                    barBtnLoad_JO.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_SalesOrder).Count() < 1)
                {
                    barBtnLoad_SalesOrder.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Quote).Count() < 1)
                {
                    barBtnLoad_Sl_Qoute.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.IC_OutTrns).Count() < 1)
                {
                    barBtn_OutTrns.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.ACC_RecieveNote).Count() < 1)
                    barBtnNotesReceivable.Enabled = false;
                else
                {
                    if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.ACC_RecieveNote).FirstOrDefault().CanAdd == false)
                        barBtnNotesReceivable.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.CashIn).Count() < 1)
                    barBtn_CashNote.Enabled = false;

                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();

                if (!prvlg.CanDel)
                {

                    if ((Shared.st_Store.ch_Authorize.HasValue ? Shared.st_Store.ch_Authorize.Value : false)
                        && (HrHelper.CheckPermission(FormAction.Delete, (int)FormsNames.SL_Invoice, invoiceId)))
                    {
                        barBtnCancel.Enabled = true;
                    }
                    else
                    {
                        barBtnCancel.Enabled = prvlg.CanDel;
                    }
                }
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
                if (!prvlg.CanPrint)
                {
                    if ((Shared.st_Store.ch_Authorize.HasValue ? Shared.st_Store.ch_Authorize.Value : false)
                        && (HrHelper.CheckPermission(FormAction.Print, (int)FormsNames.SL_Invoice, invoiceId)))
                    {
                        barbtnPrint.Enabled = true;
                        barbtnPrintF.Enabled = true;
                        barSubItemPrint.Enabled = true;
                    }
                    else
                    {
                        barbtnPrint.Enabled = false;
                        barbtnPrintF.Enabled = false;
                        barSubItemPrint.Enabled = false;
                    }
                }
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.AccStatement).FirstOrDefault();
                if (p == null)
                    btn_ShowAccStatement.Visible = false;
            }
        }

        private void lkp_Customers_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        DialogResult ChangesMade()
        {
            if (barBtnSave.Enabled == false)
                return DialogResult.No;
            if (
                DataModified ||
                dtSL_Details.GetChanges(DataRowState.Added) != null ||
                dtSL_Details.GetChanges(DataRowState.Modified) != null ||
                dtSL_Details.GetChanges(DataRowState.Deleted) != null
                )
            {
                DialogResult r = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDataModified : ResSLAr.MsgDataModified, "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    barBtnSave.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous                    
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        private bool ValidData()
        {
            DB = new ERPDataContext();

            //can't post in closed period
            if (ErpHelper.CanSaveInClsedPeriod(dtInvoiceDate.DateTime.Date, Shared.st_Store.ClosePeriodDate, Shared.user.EditInClosedPeriod) == false)
                return false;

            ((GridView)grdPrInvoice.FocusedView).FocusedRowHandle += 1;
            if (invoiceId == 0)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    // "عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvNew : ResSLAr.MsgPrvNew, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (invoiceId > 0)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {

                    if ((Shared.st_Store.ch_Authorize == true) && !prvlg.CanEdit)
                    {
                        if (!(HrHelper.CheckPermission(FormAction.Edit, (int)FormsNames.SL_Invoice, invoiceId)))
                        {
                            //"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvEdit : ResSLAr.MsgPrvEdit, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return false;
                        }
                    }
                    else if (!prvlg.CanEdit)
                    {
                        //"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvEdit : ResSLAr.MsgPrvEdit, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return false;
                    }
                }
                if (ErpHelper.CanEditPostedBill(invoiceId, (int)Process.SellInvoice, Shared.OfflinePostToGL,
                    Shared.user.UserEditPostedBills) == false)
                    return false;
            }


            if (Shared.st_Store.ch_Authorize == true)
            {
                HrHelper.UsePermission(FormAction.Edit, (int)FormsNames.SL_Invoice, invoiceId);
                LoadPrivilege();
            }

            if (lkp_Customers.EditValue == null)
            {
                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgSelectCustomer,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_Customers.Focus();

                return false;
            }

            var ccc = lkp_Customers.GetSelectedDataRow();

            if (lkp_Customers.GetSelectedDataRow() != null &&
                ((SL_Customer_Info)lkp_Customers.GetSelectedDataRow()).Is_Blocked == true)
            {
                XtraMessageBox.Show(
                       Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgBlockedCustomer,
                       Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                       MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_Customers.Focus();

                return false;
            }

            if (Shared.StockIsPeriodic && Convert.ToDecimal(txtDiscountValue.EditValue) > 0 && Shared.st_Store.SalesDiscountAcc.HasValue == false)
            {
                //check sales discount Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgDiscountAcc : ResAr.MsgDiscountAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }

            if (Convert.ToDecimal(txt_TaxValue.EditValue) > 0 && Shared.st_Store.TaxAcc.HasValue == false)
            {
                //يجب تحديد حساب الضرائب
                //check sales tax Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSalesTaxAcc : ResAr.MsgSalesTaxAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }
            if (Convert.ToDecimal(txt_CusTaxV.EditValue) > 0 && Shared.st_Store.CustomTaxAcc.HasValue == false)
            {
                //يجب تحديد حساب الجدول
                //check Custom tax Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgCusTaxAcc : ResAr.MsgCusTaxAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }
            if (Shared.RealEstateAvailable)
            {
                if (Convert.ToDecimal(txt_RetentionV.EditValue) > 0 && !Shared.st_Store.RetentionAcc.HasValue)
                {
                    MessageBox.Show(Shared.IsEnglish ? "Please Select Retention Account from Setting" : "برجاء اختيار حساب الاحتفاظ من الاعدادات", Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
                if (Convert.ToDecimal(txt_AdvancePayV.EditValue) > 0 && !Shared.st_Store.AdvancePaymentAcc.HasValue)
                {
                    MessageBox.Show(Shared.IsEnglish ? "Please Select Advance Payment Account from Setting" : "برجاء اختيار حساب الدفعات المقدمة من الاعدادات", Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

            }
            if (string.IsNullOrEmpty(txtInvoiceCode.Text.Trim()))
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateInvNumber : ResSLAr.txtValidateInvNumber, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                txtInvoiceCode.Focus();
                return false;
            }

            if (Shared.st_Store.InvoicesCodeRedundancy == false)
            {
                bool code_exist = InvCodeExist();
                if (invoiceId == 0 && code_exist && Shared.st_Store.GenerateNewInvCodeOnSave == true)
                {
                    lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);
                    try { return ValidData(); }
                    catch
                    //(OutOfMemoryException memory)
                    //{ MessageBox.Show(memory.Message); }
                    //finally
                    { MessageBox.Show(Shared.IsEnglish ? "Kindly enter invoice code manually" : "عفوا، يصعب إنشاء كود للفاتورة، يرجى إدخال كود الفاتورة يدويا"); }
                }
                if (ErpHelper.ValidateInvCodeExist(code_exist, txtInvoiceCode) == false)
                    return false;
            }

            if (Shared.SalesManAvailable && Shared.st_Store.SalesEmpMandatory && lkp_SalesEmp.EditValue == null)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.SalesEmpMandatory : ResSLAr.SalesEmpMandatory,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_SalesEmp.Focus();
                return false;
            }

            /*منع*/
            if (Shared.user.SellCustomerOverCredit == false && (cust_IsDebit == true
                        && (Convert.ToDecimal(txt_MaxCredit.Text) > 0) &&
                        (Convert.ToDecimal(txt_Balance_After.Text) > Convert.ToDecimal(txt_MaxCredit.Text))))
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebit : ResSLAr.txtValidateCustomerMaxDebit,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_Remains.Focus();
                return false;
            }
            /*تحذير*/
            else if (Shared.user.SellCustomerOverCredit == true && (cust_IsDebit == true
                        && (Convert.ToDecimal(txt_MaxCredit.Text) > 0) &&
                        (Convert.ToDecimal(txt_Balance_After.Text) > Convert.ToDecimal(txt_MaxCredit.Text))))
            {
                DialogResult dr = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebitWarn : ResSLAr.txtValidateCustomerMaxDebitWarn,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (dr == DialogResult.No)
                {
                    txt_Remains.Focus();
                    return false;
                }
            }

            //warn group max credit
            /*منع*/
            if (invoiceId == 0)
            {
                if (lkp_Customers.EditValue == null || lkp_Customers.Text == "")
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgSelectCustomer,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    lkp_Customers.Focus();

                    return false;
                }
                SL_Customer_Info custInfo = lst_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).Select(x => x).First();

                if (Shared.user.SellCustomerOverCredit == false)
                {
                    if (custInfo.GrpId != null && custInfo.GroupMaxCredit > 0 &&
                            (Convert.ToDecimal(txt_Remains.Text) + (HelperAcc.Get_account_balance(custInfo.GroupAccountId.Value) * -1) > custInfo.GroupMaxCredit))
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustGrpMaxDebit : ResSLAr.txtValidateCustGrpMaxDebit,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txt_Remains.Focus();
                        return false;
                    }
                }
                /*تحذير*/
                else if (Shared.user.SellCustomerOverCredit == true)
                {
                    if (custInfo.GrpId != null && custInfo.GroupMaxCredit > 0 &&
                            (Convert.ToDecimal(txt_Remains.Text) + (HelperAcc.Get_account_balance(custInfo.GroupAccountId.Value) * -1) > custInfo.GroupMaxCredit))
                    {
                        DialogResult dr = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtValidateCustGrpMaxDebitWarn : ResSLAr.txtValidateCustGrpMaxDebitWarn,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                        if (dr == DialogResult.No)
                        {
                            txt_Remains.Focus();
                            return false;
                        }
                    }
                }
            }

            if (Shared.user.CanSave_SL_WithOldDate == false && dtInvoiceDate.DateTime.Date < MyHelper.Get_Server_DateTime().Date)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? "Invoice Date can't be less than todays's date" : "تاريخ الفاتورة لا يمكن أن يكون أقل من تاريخ اليوم، يرجى مراجعة مدير النظام");
                dtInvoiceDate.Focus();
                return false;
            }

            if (Shared.user.CanSave_SL_WithUpcomingDate == false && dtInvoiceDate.DateTime.Date > MyHelper.Get_Server_DateTime().Date)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? "Invoice Date can't be in a future date" : "تاريخ الفاتورة لا يمكن أن يكون أكبر من تاريخ اليوم، يرجى مراجعة مدير النظام");
                dtInvoiceDate.Focus();
                return false;
            }
            int deleted_rows = 0;
            if (dtSL_Details.GetChanges(DataRowState.Deleted) != null)
                deleted_rows = dtSL_Details.GetChanges(DataRowState.Deleted).Rows.Count;

            if (dtSL_Details.Rows.Count - deleted_rows <= 0)
            {
                //يجب تسجيل صنف علي الاقل في الفاتوره
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateNoRows : ResSLAr.txtValidateNoRows, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                grdPrInvoice.Focus();
                return false;
            }

            if (dtSL_Details.Rows[dtSL_Details.Rows.Count - 1].RowState != DataRowState.Deleted
                && dtSL_Details.Rows[dtSL_Details.Rows.Count - 1]["ItemId"] == DBNull.Value)
            {
                dtSL_Details.Rows[dtSL_Details.Rows.Count - 1].Delete();
                grdPrInvoice.RefreshDataSource();
            }

            //Validate user can make on credit invoice
            if (Shared.user.UserMakeOnCreditInv == false)
            {
                if (Convert.ToDecimal(txt_Remains.EditValue) > 0)
                {
                    XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateOnCreditInv : ResSLAr.txtValidateOnCreditInv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    txt_PayAcc1_Paid.Focus();
                    return false;
                }
            }
            if (!grdPrInvoice.DefaultView.UpdateCurrentRow())
                return false;

            string msg = "";

            if (Shared.user.SellWithNoBalance != null && Shared.InvoicePostToStore)     //عدم التدخل                
            {
                if (MyHelper.Validate_detailsQty(Shared.st_Store.MultiplyDimensions, dtInvoiceDate.DateTime, Convert.ToInt32(lkpStore.EditValue), dtSL_Details, out msg))
                    return true;
                else
                {
                    if (Shared.user.SellWithNoBalance == false)     //منع
                    {
                        XtraMessageBox.Show(this.LookAndFeel, msg,
                            Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                        return false;
                    }
                    else if (Shared.user.SellWithNoBalance == true)
                    {
                        if (XtraMessageBox.Show(this.LookAndFeel, msg + "\r\n" + (Shared.IsEnglish ? ResEn.MsgContinue : ResAr.MsgContinue),
                            Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Exclamation) != DialogResult.Yes)
                            return false;
                    }
                }
            }

            if (chk_IsPosted.Checked == true && txt_Post_Date.EditValue == null)
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResPrEn.txtValidatePostDate : ResPrAr.txtValidatePostDate, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                txt_Post_Date.Focus();
                return false;
            }



            if (Shared.st_Store.InvoiceWorkflow == (int)InvoiceWorkflow.BillThenInv)
            {
                XtraMessageBox.Show(this.LookAndFeel, (Shared.IsEnglish ? ResEn.SelectBill : ResAr.SelectBill),
                    Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                return false;
            }

            if (Shared.LibraAvailabe)
            {
                if (Shared.st_Store.LaborRevenue == null)
                {
                    MessageBox.Show(Shared.IsEnglish ? "Please select Labor Revenue account from setting" : "يرجى اختيار حساب ايراد العمال من اعدادات الحسابات", "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                if (Shared.st_Store.TransferRevenue == null)
                {
                    MessageBox.Show(Shared.IsEnglish ? "Please select Transfer Revenue account from setting" : "يرجى اختيار حساب ايراد النقل من إعدادات الحسابات", "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

                var comply = from d in dtSL_Details.Select()
                             where (d["VariableWeight"].ToString() == true.ToString() ||
                             d["PricingWithSmall"].ToString() == true.ToString() ||
                             d["IsOffer"].ToString() == true.ToString() ||
                             d["Is_Libra"].ToString() == true.ToString())
                            && d["kg_Weight_libra"].ToString() == ""
                             select d;
                if (comply.FirstOrDefault() != null)
                {
                    MessageBox.Show(Shared.IsEnglish ? "Make sure to enter the Weight (kg) for required items" :
                        "تأكد من إدخال الوزن بالكيلو لكل الأصناف اللازمة", Shared.IsEnglish ? "Warning" : "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            return true;
        }

        private void lkpStore_EditValueChanged(object sender, EventArgs e)
        {
            bool containsChar = true;

            try
            {
                containsChar = (from x in DB.SL_Invoices
                                select Convert.ToInt32(x.InvoiceCode.Trim())).Max() != 0;
                containsChar = false;
            }
            catch
            {
                containsChar = true;
            }
            if ((lkp_InvoiceBook.EditValue == null || Convert.ToInt32(lkp_InvoiceBook.EditValue) == 0) && invoiceId < 1)
            {
                #region GetNextInvNumber
                string lastNumber;
                //check saving store in system to define store_id
                //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)

                if (containsChar == false)
                {
                    if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                        lastNumber = (from x in DB.SL_Invoices
                                      where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                      where x.InvoiceBookId == null
                                      orderby Convert.ToInt32(x.InvoiceCode) descending
                                      select x.InvoiceCode).FirstOrDefault();
                    else
                        lastNumber = (from x in DB.SL_Invoices
                                      join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                      where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                      where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                                      s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                                      where x.InvoiceBookId == null
                                      orderby Convert.ToInt32(x.InvoiceCode) descending
                                      select x.InvoiceCode).FirstOrDefault();
                }

                else
                {
                    if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)
                        lastNumber = (from x in DB.SL_Invoices
                                      where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                      where x.InvoiceBookId == null
                                      orderby x.InvoiceDate descending
                                      orderby x.SL_InvoiceId descending
                                      select x.InvoiceCode).FirstOrDefault();
                    else
                        lastNumber = (from x in DB.SL_Invoices
                                      join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                      where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                      where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                                      s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                                      where x.InvoiceBookId == null
                                      orderby x.InvoiceDate descending
                                      orderby x.SL_InvoiceId descending
                                      select x.InvoiceCode).FirstOrDefault();
                }
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);
                #endregion
            }
            else if (lkp_InvoiceBook.EditValue != null && Convert.ToInt32(lkp_InvoiceBook.EditValue) != 0 && invoiceId < 1)
            {
                string lastNumber;
                if (containsChar)
                {
                    #region GetNextInvNumber
                    lastNumber = (from x in DB.SL_Invoices
                                  where x.InvoiceBookId == Convert.ToInt32(lkp_InvoiceBook.EditValue)
                                  orderby x.InvoiceDate descending
                                  orderby x.SL_InvoiceId descending
                                  select x.InvoiceCode).FirstOrDefault();
                }
                #endregion}
                else
                {
                    lastNumber = (from x in DB.SL_Invoices
                                  where x.InvoiceBookId == Convert.ToInt32(lkp_InvoiceBook.EditValue)
                                  orderby Convert.ToInt32(x.InvoiceCode) descending
                                  select x.InvoiceCode).FirstOrDefault();
                }
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);


                IsTaxable = (bool?)lkp_InvoiceBook.GetColumnValue("IsTaxable");
            }

            if (lkpStore.EditValue != null && Convert.ToInt32(lkpStore.EditValue) != 0)
            {
                //lkpCostCenter.EditValue = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostCenter).FirstOrDefault();
                var StoreCC = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostCenter).FirstOrDefault();
                if (StoreCC != null)
                {
                    if (invoiceId <= 0)
                    {
                        dt_Multi_CC.Rows.Clear();
                        dt_Multi_CC.Rows.Add(StoreCC);
                    }
                }
                else if (invoiceId <= 0)
                {
                    dt_Multi_CC.Rows.Clear();
                }
            }
        }

        private void GetNextCode()
        {
            if ((lkp_InvoiceBook.EditValue == null || Convert.ToInt32(lkp_InvoiceBook.EditValue) == 0) && invoiceId < 1)
            {
                #region GetNextInvNumber
                string lastNumber;
                //check saving store in system to define store_id
                //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)
                if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == false).Count() == 0)

                    lastNumber = (from x in DB.SL_Invoices
                                  where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                  where x.InvoiceBookId == null
                                  orderby x.InvoiceDate descending
                                  orderby x.SL_InvoiceId descending
                                  select x.InvoiceCode).FirstOrDefault();
                else
                    lastNumber = (from x in DB.SL_Invoices
                                  join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                  where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                  where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                                  s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                                  where x.InvoiceBookId == null
                                  orderby x.InvoiceDate descending
                                  orderby x.SL_InvoiceId descending
                                  select x.InvoiceCode).FirstOrDefault();
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);
                #endregion
            }
            else if (lkp_InvoiceBook.EditValue != null && Convert.ToInt32(lkp_InvoiceBook.EditValue) != 0 && invoiceId < 1)
            {
                #region GetNextInvNumber
                var lastNumber = (from x in DB.SL_Invoices
                                  where x.InvoiceBookId == Convert.ToInt32(lkp_InvoiceBook.EditValue)
                                  orderby x.InvoiceDate descending
                                  orderby x.SL_InvoiceId descending
                                  select x.InvoiceCode).FirstOrDefault();
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);
                #endregion
            }
        }
        private void frm_SL_Invoice_Shown(object sender, EventArgs e)
        {
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مبيعات جديدة");
        }

        void GetLastPrices(bool vendorInvoices, bool showLastPrices)
        {
            if (!showLastPrices)
            {
                Page_LastPrices.PageVisible = false;
                return;
            }

            GridView view = grdPrInvoice.FocusedView as GridView;
            if (view.GetFocusedRowCellValue("ItemId") == null)
                return;

            int ItemId = 0;
            Int32.TryParse(view.GetFocusedRowCellValue("ItemId").ToString(), out ItemId);
            if (ItemId < 1)
            {
                return;
            }
            Page_LastPrices.PageVisible = true;
            xtraTabControl1.SelectedTabPage = Page_LastPrices;

            if (vendorInvoices)
            {
                Page_LastPrices.Text = Shared.IsEnglish ? ResSLEn.txtLastCustomerPPrices : ResSLAr.txtLastCustomerPPrices; //"اخر اسعار بيع لعميل";
                DAL.ERPDataContext DB = new DAL.ERPDataContext();
                var lastInvoices = (from p in DB.SL_Invoices
                                    join d in DB.SL_InvoiceDetails
                                    on p.SL_InvoiceId equals d.SL_InvoiceId
                                    where d.ItemId == ItemId
                                    where p.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)
                                    where Shared.user.UserChangeStore ? true : p.StoreId == Shared.user.DefaultStore
                                    select new
                                    {
                                        InvoiceCode = p.InvoiceCode,
                                        InvoiceDate = p.InvoiceDate,
                                        UOM = DB.IC_UOMs.Where(u => u.UOMId == d.UOMId).Select(u => u.UOM).FirstOrDefault(),
                                        Qty = decimal.ToDouble(d.Qty),
                                        SellPrice = decimal.ToDouble(d.SellPrice),
                                        TotalSellPrice = decimal.ToDouble(d.TotalSellPrice)
                                    })
                                    .Union
                                    (from p in DB.SL_InvoiceArchives
                                     join d in DB.SL_InvoiceDetailarchives
                                     on p.SL_InvoiceId equals d.SL_InvoiceArchiveId
                                     where d.ItemId == ItemId
                                     where p.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)
                                     where Shared.user.UserChangeStore ? true : p.StoreId == Shared.user.DefaultStore
                                     select new
                                     {
                                         InvoiceCode = p.InvoiceCode,
                                         InvoiceDate = p.InvoiceDate,
                                         UOM = DB.IC_UOMs.Where(u => u.UOMId == d.UOMId).Select(u => u.UOM).FirstOrDefault(),
                                         Qty = decimal.ToDouble(d.Qty),
                                         SellPrice = decimal.ToDouble(d.SellPrice),
                                         TotalSellPrice = decimal.ToDouble(d.TotalSellPrice)
                                     }).OrderByDescending(p => p.InvoiceDate).Take(10);
                colCustNameAr.Visible = false;
                grdLastPrices.DataSource = lastInvoices;
            }
            else
            {
                Page_LastPrices.Text = Shared.IsEnglish ? ResSLEn.txtLastPPrices : ResSLAr.txtLastPPrices; //"اخر اسعار بيع";
                var lastInvoices = (from p in DB.SL_Invoices
                                    join v in DB.SL_Customers
                                    on p.CustomerId equals v.CustomerId
                                    join d in DB.SL_InvoiceDetails
                                    on p.SL_InvoiceId equals d.SL_InvoiceId
                                    where d.ItemId == ItemId
                                    where Shared.user.UserChangeStore ? true : p.StoreId == Shared.user.DefaultStore
                                    select new
                                    {
                                        InvoiceCode = p.InvoiceCode,
                                        InvoiceDate = p.InvoiceDate,
                                        CusNameAr = v.CusNameAr,
                                        UOM = DB.IC_UOMs.Where(u => u.UOMId == d.UOMId).Select(u => u.UOM).FirstOrDefault(),
                                        Qty = decimal.ToDouble(d.Qty),
                                        SellPrice = decimal.ToDouble(d.SellPrice),
                                        TotalSellPrice = decimal.ToDouble(d.TotalSellPrice)
                                    })
                                    .Union
                                    (from p in DB.SL_InvoiceArchives
                                     join v in DB.SL_Customers
                                     on p.CustomerId equals v.CustomerId
                                     join d in DB.SL_InvoiceDetailarchives
                                     on p.SL_InvoiceId equals d.SL_InvoiceArchiveId
                                     where d.ItemId == ItemId
                                     where Shared.user.UserChangeStore ? true : p.StoreId == Shared.user.DefaultStore
                                     select new
                                     {
                                         InvoiceCode = p.InvoiceCode,
                                         InvoiceDate = p.InvoiceDate,
                                         CusNameAr = v.CusNameAr,
                                         UOM = DB.IC_UOMs.Where(u => u.UOMId == d.UOMId).Select(u => u.UOM).FirstOrDefault(),
                                         Qty = decimal.ToDouble(d.Qty),
                                         SellPrice = decimal.ToDouble(d.SellPrice),
                                         TotalSellPrice = decimal.ToDouble(d.TotalSellPrice)
                                     }).OrderByDescending(p => p.InvoiceDate).Take(10);
                colCustNameAr.VisibleIndex = 4;
                colCustNameAr.Visible = true;
                grdLastPrices.DataSource = lastInvoices;
            }
        }

        private void rep_expireDate_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            #region Expire
            if (e.Value == null || e.Value == DBNull.Value)
                return;
            try
            {
                DateTime date = Convert.ToDateTime(e.DisplayText);
                if (Shared.st_Store.ExpireDisplay == true)
                    e.DisplayText = date.Day + "-" + date.Month + "-" + date.Year;
                else
                    e.DisplayText = date.Month + "-" + date.Year;
            }
            catch
            { }

            #endregion
        }

        private void gridView5_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            #region Expire
            if (e.Column.FieldName == "Expire")
            {
                if (e.Value == null || e.Value == DBNull.Value)
                    return;
                try
                {
                    DateTime date = Convert.ToDateTime(e.Value);
                    if (Shared.st_Store.ExpireDisplay == true)
                        e.DisplayText = date.Day + "-" + date.Month + "-" + date.Year;
                    else
                        e.DisplayText = date.Month + "-" + date.Year;
                }
                catch
                { }
            }
            #endregion
        }

        private void lkp_Drawers2_EditValueChanged(object sender, EventArgs e)
        {
            if (lkp_Drawers2.EditValue != null && Convert.ToInt32(lkp_Drawers2.EditValue) == Convert.ToInt32(lkp_Drawers.EditValue))
                lkp_Drawers2.EditValue = null;

            if (lkp_Drawers2.EditValue == null)
            {
                txt_PayAcc2_Paid.EditValue = 0;
                txt_PayAcc2_Paid.Enabled = false;
            }
            else
            {
                txt_PayAcc2_Paid.Enabled = true;
            }
        }

        private void txt_DueDays_Leave(object sender, EventArgs e)
        {
            if (Convert.ToInt32(txt_DueDays.EditValue) < 0)
                txt_DueDays.EditValue = 0;

            txt_DueDate.EditValue = dtInvoiceDate.DateTime.AddDays(Convert.ToInt32(txt_DueDays.EditValue));
        }

        private void txt_DueDate_EditValueChanged(object sender, EventArgs e)
        {
            if (((DateEdit)sender).Name == "dtInvoiceDate")
            {
                if (invoiceId == 0)
                    txt_DueDate.EditValue = dtInvoiceDate.DateTime.AddDays(Convert.ToInt32(txt_DueDays.EditValue));
            }
            //else if (((DateEdit)sender).Name == "txt_DueDate")
            //{
            //    //Old 26-06-2018
            //    //txt_DueDays.EditValue = txt_DueDate.DateTime.Date.Subtract(dtInvoiceDate.DateTime.Date).Days;
            //    //New
            //    txt_DueDate.DateTime = dtInvoiceDate.DateTime.Date.AddDays(Convert.ToInt32(txt_DueDays.EditValue));

            //}

            //if (txt_DueDate.EditValue != null && txt_DueDate.DateTime.Date < dtInvoiceDate.DateTime.Date)
            //    txt_DueDate.EditValue = dtInvoiceDate.EditValue;

            //if (txt_DueDate.DateTime != DateTime.MinValue)
            //    txt_DueDays.EditValue = (txt_DueDate.DateTime - dtInvoiceDate.DateTime).Days;
        }

        private void btn_AddMatrixItems_Click(object sender, EventArgs e)
        {

            new frm_IC_MatrixAddInv().ShowDialog();

            foreach (var d in frm_IC_MatrixAddInv.lst_InvMatrixItems)
            {
                var item = DB.IC_Items.Where(x => x.ItemId == d.ItemId).FirstOrDefault();

                #region Can't Sell over current Qty
                decimal CurrentQty = 0;// MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.ItemId, Convert.ToInt32(lkpStore.EditValue), SlOrdrId);

                if (invoiceId == 0
                        && d.Qty > CurrentQty)//validate when new invoice only
                {
                    if (Shared.user.SellWithNoBalance.HasValue)
                        continue;
                }
                #endregion

                DataRow row = dtSL_Details.NewRow();
                row["ItemId"] = item.ItemId;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);
                row["SellPrice"] = Decimal.ToDouble(item.SmallUOMPrice);
                row["DiscountRatio"] = "0";
                row["DiscountValue"] = "0";

                if (IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable")))
                {
                    row["SalesTaxRatio"] = Math.Round(item.SalesTaxRatio / 100, defaultRoundingPoints);
                    row["CustomTaxRatio"] = Math.Round(item.CustomSalesTaxRatio / 100, defaultRoundingPoints);
                }
                else if (IsTaxable == true)
                {
                    row["SalesTaxRatio"] = Math.Round(item.CustomSalesTaxRatio / 100, defaultRoundingPoints);
                    row["CustomTaxRatio"] = Math.Round(item.CustomSalesTaxRatio / 100, defaultRoundingPoints);
                }
                else
                { row["SalesTaxRatio"] = 0; row["CustomTaxRatio"] = 0; }

                row["calcTaxBeforeDisc"] = item.calcTaxBeforeDisc;

                row["Qty"] = decimal.ToDouble(d.Qty);

                decimal salestaxratio = Convert.ToDecimal(row["SalesTaxRatio"]);
                decimal CustomTaxRatio = Convert.ToDecimal(row["CustomTaxRatio"]);
                decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);
                decimal TotalSellPrice = d.Qty * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                if (Shared.st_Store.PriceIncludeSalesTax)    /*السعر شامل الضريبة*/
                {
                    decimal temp = item.calcTaxBeforeDisc ? (salestaxratio * d.Qty * Convert.ToDecimal(row["SellPrice"])) / (1 + salestaxratio) :
                        (salestaxratio * TotalSellPrice) / (1 + salestaxratio);
                    row["SalesTax"] = decimal.ToDouble(temp);
                    row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice - temp);// السعر الاجمالي شامل الضريبة      

                    var totalsellp = (TotalSellPrice - temp);
                    //custom
                    decimal temp2 = item.calcTaxBeforeDisc ? (CustomTaxRatio * d.Qty * Convert.ToDecimal(row["SellPrice"])) / (1 + CustomTaxRatio) :
                       (CustomTaxRatio * totalsellp) / (1 + CustomTaxRatio);
                    row["CustomTax"] = decimal.ToDouble(temp2);
                    row["TotalSellPrice"] = decimal.ToDouble(totalsellp - temp2);// السعر الاجمالي شامل الضريبة                            
                }
                else
                {
                    decimal temp = item.calcTaxBeforeDisc ? (salestaxratio * d.Qty * Convert.ToDecimal(row["SellPrice"])) : (salestaxratio * TotalSellPrice);
                    row["SalesTax"] = decimal.ToDouble(temp);
                    row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);//ضيف الضريبة على السعر الاجمالي         

                    var totalsellp = (TotalSellPrice);
                    //custom tax
                    decimal temp2 = item.calcTaxBeforeDisc ? (CustomTaxRatio * d.Qty * Convert.ToDecimal(row["SellPrice"])) : (CustomTaxRatio * totalsellp);
                    row["CustomTax"] = decimal.ToDouble(temp2);
                    row["TotalSellPrice"] = decimal.ToDouble(totalsellp);//ضيف الضريبة على السعر الاجمالي                            
                }

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);
                MyHelper.GetUOMs(item, dtUOM, uom_list);
                row["UOM"] = dtUOM.Rows[0]["UomId"];
                row["UomIndex"] = "0";
                row["IsExpire"] = item.IsExpire;

                if (Shared.user.Sell_ShowCrntQty == true)
                {
                    decimal currentQty = 0;// MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.ItemId, Convert.ToInt32(lkpStore.EditValue), SlOrdrId);
                    row["CurrentQty"] = decimal.ToDouble(currentQty);
                }
                else
                {
                    row["CurrentQty"] = decimal.ToDouble(0);
                }
                row["ItemType"] = item.ItemType;
                row["AudiencePrice"] = item.AudiencePrice;

                row["ItemDescription"] = item.Description;
                row["ItemDescriptionEn"] = item.DescriptionEn;

                row["ParentItemId"] = item.mtrxParentItem;
                row["M1"] = item.mtrxAttribute1;
                row["M2"] = item.mtrxAttribute2;
                row["M3"] = item.mtrxAttribute3;

                row["Length"] = Decimal.ToDouble(item.Length);
                row["Width"] = Decimal.ToDouble(item.Width);
                row["Height"] = Decimal.ToDouble(item.Height);
                row["RowHandle"] = ++rowhandle;
                dtSL_Details.Rows.Add(row);
            }
        }

        private void gridView2_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            var view = ((sender) as GridView);
            decimal totalqty = 0;
            try
            {
                if (e.Column == col_TotalQty
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType") != DBNull.Value
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty") != DBNull.Value
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length") != DBNull.Value
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width") != DBNull.Value
        && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height") != DBNull.Value)
                {
                    if (Convert.ToInt32(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType")) != (int)ItemType.Subtotal)
                    {
                        totalqty = Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height"));
                    }
                    else
                    {
                        int i = e.ListSourceRowIndex < 0 ? view.RowCount - 1 : e.ListSourceRowIndex - 1;
                        while (Convert.ToInt32(view.GetListSourceRowCellValue(i, "ItemType")) != (int)ItemType.Subtotal && i >= 0)
                        {
                            if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                                totalqty += Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Length")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Height")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Width")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Qty"));
                            i--;
                        }
                    }
                    e.Value = decimal.ToDouble(totalqty);
                }

                if (e.Column.FieldName == "index")
                    e.Value = e.ListSourceRowIndex + 1;
            }
            catch { }
        }

        private void Get_SubTotal_RowData(DataRow row, GridView view, int CurrentRowHandle)
        {
            row["Qty"] = 0;
            row["Length"] = 0;
            row["Width"] = 0;
            row["Height"] = 0;
            row["DiscountRatio"] = 0;
            row["UOM"] = 0;
            row["UomIndex"] = 0;
            row["CurrentQty"] = 0;
            row["ItemDescription"] = "";
            row["ItemDescriptionEn"] = "";

            int i = CurrentRowHandle < 0 ? view.RowCount - 1 : CurrentRowHandle - 1;
            decimal TotalSellPrice = 0, TotalDiscountValue = 0, TotalSalesTax = 0, TotalCusTax = 0,
                TotalPiecesCount = 0, TotalQty = 0, SellPrice = 0;

            while (Convert.ToInt32(view.GetRowCellValue(i, "ItemType")) != (int)ItemType.Subtotal && i >= 0)
            {
                TotalQty += Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));
                TotalSellPrice += Convert.ToDecimal(view.GetRowCellValue(i, "TotalSellPrice"));
                TotalDiscountValue += Convert.ToDecimal(view.GetRowCellValue(i, "DiscountValue"));

                decimal totalrowqty = Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));
                if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                    totalrowqty = Convert.ToDecimal(view.GetRowCellValue(i, "Length")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Height")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Width")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));

                TotalSalesTax += Convert.ToDecimal(view.GetRowCellValue(i, "SalesTax"));
                TotalCusTax += Convert.ToDecimal(view.GetRowCellValue(i, "CustomTax"));
                SellPrice += (Convert.ToDecimal(view.GetRowCellValue(i, "SellPrice")) * totalrowqty);
                TotalPiecesCount += Convert.ToDecimal(view.GetRowCellValue(i, "PiecesCount"));
                i--;
            }

            row["Qty"] = decimal.ToDouble(TotalQty);
            row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);
            row["DiscountValue"] = decimal.ToDouble(TotalDiscountValue);
            row["SalesTax"] = decimal.ToDouble(TotalSalesTax);
            row["CustomTax"] = decimal.ToDouble(TotalCusTax);
            row["SellPrice"] = decimal.ToDouble(SellPrice);
            row["PiecesCount"] = decimal.ToDouble(TotalPiecesCount);

        }
        private void Update_First_SubTotal(GridView view, int CurrentRowHandle)
        {
            if (CurrentRowHandle >= 0)
            {
                for (int i = CurrentRowHandle; i < view.RowCount; i++)
                {
                    if (Convert.ToInt32(view.GetRowCellValue(i, "ItemType")) == (int)ItemType.Subtotal)
                    {
                        Get_SubTotal_RowData(view.GetDataRow(i), view, i);
                        return;
                    }
                }
            }
            else if (Convert.ToInt32(view.GetFocusedRowCellValue("ItemType")) == (int)ItemType.Subtotal)
                Get_SubTotal_RowData(view.GetFocusedDataRow(), view, CurrentRowHandle);
        }

        private void gridView2_RowStyle(object sender, RowStyleEventArgs e)
        {
            if (e.RowHandle >= 0 && Convert.ToInt32(gridView2.GetRowCellValue(e.RowHandle, "ItemType")) == (int)ItemType.Subtotal)
            {
                e.HighPriority = true;
                e.Appearance.BackColor = Shared.user.SubtotalBackcolor == null ? Color.Yellow : Color.FromName(Shared.user.SubtotalBackcolor);
            }
        }

        private void Print_Prescriptions()
        {
            //GridView view = grdPrInvoice.FocusedView as GridView;
            //DataTable dt = new DataTable();
            //dt.Columns.Add("ItemName");
            //dt.Columns.Add("ItemDescription");
            //dt.Columns.Add("Qty");

            //for (int i = 0; i < view.RowCount; i++)
            //{
            //    if (view.GetRowCellDisplayText(i, "ItemCode1") == string.Empty)
            //        continue;

            //    dt.Rows.Add(view.GetRowCellDisplayText(i, "ItemId"), view.GetRowCellDisplayText(i, "ItemDescription"), view.GetRowCellValue(i, "Qty"));
            //}
            //new Reports.rpt_PharmacyPrescriptionLabels(dt, 1, null, txt_AttnMr.Text, dtInvoiceDate.Text).ShowPreview();
        }

        private void mi_PasteRows_Click(object sender, EventArgs e)
        {
            foreach (DataRow dr in Reports.ReportsUtils.dt_Copied_Rows.Rows)
            {
                decimal totalqty = Convert.ToDecimal(dr["Qty"]);
                if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                    totalqty = Convert.ToDecimal(dr["Qty"]) * Convert.ToDecimal(dr["Length"]) * Convert.ToDecimal(dr["Width"])
                        * Convert.ToDecimal(dr["Height"]);

                DataRow row = dtSL_Details.NewRow();
                int itemId = Convert.ToInt32(dr["ItemId"]);
                var item = DB.IC_Items.Where(x => x.ItemId == itemId).FirstOrDefault();
                LoadItemRow(item, row);
                row["Qty"] = Convert.ToDouble(dr["Qty"]);
                row["Height"] = dr["Height"];
                row["Length"] = dr["Length"];
                row["Width"] = dr["Width"];
                row["PiecesCount"] = dr["PiecesCount"];
                row["ExpireDate"] = dr["Expire"];
                row["Expire"] = dr["Expire"];
                row["Batch"] = dr["Batch"];
                row["Serial"] = dr["Serial"];
                row["Serial2"] = dr["Serial2"];
                if (dr["SellPrice"] != DBNull.Value)
                    row["SellPrice"] = dr["SellPrice"];

                row["TotalSellPrice"] = decimal.ToDouble(Convert.ToDecimal(row["SellPrice"]) * totalqty);
                dtSL_Details.Rows.Add(row);
            }
        }

        private void txtDiscountValue_Leave(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }



        private void txtDiscountRatio_EditValueChanged(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void Get_TotalAccount()
        {
            gridView1.RefreshData();
            try
            {
                decimal total_sell = 0;
                decimal total_salestax = 0, total_Custax = 0;
                decimal net = 0;
                decimal discount_ratio = 0;
                decimal Discount_value = 0;
                decimal Retention_ratio = 0;
                decimal Retention_value = 0;
                decimal AdvancePay_ratio = 0;
                decimal AdvancePay_value = 0;
                decimal total_ETaxValu = 0;
                decimal total_b4_Discounts_Taxes = 0;
                decimal totalAfterCommercial_Disc = 0;
                decimal commercialDiscount = 0;
                decimal bounsDiscount = 0;

                foreach (DataRow dr in dtSL_Details.Rows)
                {
                    if (dr.RowState == DataRowState.Deleted)
                        continue;

                    if (Convert.ToInt32(dr["ItemType"]) != (int)ItemType.Subtotal)
                    {
                        total_sell += Convert.ToDecimal(dr["TotalSellPrice"]);
                        total_salestax += (Convert.ToDecimal(dr["SalesTax"]));
                        total_Custax += (Convert.ToDecimal(dr["CustomTax"]));
                        bounsDiscount += (Convert.ToDecimal(dr["bonusDiscount"]));
                        if (Shared.st_Store.E_AllowMoreThanTax == false || Shared.st_Store.E_AllowMoreThanTax == null)
                        {

                            if (dr["TaxType"] != DBNull.Value && dr["EtaxValue"] != DBNull.Value && dr["EtaxValue"].ToString() != "0")
                            {
                                var taxType = Convert.ToInt32(dr["TaxType"]);
                                var taxId = DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == taxType).ParentTaxId;
                                if (taxId == discountTaxId)
                                    total_ETaxValu -= Convert.ToDecimal(dr["EtaxValue"]);
                                else
                                    total_ETaxValu += Convert.ToDecimal(dr["EtaxValue"]);
                            }
                        }

                        else
                        {
                            //if (dr["SL_InvoiceDetailId"] != DBNull.Value)
                            //{
                            //    var detailId = Convert.ToInt32(dr["SL_InvoiceDetailId"]);
                            //    var taxes = DB.SL_InvoiceDetailSubTaxValues.Where(a => a.InvoiceDetailId == detailId);
                            //    foreach (var item in taxes)
                            //    {
                            //        var taxId = DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == item.esubTypeId).ParentTaxId;
                            //        if (taxId == discountTaxId)
                            //            total_ETaxValu -= item.value;
                            //        else
                            //            total_ETaxValu += item.value;
                            //    }
                            //}
                            //else
                            total_ETaxValu += dr["TotalTaxes"] != DBNull.Value ? Math.Round(Convert.ToDecimal(dr["TotalTaxes"]), defaultRoundingPoints) : 0;

                        }
                    }

                }
                #region Discount
                discount_ratio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
                Discount_value = Convert.ToDecimal(txtDiscountValue.EditValue);

                if (discount_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * discount_ratio, Discount_value))
                        Discount_value = total_sell * discount_ratio;
                }
                //else
                //    Discount_value = 0;
                #endregion

                #region Deduct_Tax
                decimal deductTax_ratio = Convert.ToDecimal(txt_DeductTaxR.EditValue) / 100;
                decimal deductTax_value = Convert.ToDecimal(txt_DeductTaxV.EditValue);

                if (deductTax_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * deductTax_ratio, deductTax_value))
                        deductTax_value = total_sell * deductTax_ratio;
                }
                //else
                //    deductTax_value = 0;
                #endregion

                #region Add_Tax
                decimal addTax_ratio = Convert.ToDecimal(txt_AddTaxR.EditValue) / 100;
                decimal addTax_value = Convert.ToDecimal(txt_AddTaxV.EditValue);

                if (addTax_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * addTax_ratio, addTax_value))
                        addTax_value = total_sell * addTax_ratio;
                }
                //else
                //    addTax_value = 0;
                #endregion

                #region Expenses
                decimal expenses_ratio = Convert.ToDecimal(txtExpensesR.EditValue) / 100;
                decimal expenses_value = Convert.ToDecimal(txtExpenses.EditValue);

                if (expenses_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * expenses_ratio, expenses_value))
                        expenses_value = total_sell * expenses_ratio;
                }
                decimal trans_hand = decimal.Round(Convert.ToDecimal(txt_Handing.EditValue) + Convert.ToDecimal(txt_transportation.EditValue), defaultRoundingPoints);
                decimal shiftValue = decimal.Round(Convert.ToDecimal(txt_ShiftAdd.EditValue), defaultRoundingPoints);

                //else
                //    expenses_value = 0;
                #endregion

                #region Add_CusTax
                //decimal CusTaxR = Convert.ToDecimal(txt_CusTaxR.EditValue) / 100;
                //decimal CusTaxV = Convert.ToDecimal(txt_CusTaxV.EditValue);

                //if (CusTaxR > 0)
                //{
                //    if (Utilities.ValuesNotEqual(total_sell * CusTaxR, CusTaxV))
                //        CusTaxV = total_sell * CusTaxR;
                //}

                #endregion

                #region Retention
                Retention_ratio = Convert.ToDecimal(txt_retentionR.EditValue) / 100;
                Retention_value = Convert.ToDecimal(txt_RetentionV.EditValue);

                if (Retention_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * Retention_ratio, Retention_value))
                        Retention_value = total_sell * Retention_ratio;
                }

                #endregion
                #region Advance Payment
                AdvancePay_ratio = Convert.ToDecimal(txt_AdvancePayR.EditValue) / 100;
                AdvancePay_value = Convert.ToDecimal(txt_AdvancePayV.EditValue);

                if (AdvancePay_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * AdvancePay_ratio, AdvancePay_value))
                        AdvancePay_value = total_sell * AdvancePay_ratio;
                }

                #endregion



                total_sell = decimal.Round(total_sell, defaultRoundingPoints);
                total_salestax = decimal.Round(total_salestax, defaultRoundingPoints);
                total_Custax = decimal.Round(total_Custax, defaultRoundingPoints);
                deductTax_value = decimal.Round(deductTax_value, defaultRoundingPoints);
                addTax_value = decimal.Round(addTax_value, defaultRoundingPoints);
                total_ETaxValu = decimal.Round(total_ETaxValu, defaultRoundingPoints);
                // CusTaxV = decimal.Round(CusTaxV, 4);
                Discount_value = decimal.Round(Discount_value, defaultRoundingPoints);
                expenses_value = decimal.Round(expenses_value, defaultRoundingPoints);
                Retention_value = decimal.Round(Retention_value, defaultRoundingPoints);
                AdvancePay_value = decimal.Round(AdvancePay_value, defaultRoundingPoints);

                decimal totaltaxValue = 0;
                if (Shared.E_invoiceAvailable||Shared.st_Store.E_AllowMoreThanTax==true)
                {
                    txtDiscountValue.Enabled = false;
                    txt_DeductTaxV.Enabled = false;
                    txt_AddTaxV.Enabled = false;
                    txtDiscountRatio.Enabled = false;
                    txt_DeductTaxR.Enabled = false;
                    txt_AddTaxR.Enabled = false;
                    //txt_AddTaxV.EditValue = taxValue + DiscountTotaltax;
                    if (col_CommercialDiscountValue.SummaryItem.SummaryValue != null)
                        commercialDiscount = Convert.ToDecimal(col_CommercialDiscountValue.SummaryItem.SummaryValue);

                    //Sales Tax
                    addTax_value = dtSL_Details.AsEnumerable().Where(x => x.RowState != DataRowState.Deleted).Sum(X => Convert.ToDecimal(X["TotalSubAddTax"]));
                    //Table Taxes
                    total_Custax = dtSL_Details.AsEnumerable().Where(x => x.RowState != DataRowState.Deleted).Sum(X => Convert.ToDecimal(X["TotalSubCustomTax"]));
                    //DeductTax
                    deductTax_value = dtSL_Details.AsEnumerable().Where(x => x.RowState != DataRowState.Deleted && Convert.ToDecimal(x["TotalSubDiscountTax"]) > 0).Sum(X => Convert.ToDecimal(X["TotalSubDiscountTax"]));

                    totaltaxValue = dtSL_Details.AsEnumerable()
                     .Where(r => r.RowState != DataRowState.Deleted)
                     .Sum(x => Convert.ToDecimal(x["TaxValue"] is DBNull ? 0 : Convert.ToDecimal(x["TaxValue"])));
                    txt_DeductTaxV.EditValue = decimal.Round(deductTax_value, 4);
                    txt_EtaxValue.EditValue = totaltaxValue;

                    total_sell = decimal.Round(total_sell, 4) /*- taxValue - Discount_value*/;

                    //net = decimal.Round(total_sell, 4) + taxValue - deductTax_value;
                    net = decimal.Round(total_sell, 4) + totaltaxValue/*addTax_value+total_Custax - deductTax_value*/;

                }                
                net = (total_sell + total_salestax + totaltaxValue/* - commercialDiscount*/ - Retention_value
                    - AdvancePay_value + expenses_value + txt_Handing.Value + txt_transportation.Value + txt_ShiftAdd.Value)- bounsDiscount;

                commercialDiscount = dtSL_Details.AsEnumerable().Where(x => x.RowState != DataRowState.Deleted).Sum(X => Convert.ToDecimal(X["DiscountValue"]));// (from DataRow r in dtSL_Details.Rows select (decimal)r["DiscountValue"]).ToList().DefaultIfEmpty(0).Sum();


                //if (colBonusDiscount.SummaryItem?.SummaryValue?.ToString() != "0")
                bounsDiscount = dtSL_Details.AsEnumerable().Where(x => x.RowState != DataRowState.Deleted).Sum(X => Convert.ToDecimal(X["bonusDiscount"]));

                total_b4_Discounts_Taxes = total_sell + commercialDiscount;
                totalAfterCommercial_Disc = total_b4_Discounts_Taxes - commercialDiscount;// - totaltaxValue

                txt_Subtotal.Text = (total_sell-bounsDiscount).ToString();
                txt_SubAfterTax.Text = (total_sell + total_salestax + total_Custax +
                                        addTax_value/*+ CusTaxV*/ - deductTax_value - Discount_value).ToString($"{defaultRoundingPoints}");

                txt_total_b4_Discounts.Text = Math.Round(total_b4_Discounts_Taxes,defaultRoundingPoints).ToString($"F{defaultRoundingPoints}");
                txt_totalAfterCommercial_Disc.Text = totalAfterCommercial_Disc.ToString($"F{defaultRoundingPoints}");
                txt_CommercialDiscounts.Text = commercialDiscount.ToString($"F{defaultRoundingPoints}");

                #region net Ceiling
                //if (Shared.LibraAvailabe)
                //{
                //    if ((net - Math.Truncate(net)) > 0)
                //        txt_Handing.Value += (1 - (net - Math.Truncate(net)));
                //    net = Math.Ceiling(net);
                //}
                #endregion

                txtNet.EditValue = decimal.ToDouble(net).ToString($"F{defaultRoundingPoints}");
                txtDiscountValue.EditValue = decimal.ToDouble(Discount_value).ToString($"F{defaultRoundingPoints}");
                txt_bounsDiscount.EditValue = decimal.ToDouble(bounsDiscount).ToString($"F{defaultRoundingPoints}");
                txtExpenses.EditValue = decimal.ToDouble(expenses_value).ToString($"F{defaultRoundingPoints}");
                txt_DeductTaxV.EditValue = decimal.ToDouble(deductTax_value).ToString($"F{defaultRoundingPoints}");
                txt_AddTaxV.EditValue = decimal.ToDouble(addTax_value).ToString($"F{defaultRoundingPoints}");
                txt_CusTaxV.EditValue = decimal.ToDouble(total_Custax).ToString($"F{defaultRoundingPoints}");
                txt_TaxValue.EditValue = decimal.ToDouble(total_salestax).ToString($"F{defaultRoundingPoints}");
                //Advance payment and retention
                txt_AdvancePayV.EditValue = decimal.ToDouble(AdvancePay_value).ToString($"F{defaultRoundingPoints}");
                //  txt_AdvancePayR.EditValue = decimal.ToDouble(AdvancePay_ratio);
                txt_RetentionV.EditValue = decimal.ToDouble(Retention_value).ToString($"F{defaultRoundingPoints}");
                //  txt_retentionR.EditValue = decimal.ToDouble(Retention_ratio);

                txt_Total.EditValue = decimal.ToDouble(total_sell).ToString($"F{defaultRoundingPoints}");
                txt_EtaxValue.EditValue = decimal.ToDouble(totaltaxValue).ToString($"F{defaultRoundingPoints}");
                if (Shared.user.SL_Invoice_PayMethod == false)          //اجل
                {
                    if (Convert.ToDecimal(txt_paid.EditValue) == 0)
                        txt_Remains.EditValue = decimal.ToDouble(net);
                    txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue)).ToString($"F{defaultRoundingPoints}");
                }
                else
                {
                    if (Convert.ToDecimal(txt_Remains.EditValue) == 0 && Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) == 0)
                        if (Shared.LibraAvailabe == false)
                            txt_PayAcc1_Paid.EditValue = decimal.ToDouble(net);
                    txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue)).ToString($"F{defaultRoundingPoints}");
                }
                //if (Convert.ToBoolean(cmbPayMethod.EditValue) == false)          //اجل
                //{
                //    //الدفع الافتراضي آجل
                //    if (Shared.user.SL_Invoice_PayMethod.HasValue && Shared.user.SL_Invoice_PayMethod.Value == false)
                //    { txt_PayAcc1_Paid.EditValue = txt_PayAcc2_Paid.EditValue = 0; txt_Remains.EditValue = net; }

                //    //الدفع الافتراضي كاش
                //    else if (Shared.user.SL_Invoice_PayMethod.HasValue && Shared.user.SL_Invoice_PayMethod.Value == true)
                //    {
                //        if (Convert.ToDecimal(txt_paid.EditValue) == 0)
                //            txt_Remains.EditValue = decimal.ToDouble(net);
                //        txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue));
                //    }

                //}
                //else // كاش
                //{
                //    //الدفع الافتراضي آجل
                //    if (Shared.user.SL_Invoice_PayMethod.HasValue && Shared.user.SL_Invoice_PayMethod.Value == false)
                //    { txt_PayAcc1_Paid.EditValue = net; txt_Remains.EditValue = 0; }

                //    //الدفع الافتراضي كاش
                //    else if (Shared.user.SL_Invoice_PayMethod.HasValue && Shared.user.SL_Invoice_PayMethod.Value == true)
                //    {
                //        if (Convert.ToDecimal(txt_Remains.EditValue) == 0 && Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) == 0)
                //            txt_PayAcc1_Paid.EditValue = decimal.ToDouble(net);
                //        txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue));
                //    }
                //}
            }
            catch (Exception ex)
            { }
        }



        private void txt_paid_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (Shared.user.SL_Invoice_PayMethod == true)
                {
                    if (Convert.ToDecimal(txt_Remains.EditValue) <= 0)
                    {
                        cmbPayMethod.EditValue = true;
                    }
                    else if (Convert.ToDecimal(txt_paid.EditValue) == 0)
                    {
                        cmbPayMethod.EditValue = false;
                    }
                    else
                        cmbPayMethod.EditValue = null;
                }

                if ((sender as TextEdit).Name == "txt_paid")
                    txt_Remains.EditValue = Convert.ToDecimal(txtNet.EditValue) - Convert.ToDecimal(txt_paid.EditValue);
                else if ((sender as TextEdit).Name == "txt_Remains")
                {
                    txt_paid.EditValue = Convert.ToDecimal(txtNet.EditValue) - Convert.ToDecimal(txt_Remains.EditValue);
                    //lkp_Customers_EditValueChanged(null, EventArgs.Empty);

                    double balance_before = Convert.ToDouble(txt_Balance_Before.Text);
                    if (cust_IsDebit == true)
                        balance_before = balance_before * -1;

                    double balance_after = balance_before - Convert.ToDouble(txt_Remains.EditValue);
                    txt_Balance_After.Text = Math.Abs(balance_after).ToString("0,0.00");
                    if (balance_after > 0)
                        lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                    else if (balance_after < 0)
                        lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                    else
                        lbl_IsCredit_After.Text = "";

                    double max_debit = Convert.ToDouble(txt_MaxCredit.Text);
                    if (balance_after <= 0 && Math.Abs(balance_after) > max_debit && max_debit > 0)
                    {
                        lbl_Validate_MaxLimit.Text = Shared.IsEnglish ? ResSLEn.txtValidateCustomerMaxDebit : ResSLAr.txtValidateCustomerMaxDebit;
                        lbl_Validate_MaxLimit.ForeColor = Color.Red;
                    }
                    else
                    {
                        lbl_Validate_MaxLimit.Text = "";
                    }
                }
            }
            catch
            { }

        }

        private void txt_PayAcc1_Paid_EditValueChanged(object sender, EventArgs e)
        {
            if (Convert.ToDecimal(txt_PayAcc1_Paid.EditValue) < 0)
                txt_PayAcc1_Paid.EditValue = 0;
            if (Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) < 0)
                txt_PayAcc2_Paid.EditValue = 0;

            txt_paid.EditValue = Decimal.ToDouble(Convert.ToDecimal(txt_PayAcc1_Paid.EditValue) + Convert.ToDecimal(txt_PayAcc2_Paid.EditValue));
        }


        void dt_TableNewRow(object sender, DataTableNewRowEventArgs e)
        {
            Get_TotalAccount();
        }
        void dt_RowChanged(object sender, DataRowChangeEventArgs e)
        {
            Get_TotalAccount();
        }

        private void mi_ExportData_Click(object sender, EventArgs e)
        {
            ErpUtils.Save_DataTable_To_XML(dtSL_Details);
        }

        private void mi_InvoiceStaticDisc_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_InvoiceDiscs)))
                Application.OpenForms["frm_InvoiceDiscs"].Close();
            else
                new frm_InvoiceDiscs(Process.SellInvoice).Show();
        }

        private void mi_InvoiceStaticDimensions_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_InvoiceDimenstions)))
                Application.OpenForms["frm_InvoiceDimenstions"].Close();
            else
                new frm_InvoiceDimenstions(Process.SellInvoice).Show();
        }

        void txtInvoiceCode_Leave(object sender, EventArgs e)
        {
            if (Shared.st_Store.InvoicesCodeRedundancy == null)
                return;

            bool code_exist = InvCodeExist();
            ErpHelper.ValidateInvCodeExist(code_exist, txtInvoiceCode);
        }

        public bool InvCodeExist()
        {
            int? InvbookId = (int?)lkp_InvoiceBook.EditValue;

            return (from x in DB.SL_Invoices
                    join s in DB.IC_Stores on x.StoreId equals s.StoreId
                    where InvbookId == null && Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                    where InvbookId == null && Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                       s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                    where x.InvoiceBookId == null ? true : x.InvoiceBookId == Convert.ToInt32(lkp_InvoiceBook.EditValue)
                    where x.InvoiceCode == txtInvoiceCode.Text
                    where x.SL_InvoiceId != invoiceId
                    select x.InvoiceCode).Count() > 0;

        }

        private void repItems_Popup(object sender, EventArgs e)
        {
            if (rep_layout.Length > 0)
            {
                rep_layout.Seek(0, System.IO.SeekOrigin.Begin);
                (sender as GridLookUpEdit).Properties.View.RestoreLayoutFromStream(rep_layout);
            }

            if (!string.IsNullOrEmpty(gridView2.GetFocusedRowCellDisplayText(Col_Category)))
            {
                try
                {
                    Cursor = Cursors.WaitCursor;
                    GridLookUpEdit editor = (GridLookUpEdit)sender;
                    editor.Properties.View.OptionsFilter.AllowFilterEditor = true;
                    string filter = String.Format("[CategoryNameAr] = '{0}'", gridView2.GetFocusedRowCellDisplayText(Col_Category).ToString());
                    //repItems.View.ActiveFilter.NonColumnFilter = filter;
                    editor.Properties.View.ActiveFilterString = filter;
                }
                finally
                {
                    Cursor = Cursors.Default;
                }
            }
            else
            {
                (sender as GridLookUpEdit).Properties.View.ClearColumnsFilter();
            }
        }

        private void repItems_CloseUp(object sender, DevExpress.XtraEditors.Controls.CloseUpEventArgs e)
        {
            rep_layout = new System.IO.MemoryStream();
            (sender as GridLookUpEdit).Properties.View.SaveLayoutToStream(rep_layout);
        }

        private void labelControl29_Click(object sender, EventArgs e)
        {

        }

        private void mi_ImportExcel_Click(object sender, EventArgs e)
        {
            ErpUtils.LoadInvItemsFromExcel(ref dtSL_Details, invoiceId, LoadItemRow, Process.SellInvoice);
        }

        private void chk_IsPosted_EditValueChanged(object sender, EventArgs e)
        {
            if (chk_IsPosted.Checked == true)
            {
                txt_Post_Date.Enabled = true;
                txt_Post_Date.EditValue = dtInvoiceDate.EditValue;
            }
            else
            {
                txt_Post_Date.Enabled = false;
                txt_Post_Date.EditValue = null;
            }
        }

        private void dtInvoiceDate_EditValueChanged(object sender, EventArgs e)
        {
            if (chk_IsPosted.Checked == true)
                txt_Post_Date.EditValue = dtInvoiceDate.EditValue;

            txt_DueDate_EditValueChanged(dtInvoiceDate, e);
        }

        private void txt_CusTaxV_Leave(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void txt_CusTaxV_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        private void txt_CusTaxV_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            if (e.IsSpinUp == false)
            {
                if (Convert.ToDecimal(((TextEdit)sender).Text) == 0)
                    e.Handled = true;
            }
        }

        private void txt_CusTaxR_EditValueChanged(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void txt_CusTaxR_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        private void txt_CusTaxR_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            if (e.IsSpinUp == false)
            {
                if (Convert.ToDecimal(((TextEdit)sender).Text) == 0)
                    e.Handled = true;
            }
        }

        private void txt_retentionR_EditValueChanged(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void txt_RetentionV_EditValueChanged(object sender, EventArgs e)
        {
            //Get_TotalAccount();
        }

        private void txt_AdvancePayR_EditValueChanged(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void txt_AdvancePayV_EditValueChanged(object sender, EventArgs e)
        {
            //Get_TotalAccount();
        }

        private void txt_RetentionV_Leave(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void txt_AdvancePayV_Leave(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void txt_RetentionV_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        private void txt_AdvancePayV_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        private void txt_retentionR_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            if (e.IsSpinUp == false)
            {
                if (Convert.ToDecimal(((TextEdit)sender).Text) == 0)
                    e.Handled = true;
            }
        }

        private void txt_AdvancePayR_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            if (e.IsSpinUp == false)
            {
                if (Convert.ToDecimal(((TextEdit)sender).Text) == 0)
                    e.Handled = true;
            }
        }

        private void btn_ShowAccStatement_Click(object sender, EventArgs e)
        {
            if (lkp_Customers.EditValue == null)
            {
                XtraMessageBox.Show(
                       Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgSelectCustomer,
                       Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                       MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_Customers.Focus();

                return;
            }

            if (!ErpUtils.IsFormOpen(typeof(Pharmacy.Forms.frm_ACC_Statement)))
            {
                int? customerId = lst_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue))
                .Select(x => x.AccountId).FirstOrDefault();
                if (customerId.HasValue)
                {
                    Pharmacy.Forms.frm_ACC_Statement f = new Pharmacy.Forms.frm_ACC_Statement(customerId.Value);
                    f.BringToFront();
                    f.Show();
                }
            }
            else
            {
                if (Application.OpenForms["frm_ACC_Statement"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_ACC_Statement"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_ACC_Statement"].BringToFront();
            }
        }

        private void lkpStore_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            if (Shared.InvoicePostToStore)
                if (dtSL_Details.Rows.Count > 0 && Shared.user.AccessType > 0)
                {
                    MessageBox.Show(Shared.IsEnglish ? "You have added some items, you can not change branch/store at the moment, ask Administrator for help" : "لقد قمت بإدراج بعض الأصناف، لا يمكن تغيير الفرع/المحزن حاليا، يرجى مراجعة مدير النظام");
                    e.Cancel = true;
                    return;
                }
        }

        private void gridView2_InitNewRow(object sender, InitNewRowEventArgs e)
        {
            if (Shared.st_Store.IsStoreOnEachSellRecord)
            {
                int defaultStoreId = 0;
                var store = stores_table.Where(x => x.StoreId == Shared.user.DefaultStore).FirstOrDefault();
                if (store == null)
                    store = stores_table.Where(x => x.ParentId == Shared.user.DefaultStore).FirstOrDefault();

                if (store != null)
                    (grdPrInvoice.FocusedView as GridView).SetRowCellValue(e.RowHandle, "StoreId", store.StoreId);
                else
                {
                    MyHelper.Get_StoresNotStopped(0, true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                    Shared.UserId);
                    (grdPrInvoice.FocusedView as GridView).SetRowCellValue(e.RowHandle, "StoreId", defaultStoreId);
                }
            }

            if (Shared.st_Store.PackCount == true)
                (grdPrInvoice.FocusedView as GridView).SetRowCellValue(e.RowHandle, "Pack", 1);

            //gridView1.SetFocusedRowCellValue("StoreId", lkpStore.EditValue);
        }

        private void barbtnPrintStore_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.CheckDocumentSaved(invoiceId, DataModified, dtSL_Details) == false)
                return;

            DataTable dt_PrintTable = new DataTable();
            dt_PrintTable.Columns.Add("ItemCode1");
            dt_PrintTable.Columns.Add("ItemCode2");
            dt_PrintTable.Columns.Add("DiscountValue");
            dt_PrintTable.Columns.Add("Expire");
            dt_PrintTable.Columns.Add("Batch");
            dt_PrintTable.Columns.Add("Branch");
            dt_PrintTable.Columns.Add("SellPrice");
            dt_PrintTable.Columns.Add("Qty");
            dt_PrintTable.Columns.Add("TotalSellPrice");
            dt_PrintTable.Columns.Add("ItemName");
            dt_PrintTable.Columns.Add("UOM");
            dt_PrintTable.Columns.Add("Height");
            dt_PrintTable.Columns.Add("Width");
            dt_PrintTable.Columns.Add("Length");
            dt_PrintTable.Columns.Add("TotalQty");
            dt_PrintTable.Columns.Add("Factor");
            dt_PrintTable.Columns.Add("MUOM");
            dt_PrintTable.Columns.Add("MUOM_Factor");
            dt_PrintTable.Columns.Add("ItemType");
            dt_PrintTable.Columns.Add("AudiencePrice");
            dt_PrintTable.Columns.Add("ItemDescription");
            dt_PrintTable.Columns.Add("ItemDescriptionEn");
            dt_PrintTable.Columns.Add("PiecesCount");

            dt_PrintTable.Columns.Add("SalesTax");
            dt_PrintTable.Columns.Add("CustomTax");
            dt_PrintTable.Columns.Add("DiscountRatio");
            dt_PrintTable.Columns.Add("DiscountRatio2");
            dt_PrintTable.Columns.Add("DiscountRatio3");
            dt_PrintTable.Columns.Add("SalesTaxRatio");
            dt_PrintTable.Columns.Add("CustomTaxRatio");
            dt_PrintTable.Columns.Add("PicPath");
            dt_PrintTable.Columns.Add("SL_InvoiceDetailId");
            dt_PrintTable.Columns.Add("Serial");
            dt_PrintTable.Columns.Add("Serial2");
            dt_PrintTable.Columns.Add("ManufactureDate");
            dt_PrintTable.Columns.Add("DueDate");
            dt_PrintTable.Columns.Add("BranchId");
            dt_PrintTable.Columns.Add("Index");
            dt_PrintTable.Columns.Add("bonusDiscount");


            var view = grdPrInvoice.FocusedView as GridView;
            for (int i = 0; i < view.RowCount; i++)
            {
                if (view.GetRowCellDisplayText(i, "ItemCode1") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTable.NewRow();
                dr["ItemCode1"] = view.GetRowCellDisplayText(i, "ItemCode1");
                dr["ItemCode2"] = view.GetRowCellDisplayText(i, "ItemCode2");
                dr["DiscountValue"] = view.GetRowCellDisplayText(i, "DiscountValue") == "0" ? "" : view.GetRowCellDisplayText(i, "DiscountValue"); //mahmoud:18-12-2012, naser azemi issue                
                dr["Expire"] = view.GetRowCellDisplayText(i, "Expire");
                dr["Batch"] = view.GetRowCellDisplayText(i, "Batch");

                dr["Branch"] = view.GetRowCellDisplayText(i, "StoreId");
                dr["BranchId"] = view.GetRowCellValue(i, "StoreId");

                dr["SellPrice"] = view.GetRowCellDisplayText(i, "SellPrice");
                dr["Qty"] = view.GetRowCellDisplayText(i, "Qty");
                dr["TotalSellPrice"] = view.GetRowCellDisplayText(i, "TotalSellPrice");
                dr["ItemName"] = view.GetRowCellDisplayText(i, "ItemId");
                dr["UOM"] = view.GetRowCellDisplayText(i, "UOM");
                dr["Height"] = view.GetRowCellDisplayText(i, "Height");
                dr["Width"] = view.GetRowCellDisplayText(i, "Width");
                dr["Length"] = view.GetRowCellDisplayText(i, "Length");
                dr["TotalQty"] = view.GetRowCellDisplayText(i, "TotalQty");

                if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 0)
                    dr["Factor"] = 1;
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 1)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "MediumUOMFactor");
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 2)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "LargeUOMFactor");

                if (ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM") != null)
                {
                    dr["MUOM"] = uom_list.Where(x => x.UOMId == Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM"))).Select(x => x.UOM).FirstOrDefault();
                    dr["MUOM_Factor"] = (double)MyHelper.FractionToDouble(dr["Factor"].ToString()) * Convert.ToDouble(dr["Qty"]); /*ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOMFactor");*/
                }
                else
                {
                    dr["MUOM"] = dr["UOM"];
                    dr["MUOM_Factor"] = dr["Factor"];
                }

                dr["ItemType"] = view.GetRowCellValue(i, "ItemType");
                dr["AudiencePrice"] = view.GetRowCellValue(i, "AudiencePrice") == null ? string.Empty : view.GetRowCellValue(i, "AudiencePrice");
                dr["ItemDescription"] = view.GetRowCellDisplayText(i, "ItemDescription");
                dr["ItemDescriptionEn"] = view.GetRowCellDisplayText(i, "ItemDescriptionEn");
                dr["PiecesCount"] = view.GetRowCellDisplayText(i, "PiecesCount");

                dr["SalesTax"] = view.GetRowCellDisplayText(i, "SalesTax");
                dr["CustomTax"] = view.GetRowCellDisplayText(i, "CustomTax");
                dr["DiscountRatio"] = view.GetRowCellDisplayText(i, "DiscountRatio");
                dr["DiscountRatio2"] = view.GetRowCellDisplayText(i, "DiscountRatio2");
                dr["DiscountRatio3"] = view.GetRowCellDisplayText(i, "DiscountRatio3");
                dr["SalesTaxRatio"] = view.GetDataRow(i)["SalesTaxRatio"];
                dr["CustomTaxRatio"] = view.GetDataRow(i)["CustomTaxRatio"];
                dr["SL_InvoiceDetailId"] = view.GetDataRow(i)["SL_InvoiceDetailId"];
                dr["PicPath"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "PicPath");
                dr["Serial"] = view.GetRowCellDisplayText(i, "Serial");
                dr["Serial2"] = view.GetRowCellDisplayText(i, "Serial2");
                dr["bonusDiscount"] = view.GetRowCellDisplayText(i, "bonusDiscount");
                //mohammad, 15/4/2018
                dr["ManufactureDate"] = view.GetRowCellDisplayText(i, "ManufactureDate");
                ///////////////////
                dr["Index"] = dt_PrintTable.Rows.Count + 1;



                dt_PrintTable.Rows.Add(dr);
            }

            string Customer = lkp_Customers.Text;
            string salesEmp_Job = lkp_SalesEmp.EditValue == null ? "" : lkp_SalesEmp.GetColumnValue("JobNameAr") + "";


            Reports.rpt_SL_Invoice_Store r = new Reports.rpt_SL_Invoice_Store
                (invoiceId, Customer, lkp_InvoiceBook.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text,
                cmbPayMethod.Text, lkp_Drawers.Text, txtNotes.Text, txt_Total.Text, "", txt_TaxValue.Text,
                txtDiscountRatio.Text, txtDiscountValue.Text, txtExpensesR.Text, txtExpenses.Text, txtNet.Text,
                txt_paid.Text, txt_Remains.Text, dt_PrintTable,
                Shared.lst_Users.Where(x => x.UserId == userId).Select(x => x.Name).FirstOrDefault()
                , lkp_SalesEmp.Text,
                txt_Shipping.Text, txt_PO_No.Text, dtDeliverDate.Text, salesEmp_Job, txt_DeductTaxV.Text, txt_AddTaxV.Text,
                Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue), lbl_IsCredit_Before.Text + " " + txt_Balance_Before.Text,
                lbl_IsCredit_After.Text + " " + txt_Balance_After.Text,
                txtDriverName.Text, txtVehicleNumber.Text, txtDestination.Text, "", "", txtScaleSerial.Text, txt_CusTaxR.Text, txt_CusTaxV.Text, lkpCostCenter.Text, txt_RetentionV.Text, txt_AdvancePayV.Text, txt_DueDate.Text);

            //string TemplateName = "rpt_SL_Invoice";
            //if (lkp_InvoiceBook.GetColumnValue("PrintFileName") != null && lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString().Trim() != string.Empty)
            //    TemplateName = lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString();

            //if (Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".en-US.repx"))
            //    r.LoadLayout(Shared.ReportsPath + TemplateName + ".en-US.repx");
            //else if (!Shared.IsEnglish && System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".ar-EG.repx"))
            //    r.LoadLayout(Shared.ReportsPath + TemplateName + ".ar-EG.repx");
            //else if (System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".repx"))
            //    r.LoadLayout(Shared.ReportsPath + TemplateName + ".repx");

            //r.LoadData();
            if (Shared.user.ShowPrintPreview == false)
                r.PrintDialog();
            else
                r.ShowPreview();
        }

        private void txt_transportation_Leave(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void txt_Handing_Leave(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void lkp_Cars_EditValueChanged(object sender, EventArgs e)
        {
            if (lkp_Cars.GetColumnValue("PlateNo") != null)
                txtVehicleNumber.Text = lkp_Cars.GetColumnValue("PlateNo").ToString();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            if (HrHelper.CheckPermission((int)FormsNames.SL_Invoice, invoiceId))
            {
                var notification = new System.Windows.Forms.NotifyIcon()
                {
                    Visible = true,
                    Icon = System.Drawing.SystemIcons.Information,
                    BalloonTipIcon = System.Windows.Forms.ToolTipIcon.Info,
                    BalloonTipTitle = "LinkITSys",
                    BalloonTipText = "لقد تمت الموافقه على طلبك.",
                };
                notification.ShowBalloonTip(5000);
                notification.Dispose();
                timer1.Enabled = false;
                LoadPrivilege();
            }
        }

        private void lkpCostCenter_Popup(object sender, EventArgs e)
        {
            gv_CostCenter.ClearSelection();

            if (gv_CostCenter.RowCount > 0)
            {

                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    int rowHandle = gv_CostCenter.LocateByValue("CostCenterId", Convert.ToInt32(d[0]));
                    gv_CostCenter.SelectRow(rowHandle);
                }
            }
        }

        private void lkpCostCenter_CloseUp(object sender, DevExpress.XtraEditors.Controls.CloseUpEventArgs e)
        {
            dt_Multi_CC.Rows.Clear();
            foreach (var c in gv_CostCenter.GetSelectedRows())
            {
                dt_Multi_CC.Rows.Add(gv_CostCenter.GetRowCellValue(c, "CostCenterId"));
            }
        }

        private void txtNet_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void gridView2_HideCustomizationForm(object sender, EventArgs e)
        {

        }

        private void btnCopy_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            var customerId = Convert.ToInt32(lkp_Customers.EditValue);
            new frm_SL_InvoiceList(true, customerId).ShowDialog();
            int SelectedInvId = frm_SL_InvoiceList.SelectedInvId;

            if (SelectedInvId > 0)
            {
                isCopy = true;
                GetInvoice(SelectedInvId);
                frm_SL_InvoiceList.SelectedInvId = 0;
                frm_SL_InvoiceList.SelectedInvCode = string.Empty;
                invoiceId = 0;
                isCopy = false;
            }


        }

        private void barBtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (invoiceId != 0)
            {
                var slInvoice = DB.SL_Invoices.FirstOrDefault(x => x.SL_InvoiceId == invoiceId);
                if (slInvoice.EstatusCode != 1)
                {
                    var slInvoiceDetails = DB.SL_InvoiceDetails.Where(x => x.SL_InvoiceId == invoiceId);
                    var slInvoiceTax = DB.SL_InvoiceDetailSubTaxValues.Where(x => slInvoiceDetails.Select(z => z.SL_InvoiceDetailId).Contains(x.InvoiceDetailId));

                    DB.SL_InvoiceDetailSubTaxValues.DeleteAllOnSubmit(slInvoiceTax);
                    DB.SL_InvoiceDetails.DeleteAllOnSubmit(slInvoiceDetails);
                    DB.SL_Invoices.DeleteOnSubmit(slInvoice);
                    DB.SubmitChanges();

                    MessageBox.Show("تم حذف الفاتورة بنجاح");
                }
                else
                {
                    MessageBox.Show("لا يمكن حذف فاتورة حدث لها مزامنة من قبل");
                }
            }
        }

        private void txtTserial_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void rep_btnAddTaxes_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_SL_Add_Taxes)))
                Application.OpenForms["frm_SL_Add_Taxes"].Close();

            if (ErpUtils.IsFormOpen(typeof(frm_SL_Add_Taxes)))
                Application.OpenForms["frm_SL_Add_Taxes"].BringToFront();
            else
            {

                GridView view = grdPrInvoice.FocusedView as GridView;
                //GridView view = sender as GridView;
                view.UpdateCurrentRow();
                dtSL_Details.AcceptChanges();
                if (view.GetFocusedRowCellValue("ItemId") == DBNull.Value || view.GetFocusedRowCellValue("ItemId") == null) return;
                int ItemId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                decimal SellPrice = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                decimal Qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                int rowhandle = Convert.ToInt32(view.GetFocusedRowCellValue("RowHandle"));
                frm_SL_Add_Taxes form = new frm_SL_Add_Taxes(invoiceId, Dt_Rows, ItemId, SellPrice, Qty, rowhandle);

                if (form.ShowDialog() == DialogResult.OK)
                {
                    foreach (DataRow detail in dtSL_Details.Rows)
                    {
                        if (Convert.ToInt32(detail["RowHandle"]) == rowhandle)
                        {
                            detail["TaxValue"] = 0;
                            detail["totalTaxesRatio"] = detail["TotalTaxes"] = 0;
                            calcSubTaxes(detail, view);
                        }
                    }

                }
            }
        }
        //private void calcSubTaxes(DataRow detail, GridView view)
        //{
        //    decimal totalTaxValue = totalTaxRatio = 0;
        //    decimal totalTableTaxRatio = 0;
        //    decimal tableTaxValue = 0;
        //    decimal tableTaxratio = 0;
        //    var discountTaxId = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault().E_TaxableTypeId;
        //    //==================Updata==================//
        //    var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId.ToString()).ToList();

        //    var tableTaxes = Dt_Rows.AsEnumerable()
        //     .Where(a => a.RowState != DataRowState.Deleted)
        //     .Where(r => tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();

        //    var otherTaxes = Dt_Rows.AsEnumerable()
        //    .Where(a => a.RowState != DataRowState.Deleted)
        //    .Where(r => !tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();

        //    var addTaxType = Dt_Rows.AsEnumerable()
        //          .Where(a => a.RowState != DataRowState.Deleted)
        //     .Where(r => r["Tax"].ToString() == "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();

        //    //=================Case Tab Tax===================//
        //    foreach (DataRow row in tableTaxes)
        //    {

        //        if (Convert.ToInt32(row["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
        //        {
        //            decimal TotalSellPrice = 0;
        //            decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
        //            if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
        //            else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

        //            if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
        //            else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);

        //            bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);
        //            TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //            decimal taxValue = calcTaxBeforeDisc ?
        //                Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints)
        //                : Math.Round(TotalSellPrice * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints);
        //            row["TaxValue"] = taxValue;
        //            //=================Check DiscountTax or Not================//
        //            if (Convert.ToInt32(row["Tax"]) == discountTaxId)
        //            {
        //                decimal TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
        //                totalTaxValue -= taxValue;
        //                totalTaxRatio -= TaxRatio;
        //                totalTableTaxRatio -= TaxRatio;
        //            }

        //            else
        //            {
        //                decimal TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
        //                totalTaxValue += taxValue;
        //                totalTaxRatio += TaxRatio;
        //                totalTableTaxRatio += TaxRatio;
        //                tableTaxValue += taxValue;
        //                tableTaxratio += TaxRatio;
        //            }
        //            row["TaxValue"] = taxValue;
        //            detail["tableTaxValue"] = Math.Round(Convert.ToDecimal(taxValue), defaultRoundingPoints);
        //            view.SetFocusedRowCellValue(totalTaxesRatio, totalTaxRatio);
        //            //detail["salePriceWithTaxTable"] = Math.Round(Convert.ToDecimal(TotalSellPrice + tableTaxValue), defaultRoundingPoints);
        //            //   detail["totalTaxesRatio"] = Math.Round(Convert.ToDecimal(totalTaxRatio), defaultRoundingPoints);
        //            detail["totalTableTaxes"] = Math.Round(Convert.ToDecimal(tableTaxratio), defaultRoundingPoints);
        //            //========================================================//

        //        }
        //    }
        //    if (tableTaxes.Count == 0)
        //    {
        //        detail["totalTableTaxes"] = Math.Round(Convert.ToDecimal(0), defaultRoundingPoints);
        //        detail["tableTaxValue"] = Math.Round(Convert.ToDecimal(0), defaultRoundingPoints);
        //    }
        //    //=================Case 2 Add Taxes===================//
        //    foreach (DataRow mm in addTaxType)
        //    {

        //        if (Convert.ToInt32(mm["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
        //        {
        //            decimal TotalSellPrice = 0;
        //            decimal taxRatio = 0;
        //            decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
        //            var xx = detail["totalTaxesRatio"];
        //            if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
        //            else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

        //            if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
        //            else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);
        //            TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //            var totalSellPriceWithTableTaxes = Convert.ToDecimal(totalTableTaxRatio / 100) * TotalSellPrice;
        //            TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;


        //            //=================================//
        //            bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);

        //            decimal taxValue = calcTaxBeforeDisc ?
        //                Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints)
        //                : Math.Round(TotalSellPrice * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints);

        //            mm["TaxValue"] = taxValue;


        //            TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //            taxRatio = Math.Round(Convert.ToDecimal(taxValue / (TotalSellPrice == 0 ? 1: TotalSellPrice)) * 100, defaultRoundingPoints);


        //            //=================Check DiscountTax or Not================//
        //            if (Convert.ToInt32(mm["Tax"]) == discountTaxId)
        //            {

        //                decimal TaxRatio = taxRatio;
        //                totalTaxValue -= taxValue;
        //                totalTaxRatio -= TaxRatio;
        //            }

        //            else
        //            {
        //                decimal TaxRatio = taxRatio;
        //                totalTaxValue += taxValue;
        //                totalTaxRatio += TaxRatio;

        //            }
        //            mm["TaxValue"] = taxValue;
        //            detail["addTaxValue"] = Math.Round(Convert.ToDecimal(taxValue), defaultRoundingPoints);
        //            // detail["totalTaxesRatio"] = Math.Round(Convert.ToDecimal(totalTaxRatio), defaultRoundingPoints);
        //            view.SetFocusedRowCellValue(totalTaxesRatio, totalTaxRatio);
        //            //========================================================//

        //        }
        //    }
        //    if (addTaxType.Count == 0)
        //    {
        //        detail["addTaxValue"] = Math.Round(Convert.ToDecimal(0), defaultRoundingPoints);
        //    }
        //    //==============Case 3=================//
        //    foreach (DataRow m in otherTaxes)
        //    {
        //        if (Convert.ToInt32(m["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
        //        {
        //            decimal TotalSellPrice = 0;
        //            decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
        //            var xx = detail["totalTaxesRatio"];
        //            if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
        //            else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

        //            if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
        //            else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);
        //            TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //            bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);

        //            decimal taxValue = calcTaxBeforeDisc ?
        //                Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints)
        //                : Math.Round(TotalSellPrice * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints);
        //            m["TaxValue"] = taxValue;
        //            //=================Check DiscountTax or Not================//
        //            if (Convert.ToInt32(m["Tax"]) == discountTaxId)
        //            {

        //                decimal TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
        //                totalTaxValue -= taxValue;
        //                totalTaxRatio -= TaxRatio;
        //            }

        //            else
        //            {
        //                decimal TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
        //                totalTaxValue += taxValue;
        //                totalTaxRatio += TaxRatio;

        //            }
        //            m["TaxValue"] = taxValue;
        //            //detail["totalTaxesRatio"] = Math.Round(Convert.ToDecimal(totalTaxRatio), defaultRoundingPoints);
        //            view.SetFocusedRowCellValue(totalTaxesRatio, totalTaxRatio);

        //            //========================================================//

        //        }
        //    }
        //    view.UpdateCurrentRow();
        //    Get_TotalAccount();
        //}
        private void calcSubTaxesAgin(DataRow detail, GridView view)
        {

            decimal totalTaxValue = totalTaxRatio = 0;
            decimal totalTableTaxRatio = 0;
            decimal tableTaxValue = 0;
            decimal tableTaxratio = 0;
            var discountTaxId = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault().E_TaxableTypeId;
            //==================Updata==================//
            var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId.ToString()).ToList();

            var tableTaxes = Dt_Rows.AsEnumerable()
             .Where(a => a.RowState != DataRowState.Deleted)
             .Where(r => tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();

            var otherTaxes = Dt_Rows.AsEnumerable()
            .Where(a => a.RowState != DataRowState.Deleted)
            .Where(r => !tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();

            var addTaxType = Dt_Rows.AsEnumerable()
                 .Where(a => a.RowState != DataRowState.Deleted)
            .Where(r => r["Tax"].ToString() == "1"
            && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();
            //=================Case 1===================//
            foreach (DataRow row in tableTaxes)
            {

                if (Convert.ToInt32(row["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
                {
                    decimal TotalSellPrice = 0;
                    decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
                    if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
                    else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

                    if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
                    else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);

                    bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);
                    TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                    decimal taxValue = calcTaxBeforeDisc ?
                        Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints)
                        : Math.Round(TotalSellPrice * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints);
                    row["TaxValue"] = taxValue;
                    //=================Check DiscountTax or Not================//
                    if (Convert.ToInt32(row["Tax"]) == discountTaxId)
                    {
                        decimal TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
                        totalTaxValue -= taxValue;
                        totalTaxRatio -= TaxRatio;
                        totalTableTaxRatio -= TaxRatio;
                    }

                    else
                    {
                        decimal TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
                        totalTaxValue += taxValue;
                        totalTaxRatio += TaxRatio;
                        totalTableTaxRatio += TaxRatio;
                        tableTaxValue += taxValue;
                        tableTaxratio += TaxRatio;
                    }
                    row["TaxValue"] = taxValue;

                    //========================================================//

                }
            }

            //=================Case 2===================//
            foreach (DataRow mm in addTaxType)
            {

                if (Convert.ToInt32(mm["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
                {
                    decimal TotalSellPrice = 0;
                    decimal taxRatio = 0;
                    decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
                    var xx = detail["totalTaxesRatio"];
                    if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
                    else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

                    if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
                    else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);
                    TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                    var totalSellPriceWithTableTaxes = Convert.ToDecimal(totalTableTaxRatio / 100) * TotalSellPrice;
                    TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;


                    //=================================//
                    bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);

                    decimal taxValue = calcTaxBeforeDisc ?
                        Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints)
                        : Math.Round(TotalSellPrice * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints);

                    mm["TaxValue"] = taxValue;


                    TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                    taxRatio = Math.Round(Convert.ToDecimal(taxValue / (TotalSellPrice == 0 ? 1 : TotalSellPrice)) * 100, defaultRoundingPoints);


                    //=================Check DiscountTax or Not================//
                    if (Convert.ToInt32(mm["Tax"]) == discountTaxId)
                    {

                        decimal TaxRatio = taxRatio;
                        totalTaxValue -= taxValue;
                        totalTaxRatio -= TaxRatio;
                    }

                    else
                    {
                        decimal TaxRatio = taxRatio;
                        totalTaxValue += taxValue;
                        totalTaxRatio += TaxRatio;

                    }

                    mm["TaxValue"] = taxValue;
                    //========================================================//

                }
            }
            //==============Case 3=================//
            foreach (DataRow m in otherTaxes)
            {
                if (Convert.ToInt32(m["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
                {
                    decimal TotalSellPrice = 0;
                    decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
                    var xx = detail["totalTaxesRatio"];
                    if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
                    else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

                    if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
                    else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);
                    TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                    bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);

                    decimal taxValue = calcTaxBeforeDisc ?
                        Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints)
                        : Math.Round(TotalSellPrice * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints);
                    m["TaxValue"] = taxValue;
                    //=================Check DiscountTax or Not================//
                    if (Convert.ToInt32(m["Tax"]) == discountTaxId)
                    {

                        decimal TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
                        totalTaxValue -= taxValue;
                        totalTaxRatio -= TaxRatio;
                    }

                    else
                    {
                        decimal TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
                        totalTaxValue += taxValue;
                        totalTaxRatio += TaxRatio;

                    }
                    m["TaxValue"] = taxValue;



                }
            }
        }
        private void updateSubTaxGrid()
        {
            dt_SubTax.Rows.Clear();
            var dataRows = Dt_Rows.AsEnumerable()
                   .Where(a => a.RowState != DataRowState.Deleted)
                   .GroupBy(r => Convert.ToInt32(r["SubTax"].ToString()))
                   .Select(x =>
                      new
                      {
                          SubTax = x.Key,
                          Percentage = x.Select(y => Convert.ToDouble(y["Percentage"])).Sum(),
                          TaxValue = x.Select(y => Convert.ToDouble(y["TaxValue"])).Sum()
                      })
                   .ToList();
            foreach (var item in dataRows)
            {
                DataRow dTax = dt_SubTax.NewRow();
                dTax["SubTaxId"] = item.SubTax;
                dTax["Rate"] = item.Percentage;
                dTax["Value"] = item.TaxValue;
                dt_SubTax.Rows.Add(dTax);
            }

        }
        private void lkp_Drawers_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void btnCeil_Click(object sender, EventArgs e)
        {
            decimal net = Convert.ToDecimal(txtNet.EditValue);
            if ((net - Math.Truncate(net)) > 0)
                txt_Handing.Value += (1 - (net - Math.Truncate(net)));
            net = Math.Ceiling(net);
            txtNet.EditValue = net;
        }
    }

    public class ItemPosting
    {
        public int catId { get; set; }
        public decimal Price { get; set; }
        public decimal Cost { get; set; }

        public int SellAcc { get; set; }
        public int COGSAcc { get; set; }
        public int InvAcc { get; set; }

        public int SellReturnAcc { get; set; }

        public int PurchaseAcc { get; set; }
        public int OpenInvAcc { get; set; }
        public int CloseInvAcc { get; set; }
    }


}