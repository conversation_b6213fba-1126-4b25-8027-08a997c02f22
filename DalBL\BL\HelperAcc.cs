﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml;
using DAL.Res;

namespace DAL
{
    public static class HelperAcc
    {
        public static List<int> SettingsAccounts;

        public static int AssetsAcc;
        public static int Liabilities_OwnerEquityAcc;
        public static int ExpensesAcc;
        public static int RevenuesAcc;

        public static int AccSecurityLevel = 1;

        static HelperAcc()
        {
            //FixedAssetsAcc = 0;
            //DrawersAcc = 0;
            //BanksAcc = 0;
            //CustomersAcc = 0;
            //NotesReceivableAcc = 0;
            //InventoryAcc = 0;

            //VendorsAcc = 0;
            //CapitalAcc = 0;
            //NotesPayableAcc = 0;
            //SalesTaxAcc = 0;

            //ManufacturingExpAcc = 0;

            //MerchandisingAcc = 0;
            //PurchasesAcc = 0;
            //PurchasesReturnAcc = 0;
            //SalesAcc = 0;
            //SalesReturnAcc = 0;
            //OpenInventoryAcc = 0;
            //CloseInventoryAcc = 0;
            //PurchaseDiscountAcc = 0;
            //SalesDiscount = 0;

            //empLoanMasterAccount = 0;
            //empSalaryExpensesMasterAccount = 0;
            //empAccruedSalaryAccount = 0;
            SettingsAccounts = new List<int>();

            AssetsAcc = 1;
            Liabilities_OwnerEquityAcc = 2;
        }

        //public static int AnonymousCustomer = 13;
        //public static int AnonymousVendor = 16;

        public static List<int> Get_Accounts(out int revenueAcc, out int expensesAcc)
        {
            List<int> SettingsAccounts = new List<int>();
            ERPDataContext DB = new ERPDataContext();
            var st_store = DB.ST_Stores.FirstOrDefault();

            if (st_store.FixedAssets.HasValue)
                SettingsAccounts.Add(st_store.FixedAssets.Value);

            if (st_store.DrawersAcc.HasValue)
                SettingsAccounts.Add(st_store.DrawersAcc.Value);

            if (st_store.BanksAcc.HasValue)
                SettingsAccounts.Add(st_store.BanksAcc.Value);

            if (st_store.CustomersAcc.HasValue)
                SettingsAccounts.Add(st_store.CustomersAcc.Value);

            if (st_store.NotesReceivableAcc.HasValue)
                SettingsAccounts.Add(st_store.NotesReceivableAcc.Value);

            if (st_store.RecieveNotesUnderCollectAccId.HasValue)
                SettingsAccounts.Add(st_store.RecieveNotesUnderCollectAccId.Value);

            if (st_store.InventoryAcc.HasValue)
                SettingsAccounts.Add(st_store.InventoryAcc.Value);

            if (st_store.VendorsAcc.HasValue)
                SettingsAccounts.Add(st_store.VendorsAcc.Value);

            if (st_store.CapitalAcc.HasValue)
                SettingsAccounts.Add(st_store.CapitalAcc.Value);

            if (st_store.NotesPayableAcc.HasValue)
                SettingsAccounts.Add(st_store.NotesPayableAcc.Value);

            if (st_store.DepreciationAcc.HasValue)
                SettingsAccounts.Add(st_store.DepreciationAcc.Value);

            if (st_store.TaxAcc.HasValue)
                SettingsAccounts.Add(st_store.TaxAcc.Value);

            if (st_store.PurchaseDeductTaxAccount.HasValue)
                SettingsAccounts.Add(st_store.PurchaseDeductTaxAccount.Value);

            if (st_store.SalesDeductTaxAccount.HasValue)
                SettingsAccounts.Add(st_store.SalesDeductTaxAccount.Value);

            if (st_store.PurchaseAddTaxAccount.HasValue)
                SettingsAccounts.Add(st_store.PurchaseAddTaxAccount.Value);

            if (st_store.SalesAddTaxAccount.HasValue)
                SettingsAccounts.Add(st_store.SalesAddTaxAccount.Value);

            if (st_store.ManufacturingExpAcc.HasValue)
                SettingsAccounts.Add(st_store.ManufacturingExpAcc.Value);

            //Trade
            if (st_store.MerchandisingAcc.HasValue)
                SettingsAccounts.Add(st_store.MerchandisingAcc.Value);

            if (st_store.PurchasesAcc.HasValue)
                SettingsAccounts.Add(st_store.PurchasesAcc.Value);

            if (st_store.PurchasesReturnAcc.HasValue)
                SettingsAccounts.Add(st_store.PurchasesReturnAcc.Value);

            if (st_store.SalesAcc.HasValue)
                SettingsAccounts.Add(st_store.SalesAcc.Value);

            if (st_store.SalesReturnAcc.HasValue)
                SettingsAccounts.Add(st_store.SalesReturnAcc.Value);

            if (st_store.OpenInventoryAcc.HasValue)
                SettingsAccounts.Add(st_store.OpenInventoryAcc.Value);

            if (st_store.CloseInventoryAcc.HasValue)
                SettingsAccounts.Add(st_store.CloseInventoryAcc.Value);

            if (st_store.PurchaseDiscountAcc.HasValue)
                SettingsAccounts.Add(st_store.PurchaseDiscountAcc.Value);

            if (st_store.SalesDiscountAcc.HasValue)
                SettingsAccounts.Add(st_store.SalesDiscountAcc.Value);

            if (st_store.CostOfSoldGoodsAcc.HasValue)
                SettingsAccounts.Add(st_store.CostOfSoldGoodsAcc.Value);

            //HR
            if (st_store.HrLoansAccount.HasValue)
                SettingsAccounts.Add(st_store.HrLoansAccount.Value);

            if (st_store.HrSalaryExpensesAccount.HasValue)
                SettingsAccounts.Add(st_store.HrSalaryExpensesAccount.Value);

            if (st_store.HrAccruedSalaryAccount.HasValue)
                SettingsAccounts.Add(st_store.HrAccruedSalaryAccount.Value);

            //Debit Credit Notes
            if (st_store.DebitNoteAcc.HasValue)
                SettingsAccounts.Add(st_store.DebitNoteAcc.Value);

            if (st_store.CreditNoteAcc.HasValue)
                SettingsAccounts.Add(st_store.CreditNoteAcc.Value);

            //Other
            if (st_store.LetterOfCreditAcc.HasValue)
                SettingsAccounts.Add(st_store.LetterOfCreditAcc.Value);

            if (st_store.RealStateSellRvnuAcc.HasValue)
                SettingsAccounts.Add(st_store.RealStateSellRvnuAcc.Value);

            //revenue & expenses
            if (st_store.RevenueAcc.HasValue)
            {
                SettingsAccounts.Add(st_store.RevenueAcc.Value);
            }
            if (st_store.ExpensesAcc.HasValue)
            {
                SettingsAccounts.Add(st_store.ExpensesAcc.Value);
            }

            revenueAcc = st_store.RevenueAcc.Value;
            expensesAcc = st_store.ExpensesAcc.Value;


            return SettingsAccounts;
        }


        /// <summary>
        /// load authorized drawers and banks accounts
        /// </summary>
        /// <param name="dtPayAccounts"></param>
        public static int LoadPayAccounts(DataTable dtPayAccounts, bool UserChangeDrawer, int? DefaultDrawer, bool IsEnglish, bool isCustody = false)
        {
            dtPayAccounts.Clear();
            dtPayAccounts.Columns.Clear();
            dtPayAccounts.Columns.Add("AccountId");
            dtPayAccounts.Columns.Add("AccountName");
            dtPayAccounts.Columns.Add("AccountNameEn");

            dtPayAccounts.Columns.Add("CrncId");
            dtPayAccounts.Columns.Add("CrncRate");

            ERPDataContext DB2 = new ERPDataContext();

            if (isCustody)
            {
                List<acc> custodyAcc = LoadAccountsTree(Shared.st_Store.CustodyAcc.Value, true);
                foreach (var d in custodyAcc)
                {
                    dtPayAccounts.Rows.Add(d.AccId,
                        d.AccName, d.AccNameEn);
                }
                if (dtPayAccounts.Rows.Count > 0)
                    return Convert.ToInt32(dtPayAccounts.Rows[0][0]);
                else
                    return 0;
            }
            else
            {

                var DrawerQuery = (from d in DB2.ACC_Drawers
                                   join c in DB2.ACC_Accounts on d.AccountId equals c.AccountId
                                   where c.AccSecurityLevel <= AccSecurityLevel
                                   select d).ToList();

                foreach (var d in DrawerQuery)
                {
                    if (UserChangeDrawer == false && d.AccountId != DefaultDrawer)
                        continue;//don't add unauthorized drawer for user

                    dtPayAccounts.Rows.Add(d.AccountId,
                        d.DrawerNameAr, d.DrawerNameEn);
                }
                var BanksQuery = (from d in DB2.ACC_Banks
                                  join c in DB2.ACC_Accounts on d.AccountId equals c.AccountId
                                  where c.AccSecurityLevel <= AccSecurityLevel
                                  select d).ToList();

                foreach (var d in BanksQuery)
                {
                    dtPayAccounts.Rows.Add(d.AccountId,
                        d.BankName + " " + d.BankAccountNumber, null, d.CrncId, d.CrncRate);
                }

                // add visa
                var visalist = (from vis in DB2.ACC_Visas
                                join c in DB2.ACC_Accounts on vis.VisaAccountId equals c.AccountId
                                where c.AccSecurityLevel <= AccSecurityLevel
                                select new
                                {
                                    AccountName = vis.VisaNameAr,
                                    AccountNameEn = vis.VisaNameEn,
                                    AccountId = vis.VisaAccountId
                                }).ToList();
                if (visalist.Count() > 0)
                {
                    foreach (var d in visalist)
                    {
                        dtPayAccounts.Rows.Add(d.AccountId,
                        d.AccountName, d.AccountNameEn);
                    }
                }

                if (DefaultDrawer.HasValue)
                    return DefaultDrawer.Value;
                else
                {
                    if (dtPayAccounts.Rows.Count > 0)
                        return Convert.ToInt32(dtPayAccounts.Rows[0][0]);
                    else
                        return 0;

                }
            }
        }


        /// <summary>
        /// Load DataTable with Banks
        /// </summary>
        /// <param name="BankDataTable"> Bank DataTable</param>
        /// <returns>return Default Bank or first Bank</returns>
        /// <summary>
        public static int GetBanks(DataTable BankDataTable)
        {
            int DefaultBankAccountId = 0;
            int counter = 0;
            BankDataTable.Columns.Clear();
            BankDataTable.Columns.Add("BankId");
            BankDataTable.Columns.Add("BankName");
            BankDataTable.Columns.Add("BranchName");
            BankDataTable.Columns.Add("BankAccountNumber");
            BankDataTable.Columns.Add("AccountId");
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var BanksQuery = (from d in DB.ACC_Banks
                              join c in DB.ACC_Accounts on d.AccountId equals c.AccountId
                              where c.AccSecurityLevel <= AccSecurityLevel
                              select d).ToList();

            foreach (var d in BanksQuery)
            {
                if (counter == 0)// get first Bank Id
                    DefaultBankAccountId = d.AccountId;

                BankDataTable.Rows.Add(d.BankId,
                    d.BankName,
                    d.BranchName,
                    d.BankAccountNumber,
                    d.AccountId);

                counter += 1;
            }

            return DefaultBankAccountId;
        }

        /// <summary>
        /// method return account_id for vendor by vedor_id and create Account if he has no Account
        /// </summary>
        /// <param name="Vendor_Id"></param>
        /// <returns></returns>
        public static int Get_VendorAccount_Id(int Vendor_Id, int groupAccountId, int? VendorsAcc)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var vendor = DB.PR_Vendors.Where(x => x.VendorId == Vendor_Id).FirstOrDefault();
            if (vendor != null && vendor.AccountId != null)
                return vendor.AccountId.Value;

            ACC_Account acc = new ACC_Account();
            acc.AcNameAr = vendor.VenNameAr.Trim();//"مورد"
            acc.AcNameEn = vendor.VenNameEn.Trim();
            acc.AcType = true;
            acc.AllowChild = false;
            acc.AllowEdit = false;
            acc.Notes = Shared.IsEnglish ? ResEn.accountCreationBy : ResAr.accountCreationBy;//"تم انشاء الحساب بواسطة النظام"
            acc.AccSecurityLevel = 1;   //default

            if (groupAccountId == 0)
                groupAccountId = VendorsAcc.Value;

            var parentAcc = DB.ACC_Accounts.Where(x => x.AccountId == groupAccountId).FirstOrDefault();
            acc.ParentActId = parentAcc.AccountId;
            acc.Level = parentAcc.Level + 1;
            acc.AcNumber = HelperAcc.AccNumGenerated(parentAcc);

            if (acc.AcNumber == "-1")
            {
                //XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.MsgAccLevel : ResAccAr.MsgAccLevel
                //  , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return -1;
            }

            DB.ACC_Accounts.InsertOnSubmit(acc);
            DB.SubmitChanges();

            vendor.AccountId = acc.AccountId;
            DB.SubmitChanges();

            return acc.AccountId;
        }

        /// <summary>
        /// method return OpenBalance of Vendor
        /// </summary>
        /// <param name="Vendor_Id"></param>
        /// <returns></returns>
        public static decimal Get_Vendor_OpenBalance(int Vendor_Id)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var vendor = DB.PR_Vendors.Where(x => x.VendorId == Vendor_Id).FirstOrDefault();
            if (vendor.AccountId == null)
                return 0;

            var openbalance = (from jd in DB.ACC_JournalDetails
                               join j in DB.ACC_Journals on jd.JournalId equals j.JournalId
                               where jd.AccountId == vendor.AccountId.Value
                               && j.ProcessId == (int)Process.OpenBalance
                               select jd).FirstOrDefault();

            if (openbalance == null)
                return 0;
            else
                return (openbalance.Credit - openbalance.Debit);

            /*Positive value credit Negative value depit*/
        }

        /// <summary>
        /// method Set OpenBalance of Vendor
        /// </summary>
        /// <param name="Vendor_Id"></param>
        /// <param name="credit"></param>
        /// <param name="depit"></param>
        public static void Set_Vendor_OpenBalance(int accId, decimal amount, DateTime? openDate, bool? isCredit, int? VendorsAcc,
            bool OfflinePostToGL, ST_Store st_Store)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var openbalance = (from jd in DB.ACC_JournalDetails
                               join j in DB.ACC_Journals on jd.JournalId equals j.JournalId
                               where jd.AccountId == accId
                               && j.ProcessId == (int)Process.OpenBalance
                               select jd).FirstOrDefault();

            decimal debit = 0, credit = 0;
            if (isCredit.HasValue && isCredit.Value)
            {
                credit = amount;
                debit = 0;
            }
            else if (isCredit.HasValue && isCredit.Value == false)
            {
                debit = amount;
                credit = 0;
            }

            #region Vendor_has_no_open_Balance
            if (openbalance == null && amount > 0)
            {
                ACC_Journal jornal = new ACC_Journal();
                jornal.InsertUser = Shared.UserId;
                jornal.InsertDate = openDate.HasValue ? openDate.Value : MyHelper.Get_Server_DateTime();
                jornal.JCode = Get_Jornal_Code(); ;
                jornal.JNotes = Shared.IsEnglish ? ResEn.VendOpen : ResAr.VendOpen; //"رصيد افتتاحي مورد"
                jornal.ProcessId = (int)Process.OpenBalance;
                jornal.SourceId = 0;
                jornal.IsPosted = !OfflinePostToGL;
                jornal.StoreId = 1;
                jornal.CrncId = 0;
                jornal.CrncRate = 1;
                DB.ACC_Journals.InsertOnSubmit(jornal);
                DB.SubmitChanges();

                /*من حساب رأس المال */
                ACC_JournalDetail jornal_detail_Capital = new ACC_JournalDetail();
                jornal_detail_Capital.AccountId = st_Store.CapitalAcc.Value;       //رأس المال
                jornal_detail_Capital.Credit = debit;
                jornal_detail_Capital.Debit = credit;
                jornal_detail_Capital.JournalId = jornal.JournalId;
                jornal_detail_Capital.Notes = Shared.IsEnglish ? ResEn.VendOpen : ResAr.VendOpen; //"رصيد افتتاحي مورد"
                jornal_detail_Capital.CrncId = 0;
                jornal_detail_Capital.CrncRate = 1;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_detail_Capital);
                DB.SubmitChanges();

                /*الى حساب المال*/
                ACC_JournalDetail jornal_detail_vendor = new ACC_JournalDetail();
                jornal_detail_vendor.AccountId = accId;
                jornal_detail_vendor.Credit = credit;
                jornal_detail_vendor.Debit = debit;
                jornal_detail_vendor.JournalId = jornal.JournalId;
                jornal_detail_vendor.Notes = Shared.IsEnglish ? ResEn.VendOpen : ResAr.VendOpen; //"رصيد افتتاحي مورد"
                jornal_detail_vendor.CrncId = 0;
                jornal_detail_vendor.CrncRate = 1;
                jornal_detail_vendor.DueDate = jornal.InsertDate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_detail_vendor);
                DB.SubmitChanges();
                return;
            }
            #endregion

            #region Vendor_has_open_Balance
            if (openbalance != null)
            {
                if (amount > 0)
                {
                    ACC_Journal jornal = DB.ACC_Journals.Where(x => x.JournalId == openbalance.JournalId).FirstOrDefault();
                    jornal.InsertDate = openDate.HasValue ? openDate.Value : MyHelper.Get_Server_DateTime();
                    jornal.LastUpdateUser = Shared.UserId;
                    jornal.LastUpdateDate = MyHelper.Get_Server_DateTime();
                    DB.SubmitChanges();

                    /*من حساب رأس المال */
                    ACC_JournalDetail jornal_detail_Capital = DB.ACC_JournalDetails.Where(x => x.JournalId == jornal.JournalId && x.AccountId ==
                        st_Store.CapitalAcc.Value).FirstOrDefault();
                    jornal_detail_Capital.Credit = debit;
                    jornal_detail_Capital.Debit = credit;
                    DB.SubmitChanges();

                    /*الى حساب المال*/
                    ACC_JournalDetail jornal_detail_vendor = DB.ACC_JournalDetails.Where(x => x.JDetailId == openbalance.JDetailId).FirstOrDefault();
                    jornal_detail_vendor.Credit = credit;
                    jornal_detail_vendor.Debit = debit;
                    jornal_detail_vendor.DueDate = jornal.InsertDate;

                    DB.SubmitChanges();
                }
                else
                {
                    var jr = DB.ACC_Journals.Where(x => x.JournalId == openbalance.JournalId).FirstOrDefault();
                    var jd1 = DB.ACC_JournalDetails.Where(x => x.JDetailId == openbalance.JDetailId).FirstOrDefault();
                    var jd2 = DB.ACC_JournalDetails.Where(x => x.JournalId == jr.JournalId && x.AccountId ==
                        st_Store.CapitalAcc.Value).FirstOrDefault();
                    DB.ACC_JournalDetails.DeleteOnSubmit(jd1);
                    DB.ACC_JournalDetails.DeleteOnSubmit(jd2);
                    DB.ACC_Journals.DeleteOnSubmit(jr);
                    DB.SubmitChanges();
                }
                return;
            }
            #endregion
        }


        /// <summary>
        /// method Set OpenBalance of Customer
        /// </summary>
        /// <param name="Customer_Id"></param>
        /// <param name="credit"></param>
        /// <param name="depit"></param>
        public static void Set_Customer_OpenBalance(int AccId, decimal amount, DateTime? openDate, bool? isCredit,
            bool OfflinePostToGL, int? CapitalAcc, int? CustomersAcc)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var openbalance = (from jd in DB.ACC_JournalDetails
                               join j in DB.ACC_Journals on jd.JournalId equals j.JournalId
                               where jd.AccountId == AccId
                               && j.ProcessId == (int)Process.OpenBalance
                               select jd).FirstOrDefault();

            decimal debit = 0, credit = 0;
            if (isCredit.HasValue && isCredit.Value)
            {
                credit = amount;
                debit = 0;
            }
            else if (isCredit.HasValue && isCredit.Value == false)
            {
                debit = amount;
                credit = 0;
            }

            #region Customer_has_no_open_Balance
            if (openbalance == null && amount > 0)
            {
                ACC_Journal jornal = new ACC_Journal();
                jornal.InsertUser = Shared.UserId;
                jornal.InsertDate = openDate.HasValue ? openDate.Value : MyHelper.Get_Server_DateTime();
                jornal.JCode = Get_Jornal_Code();
                jornal.JNotes = Shared.IsEnglish ? ResEn.custOpen : ResAr.custOpen; //"رصيد افتتاحي عميل"
                jornal.ProcessId = (int)Process.OpenBalance;
                jornal.SourceId = 0;
                jornal.IsPosted = !OfflinePostToGL;
                jornal.StoreId = 1;
                jornal.CrncId = 0;
                jornal.CrncRate = 1;
                DB.ACC_Journals.InsertOnSubmit(jornal);
                DB.SubmitChanges();

                /*من حساب العميل */
                ACC_JournalDetail jornal_detail_Capital = new ACC_JournalDetail();
                jornal_detail_Capital.AccountId = AccId;
                jornal_detail_Capital.Credit = credit;
                jornal_detail_Capital.Debit = debit;
                jornal_detail_Capital.JournalId = jornal.JournalId;
                jornal_detail_Capital.Notes = Shared.IsEnglish ? ResEn.custOpen : ResAr.custOpen; //"رصيد افتتاحي عميل"
                jornal_detail_Capital.CrncId = 0;
                jornal_detail_Capital.CrncRate = 1;
                jornal_detail_Capital.DueDate = jornal.InsertDate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_detail_Capital);
                DB.SubmitChanges();

                /*الى حساب رأس المال*/
                ACC_JournalDetail jornal_detail_vendor = new ACC_JournalDetail();
                jornal_detail_vendor.AccountId = CapitalAcc.Value;       //رأس المال
                jornal_detail_vendor.Credit = debit;
                jornal_detail_vendor.Debit = credit;
                jornal_detail_vendor.JournalId = jornal.JournalId;
                jornal_detail_vendor.Notes = Shared.IsEnglish ? ResEn.custOpen : ResAr.custOpen; //"رصيد افتتاحي عميل"
                jornal_detail_vendor.CrncId = 0;
                jornal_detail_vendor.CrncRate = 1;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_detail_vendor);
                DB.SubmitChanges();
                return;
            }
            #endregion

            #region Customer_has_open_Balance
            if (openbalance != null)
            {
                if (amount > 0)
                {
                    ACC_Journal jornal = DB.ACC_Journals.Where(x => x.JournalId == openbalance.JournalId).FirstOrDefault();
                    jornal.InsertDate = openDate.HasValue ? openDate.Value : MyHelper.Get_Server_DateTime();
                    jornal.LastUpdateUser = Shared.UserId;
                    jornal.LastUpdateDate = MyHelper.Get_Server_DateTime();
                    DB.SubmitChanges();

                    /*من حساب العميل*/
                    ACC_JournalDetail jornal_detail_vendor = DB.ACC_JournalDetails.Where(x => x.JDetailId == openbalance.JDetailId).FirstOrDefault();
                    jornal_detail_vendor.Credit = credit;
                    jornal_detail_vendor.Debit = debit;
                    jornal_detail_vendor.DueDate = jornal.InsertDate;

                    DB.SubmitChanges();

                    /*الى حساب رأس المال */
                    ACC_JournalDetail jornal_detail_Capital = DB.ACC_JournalDetails.Where(x => x.JournalId == jornal.JournalId && x.AccountId == CapitalAcc.Value).FirstOrDefault();
                    jornal_detail_Capital.Credit = debit;
                    jornal_detail_Capital.Debit = credit;
                    DB.SubmitChanges();
                }
                else
                {
                    var jr = DB.ACC_Journals.Where(x => x.JournalId == openbalance.JournalId).FirstOrDefault();
                    var jd1 = DB.ACC_JournalDetails.Where(x => x.JDetailId == openbalance.JDetailId).FirstOrDefault();
                    var jd2 = DB.ACC_JournalDetails.Where(x => x.JournalId == jr.JournalId && x.AccountId == CapitalAcc.Value).FirstOrDefault();
                    DB.ACC_JournalDetails.DeleteOnSubmit(jd1);
                    DB.ACC_JournalDetails.DeleteOnSubmit(jd2);
                    DB.ACC_Journals.DeleteOnSubmit(jr);
                    DB.SubmitChanges();
                }
                return;
            }
            #endregion
        }

        /// <summary>
        /// method return account_id for Customer by Customer_id and create Account if he has no Account
        /// </summary>
        /// <param name="Customer_Id"></param>
        /// <returns></returns>
        public static int Get_CustomerAccount_Id(int Customer_Id, int groupAccountId, int? CustomersAcc)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var cutomer = DB.SL_Customers.Where(x => x.CustomerId == Customer_Id).FirstOrDefault();
            if (cutomer != null && cutomer.AccountId != null)
                return cutomer.AccountId.Value;

            ACC_Account acc = new ACC_Account();
            acc.AcNameAr = cutomer.CusNameAr.Trim();//"عميل"
            acc.AcNameEn = cutomer.CusNameEn.Trim();
            acc.AcType = false;
            acc.AllowChild = false;
            acc.AllowEdit = false;
            acc.Notes = Shared.IsEnglish ? ResEn.accountCreationBy : ResAr.accountCreationBy;//"تم انشاء الحساب بواسطة النظام"
            acc.AccSecurityLevel = 1;   //default

            if (groupAccountId == 0)
                groupAccountId = CustomersAcc.Value;

            var parentAcc = DB.ACC_Accounts.Where(x => x.AccountId == groupAccountId).FirstOrDefault();
            acc.ParentActId = parentAcc.AccountId;
            acc.Level = parentAcc.Level + 1;
            acc.AcNumber = HelperAcc.AccNumGenerated(parentAcc);

            if (acc.AcNumber == "-1")
            {
                //XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.MsgAccLevel : ResAccAr.MsgAccLevel
                //  , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return -1;
            }

            DB.ACC_Accounts.InsertOnSubmit(acc);
            DB.SubmitChanges();

            cutomer.AccountId = acc.AccountId;
            DB.SubmitChanges();

            return acc.AccountId;
        }

        /// <summary>
        /// method return OpenBalance of Customer
        /// </summary>
        /// <param name="Customer_Id"></param>
        /// <returns></returns>
        public static decimal Get_Customer_OpenBalance(int Customer_Id)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var customer = DB.SL_Customers.Where(x => x.CustomerId == Customer_Id).FirstOrDefault();
            if (customer.AccountId == null)
                return 0;

            var openbalance = (from jd in DB.ACC_JournalDetails
                               join j in DB.ACC_Journals on jd.JournalId equals j.JournalId
                               where jd.AccountId == customer.AccountId.Value
                               && j.ProcessId == (int)Process.OpenBalance
                               select jd).FirstOrDefault();

            if (openbalance == null)
                return 0;
            else
                return (openbalance.Credit - openbalance.Debit);

            /*Positive value credit Negative value depit*/
        }

        /// <summary>
        /// Get Account Open Balance
        /// </summary>
        /// <param name="AccountId"></param>
        /// <returns></returns>
        public static decimal Get_Account_OpenBalance(int AccountId, out DateTime? openBlncDate)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var openbalance = (from jd in DB.ACC_JournalDetails
                               join j in DB.ACC_Journals on jd.JournalId equals j.JournalId
                               where jd.AccountId == AccountId
                               && j.ProcessId == (int)Process.OpenBalance
                               select new
                               {
                                   j.InsertDate,
                                   jd.Credit,
                                   jd.Debit
                               }).FirstOrDefault();

            if (openbalance == null)
            {
                openBlncDate = (DateTime?)null;
                return 0;
            }
            else
            {
                openBlncDate = openbalance.InsertDate;
                return (openbalance.Credit - openbalance.Debit);
            }

            /*Positive value credit Negative value depit*/
        }


        /// <summary>
        /// method Get Next Jornal Code by today
        /// </summary>
        /// <param name="today"></param>
        /// <returns></returns>
        public static int Get_Jornal_Code()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            return (from j in DB.ACC_Journals
                    select j.JCode).ToList().DefaultIfEmpty(0).Max() + 1;
        }

        public static string Get_Jornal_Monthly_Code(DateTime Journal_Date, int ProcessId)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var lastSerial = (from j in DB.ACC_Journals
                              where j.ProcessId == ProcessId
                              where j.InsertDate.Month == Journal_Date.Month
                              //orderby j.InsertDate ascending
                              orderby j.JournalId descending
                              select j.Monthly_Code).FirstOrDefault();
            return MyHelper.GetNextNumberInString(lastSerial, Journal_Date.Month);
        }

        /// <summary>
        /// Get Account balance positive value to be creditor negative debitor
        /// </summary>
        /// <param name="account_Id"></param>
        /// <returns>Balance</returns>
        public static decimal Get_account_balance(int account_Id)
        {
            ERPDataContext DB = new ERPDataContext();

            var balance = (from d in DB.ACC_JournalDetails
                           join a in DB.ACC_Accounts on d.AccountId equals a.AccountId
                           where d.AccountId == account_Id
                           && a.AccSecurityLevel <= AccSecurityLevel
                           select (d.Credit - d.Debit) * d.CrncRate).ToList().DefaultIfEmpty(0).Sum();

            return balance;
        }

        /// <summary>
        /// Get Visa Details
        /// </summary>
        /// <param name="account_Id"></param>
        /// <returns>Visa</returns>
        public static ACC_Visa Get_Visa(int account_Id)
        {
            ERPDataContext DB = new ERPDataContext();

            return DB.ACC_Visas.Where(x => x.VisaAccountId == account_Id).FirstOrDefault();
        }

        public static decimal Get_account_balance(string accNumber)
        {
            ERPDataContext DB = new ERPDataContext();

            var balance = (from d in DB.ACC_JournalDetails
                           join a in DB.ACC_Accounts on d.AccountId equals a.AccountId
                           where a.AcNumber.StartsWith(accNumber)
                           && a.AccSecurityLevel <= AccSecurityLevel
                           select (d.Credit - d.Debit) * d.CrncRate).ToList().DefaultIfEmpty(0).Sum();

            return balance;
        }

        public static decimal Get_account_balance(int account_Id, DateTime? start_Date, DateTime? end_Date)
        {
            ERPDataContext DB = new ERPDataContext();

            var balance = (from d in DB.ACC_JournalDetails
                           join a in DB.ACC_Accounts on d.AccountId equals a.AccountId
                           where d.AccountId == account_Id
                           && a.AccSecurityLevel <= AccSecurityLevel
                           join j in DB.ACC_Journals on d.JournalId equals j.JournalId
                           where start_Date == null ? true : j.InsertDate >= start_Date
                           where end_Date == null ? true : j.InsertDate <= end_Date
                           select (d.Credit - d.Debit) * d.CrncRate).ToList().DefaultIfEmpty(0).Sum();

            return balance;
        }

        public static decimal Get_account_balance(string accNumber, DateTime? start_Date, DateTime? end_Date)
        {
            ERPDataContext DB = new ERPDataContext();

            var balance = (from d in DB.ACC_JournalDetails
                           join a in DB.ACC_Accounts on d.AccountId equals a.AccountId
                           where a.AcNumber.StartsWith(accNumber)
                           && a.AccSecurityLevel <= AccSecurityLevel
                           join j in DB.ACC_Journals on d.JournalId equals j.JournalId
                           where start_Date == null ? true : j.InsertDate.Date >= start_Date
                           where end_Date == null ? true : j.InsertDate.Date <= end_Date
                           select (d.Credit - d.Debit) * d.CrncRate).ToList().DefaultIfEmpty(0).Sum();

            return balance;
        }

        public static decimal Get_Parent_Account_Total_Balance(int account_Id, DateTime? start_Date, DateTime? end_Date)
        {
            ERPDataContext DB = new ERPDataContext();
            string accNumber = DB.ACC_Accounts.Where(x => x.AccountId == account_Id).Select(x => x.AcNumber).FirstOrDefault();

            var balance = (from d in DB.ACC_JournalDetails
                           join a in DB.ACC_Accounts on d.AccountId equals a.AccountId
                           where a.AcNumber.StartsWith(accNumber)
                           join j in DB.ACC_Journals on d.JournalId equals j.JournalId
                           where start_Date == null ? true : j.InsertDate.Date >= start_Date
                           where end_Date == null ? true : j.InsertDate.Date <= end_Date
                           select (d.Credit - d.Debit) * d.CrncRate).ToList().DefaultIfEmpty(0).Sum();

            return balance;
        }

        //update 24/10/2017
        public static decimal Get_Parent_Account_Total_Balance_Archive(int account_Id, DateTime? start_Date, DateTime? end_Date)
        {
            ERPDataContext DB = new ERPDataContext();
            string accNumber = DB.ACC_Accounts.Where(x => x.AccountId == account_Id).Select(x => x.AcNumber).FirstOrDefault();

            var balance = (from d in DB.ACC_JournalDetailArchives
                           join a in DB.ACC_Accounts on d.AccountId equals a.AccountId
                           where a.AcNumber.StartsWith(accNumber)
                           join j in DB.ACC_JournalArchives on d.JournalId equals j.JournalArchiveId
                           where start_Date == null ? true : j.InsertDate.Date >= start_Date
                           where end_Date == null ? true : j.InsertDate.Date <= end_Date
                           select (d.Credit - d.Debit) * d.CrncRate).ToList().DefaultIfEmpty(0).Sum();

            return balance;
        }


        public static decimal Get_TotalDebit(int account_Id)
        {
            ERPDataContext DB = new ERPDataContext();

            var balance = (from d in DB.ACC_JournalDetails
                           where d.AccountId == account_Id
                           where d.Debit > 0
                           select d.Debit * d.CrncRate).ToList().DefaultIfEmpty(0).Sum();

            return balance;

        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <param name="expenses">Total Expenses during period</param>
        /// <param name="revenues">Total revenues during period</param>
        /// <param name="profit">total Profit "merchandise profit", if value is in minus, means loss</param>
        /// <param name="netProfit">net Profit "income profit", if value is in minus, means loss</param>
        /// <returns>datatable of summarized income statement</returns>
        public static DataTable GetIncomeSheet(DateTime fromDate, DateTime toDate, out decimal expenses, out decimal revenues,
            out decimal profit, out decimal netProfit, ST_Store st_Store)
        {

            DataTable dtStatement = new DataTable();
            dtStatement.Columns.Add("Name");
            dtStatement.Columns.Add("Value");

            //1.get accounts tree
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var Account = DB.ACC_Accounts.OrderBy(x => x.AcNumber).Select(x => x).ToList();

            //2.get needed accounts accounts balances

            decimal Sales = 0, SalesReturn = 0, SalesDiscount = 0, purchases = 0, purchasesReturn = 0, purchaseDiscount = 0,
                outCloseValue = 0, outOpenValue = 0;
            //DataTable dt = new DataTable();

            if (st_Store.MerchandisingAcc.HasValue &&
                st_Store.SalesAcc.HasValue &&
                st_Store.SalesReturnAcc.HasValue &&
                st_Store.PurchasesAcc.HasValue &&
                st_Store.PurchasesReturnAcc.HasValue &&
                st_Store.OpenInventoryAcc.HasValue &&
                st_Store.OpenInventoryAcc.HasValue &&
                st_Store.SalesDiscountAcc.HasValue &&
                st_Store.PurchaseDiscountAcc.HasValue)
            {

                var dakhlAccounts = (from a in DB.ACC_Accounts
                                     join d in DB.ACC_JournalDetails
                                     on a.AccountId equals d.AccountId

                                     join j in DB.ACC_Journals.Where(j => j.IsPosted)
                                     on d.JournalId equals j.JournalId

                                     where
                                     (a.AccountId == st_Store.MerchandisingAcc.Value
                                     || a.ParentActId == st_Store.MerchandisingAcc.Value
                                     || a.ParentActId == st_Store.SalesAcc.Value
                                     || a.ParentActId == st_Store.SalesReturnAcc.Value
                                     || a.ParentActId == st_Store.PurchasesAcc.Value
                                     || a.ParentActId == st_Store.PurchasesReturnAcc.Value

                                     || a.AccountId == st_Store.OpenInventoryAcc.Value
                                     || a.ParentActId == st_Store.OpenInventoryAcc.Value

                                     || a.ParentActId == st_Store.SalesDiscountAcc.Value
                                     || a.ParentActId == st_Store.PurchaseDiscountAcc.Value)

                                     //&& costCenterId == 0 ? true : d.CostCenter == costCenterId
                                      && j.InsertDate >= fromDate
                                      && j.InsertDate <= toDate
                                     group d by d.AccountId into grp
                                     join x in DB.ACC_Accounts
                                     on grp.Key equals x.AccountId

                                     select new
                                     {
                                         AccountId = grp.Key,
                                         x.AcNameAr,
                                         x.ParentActId,
                                         Debit = grp.Where(d => d.Debit != 0).Count() > 0 ?
                                         grp.Sum(d => d.Debit) : 0,
                                         Credit = grp.Where(d => d.Credit != 0).Count() > 0 ?
                                         grp.Sum(d => d.Credit) : 0
                                     }).ToList();

                //sales, and its details
                Sales = (from a in dakhlAccounts
                         where a.AccountId == st_Store.SalesAcc.Value
                         || a.ParentActId == st_Store.SalesAcc.Value
                         select new
                         {
                             Balance = a.Credit - a.Debit
                         }.Balance).Sum();

                //sales returns, and its details
                SalesReturn = (from a in dakhlAccounts
                               where a.AccountId == st_Store.SalesReturnAcc.Value
                               || a.ParentActId == st_Store.SalesReturnAcc.Value
                               select new
                               {
                                   Balance = a.Debit - a.Credit
                               }.Balance).Sum();

                //sales discount, and its details
                SalesDiscount = (from a in dakhlAccounts
                                 where a.AccountId == st_Store.SalesDiscountAcc.Value
                                 select new
                                 {
                                     Balance = a.Debit - a.Credit
                                 }.Balance).Sum();

                //purchases, and its details
                purchases = (from a in dakhlAccounts
                             where a.AccountId == st_Store.PurchasesAcc.Value
                               || a.ParentActId == st_Store.PurchasesAcc.Value
                             select new
                             {
                                 Balance = a.Debit - a.Credit
                             }.Balance).Sum();

                //purchases returns, and its details
                purchasesReturn = (from a in dakhlAccounts
                                   where a.AccountId == st_Store.PurchasesReturnAcc.Value
                                     || a.ParentActId == st_Store.PurchasesReturnAcc.Value
                                   select new
                                   {
                                       Balance = a.Credit - a.Debit
                                   }.Balance).Sum();

                //purchase discount, and its details
                purchaseDiscount = (from a in dakhlAccounts
                                    where a.AccountId == st_Store.PurchaseDiscountAcc
                                    select new
                                    {
                                        Balance = a.Credit - a.Debit
                                    }.Balance).Sum();

                //Get Open & Close Inventory                        
                //dt = MyHelper.GetCloseInventory(fromDate, toDate, out outCloseValue, out outOpenValue, 0);
            }

            decimal salesNet = Sales - SalesReturn - SalesDiscount;
            if (Shared.IsEnglish)
                dtStatement.Rows.Add(" : Net Sales" + "\r\n" +
                    " Sales - " + "\r\n" +
                    " Sales Returns - " + "\r\n" +
                    " Sales Discount"
                    , decimal.ToDouble(salesNet));
            else
                dtStatement.Rows.Add(" : صافي المبيعات" + "\r\n" +
                    " - المبيعات" + "\r\n" +
                    " - مردود المبيعات" + "\r\n" +
                    " الخصم المسموح به"
                    , decimal.ToDouble(salesNet));

            decimal purchaseNet = outOpenValue + purchases - purchasesReturn - purchaseDiscount - outCloseValue;
            if (Shared.IsEnglish)
                dtStatement.Rows.Add(
                @"Cost Of Sold Items" + "\r\n" +
                "Open Balance + " + "\r\n" +
                " Purchases - " + "\r\n" +
                " Purchases Returns - " + "\r\n" +
                "Purchase Disount +" + "\r\n" +

                "Purchases Expenses -" + "\r\n" +
                "Close Inventory"
                , decimal.ToDouble(purchaseNet));
            else
                dtStatement.Rows.Add(
                @" : تكلفة البضاعه المباعة" + "\r\n" +
                " + بضاعة أول المدة" + "\r\n" +
                " - المشتريات" + "\r\n" +
                " - مردود المشتريات" + "\r\n" +
                " + الخصم المكتسب" + "\r\n" +

                " - مصروفات الشراء" + "\r\n" +
                " بضاعة اخر المدة"
                , decimal.ToDouble(purchaseNet));

            //expenses, and its details
            expenses = HelperAcc.Get_Parent_Account_Total_Balance(HelperAcc.ExpensesAcc, fromDate, toDate) * -1;

            //revenues, and its details
            revenues = HelperAcc.Get_Parent_Account_Total_Balance(HelperAcc.RevenuesAcc, fromDate, toDate);

            profit = salesNet - purchaseNet;
            netProfit = profit + revenues - expenses;

            dtStatement.Rows.Add(Shared.IsEnglish ? "Total Profit" : "مجمل الربح", decimal.ToDouble(profit));
            dtStatement.Rows.Add(Shared.IsEnglish ? "Expenses" : "المصروفات", decimal.ToDouble(expenses));
            dtStatement.Rows.Add(Shared.IsEnglish ? "Revenue" : "الإيرادات", decimal.ToDouble(revenues));
            dtStatement.Rows.Add(Shared.IsEnglish ? "Net Income (Profit/Loss of year)" : "(صافي الدخل (ربح/خسائر السنة", decimal.ToDouble(netProfit));

            return dtStatement;
        }


        /// <summary>
        /// Check if account has childs accounts
        /// </summary>
        /// <param name="account_Id"></param>
        /// <returns></returns>
        public static bool Is_AccountHasChilds(int account_Id)
        {
            ERPDataContext DB = new ERPDataContext();

            if (DB.ACC_Accounts.Where(x => x.ParentActId == account_Id).Count() == 0)
                return false;
            return true;
        }

        /// <summary>
        /// Load DataTable with Revenue Accounts or Expenses Accounts
        /// </summary>
        /// <param name="dtRevExp"></param>
        /// <param name="isRevenue"></param>
        //public static int GetRevExpAccounts(DataTable dtRevExp, bool isRevenue)
        //{
        //    int counter = 0;
        //    int firstAccount = 0;
        //    dtRevExp.Columns.Clear();
        //    dtRevExp.Columns.Add("AccountId");
        //    dtRevExp.Columns.Add("AcNameAr");
        //    dtRevExp.Columns.Add("AcNameEn");
        //    dtRevExp.Columns.Add("Notes");
        //    dtRevExp.Columns.Add("CostCenter");
        //    dtRevExp.Rows.Clear();
        //    DAL.ERPDataContext DB = new Pharmacy.DAL.ERPDataContext();
        //    var accountsQuery = (from a in DB.ACC_Accounts
        //                         where isRevenue == true ? a.ParentActId == 5 : a.ParentActId == 4
        //                         select a).ToList();
        //    foreach (var d in accountsQuery)
        //    {
        //        if (counter == 0)// get first drawer Id
        //            firstAccount = d.AccountId;

        //        dtRevExp.Rows.Add(d.AccountId,
        //            d.AcNameAr,
        //            d.AcNameEn,
        //            d.Notes,
        //            d.CostCenter);

        //        counter += 1;
        //    }
        //    return firstAccount;
        //}

        public static List<ACC_CostCenter> GetCostCentersLst(bool childsOnly)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            List<ACC_CostCenter> cstCntrs = DB.ACC_CostCenters.Select(d => d).OrderBy(d => d.ccNumber).ToList();

            List<ACC_CostCenter> lst = new List<ACC_CostCenter>();

            foreach (var d in cstCntrs)
            {
                if (childsOnly)
                {
                    if (cstCntrs.Where(c => c.ParentCCId == d.CostCenterId).Count() > 0)
                        continue;
                }

                lst.Add(
                    new ACC_CostCenter
                    {
                        CostCenterId = d.CostCenterId,
                        ccNumber = d.ccNumber,
                        CostCenterName = d.ParentCCId.HasValue ?
                        cstCntrs.Where(c => c.CostCenterId == d.ParentCCId).First().CostCenterName + " - " + d.CostCenterName
                        : d.CostCenterName,

                        CostCenterNameEn = d.CostCenterNameEn,
                        Level = d.Level,
                        Notes = d.Notes,
                        ParentCCId = d.ParentCCId,
                        CostCenterCode = d.CostCenterCode
                    });
            }

            lst.Insert(0, new ACC_CostCenter
            {
                CostCenterId = 0,
                ccNumber = "",
                CostCenterName = "",
                CostCenterNameEn = "",
                Level = 0,
                Notes = "",
                ParentCCId = 0,
                CostCenterCode = "0",
            });

            return lst;
        }

        /// <summary>
        /// Load DataTable with Accounts Custom Lists
        /// </summary>
        /// <param name="CostCentersDataTable"> Accounts Custom Lists DataTable</param>
        /// <returns>datatable of Accounts Custom Lists </returns>
        /// <summary>
        public static DataTable GetCustomAccountsLists()
        {
            DataTable CustomListsDt = new DataTable();
            CustomListsDt.Columns.Clear();
            CustomListsDt.Columns.Add("CustomAccListId");
            CustomListsDt.Columns.Add("CustomAccListCode");
            CustomListsDt.Columns.Add("CustomAccListName");
            CustomListsDt.Columns.Add("CustomAccListNameEn");

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            CustomListsDt.Rows.Add(-1,
                        -1,
                        string.Empty,
                        string.Empty);

            var cList = DB.Acc_CustomAccLists.Select(d => d).ToList();
            if (cList.Count > 0)
            {
                foreach (var d in cList)
                {
                    CustomListsDt.Rows.Add(d.CustomAccListId,
                        d.CustomAccListCode,
                        d.CustomAccListName,
                        d.CustomAccListNameEn);
                }
            }
            return CustomListsDt;
        }


        #region Show Money in English Words

        static string[] Tens = new string[] { "Ten", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety" };
        static string[] Ones = new string[] { "One","Two","Three","Four","Five","Six","Seven","Eight","Nine","Ten","Eleven","Twelve",
                                              "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen" };

        public static string ConvertMoneyToText(string value, int currencyId, List<ST_Currency> lstCurrency)
        {
            var curr = lstCurrency.Where(x => x.CrncId == currencyId).FirstOrDefault();

            value = value.Replace(",", "").Replace("$", "");
            int decimalCount = 0;
            int Val = value.Length - 1;
            for (int x = 0; x <= Val; x++)
            {
                char Val2 = value[x];
                if (Val2.ToString() == ".")
                {
                    decimalCount++;
                    if (decimalCount > 1)
                    {
                        throw new ArgumentException("Only monetary values are accepted");
                    }
                }
                Val2 = value[x];
                char Valtemp = value[x];
                if (!(char.IsDigit(value[x]) | (Val2.ToString() == ".")) & !((x == 0) & (Valtemp.ToString() == "-")))
                {
                    throw new ArgumentException("Only monetary values are accepted");
                }
            }
            string returnValue = "";
            string[] parts;
            if (value.Contains("."))
                parts = value.Split(new char[] { '.' });
            else
                parts = (value + ".00").Split(new char[] { '.' });


            parts[1] = new string((parts[1] + "00").Substring(0, 2).ToCharArray());
            bool IsNegative = parts[0].Contains("-");
            if (parts[0].Replace("-", "").Length > 0x12)
            {
                throw new ArgumentException("Maximum value is $999,999,999,999,999,999.99");
            }
            if (IsNegative)
            {
                parts[0] = parts[0].Replace("-", "");
                returnValue = returnValue + "Minus ";
            }
            if (parts[0].Length > 15)
            {
                returnValue = ((((returnValue + HundredsText(parts[0].PadLeft(0x12, '0').Substring(0, 3)) + "Quadrillion ")
                    + HundredsText(parts[0].PadLeft(0x12, '0').Substring(3, 3)) + "Trillion ") +
                    HundredsText(parts[0].PadLeft(0x12, '0').Substring(6, 3)) + "Billion ") +
                    HundredsText(parts[0].PadLeft(0x12, '0').Substring(9, 3)) + "Million ") +
                    HundredsText(parts[0].PadLeft(0x12, '0').Substring(12, 3)) + "Thousand ";
            }
            else if (parts[0].Length > 12)
            {
                returnValue = (((returnValue + HundredsText(parts[0].PadLeft(15, '0').Substring(0, 3)) +
                    "Trillion ") + HundredsText(parts[0].PadLeft(15, '0').Substring(3, 3)) + "Billion ") +
                    HundredsText(parts[0].PadLeft(15, '0').Substring(6, 3)) + "Million ") +
                    HundredsText(parts[0].PadLeft(15, '0').Substring(9, 3)) + "Thousand ";
            }
            else if (parts[0].Length > 9)
            {
                returnValue = ((returnValue + HundredsText(parts[0].PadLeft(12, '0').Substring(0, 3)) +
                    "Billion ") + HundredsText(parts[0].PadLeft(12, '0').Substring(3, 3)) + "Million ") +
                    HundredsText(parts[0].PadLeft(12, '0').Substring(6, 3)) + "Thousand ";
            }
            else if (parts[0].Length > 6)
            {
                returnValue = (returnValue + HundredsText(parts[0].PadLeft(9, '0').Substring(0, 3)) +
                    "Million ") + HundredsText(parts[0].PadLeft(9, '0').Substring(3, 3)) + "Thousand ";
            }
            else if (parts[0].Length > 3)
            {
                returnValue = returnValue + HundredsText(parts[0].PadLeft(6, '0').Substring(0, 3)) +
                    "Thousand ";
            }
            string hundreds = parts[0].PadLeft(3, '0');
            int tempInt = 0;
            hundreds = hundreds.Substring(hundreds.Length - 3, 3);
            if (int.TryParse(hundreds, out tempInt) == true)
            {
                if (int.Parse(hundreds) < 100)
                {
                    //returnValue = returnValue + "and ";
                }
                returnValue = returnValue + HundredsText(hundreds) + curr.CurrencyPound1;
                if (int.Parse(hundreds) != 1)
                {
                    returnValue = returnValue + "s";
                }
                if (int.Parse(parts[1]) != 0)
                {
                    returnValue = returnValue + " and ";
                }
            }
            if ((parts.Length == 2) && (int.Parse(parts[1]) != 0))
            {
                returnValue = returnValue + HundredsText(parts[1].PadLeft(3, '0')) + curr.CurrencyPiaster1;
                if (int.Parse(parts[1]) != 1)
                {
                    returnValue = returnValue + "s";
                }
            }
            return returnValue;
        }

        private static string HundredsText(string value)
        {
            char Val_1;
            char Val_2;

            string returnValue = "";
            bool IsSingleDigit = true;
            char Val = value[0];
            if (int.Parse(Val.ToString()) != 0)
            {
                Val_1 = value[0];
                returnValue = returnValue + Ones[int.Parse(Val_1.ToString()) - 1] + " Hundred ";
                IsSingleDigit = false;
            }
            Val_1 = value[1];
            if (int.Parse(Val_1.ToString()) > 1)
            {
                Val = value[1];
                returnValue = returnValue + Tens[int.Parse(Val.ToString()) - 1] + " ";
                Val_1 = value[2];
                if (int.Parse(Val_1.ToString()) != 0)
                {
                    Val = value[2];
                    returnValue = returnValue + Ones[int.Parse(Val.ToString()) - 1] + " ";
                }
                return returnValue;
            }
            Val_1 = value[1];
            if (int.Parse(Val_1.ToString()) == 1)
            {
                Val = value[1];
                Val_2 = value[2];
                return (returnValue + Ones[int.Parse(Val.ToString() + Val_2.ToString()) - 1] + " ");
            }
            Val_2 = value[2];
            if (int.Parse(Val_2.ToString()) == 0)
            {
                return returnValue;
            }
            if (!IsSingleDigit)
            {
                returnValue = returnValue + "and ";
            }
            Val_2 = value[2];
            return (returnValue + Ones[int.Parse(Val_2.ToString()) - 1] + " ");
        }

        #endregion

        #region Show Money in Arabic Words
        //مصفوفات الكلمات
        static string[] ahad = { "", "واحد", "إثنين", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", " ثمانية", " تسعة", " عشرة", " أحد", " اثنى" };
        static string[] ahad2 = { "", "واحد", "إثنين", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة", " عشر", " أحد", " اثنى" };
        static string[] asharat = { "", "واحد", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون" };
        static string[] meat = { "", "مائة", "مائتين", "ثلاثمائة", "أربعمائة", "خمسمائة", "ستمائة", "سبعمائة", "ثمانمائة", "تسعمائة" };
        static string[] melion = { "", " مليون", " مليونان", " ملايين" };
        static string[] alf = { "", " ألف", " ألفين", " آلاف" };
        //static string[] bcur = { " " + Shared.st_Store.CurrencyPound1, 
        //                         " " + Shared.st_Store.CurrencyPound2,
        //                         " " + Shared.st_Store.CurrencyPound3};

        public static string ConvertMoneyToArabicText(string value, int currencyId, List<ST_Currency> lstCurrency)
        {
            var curr = lstCurrency.Where(x => x.CrncId == currencyId).FirstOrDefault();

            double P_Num = 0;
            if (double.TryParse(value, out P_Num) == false)
                return "";

            double rv;
            string accum = "";
            //الملايين
            rv = (int)(P_Num / 1000000);

            if (rv > 2)
                accum = NumToStr1(rv, accum);

            if (rv >= 3 && rv < 10)
                accum = accum + melion[3];
            else if (rv == 2)
                accum = accum + melion[2];
            else if ((rv == 1) || (rv >= 10 && rv <= 999))
                accum = accum + melion[1];
            //الآلاف
            rv = P_Num - (int)(P_Num / 1000000) * 1000000;
            rv = (int)(rv / 1000);
            if ((P_Num != ((int)(P_Num / 1000000)) * 1000000) && (P_Num > 1000000))
                accum = accum + " و";
            if (rv > 2)
                accum = NumToStr1(rv, accum);
            if (rv >= 3 && rv < 10)
                accum = accum + alf[3];
            else if (rv == 2)
                accum = accum + alf[2];
            else if ((rv == 1) || (rv >= 10 && rv <= 999))
                accum = accum + alf[1];
            //الباقي
            rv = P_Num - ((int)(P_Num / 1000)) * 1000;
            rv = (int)(rv + 0.0001);

            if ((P_Num != ((int)(P_Num / 1000)) * 1000) && (P_Num > 1000) && (rv != 0))
                accum = accum + " و ";

            if ((rv >= 2) && (P_Num != 2))
                accum = NumToStr1(rv, accum);

            if (P_Num > 0.999)
            {
                if ((P_Num < 11) && (rv > 2))
                    accum = accum + " " + curr.CurrencyPound3;
                else if (P_Num == 2)
                    accum = accum + " " + curr.CurrencyPound2;
                else
                    accum = accum + " " + curr.CurrencyPound1;
            }

            //الهللات             
            rv = P_Num - ((int)(P_Num + 0.0001)) + 0.0001;
            rv = (int)(rv * 1000);
            rv = rv / 10;

            if ((rv >= 1) && (P_Num > 0.99))
                accum = accum + " و";

            int fraction_number = curr.CurrencyDigitsCount.HasValue ? curr.CurrencyDigitsCount.Value : 2;
            if (fraction_number == 2)
            {
                if (rv > 2.9)
                    accum = NumToStr1(rv, accum);

                if (rv >= 1)
                {
                    if ((rv >= 2) && (rv < 2.99))
                        accum = accum + " " + curr.CurrencyPiaster2;
                    else if ((rv < 11) && (rv > 2.9))
                        accum = accum + " " + curr.CurrencyPiaster3;
                    else
                        accum = accum + " " + curr.CurrencyPiaster1;
                }
            }
            if (fraction_number == 3)
            {
                rv *= 10;
                accum = NumToStr1(rv, accum);

                if (rv >= 1)
                {
                    if ((rv == 2))
                        accum = accum + " " + curr.CurrencyPiaster2;
                    else if ((rv < 11) && (rv > 2))
                        accum = accum + " " + curr.CurrencyPiaster3;
                    else
                        accum = accum + " " + curr.CurrencyPiaster1;
                }
            }
            return accum;
        }

        //******************* NumToStr1 *************************
        // used by NmToStr
        public static string NumToStr1(double rv, string accum)
        {
            int b, c;
            if (rv >= 100)
            {
                b = (int)(rv / 100);
                accum = accum + meat[b];
            }

            b = (int)(rv - ((int)(rv / 100) * 100));
            if ((b != 0) && (rv > 99))
                accum = accum + " و";

            c = b - ((int)(b / 10) * 10);
            if ((b < 13) && (b != 0))
                accum = accum + ahad[b];

            if ((b > 12) && (c != 0))
                accum = accum + ahad2[c];
            if ((b > 10) && (b < 20))
                accum = accum + ahad2[10];

            if (b > 19)
            {
                if (c != 0)
                    accum = accum + " و";
                accum = accum + asharat[b / 10];
            }
            return accum;
        }
        #endregion


        /// <summary>
        /// Get Account All Childs
        /// </summary>
        /// <param name="account_Id"></param>
        /// <param name="dt"></param>
        public static int Get_All_Child_Accounts(int account_Id, DataTable dt)
        {
            int firstAccount = 0;

            if (dt.Columns.Count == 0)
            {
                dt.Columns.Add("AccountId");
                dt.Columns.Add("AcNameAr");
                dt.Columns.Add("AcNameEn");
                dt.Columns.Add("Notes");
                dt.Columns.Add("CostCenter");
            }

            ERPDataContext DB = new ERPDataContext();

            if (DB.ACC_Accounts.Where(x => x.ParentActId == account_Id).Count() == 0)
                return 0;

            string parentNumber = DB.ACC_Accounts.Where(x => x.AccountId == account_Id).Select(x => x.AcNumber).First();
            var accountsLst = DB.ACC_Accounts.Where(x => x.AcNumber.StartsWith(parentNumber)).ToList();

            foreach (var acc in accountsLst)
                dt.Rows.Add(acc.AccountId, acc.AcNameAr, acc.AcNameEn, acc.Notes, acc.CostCenter);

            return firstAccount;
        }

        public static void Get_All_Child_Accounts(int account_Id, List<ACC_Account> lstSrcAccs, ref List<ACC_Account> lst)
        {
            ERPDataContext DB = new ERPDataContext();
            if (DB.ACC_Accounts.Where(x => x.ParentActId == account_Id).Count() == 0)
                return;

            string parentNumber = DB.ACC_Accounts.Where(x => x.AccountId == account_Id).Select(x => x.AcNumber).First();
            lst = DB.ACC_Accounts.Where(x => x.AcNumber.StartsWith(parentNumber)).ToList();
        }

        public static List<acc> Get_All_Child_Accounts(int account_Id)
        {
            List<acc> lst = new List<acc>();
            ERPDataContext DB = new ERPDataContext();
            if (DB.ACC_Accounts.Where(x => x.ParentActId == account_Id).Count() == 0)
                return null;

            string parentNumber = DB.ACC_Accounts.Where(x => x.AccountId == account_Id).Select(x => x.AcNumber).First();
            lst = (from ac in DB.ACC_Accounts
                   where ac.AcNumber.StartsWith(parentNumber)
                   orderby ac.AcNumber
                   select new acc
                   {
                       AccId = ac.AccountId,
                       AccName = ac.AcNameAr,
                       ParentId = ac.ParentActId.HasValue ? ac.ParentActId.Value : 0,
                       NodeLevel = ac.Level,
                       CostCenter = ac.CostCenter,
                       AccNumber = ac.AcNumber,
                       AccSecurityLevel = ac.AccSecurityLevel,
                       AcType = ac.AcType,
                       AccNameEn = ac.AcNameEn
                   }).ToList();
            return lst;
        }

        public static void Get_All_Child_CostCenters(int CostCenterId, List<ACC_CostCenter> lstSrcCostCenters, ref List<ACC_CostCenter> lst)
        {
            ERPDataContext DB = new ERPDataContext();
            if (DB.ACC_CostCenters.Where(x => x.ParentCCId == CostCenterId).Count() == 0)
                return;

            int parentId = DB.ACC_CostCenters.Where(x => x.CostCenterId == CostCenterId).Select(x => x.CostCenterId).First();
            lst = DB.ACC_CostCenters.Where(x => x.ParentCCId == parentId).ToList();
        }

        public static int Get_Parent_AccountId(int account_Id, List<ACC_Account> lstAllAccounts, int digits)
        {
            var acc_number = lstAllAccounts.Where(x => x.AccountId == account_Id).Select(x => x.AcNumber).FirstOrDefault();

            return lstAllAccounts.Where(x => x.AcNumber == acc_number.Substring(0, digits)).Select(x => x.AccountId).FirstOrDefault();
        }

        public static List<acc> LoadAccountsTree(int parentId, bool asTree)
        {
            ERPDataContext DB = new ERPDataContext();
            ACC_Account parent = null;
            if (parentId > 0)
                parent = DB.ACC_Accounts.Where(a => a.AccountId == parentId).First();

            var All_Accounts = DB.ACC_Accounts.ToList();
            var exclude_GL_Leaves = DB.ST_Stores.Select(x => x.ExcludeLeavesN_GL).FirstOrDefault() == true ?
                                    (from a in DB.ACC_Accounts
                                     join e in DB.HR_Employees on a.AccountId equals e.AccountId
                                     select a
                                    ).Union
                                    (from a in DB.ACC_Accounts
                                     join c in DB.SL_Customers on a.AccountId equals c.AccountId
                                     select a).Union
                                     (from a in DB.ACC_Accounts
                                      join v in DB.PR_Vendors on a.AccountId equals v.AccountId
                                      select a).ToList() : new List<ACC_Account>();

            if (parentId > 0)
                return (from a in All_Accounts.Except(exclude_GL_Leaves)
                        where a.AcNumber.StartsWith(parent.AcNumber)
                        orderby a.AcNumber
                        select new acc
                        {
                            AccId = a.AccountId,
                            AccName = a.AcNameAr,
                            ParentId = a.ParentActId.HasValue ? a.ParentActId.Value : 0,
                            NodeLevel = a.Level,
                            CostCenter = a.CostCenter,
                            AccNumber = a.AcNumber,
                            AccSecurityLevel = a.AccSecurityLevel,
                            AcType = a.AcType,
                            AccNameEn = a.AcNameEn
                        }).ToList();
            else
                return (from a in All_Accounts.Except(exclude_GL_Leaves)
                        orderby a.AcNumber
                        select new acc
                        {
                            AccId = a.AccountId,
                            AccName = a.AcNameAr,
                            ParentId = a.ParentActId.HasValue ? a.ParentActId.Value : 0,
                            NodeLevel = a.Level,
                            CostCenter = a.CostCenter,
                            AccNumber = a.AcNumber,
                            AccSecurityLevel = a.AccSecurityLevel,
                            AcType = a.AcType,
                            AccNameEn = a.AcNameEn
                        }).ToList();
        }

        public static decimal Get_SL_Invoice_Remains(List<int> lst_SL_InvoiceIds)
        {
            ERPDataContext DB = new ERPDataContext();
            decimal Remains = 0;

            foreach (int SL_InvoiceId in lst_SL_InvoiceIds)
            {
                var sl = (from x in DB.SL_Invoices
                          where x.SL_InvoiceId == SL_InvoiceId
                          select new
                          {
                              x.CustomerId,
                              x.Net,
                              Total_Paid = x.Paid + (x.PayAcc2_Paid.HasValue ? x.PayAcc2_Paid.Value : 0)
                          }).FirstOrDefault();

                var cash_Payments = (from x in DB.ACC_CashNotes
                                     where x.ProcessId == (byte)Process.SellInvoice && x.IsPay == false
                                     && x.SourceId == SL_InvoiceId && x.IsVendor == (byte)IsVendor.Customer
                                     && x.DealerId == sl.CustomerId
                                     select new
                                     {
                                         Paid = x.Amount + x.DiscountValue
                                     }).Select(x => x.Paid).ToList().DefaultIfEmpty(0).Sum();

                var NotesReceivables_Payments = (from x in DB.ACC_NotesReceivables
                                                 where x.ProcessId == (byte)Process.SellInvoice && x.SourceId == SL_InvoiceId
                                                 && x.IsVendor == false && x.DealerId == sl.CustomerId
                                                 select x.Amount).ToList().DefaultIfEmpty(0).Sum();

                Remains += sl.Net - sl.Total_Paid - cash_Payments - NotesReceivables_Payments;
            }
            return Remains;
        }

        public static decimal Get_SL_Invoice_Remains(int SL_InvoiceId)
        {
            ERPDataContext DB = new ERPDataContext();
            decimal Remains = 0;
            var sl = (from x in DB.SL_Invoices
                      where x.SL_InvoiceId == SL_InvoiceId
                      select new
                      {
                          x.CustomerId,
                          x.Net,
                          Total_Paid = x.Paid + (x.PayAcc2_Paid.HasValue ? x.PayAcc2_Paid.Value : 0)
                      }).FirstOrDefault();

            var cash_Payments = (from x in DB.ACC_CashNotes
                                 where x.ProcessId == (byte)Process.SellInvoice && x.IsPay == false
                                 && x.SourceId == SL_InvoiceId && x.IsVendor == (byte)IsVendor.Customer
                                 && x.DealerId == sl.CustomerId
                                 select new
                                 {
                                     Paid = x.Amount + x.DiscountValue
                                 }).Select(x => x.Paid).ToList().DefaultIfEmpty(0).Sum();

            var NotesReceivables_Payments = (from x in DB.ACC_NotesReceivables
                                             where x.ProcessId == (byte)Process.SellInvoice && x.SourceId == SL_InvoiceId
                                             && x.IsVendor == false && x.DealerId == sl.CustomerId
                                             select x.Amount).ToList().DefaultIfEmpty(0).Sum();

            Remains = sl.Net - sl.Total_Paid - cash_Payments - NotesReceivables_Payments;

            return Remains;
        }

        public static decimal Get_SL_Invoice_Remains_Till_Date(int SL_InvoiceId, byte fltrTyp_Date, DateTime date1, DateTime date2, int salesEmpId)
        {
            ERPDataContext DB = new ERPDataContext();
            decimal Remains = 0;
            var sl = (from x in DB.SL_Invoices
                      where x.SL_InvoiceId == SL_InvoiceId
                      select new
                      {
                          x.CustomerId,
                          x.Net,
                          Total_Paid = x.Paid
                      }).FirstOrDefault();

            var cash_Payments = (from x in DB.ACC_CashNotes
                                 where x.ProcessId == (byte)Process.SellInvoice && x.IsPay == false
                                 && x.SourceId == SL_InvoiceId
                                 where fltrTyp_Date == 1 ? x.NoteDate.Date == date1.Date : true
                                 where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                 x.NoteDate >= date1 && x.NoteDate <= date2 : true
                                 where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                 x.NoteDate >= date1 : true
                                 where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                 x.NoteDate <= date2 : true
                                 where x.EmpId == salesEmpId
                                 select new
                                 {
                                     Paid = x.Amount + x.DiscountValue
                                 }).Select(x => x.Paid).ToList().DefaultIfEmpty(0).Sum();

            var NotesReceivables_Payments = (from x in DB.ACC_NotesReceivables
                                             where x.ProcessId == (byte)Process.SellInvoice && x.SourceId == SL_InvoiceId
                                             where fltrTyp_Date == 1 ? x.DueDate.Date == date1.Date : true
                                             where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                             x.DueDate >= date1 && x.DueDate <= date2 : true
                                             where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                             x.DueDate >= date1 : true
                                             where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                             x.DueDate <= date2 : true
                                             where x.RecipientEmpId == salesEmpId
                                             select x.Amount).ToList().DefaultIfEmpty(0).Sum();

            Remains = sl.Net - sl.Total_Paid - cash_Payments - NotesReceivables_Payments;

            return Remains;
        }
        public static void Get_SalesEmp_CommissionRatio(int EmpId, decimal Amount, out decimal Target, out decimal CommissionRatio)
        {
            ERPDataContext DB = new ERPDataContext();
            var data = (from d in DB.HR_EmpCommissionTargets
                        where d.EmpId == EmpId
                        orderby d.Target descending
                        where Amount >= d.Target
                        select new { d.Target, d.Commision }).ToList().FirstOrDefault();

            if (data == null)
            {
                var lowerTarget = (from d in DB.HR_EmpCommissionTargets
                                   where d.EmpId == EmpId
                                   orderby d.Target ascending
                                   where Amount >= d.Target
                                   select new { d.Target, d.Commision }).ToList().FirstOrDefault();
                if (lowerTarget != null)
                {
                    Target = lowerTarget.Target;
                    CommissionRatio = lowerTarget.Commision;
                }
                else
                {
                    Target = 0;
                    CommissionRatio = 0;
                }
            }
            else
            {
                Target = data.Target;
                CommissionRatio = data.Commision;
            }
        }

        public static void GetAccountStatement(SrcBtn srcBtn_, int AccountId, int CostCenterId,
            int CrncyId, List<int> customizedList, List<ST_DuePeriod> lst_DuePeriods,
            bool isFutureDueDate, DateTime fromDate, DateTime toDate,
            ref DataTable dtJDetails, ref List<DueAmounts> lstDueAmounts, string accNum, string ccNum, string currencyName, decimal lastRate,
            bool getOpenBalance = true)
        {
            dtJDetails.Rows.Clear();
            double balance_Before = 0;

            #region Due Periods
            DateTime today = toDate == Shared.maxDate ? MyHelper.Get_Server_DateTime().Date : toDate;

            lstDueAmounts.Clear();
            lstDueAmounts.Add(new DueAmounts { P0 = 0, P1 = 0, P2 = 0, P3 = 0, P4 = 0, P5 = 0 });
            List<period> lstPeriods = new List<period>();

            if (isFutureDueDate)
            {
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[0].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[1].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[2].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[3].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = Shared.maxDate.Date });
            }
            else
            {
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[0].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[1].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[2].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[3].To * -1) });
                //lstPeriods.Add(new period { PeriodId = 1, PeriodDate = Shared.maxDate.Date });
            }
            #endregion

            decimal totalPaid = 0;
            double balance = 0;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stores = DB.IC_Stores.ToList();

            if (srcBtn_ == SrcBtn.Account)
            {
                #region Account Statement

                var allTrns = (from d in DB.ACC_JournalDetails
                               join j in DB.ACC_Journals//.Where(j => j.IsPosted)
                               on d.JournalId equals j.JournalId
                               //join cjd in DB.Acc_Journal_CostCenters.DefaultIfEmpty()
                               //on d.JDetailId equals cjd.Journal_Detail_Id
                               join a in DB.ACC_Accounts
                               on d.AccountId equals a.AccountId

                               from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()
                               from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                               where AccountId == 0 ? true :
                               a.AcNumber.StartsWith(accNum)

                               where CostCenterId == 0 ? true :
                               cc.ccNumber.StartsWith(ccNum)

                               where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                               //where j.InsertDate >= fromDate
                               where j.InsertDate.Date <= toDate
                               orderby j.InsertDate, j.JournalId

                               select new
                               {
                                   Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                                   (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                                   : decimal.ToDouble(d.Debit * d.CrncRate)),

                                   Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                                   (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                                   : decimal.ToDouble(d.Credit * d.CrncRate)),

                                   Fcredit = d.Credit,
                                   Fdebit = d.Debit,
                                   CrncId = c == null ? currencyName : c.crncName,
                                   CrncRate = c == null ? 1 : (CrncyId == -1 ? c.LastRate : d.CrncRate),

                                   d.Notes,
                                   j.JCode,
                                   j.JNumber,
                                   InsertDate = j.InsertDate,
                                   ProcessName =
                                   (Shared.IsEnglish ? DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessEnglishName).Single() :
                                                    DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessName).Single())
                                   ,
                                   ProcessId = j.ProcessId,
                                   SourceId = j.SourceId,
                                   JournalId = j.JournalId,
                                   DueDate = d.DueDate,
                                   AcType = a.AcType,
                                   a.AccSecurityLevel,
                                   d.JDetailId,
                                   CostCenterId = (d.CostCenter == null || d.CostCenter == 0) ? 0 : cc.CostCenterId
                               }).Union(
                    from d in DB.ACC_JournalDetails
                    join j in DB.ACC_Journals//.Where(j => j.IsPosted)
                    on d.JournalId equals j.JournalId
                    join cjd in DB.Acc_Journal_CostCenters
                    on d.JDetailId equals cjd.Journal_Detail_Id
                    join a in DB.ACC_Accounts
                    on d.AccountId equals a.AccountId

                    from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()
                    from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == cjd.CostCenter_Id)

                    where AccountId == 0 ? true :
                    a.AcNumber.StartsWith(accNum)

                    where CostCenterId == 0 ? true :
                    cc.ccNumber.StartsWith(ccNum)

                    where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                    //where j.InsertDate >= fromDate
                    where j.InsertDate.Date <= toDate
                    orderby j.InsertDate, j.JournalId

                    select new
                    {
                        Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                        (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                        : decimal.ToDouble(d.Debit * d.CrncRate)),

                        Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                        (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                        : decimal.ToDouble(d.Credit * d.CrncRate)),

                        Fcredit = d.Credit,
                        Fdebit = d.Debit,
                        CrncId = c == null ? currencyName : c.crncName,
                        CrncRate = c == null ? 1 : (CrncyId == -1 ? c.LastRate : d.CrncRate),

                        d.Notes,
                        j.JCode,
                        j.JNumber,
                        InsertDate = j.InsertDate,
                        ProcessName =
                        (Shared.IsEnglish ? DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessEnglishName).Single() :
                                         DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessName).Single())
                        ,
                        ProcessId = j.ProcessId,
                        SourceId = j.SourceId,
                        JournalId = j.JournalId,
                        DueDate = d.DueDate,
                        AcType = a.AcType,
                        a.AccSecurityLevel,
                        d.JDetailId,
                        cc.CostCenterId
                    }

                    ).OrderBy(x => x.InsertDate).ToList();

                if (getOpenBalance)
                {
                    balance_Before = (from d in allTrns
                                      where d.InsertDate.Date < fromDate
                                      select d.Credit - d.Debit).ToList().DefaultIfEmpty(0).Sum();

                    if (balance_Before != 0)
                    {
                        string crncyName = string.Empty;
                        decimal crncyRate = 1;
                        decimal fBalanceBefore = 0;
                        if (CrncyId == 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId == -1)
                        {
                            crncyName = currencyName;
                            crncyRate = lastRate;
                        }
                        else if (CrncyId == -2)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId > 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 0;
                            fBalanceBefore = (from d in allTrns
                                              where d.InsertDate.Date < fromDate
                                              select d.Fcredit - d.Fdebit).ToList().DefaultIfEmpty(0).Sum();
                        }

                        balance += balance_Before;
                        dtJDetails.Rows.Add(string.Empty, Shared.IsEnglish ? ResAccEn.txtOpenBalance : ResAccAr.txtOpenBalance,
                         fromDate.ToShortDateString(),
                         balance_Before > 0 ? 0 : Math.Abs(balance_Before),
                         balance_Before > 0 ? Math.Abs(balance_Before) : 0,
                         string.Empty, 0, 0, 0, string.Empty, string.Empty, balance, 1/*SecurityLevel*/, string.Empty, crncyName, crncyRate,
                         fBalanceBefore == 0 ? 0 : (fBalanceBefore > 0 ? 0 : Math.Abs(fBalanceBefore)),
                         fBalanceBefore == 0 ? 0 : (fBalanceBefore < 0 ? Math.Abs(fBalanceBefore) : 0), 0);
                    }
                }

                foreach (var d in allTrns)
                {
                    #region Due Dates
                    if (d.AcType.HasValue)
                    {
                        if (isFutureDueDate)
                        {
                            //due date for future due
                            if (d.DueDate.HasValue && d.DueDate.Value.Date <= today)
                                lstDueAmounts[0].P0 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > today && d.DueDate.Value.Date <= lstPeriods[0].PeriodDate)
                                lstDueAmounts[0].P1 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[0].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[1].PeriodDate)
                                lstDueAmounts[0].P2 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[1].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[2].PeriodDate)
                                lstDueAmounts[0].P3 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[2].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[3].PeriodDate)
                                lstDueAmounts[0].P4 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue == false || (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[3].PeriodDate))
                                lstDueAmounts[0].P5 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);
                        }
                        else
                        {
                            //due date for past due
                            if (d.DueDate.HasValue == false || (d.DueDate.HasValue && d.DueDate.Value.Date > today))
                                lstDueAmounts[0].P0 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[0].PeriodDate && d.DueDate.Value.Date <= today)
                                lstDueAmounts[0].P1 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[1].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[0].PeriodDate)
                                lstDueAmounts[0].P2 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[2].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[1].PeriodDate)
                                lstDueAmounts[0].P3 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[3].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[2].PeriodDate)
                                lstDueAmounts[0].P4 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.Value.Date <= lstPeriods[3].PeriodDate)
                                lstDueAmounts[0].P5 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);
                        }
                    }

                    if (d.AcType.HasValue)
                        totalPaid += Convert.ToDecimal(d.AcType.Value == false ? d.Credit : d.Debit);
                    #endregion

                    if (d.InsertDate < fromDate)
                        continue;

                    balance += d.Credit - d.Debit;
                    dtJDetails.Rows.Add(d.JCode, d.ProcessName, d.InsertDate,
                        d.Debit, d.Credit, d.Notes, d.ProcessId, d.SourceId, d.JournalId, d.DueDate, d.AcType, balance, d.AccSecurityLevel, d.JNumber,
                        d.CrncId, d.CrncRate, d.Fdebit, d.Fcredit, 0, d.CostCenterId);
                }

                #region Due Date Calc
                if (isFutureDueDate)
                {
                    lstDueAmounts[0].P0 -= totalPaid;
                    if (lstDueAmounts[0].P0 < 0)
                    {
                        lstDueAmounts[0].P1 -= (lstDueAmounts[0].P0 * -1);
                        lstDueAmounts[0].P0 = 0;
                    }

                    if (lstDueAmounts[0].P1 < 0)
                    {
                        lstDueAmounts[0].P2 -= (lstDueAmounts[0].P1 * -1);
                        lstDueAmounts[0].P1 = 0;
                    }

                    if (lstDueAmounts[0].P2 < 0)
                    {
                        lstDueAmounts[0].P3 -= (lstDueAmounts[0].P2 * -1);
                        lstDueAmounts[0].P2 = 0;
                    }
                    if (lstDueAmounts[0].P3 < 0)
                    {
                        lstDueAmounts[0].P4 -= (lstDueAmounts[0].P3 * -1);
                        lstDueAmounts[0].P3 = 0;
                    }
                    if (lstDueAmounts[0].P4 < 0)
                    {
                        lstDueAmounts[0].P5 -= (lstDueAmounts[0].P4 * -1);
                        lstDueAmounts[0].P4 = 0;
                    }
                    if (lstDueAmounts[0].P5 < 0)
                        lstDueAmounts[0].P5 = 0;
                }
                else
                {
                    lstDueAmounts[0].P5 -= totalPaid;

                    if (lstDueAmounts[0].P5 < 0)
                    {
                        lstDueAmounts[0].P4 -= (lstDueAmounts[0].P5 * -1);
                        lstDueAmounts[0].P5 = 0;
                    }
                    if (lstDueAmounts[0].P4 < 0)
                    {
                        lstDueAmounts[0].P3 -= (lstDueAmounts[0].P4 * -1);
                        lstDueAmounts[0].P4 = 0;
                    }

                    if (lstDueAmounts[0].P3 < 0)
                    {
                        lstDueAmounts[0].P2 -= (lstDueAmounts[0].P3 * -1);
                        lstDueAmounts[0].P3 = 0;
                    }

                    if (lstDueAmounts[0].P2 < 0)
                    {
                        lstDueAmounts[0].P1 -= (lstDueAmounts[0].P2 * -1);
                        lstDueAmounts[0].P2 = 0;
                    }
                    if (lstDueAmounts[0].P1 < 0)
                    {
                        lstDueAmounts[0].P0 -= (lstDueAmounts[0].P1 * -1);
                        lstDueAmounts[0].P1 = 0;
                    }
                    if (lstDueAmounts[0].P0 < 0)
                        lstDueAmounts[0].P0 = 0;
                }
                #endregion

                #endregion
            }
            else if (srcBtn_ == SrcBtn.CustomList)
            {
                #region custom list
                if (getOpenBalance)
                {
                    balance_Before = decimal.ToDouble((from d in DB.ACC_JournalDetails.Where(d => customizedList.Count < 1 ? true : customizedList.Contains(d.AccountId))
                                                       join j in DB.ACC_Journals//.Where(j => j.IsPosted)
                                                       on d.JournalId equals j.JournalId

                                                       from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()

                                                       where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                                                       where j.InsertDate.Date < fromDate
                                                       select new
                                                       {
                                                           Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                                                           (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                                                           : decimal.ToDouble(d.Debit * d.CrncRate)),

                                                           Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                                                           (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                                                           : decimal.ToDouble(d.Credit * d.CrncRate)),

                                                           Balance = d.Credit - d.Debit,
                                                       }).Select(x => x.Balance).ToList().DefaultIfEmpty(0).Sum());

                    if (balance_Before != 0)
                    {
                        string crncyName = string.Empty;
                        decimal crncyRate = 1;
                        //decimal fBalanceBefore = 0;

                        if (CrncyId == 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId == -1)
                        {
                            crncyName = currencyName;
                            crncyRate = lastRate;
                        }
                        else if (CrncyId == -2)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }

                        balance += balance_Before;
                        dtJDetails.Rows.Add(string.Empty, Shared.IsEnglish ? ResAccEn.txtOpenBalance : ResAccAr.txtOpenBalance,
                         fromDate.ToShortDateString(),
                         balance_Before > 0 ? 0 : Math.Abs(balance_Before),
                             balance_Before > 0 ? Math.Abs(balance_Before) : 0,
                         string.Empty, 0, 0, 0, string.Empty, string.Empty, balance, 1/*SecurityLevel*/, string.Empty, string.Empty,
                         /*crncyName, mohammad 17-09-2018*/ crncyRate, 0, 0, 0);
                    }
                }

                var dd = from d in DB.ACC_JournalDetails.Where(d => customizedList.Count < 1 ? true : customizedList.Contains(d.AccountId))
                         join j in DB.ACC_Journals//.Where(j => j.IsPosted)
                         on d.JournalId equals j.JournalId
                         join a in DB.ACC_Accounts
                         on d.AccountId equals a.AccountId

                         from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()

                         where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                         where j.InsertDate.Date >= fromDate
                         where j.InsertDate.Date <= toDate
                         orderby j.InsertDate, j.JournalId

                         select new
                         {
                             Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                             (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                             : decimal.ToDouble(d.Debit * d.CrncRate)),

                             Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                             (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                             : decimal.ToDouble(d.Credit * d.CrncRate)),

                             CrncId = c == null ? currencyName : c.crncName,
                             CrncRate = CrncyId == -1 ? c.LastRate : d.CrncRate,
                             fDebit = d.Debit,
                             fCredit = d.Credit,
                             d.Notes,
                             j.JCode,
                             j.JNumber,
                             InsertDate = j.InsertDate,
                             ProcessName =
                             (Shared.IsEnglish ? DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessEnglishName).Single() :
                                              DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessName).Single())
                             ,
                             ProcessId = j.ProcessId,
                             SourceId = j.SourceId,
                             JournalId = j.JournalId,
                             DueDate = d.DueDate,
                             AcType = a.AcType,
                             a.AccSecurityLevel,
                         };
                foreach (var d in dd)
                {
                    balance += d.Credit - d.Debit;
                    dtJDetails.Rows.Add(d.JCode, d.ProcessName, d.InsertDate,
                        d.Debit, d.Credit, d.Notes, d.ProcessId, d.SourceId, d.JournalId, d.DueDate, d.AcType, balance, d.AccSecurityLevel, d.JNumber,
                        d.CrncId, d.CrncRate, d.fDebit, d.fCredit, 0);
                }
                #endregion
            }
            //col_InsertDate.SortIndex = 0;            
        }

        public static void GetAccountStatement(SrcBtn srcBtn_, int AccountId, int CostCenterId,
           int CrncyId, List<int> customizedList, List<ST_DuePeriod> lst_DuePeriods,
           bool isFutureDueDate, DateTime fromDate, DateTime toDate,
           ref DataTable dtJDetails, ref List<DueAmounts> lstDueAmounts, string accNum, string accNum2, string ccNum, string currencyName, decimal lastRate,
           bool getOpenBalance = true)
        {
            dtJDetails.Rows.Clear();
            double balance_Before = 0;

            #region Due Periods
            DateTime today = toDate == Shared.maxDate ? MyHelper.Get_Server_DateTime().Date : toDate;

            lstDueAmounts.Clear();
            lstDueAmounts.Add(new DueAmounts { P0 = 0, P1 = 0, P2 = 0, P3 = 0, P4 = 0, P5 = 0 });
            List<period> lstPeriods = new List<period>();

            if (isFutureDueDate)
            {
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[0].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[1].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[2].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[3].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = Shared.maxDate.Date });
            }
            else
            {
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[0].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[1].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[2].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[3].To * -1) });
                //lstPeriods.Add(new period { PeriodId = 1, PeriodDate = Shared.maxDate.Date });
            }
            #endregion

            decimal totalPaid = 0;
            double balance = 0;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stores = DB.IC_Stores.ToList();

            var Details = (from d in DB.ACC_JournalDetails
                                     join j in DB.ACC_Journals//.Where(j => j.IsPosted)
                                     on d.JournalId equals j.JournalId
                                     join a in DB.ACC_Accounts
                                     on d.AccountId equals a.AccountId
                                     from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()
                                     where j.InsertDate.Date <= toDate
                          where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                          orderby j.InsertDate, j.JournalId


                          select new
                                     {
                                         Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                                   (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                                   : decimal.ToDouble(d.Debit * d.CrncRate)),

                                         Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                                   (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                                   : decimal.ToDouble(d.Credit * d.CrncRate)),

                                         Fcredit = d.Credit,
                                         Fdebit = d.Debit,
                                         CrncId = c == null ? currencyName : c.crncName,
                                         CrncRate = c == null ? 1 : (CrncyId == -1 ? c.LastRate : d.CrncRate),

                                         d.Notes,
                                         j.JCode,
                                         j.JNumber,
                                         InsertDate = j.InsertDate,
                                         ProcessName =
                                   (Shared.IsEnglish ? DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessEnglishName).Single() :
                                                    DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessName).Single())
                                   ,
                                         ProcessId = j.ProcessId,
                                         SourceId = j.SourceId,
                                         JournalId = j.JournalId,
                                         DueDate = d.DueDate,
                                         AcType = a.AcType,
                                         a.AccSecurityLevel,
                                         a.AcNumber,
                                         a
                                     }).ToList()
                                     ;

            if (srcBtn_ == SrcBtn.Account)
            {
                #region Account Statement

                var allTrns = (from a in Details
                                   //from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                               orderby a.AcNumber

                               let lstAllAccounts = DB.ACC_Accounts.OrderBy(x => x.AcNumber).ToList()

                               let startindx = lstAllAccounts.Select((x, y) => new { Item = x, Index = y }).
                               Where(x => x.Item.AcNumber == accNum).FirstOrDefault()

                               let endindx = lstAllAccounts.Select((x, y) => new { Item = x, Index = y }).
                              Where(x => x.Item.AcNumber == accNum2).FirstOrDefault()

                               //where lstAllAccounts.Skip(startindx.Index - 1).Take(endindx.Index - startindx.Index + 1).Contains(a.a)
                               where lstAllAccounts.GetRange(startindx.Index, endindx.Index - startindx.Index + 1).Contains(a.a)

                               //where CostCenterId == 0 ? true :
                               //cc.ccNumber.StartsWith(ccNum)

                               //where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                               ////where j.InsertDate >= fromDate
                               //where j.InsertDate.Date <= toDate
                               //orderby j.InsertDate, j.JournalId

                               select a).ToList();

                if (getOpenBalance)
                {
                    balance_Before = (from d in allTrns
                                      where d.InsertDate.Date < fromDate
                                      select d.Credit - d.Debit).ToList().DefaultIfEmpty(0).Sum();

                    if (balance_Before != 0)
                    {
                        string crncyName = string.Empty;
                        decimal crncyRate = 1;
                        decimal fBalanceBefore = 0;
                        if (CrncyId == 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId == -1)
                        {
                            crncyName = currencyName;
                            crncyRate = lastRate;
                        }
                        else if (CrncyId == -2)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId > 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 0;
                            fBalanceBefore = (from d in allTrns
                                              where d.InsertDate.Date < fromDate
                                              select d.Fcredit - d.Fdebit).ToList().DefaultIfEmpty(0).Sum();
                        }

                        balance += balance_Before;
                        dtJDetails.Rows.Add(string.Empty, Shared.IsEnglish ? ResAccEn.txtOpenBalance : ResAccAr.txtOpenBalance,
                         fromDate.ToShortDateString(),
                         balance_Before > 0 ? 0 : Math.Abs(balance_Before),
                         balance_Before > 0 ? Math.Abs(balance_Before) : 0,
                         string.Empty, 0, 0, 0, string.Empty, string.Empty, balance, 1/*SecurityLevel*/, string.Empty, crncyName, crncyRate,
                         fBalanceBefore == 0 ? 0 : (fBalanceBefore > 0 ? 0 : Math.Abs(fBalanceBefore)),
                         fBalanceBefore == 0 ? 0 : (fBalanceBefore < 0 ? Math.Abs(fBalanceBefore) : 0), 0);
                    }
                }

                foreach (var d in allTrns)
                {
                    #region Due Dates
                    if (d.AcType.HasValue)
                    {
                        //if (isFutureDueDate)
                        //{
                        //    //due date for future due
                        //    if (d.DueDate.HasValue && d.DueDate.Value.Date <= today)
                        //        lstDueAmounts[0].P0 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.HasValue && d.DueDate.Value.Date > today && d.DueDate.Value.Date <= lstPeriods[0].PeriodDate)
                        //        lstDueAmounts[0].P1 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[0].PeriodDate &&
                        //        d.DueDate.Value.Date <= lstPeriods[1].PeriodDate)
                        //        lstDueAmounts[0].P2 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[1].PeriodDate &&
                        //        d.DueDate.Value.Date <= lstPeriods[2].PeriodDate)
                        //        lstDueAmounts[0].P3 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[2].PeriodDate &&
                        //        d.DueDate.Value.Date <= lstPeriods[3].PeriodDate)
                        //        lstDueAmounts[0].P4 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.HasValue == false || (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[3].PeriodDate))
                        //        lstDueAmounts[0].P5 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);
                        //}
                        //else
                        //{
                        //    //due date for past due
                        //    if (d.DueDate.HasValue == false || (d.DueDate.HasValue && d.DueDate.Value.Date > today))
                        //        lstDueAmounts[0].P0 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[0].PeriodDate && d.DueDate.Value.Date <= today)
                        //        lstDueAmounts[0].P1 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[1].PeriodDate &&
                        //        d.DueDate.Value.Date <= lstPeriods[0].PeriodDate)
                        //        lstDueAmounts[0].P2 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[2].PeriodDate &&
                        //        d.DueDate.Value.Date <= lstPeriods[1].PeriodDate)
                        //        lstDueAmounts[0].P3 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[3].PeriodDate &&
                        //        d.DueDate.Value.Date <= lstPeriods[2].PeriodDate)
                        //        lstDueAmounts[0].P4 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                        //    else if (d.DueDate.Value.Date <= lstPeriods[3].PeriodDate)
                        //        lstDueAmounts[0].P5 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);
                        //}
                    }

                    if (d.AcType.HasValue)
                        totalPaid += Convert.ToDecimal(d.AcType.Value == false ? d.Credit : d.Debit);
                    #endregion

                    if (d.InsertDate < fromDate)
                        continue;

                    balance += d.Credit - d.Debit;
                    dtJDetails.Rows.Add(d.JCode, d.ProcessName, d.InsertDate,
                        d.Debit, d.Credit, d.Notes, d.ProcessId, d.SourceId, d.JournalId, d.DueDate, d.AcType, balance, d.AccSecurityLevel, d.JNumber,
                        d.CrncId, d.CrncRate, d.Fdebit, d.Fcredit, 0);
                }

                #region Due Date Calc
                //if (isFutureDueDate)
                //{
                //    lstDueAmounts[0].P0 -= totalPaid;
                //    if (lstDueAmounts[0].P0 < 0)
                //    {
                //        lstDueAmounts[0].P1 -= (lstDueAmounts[0].P0 * -1);
                //        lstDueAmounts[0].P0 = 0;
                //    }

                //    if (lstDueAmounts[0].P1 < 0)
                //    {
                //        lstDueAmounts[0].P2 -= (lstDueAmounts[0].P1 * -1);
                //        lstDueAmounts[0].P1 = 0;
                //    }

                //    if (lstDueAmounts[0].P2 < 0)
                //    {
                //        lstDueAmounts[0].P3 -= (lstDueAmounts[0].P2 * -1);
                //        lstDueAmounts[0].P2 = 0;
                //    }
                //    if (lstDueAmounts[0].P3 < 0)
                //    {
                //        lstDueAmounts[0].P4 -= (lstDueAmounts[0].P3 * -1);
                //        lstDueAmounts[0].P3 = 0;
                //    }
                //    if (lstDueAmounts[0].P4 < 0)
                //    {
                //        lstDueAmounts[0].P5 -= (lstDueAmounts[0].P4 * -1);
                //        lstDueAmounts[0].P4 = 0;
                //    }
                //    if (lstDueAmounts[0].P5 < 0)
                //        lstDueAmounts[0].P5 = 0;
                //}
                //else
                //{
                //    lstDueAmounts[0].P5 -= totalPaid;

                //    if (lstDueAmounts[0].P5 < 0)
                //    {
                //        lstDueAmounts[0].P4 -= (lstDueAmounts[0].P5 * -1);
                //        lstDueAmounts[0].P5 = 0;
                //    }
                //    if (lstDueAmounts[0].P4 < 0)
                //    {
                //        lstDueAmounts[0].P3 -= (lstDueAmounts[0].P4 * -1);
                //        lstDueAmounts[0].P4 = 0;
                //    }

                //    if (lstDueAmounts[0].P3 < 0)
                //    {
                //        lstDueAmounts[0].P2 -= (lstDueAmounts[0].P3 * -1);
                //        lstDueAmounts[0].P3 = 0;
                //    }

                //    if (lstDueAmounts[0].P2 < 0)
                //    {
                //        lstDueAmounts[0].P1 -= (lstDueAmounts[0].P2 * -1);
                //        lstDueAmounts[0].P2 = 0;
                //    }
                //    if (lstDueAmounts[0].P1 < 0)
                //    {
                //        lstDueAmounts[0].P0 -= (lstDueAmounts[0].P1 * -1);
                //        lstDueAmounts[0].P1 = 0;
                //    }
                //    if (lstDueAmounts[0].P0 < 0)
                //        lstDueAmounts[0].P0 = 0;
                //}
                #endregion

                #endregion
            }
        }

        //for subledger report
        public static void GetAccountStatement2(SrcBtn srcBtn_, int AccountId, int CostCenterId,
         int CrncyId, List<int> customizedList, List<ST_DuePeriod> lst_DuePeriods,
         bool isFutureDueDate, byte fltrTyp_Date, DateTime fromDate, DateTime toDate,
         ref DataTable dtJDetails, ref List<DueAmounts> lstDueAmounts, string accNum, string ccNum, string currencyName, decimal lastRate,
         bool getOpenBalance = true)
        {
            dtJDetails.Rows.Clear();
            double balance_Before = 0;

            #region Due Periods
            DateTime today = toDate == Shared.maxDate ? MyHelper.Get_Server_DateTime().Date : toDate;

            lstDueAmounts.Clear();
            lstDueAmounts.Add(new DueAmounts { P0 = 0, P1 = 0, P2 = 0, P3 = 0, P4 = 0, P5 = 0 });
            List<period> lstPeriods = new List<period>();

            if (isFutureDueDate)
            {
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[0].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[1].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[2].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[3].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = Shared.maxDate.Date });
            }
            else
            {
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[0].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[1].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[2].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[3].To * -1) });
                //lstPeriods.Add(new period { PeriodId = 1, PeriodDate = Shared.maxDate.Date });
            }
            #endregion

            decimal totalPaid = 0;
            double balance = 0;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stores = DB.IC_Stores.ToList();

            if (srcBtn_ == SrcBtn.Account)
            {
                #region Account Statement

                var allTrns = (from d in DB.ACC_JournalDetails
                               join j in DB.ACC_Journals//.Where(j => j.IsPosted)
                               on d.JournalId equals j.JournalId
                               join a in DB.ACC_Accounts
                               on d.AccountId equals a.AccountId

                               from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()
                               from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                               where AccountId == 0 ? true :
                               a.AcNumber.StartsWith(accNum)

                               where CostCenterId == 0 ? true :
                               cc.ccNumber.StartsWith(ccNum)

                               where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                               //where j.InsertDate >= fromDate
                               where j.InsertDate.Date <= toDate
                               orderby j.InsertDate, j.JournalId

                               select new
                               {
                                   Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                                   (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                                   : decimal.ToDouble(d.Debit * d.CrncRate)),

                                   Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                                   (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                                   : decimal.ToDouble(d.Credit * d.CrncRate)),

                                   Fcredit = d.Credit,
                                   Fdebit = d.Debit,
                                   CrncId = c == null ? currencyName : c.crncName,
                                   CrncRate = c == null ? 1 : (CrncyId == -1 ? c.LastRate : d.CrncRate),

                                   d.Notes,
                                   j.JCode,
                                   j.JNumber,
                                   InsertDate = j.InsertDate,
                                   ProcessName =
                                   (Shared.IsEnglish ? DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessEnglishName).Single() :
                                                    DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessName).Single())
                                   ,
                                   ProcessId = j.ProcessId,
                                   SourceId = j.SourceId,
                                   JournalId = j.JournalId,
                                   DueDate = d.DueDate,
                                   AcType = a.AcType,
                                   a.AccSecurityLevel,
                               }).ToList();

                if (getOpenBalance)
                {
                    balance_Before = (from d in allTrns
                                      where d.InsertDate.Date < fromDate
                                      select d.Credit - d.Debit).ToList().DefaultIfEmpty(0).Sum();

                    if (balance_Before != 0)
                    {
                        string crncyName = string.Empty;
                        decimal crncyRate = 1;
                        decimal fBalanceBefore = 0;
                        if (CrncyId == 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId == -1)
                        {
                            crncyName = currencyName;
                            crncyRate = lastRate;
                        }
                        else if (CrncyId == -2)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId > 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 0;
                            fBalanceBefore = (from d in allTrns
                                              where d.InsertDate.Date < fromDate
                                              select d.Fcredit - d.Fdebit).ToList().DefaultIfEmpty(0).Sum();
                        }

                        balance += balance_Before;
                        dtJDetails.Rows.Add(string.Empty, Shared.IsEnglish ? ResAccEn.txtOpenBalance : ResAccAr.txtOpenBalance,
                         fromDate.ToShortDateString(),
                         balance_Before > 0 ? 0 : Math.Abs(balance_Before),
                         balance_Before > 0 ? Math.Abs(balance_Before) : 0,
                         string.Empty, 0, 0, 0, string.Empty, string.Empty, balance, 1/*SecurityLevel*/, string.Empty, crncyName, crncyRate,
                         fBalanceBefore == 0 ? 0 : (fBalanceBefore > 0 ? 0 : Math.Abs(fBalanceBefore)),
                         fBalanceBefore == 0 ? 0 : (fBalanceBefore < 0 ? Math.Abs(fBalanceBefore) : 0), 0);
                    }
                }

                foreach (var d in allTrns)
                {
                    #region Due Dates
                    if (d.AcType.HasValue)
                    {
                        if (isFutureDueDate)
                        {
                            //due date for future due
                            if (d.DueDate.HasValue && d.DueDate.Value.Date <= today)
                                lstDueAmounts[0].P0 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > today && d.DueDate.Value.Date <= lstPeriods[0].PeriodDate)
                                lstDueAmounts[0].P1 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[0].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[1].PeriodDate)
                                lstDueAmounts[0].P2 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[1].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[2].PeriodDate)
                                lstDueAmounts[0].P3 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[2].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[3].PeriodDate)
                                lstDueAmounts[0].P4 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue == false || (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[3].PeriodDate))
                                lstDueAmounts[0].P5 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);
                        }
                        else
                        {
                            //due date for past due
                            if (d.DueDate.HasValue == false || (d.DueDate.HasValue && d.DueDate.Value.Date > today))
                                lstDueAmounts[0].P0 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[0].PeriodDate && d.DueDate.Value.Date <= today)
                                lstDueAmounts[0].P1 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[1].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[0].PeriodDate)
                                lstDueAmounts[0].P2 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[2].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[1].PeriodDate)
                                lstDueAmounts[0].P3 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[3].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[2].PeriodDate)
                                lstDueAmounts[0].P4 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.Value.Date <= lstPeriods[3].PeriodDate)
                                lstDueAmounts[0].P5 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);
                        }
                    }

                    if (d.AcType.HasValue)
                        totalPaid += Convert.ToDecimal(d.AcType.Value == false ? d.Credit : d.Debit);
                    #endregion

                    if (d.InsertDate < fromDate)
                        continue;

                    balance += d.Credit - d.Debit;
                    dtJDetails.Rows.Add(d.JCode, d.ProcessName, d.InsertDate,
                        d.Debit, d.Credit, d.Notes, d.ProcessId, d.SourceId, d.JournalId, d.DueDate, d.AcType, balance, d.AccSecurityLevel, d.JNumber,
                        d.CrncId, d.CrncRate, d.Fdebit, d.Fcredit, 0);
                }

                #region Due Date Calc
                if (isFutureDueDate)
                {
                    lstDueAmounts[0].P0 -= totalPaid;
                    if (lstDueAmounts[0].P0 < 0)
                    {
                        lstDueAmounts[0].P1 -= (lstDueAmounts[0].P0 * -1);
                        lstDueAmounts[0].P0 = 0;
                    }

                    if (lstDueAmounts[0].P1 < 0)
                    {
                        lstDueAmounts[0].P2 -= (lstDueAmounts[0].P1 * -1);
                        lstDueAmounts[0].P1 = 0;
                    }

                    if (lstDueAmounts[0].P2 < 0)
                    {
                        lstDueAmounts[0].P3 -= (lstDueAmounts[0].P2 * -1);
                        lstDueAmounts[0].P2 = 0;
                    }
                    if (lstDueAmounts[0].P3 < 0)
                    {
                        lstDueAmounts[0].P4 -= (lstDueAmounts[0].P3 * -1);
                        lstDueAmounts[0].P3 = 0;
                    }
                    if (lstDueAmounts[0].P4 < 0)
                    {
                        lstDueAmounts[0].P5 -= (lstDueAmounts[0].P4 * -1);
                        lstDueAmounts[0].P4 = 0;
                    }
                    if (lstDueAmounts[0].P5 < 0)
                        lstDueAmounts[0].P5 = 0;
                }
                else
                {
                    lstDueAmounts[0].P5 -= totalPaid;

                    if (lstDueAmounts[0].P5 < 0)
                    {
                        lstDueAmounts[0].P4 -= (lstDueAmounts[0].P5 * -1);
                        lstDueAmounts[0].P5 = 0;
                    }
                    if (lstDueAmounts[0].P4 < 0)
                    {
                        lstDueAmounts[0].P3 -= (lstDueAmounts[0].P4 * -1);
                        lstDueAmounts[0].P4 = 0;
                    }

                    if (lstDueAmounts[0].P3 < 0)
                    {
                        lstDueAmounts[0].P2 -= (lstDueAmounts[0].P3 * -1);
                        lstDueAmounts[0].P3 = 0;
                    }

                    if (lstDueAmounts[0].P2 < 0)
                    {
                        lstDueAmounts[0].P1 -= (lstDueAmounts[0].P2 * -1);
                        lstDueAmounts[0].P2 = 0;
                    }
                    if (lstDueAmounts[0].P1 < 0)
                    {
                        lstDueAmounts[0].P0 -= (lstDueAmounts[0].P1 * -1);
                        lstDueAmounts[0].P1 = 0;
                    }
                    if (lstDueAmounts[0].P0 < 0)
                        lstDueAmounts[0].P0 = 0;
                }
                #endregion

                #endregion
            }
            else if (srcBtn_ == SrcBtn.CustomList)
            {
                #region custom list
                if (getOpenBalance)
                {
                    balance_Before = decimal.ToDouble((from d in DB.ACC_JournalDetails.Where(d => customizedList.Count < 1 ? true : customizedList.Contains(d.AccountId))
                                                       join j in DB.ACC_Journals//.Where(j => j.IsPosted)
                                                       on d.JournalId equals j.JournalId

                                                       from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()

                                                       where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                                                       where j.InsertDate.Date < fromDate
                                                       select new
                                                       {
                                                           Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                                                           (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                                                           : decimal.ToDouble(d.Debit * d.CrncRate)),

                                                           Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                                                           (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                                                           : decimal.ToDouble(d.Credit * d.CrncRate)),

                                                           Balance = d.Credit - d.Debit,
                                                       }).Select(x => x.Balance).ToList().DefaultIfEmpty(0).Sum());

                    if (balance_Before != 0)
                    {
                        string crncyName = string.Empty;
                        decimal crncyRate = 1;
                        //decimal fBalanceBefore = 0;

                        if (CrncyId == 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId == -1)
                        {
                            crncyName = currencyName;
                            crncyRate = lastRate;
                        }
                        else if (CrncyId == -2)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }

                        balance += balance_Before;
                        dtJDetails.Rows.Add(string.Empty, Shared.IsEnglish ? ResAccEn.txtOpenBalance : ResAccAr.txtOpenBalance,
                         fromDate.ToShortDateString(),
                         balance_Before > 0 ? 0 : Math.Abs(balance_Before),
                             balance_Before > 0 ? Math.Abs(balance_Before) : 0,
                         string.Empty, 0, 0, 0, string.Empty, string.Empty, balance, 1/*SecurityLevel*/, string.Empty, string.Empty,
                         /*crncyName, mohammad 17-09-2018*/ crncyRate, 0, 0, 0);
                    }
                }

                var dd = from d in DB.ACC_JournalDetails.Where(d => customizedList.Count < 1 ? true : customizedList.Contains(d.AccountId))
                         join j in DB.ACC_Journals//.Where(j => j.IsPosted)
                         on d.JournalId equals j.JournalId
                         join a in DB.ACC_Accounts
                         on d.AccountId equals a.AccountId

                         from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()

                         where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                         where j.InsertDate.Date >= fromDate
                         where j.InsertDate.Date <= toDate
                         orderby j.InsertDate, j.JournalId

                         select new
                         {
                             Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                             (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                             : decimal.ToDouble(d.Debit * d.CrncRate)),

                             Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                             (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                             : decimal.ToDouble(d.Credit * d.CrncRate)),

                             CrncId = c == null ? currencyName : c.crncName,
                             CrncRate = CrncyId == -1 ? c.LastRate : d.CrncRate,
                             fDebit = d.Debit,
                             fCredit = d.Credit,
                             d.Notes,
                             j.JCode,
                             j.JNumber,
                             InsertDate = j.InsertDate,
                             ProcessName =
                             (Shared.IsEnglish ? DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessEnglishName).Single() :
                                              DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessName).Single())
                             ,
                             ProcessId = j.ProcessId,
                             SourceId = j.SourceId,
                             JournalId = j.JournalId,
                             DueDate = d.DueDate,
                             AcType = a.AcType,
                             a.AccSecurityLevel,
                         };
                foreach (var d in dd)
                {
                    balance += d.Credit - d.Debit;
                    dtJDetails.Rows.Add(d.JCode, d.ProcessName, d.InsertDate,
                        d.Debit, d.Credit, d.Notes, d.ProcessId, d.SourceId, d.JournalId, d.DueDate, d.AcType, balance, d.AccSecurityLevel, d.JNumber,
                        d.CrncId, d.CrncRate, d.fDebit, d.fCredit, 0);
                }
                #endregion
            }
            //col_InsertDate.SortIndex = 0;            
        }





        //update 18/10/2017

        public static void GetAccountStatementArchive(SrcBtn srcBtn_, int AccountId, int CostCenterId,
        int CrncyId, List<int> customizedList, List<ST_DuePeriod> lst_DuePeriods,
        bool isFutureDueDate, DateTime fromDate, DateTime toDate,
        ref DataTable dtJDetails, ref List<DueAmounts> lstDueAmounts, string accNum, string ccNum, string currencyName, decimal lastRate,
        bool UnionWithLive, bool getOpenBalance = true)
        {
            if (UnionWithLive == false)
                dtJDetails.Rows.Clear();
            double balance_Before = 0;

            #region Due Periods
            DateTime today = toDate == Shared.maxDate ? MyHelper.Get_Server_DateTime().Date : toDate;

            if (UnionWithLive == false)
                lstDueAmounts.Clear();
            lstDueAmounts.Add(new DueAmounts { P0 = 0, P1 = 0, P2 = 0, P3 = 0, P4 = 0, P5 = 0 });
            List<period> lstPeriods = new List<period>();

            if (isFutureDueDate)
            {
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[0].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[1].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[2].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[3].To) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = Shared.maxDate.Date });
            }
            else
            {
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[0].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[1].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[2].To * -1) });
                lstPeriods.Add(new period { PeriodId = 1, PeriodDate = today.AddDays(lst_DuePeriods[3].To * -1) });
                //lstPeriods.Add(new period { PeriodId = 1, PeriodDate = Shared.maxDate.Date });
            }
            #endregion

            decimal totalPaid = 0;
            double balance = 0;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stores = DB.IC_Stores.ToList();

            if (srcBtn_ == SrcBtn.Account)
            {
                #region Account Statement

                var allTrns = (from d in DB.ACC_JournalDetailArchives
                               join j in DB.ACC_JournalArchives//.Where(j => j.IsPosted)
                               on d.JournalId equals j.JournalArchiveId
                               join a in DB.ACC_Accounts
                               on d.AccountId equals a.AccountId

                               from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()
                               from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                               where AccountId == 0 ? true :
                               a.AcNumber.StartsWith(accNum)

                               where CostCenterId == 0 ? true :
                               cc.ccNumber.StartsWith(ccNum)

                               where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                               //where j.InsertDate >= fromDate
                               where j.InsertDate.Date <= toDate
                               orderby j.InsertDate, j.JournalArchiveId

                               select new
                               {
                                   Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                                   (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                                   : decimal.ToDouble(d.Debit * d.CrncRate)),

                                   Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                                   (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                                   : decimal.ToDouble(d.Credit * d.CrncRate)),

                                   Fcredit = d.Credit,
                                   Fdebit = d.Debit,
                                   CrncId = c == null ? currencyName : c.crncName,
                                   CrncRate = c == null ? 1 : (CrncyId == -1 ? c.LastRate : d.CrncRate),

                                   d.Notes,
                                   j.JCode,
                                   j.JNumber,
                                   InsertDate = j.InsertDate,
                                   ProcessName =
                                   (Shared.IsEnglish ? DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessEnglishName).Single() :
                                                    DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessName).Single())
                                   ,
                                   ProcessId = j.ProcessId,
                                   SourceId = j.SourceId,
                                   JournalId = j.JournalArchiveId,
                                   DueDate = d.DueDate,
                                   AcType = a.AcType,
                                   a.AccSecurityLevel,
                               }).ToList();

                if (getOpenBalance)
                {
                    balance_Before = (from d in allTrns
                                      where d.InsertDate.Date < fromDate
                                      select d.Credit - d.Debit).ToList().DefaultIfEmpty(0).Sum();

                    if (balance_Before != 0)
                    {
                        string crncyName = string.Empty;
                        decimal crncyRate = 1;
                        decimal fBalanceBefore = 0;
                        if (CrncyId == 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId == -1)
                        {
                            crncyName = currencyName;
                            crncyRate = lastRate;
                        }
                        else if (CrncyId == -2)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId > 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 0;
                            fBalanceBefore = (from d in allTrns
                                              where d.InsertDate.Date < fromDate
                                              select d.Fcredit - d.Fdebit).ToList().DefaultIfEmpty(0).Sum();
                        }

                        balance += balance_Before;
                        dtJDetails.Rows.Add(string.Empty, Shared.IsEnglish ? ResAccEn.txtOpenBalance : ResAccAr.txtOpenBalance,
                         fromDate.ToShortDateString(),
                         balance_Before > 0 ? 0 : Math.Abs(balance_Before),
                         balance_Before > 0 ? Math.Abs(balance_Before) : 0,
                         string.Empty, 0, 0, 0, string.Empty, string.Empty, balance, 1/*SecurityLevel*/, string.Empty, crncyName, crncyRate,
                         fBalanceBefore == 0 ? 0 : (fBalanceBefore > 0 ? 0 : Math.Abs(fBalanceBefore)),
                         fBalanceBefore == 0 ? 0 : (fBalanceBefore < 0 ? Math.Abs(fBalanceBefore) : 0), 0);
                    }
                }

                foreach (var d in allTrns)
                {
                    #region Due Dates
                    if (d.AcType.HasValue)
                    {
                        if (isFutureDueDate)
                        {
                            //due date for future due
                            if (d.DueDate.HasValue && d.DueDate.Value.Date <= today)
                                lstDueAmounts[0].P0 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > today && d.DueDate.Value.Date <= lstPeriods[0].PeriodDate)
                                lstDueAmounts[0].P1 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[0].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[1].PeriodDate)
                                lstDueAmounts[0].P2 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[1].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[2].PeriodDate)
                                lstDueAmounts[0].P3 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[2].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[3].PeriodDate)
                                lstDueAmounts[0].P4 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue == false || (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[3].PeriodDate))
                                lstDueAmounts[0].P5 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);
                        }
                        else
                        {
                            //due date for past due
                            if (d.DueDate.HasValue == false || (d.DueDate.HasValue && d.DueDate.Value.Date > today))
                                lstDueAmounts[0].P0 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[0].PeriodDate && d.DueDate.Value.Date <= today)
                                lstDueAmounts[0].P1 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[1].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[0].PeriodDate)
                                lstDueAmounts[0].P2 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[2].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[1].PeriodDate)
                                lstDueAmounts[0].P3 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.HasValue && d.DueDate.Value.Date > lstPeriods[3].PeriodDate &&
                                d.DueDate.Value.Date <= lstPeriods[2].PeriodDate)
                                lstDueAmounts[0].P4 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);

                            else if (d.DueDate.Value.Date <= lstPeriods[3].PeriodDate)
                                lstDueAmounts[0].P5 += Convert.ToDecimal(d.AcType.Value == false ? d.Debit : d.Credit);
                        }
                    }

                    if (d.AcType.HasValue)
                        totalPaid += Convert.ToDecimal(d.AcType.Value == false ? d.Credit : d.Debit);
                    #endregion

                    if (d.InsertDate < fromDate)
                        continue;

                    balance += d.Credit - d.Debit;
                    dtJDetails.Rows.Add(d.JCode, d.ProcessName, d.InsertDate,
                        d.Debit, d.Credit, d.Notes, d.ProcessId, d.SourceId, d.JournalId, d.DueDate, d.AcType, balance, d.AccSecurityLevel, d.JNumber,
                        d.CrncId, d.CrncRate, d.Fdebit, d.Fcredit, 0);
                }

                #region Due Date Calc
                if (isFutureDueDate)
                {
                    lstDueAmounts[0].P0 -= totalPaid;
                    if (lstDueAmounts[0].P0 < 0)
                    {
                        lstDueAmounts[0].P1 -= (lstDueAmounts[0].P0 * -1);
                        lstDueAmounts[0].P0 = 0;
                    }

                    if (lstDueAmounts[0].P1 < 0)
                    {
                        lstDueAmounts[0].P2 -= (lstDueAmounts[0].P1 * -1);
                        lstDueAmounts[0].P1 = 0;
                    }

                    if (lstDueAmounts[0].P2 < 0)
                    {
                        lstDueAmounts[0].P3 -= (lstDueAmounts[0].P2 * -1);
                        lstDueAmounts[0].P2 = 0;
                    }
                    if (lstDueAmounts[0].P3 < 0)
                    {
                        lstDueAmounts[0].P4 -= (lstDueAmounts[0].P3 * -1);
                        lstDueAmounts[0].P3 = 0;
                    }
                    if (lstDueAmounts[0].P4 < 0)
                    {
                        lstDueAmounts[0].P5 -= (lstDueAmounts[0].P4 * -1);
                        lstDueAmounts[0].P4 = 0;
                    }
                    if (lstDueAmounts[0].P5 < 0)
                        lstDueAmounts[0].P5 = 0;
                }
                else
                {
                    lstDueAmounts[0].P5 -= totalPaid;

                    if (lstDueAmounts[0].P5 < 0)
                    {
                        lstDueAmounts[0].P4 -= (lstDueAmounts[0].P5 * -1);
                        lstDueAmounts[0].P5 = 0;
                    }
                    if (lstDueAmounts[0].P4 < 0)
                    {
                        lstDueAmounts[0].P3 -= (lstDueAmounts[0].P4 * -1);
                        lstDueAmounts[0].P4 = 0;
                    }

                    if (lstDueAmounts[0].P3 < 0)
                    {
                        lstDueAmounts[0].P2 -= (lstDueAmounts[0].P3 * -1);
                        lstDueAmounts[0].P3 = 0;
                    }

                    if (lstDueAmounts[0].P2 < 0)
                    {
                        lstDueAmounts[0].P1 -= (lstDueAmounts[0].P2 * -1);
                        lstDueAmounts[0].P2 = 0;
                    }
                    if (lstDueAmounts[0].P1 < 0)
                    {
                        lstDueAmounts[0].P0 -= (lstDueAmounts[0].P1 * -1);
                        lstDueAmounts[0].P1 = 0;
                    }
                    if (lstDueAmounts[0].P0 < 0)
                        lstDueAmounts[0].P0 = 0;
                }
                #endregion

                #endregion
            }
            else if (srcBtn_ == SrcBtn.CustomList)
            {
                #region custom list
                if (getOpenBalance)
                {
                    balance_Before = decimal.ToDouble((from d in DB.ACC_JournalDetailArchives.Where(d => customizedList.Count < 1 ? true : customizedList.Contains(d.AccountId))
                                                       join j in DB.ACC_JournalArchives//.Where(j => j.IsPosted)
                                                       on d.JournalId equals j.JournalArchiveId

                                                       from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()

                                                       where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                                                       where j.InsertDate.Date < fromDate
                                                       select new
                                                       {
                                                           Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                                                           (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                                                           : decimal.ToDouble(d.Debit * d.CrncRate)),

                                                           Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                                                           (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                                                           : decimal.ToDouble(d.Credit * d.CrncRate)),

                                                           Balance = d.Credit - d.Debit,
                                                       }).Select(x => x.Balance).ToList().DefaultIfEmpty(0).Sum());

                    if (balance_Before != 0)
                    {
                        string crncyName = string.Empty;
                        decimal crncyRate = 1;
                        //decimal fBalanceBefore = 0;

                        if (CrncyId == 0)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }
                        else if (CrncyId == -1)
                        {
                            crncyName = currencyName;
                            crncyRate = lastRate;
                        }
                        else if (CrncyId == -2)
                        {
                            crncyName = currencyName;
                            crncyRate = 1;
                        }

                        balance += balance_Before;
                        dtJDetails.Rows.Add(string.Empty, Shared.IsEnglish ? ResAccEn.txtOpenBalance : ResAccAr.txtOpenBalance,
                         fromDate.ToShortDateString(),
                         balance_Before > 0 ? 0 : Math.Abs(balance_Before),
                             balance_Before > 0 ? Math.Abs(balance_Before) : 0,
                         string.Empty, 0, 0, 0, string.Empty, string.Empty, balance, 1/*SecurityLevel*/, string.Empty, string.Empty,
                         crncyName, crncyRate, 0, 0, 0);
                    }
                }

                var dd = from d in DB.ACC_JournalDetailArchives.Where(d => customizedList.Count < 1 ? true : customizedList.Contains(d.AccountId))
                         join j in DB.ACC_JournalArchives//.Where(j => j.IsPosted)
                         on d.JournalId equals j.JournalArchiveId
                         join a in DB.ACC_Accounts
                         on d.AccountId equals a.AccountId

                         from c in DB.ST_Currencies.Where(c => c.CrncId == d.CrncId).DefaultIfEmpty()

                         where CrncyId == -1 || CrncyId == -2 ? true : d.CrncId == CrncyId

                         where j.InsertDate.Date >= fromDate
                         where j.InsertDate.Date <= toDate
                         orderby j.InsertDate, j.JournalArchiveId

                         select new
                         {
                             Debit = CrncyId >= 0 ? decimal.ToDouble(d.Debit) :
                             (CrncyId == -1 ? decimal.ToDouble(d.Debit * (c == null ? 1 : c.LastRate))
                             : decimal.ToDouble(d.Debit * d.CrncRate)),

                             Credit = CrncyId >= 0 ? decimal.ToDouble(d.Credit) :
                             (CrncyId == -1 ? decimal.ToDouble(d.Credit * (c == null ? 1 : c.LastRate))
                             : decimal.ToDouble(d.Credit * d.CrncRate)),

                             CrncId = c == null ? currencyName : c.crncName,
                             CrncRate = CrncyId == -1 ? c.LastRate : d.CrncRate,
                             fDebit = d.Debit,
                             fCredit = d.Credit,
                             d.Notes,
                             j.JCode,
                             j.JNumber,
                             InsertDate = j.InsertDate,
                             ProcessName =
                             (Shared.IsEnglish ? DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessEnglishName).Single() :
                                              DB.LKP_Processes.Where(p => p.ProcessId == j.ProcessId).Select(p => p.ProcessName).Single())
                             ,
                             ProcessId = j.ProcessId,
                             SourceId = j.SourceId,
                             JournalId = j.JournalArchiveId,
                             DueDate = d.DueDate,
                             AcType = a.AcType,
                             a.AccSecurityLevel,
                         };
                foreach (var d in dd)
                {
                    balance += d.Credit - d.Debit;
                    dtJDetails.Rows.Add(d.JCode, d.ProcessName, d.InsertDate,
                        d.Debit, d.Credit, d.Notes, d.ProcessId, d.SourceId, d.JournalId, d.DueDate, d.AcType, balance, d.AccSecurityLevel, d.JNumber,
                        d.CrncId, d.CrncRate, d.fDebit, d.fCredit, 0);
                }
                #endregion
            }
            //col_InsertDate.SortIndex = 0;            
        }






        public static void GetTradeSummary(DateTime fromDate, DateTime toDate, out decimal tradeDebit, out decimal tradeCredit,
                 out decimal tradeProfit, string costCenterNum, int crncy, ST_Store st_Store /*, out decimal openInvBalance, out decimal closeInvBalance*/)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            string mainAccNum = DB.ACC_Accounts.Where(x => x.AccountId == st_Store.MerchandisingAcc).Select(x => x.AcNumber).First();
            var MerchndBal = (from a in DB.ACC_Accounts.Where(a => a.AcNumber.StartsWith(mainAccNum))

                              join d in DB.ACC_JournalDetails
                              on a.AccountId equals d.AccountId
                              join j in DB.ACC_Journals.Where(j => j.IsPosted)
                              on d.JournalId equals j.JournalId

                              from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                              where costCenterNum == string.Empty ? true :
                              cc.ccNumber.StartsWith(costCenterNum)


                              from r in DB.ST_Currencies.Where(r => r.CrncId == d.CrncId).DefaultIfEmpty()
                              where crncy == -1 || crncy == -2 ? true : d.CrncId == crncy

                              where j.InsertDate.Date >= fromDate
                              && j.InsertDate.Date <= toDate

                              group new { d, r } by d.AccountId into g
                              select new
                              {
                                  AccId = g.Key,

                                  Debit = crncy == 0 ? (g.Sum(c => c.d.Debit)) :
                                    (crncy == -1 ?
                                    (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.r.LastRate))) :
                                    (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  Credit = crncy == 0 ? (g.Sum(c => c.d.Credit)) :
                                      (crncy == -1 ?
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.r.LastRate))) :
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  //Debit = grp.Where(d => d.Debit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Debit : 0),
                                  //Credit = grp.Where(d => d.Credit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Credit : 0),

                              }).Distinct().ToList();

            if (costCenterNum != string.Empty)
            {
                MerchndBal = MerchndBal.Union(from a in DB.ACC_Accounts.Where(a => a.AcNumber.StartsWith(mainAccNum))

                              join d in DB.ACC_JournalDetails
                              on a.AccountId equals d.AccountId
                              join j in DB.ACC_Journals.Where(j => j.IsPosted)
                              on d.JournalId equals j.JournalId

                                              join cjd in DB.Acc_Journal_CostCenters
                                              on d.JDetailId equals cjd.Journal_Detail_Id

                                              from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == cjd.CostCenter_Id)

                              where costCenterNum == string.Empty ? true :
                              cc.ccNumber.StartsWith(costCenterNum)


                              from r in DB.ST_Currencies.Where(r => r.CrncId == d.CrncId).DefaultIfEmpty()
                              where crncy == -1 || crncy == -2 ? true : d.CrncId == crncy

                              where j.InsertDate.Date >= fromDate
                              && j.InsertDate.Date <= toDate

                              group new { d, r } by d.AccountId into g
                              select new
                              {
                                  AccId = g.Key,

                                  Debit = crncy == 0 ? (g.Sum(c => c.d.Debit)) :
                                    (crncy == -1 ?
                                    (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.r.LastRate))) :
                                    (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  Credit = crncy == 0 ? (g.Sum(c => c.d.Credit)) :
                                      (crncy == -1 ?
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.r.LastRate))) :
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  //Debit = grp.Where(d => d.Debit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Debit : 0),
                                  //Credit = grp.Where(d => d.Credit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Credit : 0),

                              }).Distinct().ToList();
            }


            tradeCredit = MerchndBal == null ? 0 : MerchndBal.Sum(x => x.Credit);
            tradeDebit = MerchndBal == null ? 0 : MerchndBal.Sum(x => x.Debit);

            if (tradeCredit > tradeDebit)
            {
                tradeCredit = (tradeCredit - tradeDebit);
                tradeDebit = 0;
            }
            else if (tradeCredit < tradeDebit)
            {
                tradeDebit = (tradeDebit - tradeCredit);
                tradeCredit = 0;
            }

            #region Get Open & Close Inventory
            //decimal outCloseValue = 0, outOpenValue = 0;

            //if (st_Store.InventoryAcc.HasValue)
            //{
            //    MyHelper.GetCloseInventory(fromDate, toDate, out outCloseValue, out outOpenValue, costCenter);
            //    tradeCredit += outCloseValue;
            //    tradeDebit += outOpenValue;
            //}
            //closeInvBalance = outCloseValue;
            //openInvBalance = outOpenValue;
            #endregion

            //calculate summary
            tradeProfit = tradeCredit - tradeDebit;
        }


        public static void GetRevExpSummary(DateTime fromDate, DateTime toDate, decimal tradeDebit, decimal tradeCredit
            , ref decimal revExpDebit, ref decimal revExpCredit, ref decimal netProfit, string costCenterNum, int crncy)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            string mainExpAccNum = DB.ACC_Accounts.Where(x => x.AccountId == HelperAcc.ExpensesAcc).Select(x => x.AcNumber).First();
            string mainRevAccNum = DB.ACC_Accounts.Where(x => x.AccountId == HelperAcc.RevenuesAcc).Select(x => x.AcNumber).First();

            var MerchndBal = (from a in DB.ACC_Accounts.Where(a =>
                a.AcNumber.StartsWith(mainExpAccNum)
                ||
                a.AcNumber.StartsWith(mainRevAccNum)
                )
                              join d in DB.ACC_JournalDetails
                              on a.AccountId equals d.AccountId

                              join j in DB.ACC_Journals.Where(j => j.IsPosted)
                              on d.JournalId equals j.JournalId

                              from r in DB.ST_Currencies.Where(r => r.CrncId == d.CrncId).DefaultIfEmpty()
                              where crncy == -1 || crncy == -2 ? true : d.CrncId == crncy

                              from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                              where costCenterNum == string.Empty ? true :
                              cc.ccNumber.StartsWith(costCenterNum)

                              where j.InsertDate.Date >= fromDate
                              && j.InsertDate.Date <= toDate

                              group new { d, r } by d.AccountId into g
                              select new
                              {
                                  AccId = g.Key,
                                  Debit = crncy == 0 ? (g.Sum(c => c.d.Debit)) :
                                      (crncy == -1 ?
                                      (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.r.LastRate))) :
                                      (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  Credit = crncy == 0 ? (g.Sum(c => c.d.Credit)) :
                                      (crncy == -1 ?
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.r.LastRate))) :
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  //Debit = grp.Where(d => d.Debit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Debit : 0),
                                  //Credit = grp.Where(d => d.Credit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Credit : 0),

                              }).Distinct().ToList();
            if (costCenterNum != string.Empty)
            {
                MerchndBal = MerchndBal.Union(from a in DB.ACC_Accounts.Where(a =>
                    a.AcNumber.StartsWith(mainExpAccNum)
                    ||
                    a.AcNumber.StartsWith(mainRevAccNum)
                    )
                                              join d in DB.ACC_JournalDetails
                                              on a.AccountId equals d.AccountId

                                              join j in DB.ACC_Journals.Where(j => j.IsPosted)
                                              on d.JournalId equals j.JournalId

                                              from r in DB.ST_Currencies.Where(r => r.CrncId == d.CrncId).DefaultIfEmpty()
                                              where crncy == -1 || crncy == -2 ? true : d.CrncId == crncy

                                              from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                                              where costCenterNum == string.Empty ? true :
                                              cc.ccNumber.StartsWith(costCenterNum)

                                              where j.InsertDate.Date >= fromDate
                                              && j.InsertDate.Date <= toDate

                                              group new { d, r } by d.AccountId into g
                                              select new
                                              {
                                                  AccId = g.Key,
                                                  Debit = crncy == 0 ? (g.Sum(c => c.d.Debit)) :
                                                      (crncy == -1 ?
                                                      (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.r.LastRate))) :
                                                      (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.d.CrncRate)))),

                                                  Credit = crncy == 0 ? (g.Sum(c => c.d.Credit)) :
                                                      (crncy == -1 ?
                                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.r.LastRate))) :
                                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.d.CrncRate)))),

                                                  //Debit = grp.Where(d => d.Debit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Debit : 0),
                                                  //Credit = grp.Where(d => d.Credit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Credit : 0),

                                              }).Distinct().ToList();
            }
            if (MerchndBal != null)
            {
                revExpDebit = MerchndBal.Sum(x => x.Debit);
                revExpCredit = MerchndBal.Sum(x => x.Credit);
            }
            //calculate summary
            if (revExpDebit + tradeDebit > revExpCredit + tradeCredit)//"مجمل الخساره:" 
            {
                netProfit = revExpCredit + tradeCredit - revExpDebit - tradeDebit;

                revExpDebit = revExpDebit + tradeDebit - revExpCredit - tradeCredit;
                revExpCredit = 0;
            }
            else if (revExpCredit + tradeCredit > revExpDebit + tradeDebit)//"مجمل الربح:" 
            {
                netProfit = revExpCredit + tradeCredit - revExpDebit - tradeDebit;

                revExpCredit = revExpCredit + tradeCredit - revExpDebit - tradeDebit;
                revExpDebit = 0;
            }
            else//"لايوجد ربح أو خسارة"
            {
                revExpDebit = revExpCredit = 0;
            }

        }





        public static void GetTradeSummary_archive(DateTime fromDate, DateTime toDate, out decimal tradeDebit, out decimal tradeCredit,
                 out decimal tradeProfit, string costCenterNum, int crncy, ST_Store st_Store /*, out decimal openInvBalance, out decimal closeInvBalance*/)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            string mainAccNum = DB.ACC_Accounts.Where(x => x.AccountId == st_Store.MerchandisingAcc).Select(x => x.AcNumber).First();
            var MerchndBal = (from a in DB.ACC_Accounts.Where(a => a.AcNumber.StartsWith(mainAccNum))

                              join d in DB.ACC_JournalDetailArchives
                              on a.AccountId equals d.AccountId
                              join j in DB.ACC_JournalArchives.Where(j => j.IsPosted)
                              on d.JournalId equals j.JournalArchiveId

                              from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                              where costCenterNum == string.Empty ? true :
                              cc.ccNumber.StartsWith(costCenterNum)


                              from r in DB.ST_Currencies.Where(r => r.CrncId == d.CrncId).DefaultIfEmpty()
                              where crncy == -1 || crncy == -2 ? true : d.CrncId == crncy

                              where j.InsertDate.Date >= fromDate
                              && j.InsertDate.Date <= toDate

                              group new { d, r } by d.AccountId into g
                              select new
                              {
                                  AccId = g.Key,

                                  Debit = crncy == 0 ? (g.Sum(c => c.d.Debit)) :
                                    (crncy == -1 ?
                                    (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.r.LastRate))) :
                                    (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  Credit = crncy == 0 ? (g.Sum(c => c.d.Credit)) :
                                      (crncy == -1 ?
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.r.LastRate))) :
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  //Debit = grp.Where(d => d.Debit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Debit : 0),
                                  //Credit = grp.Where(d => d.Credit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Credit : 0),

                              }).Distinct().ToList();

            #region commented MultiCostCenter
            /*if (costCenterNum != string.Empty)
            {
                MerchndBal = MerchndBal.Union(from a in DB.ACC_Accounts.Where(a => a.AcNumber.StartsWith(mainAccNum))

                              join d in DB.ACC_JournalDetails
                              on a.AccountId equals d.AccountId
                              join j in DB.ACC_Journals.Where(j => j.IsPosted)
                              on d.JournalId equals j.JournalId

                                              join cjd in DB.Acc_Journal_CostCenters
                                              on d.JDetailId equals cjd.Journal_Detail_Id

                                              from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == cjd.CostCenter_Id)

                              where costCenterNum == string.Empty ? true :
                              cc.ccNumber.StartsWith(costCenterNum)


                              from r in DB.ST_Currencies.Where(r => r.CrncId == d.CrncId).DefaultIfEmpty()
                              where crncy == -1 || crncy == -2 ? true : d.CrncId == crncy

                              where j.InsertDate.Date >= fromDate
                              && j.InsertDate.Date <= toDate

                              group new { d, r } by d.AccountId into g
                              select new
                              {
                                  AccId = g.Key,

                                  Debit = crncy == 0 ? (g.Sum(c => c.d.Debit)) :
                                    (crncy == -1 ?
                                    (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.r.LastRate))) :
                                    (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  Credit = crncy == 0 ? (g.Sum(c => c.d.Credit)) :
                                      (crncy == -1 ?
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.r.LastRate))) :
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  //Debit = grp.Where(d => d.Debit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Debit : 0),
                                  //Credit = grp.Where(d => d.Credit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Credit : 0),

                              }).Distinct().ToList();
            }
            */
            #endregion

            tradeCredit = MerchndBal == null ? 0 : MerchndBal.Sum(x => x.Credit);
            tradeDebit = MerchndBal == null ? 0 : MerchndBal.Sum(x => x.Debit);

            if (tradeCredit > tradeDebit)
            {
                tradeCredit = (tradeCredit - tradeDebit);
                tradeDebit = 0;
            }
            else if (tradeCredit < tradeDebit)
            {
                tradeDebit = (tradeDebit - tradeCredit);
                tradeCredit = 0;
            }

            #region Get Open & Close Inventory
            //decimal outCloseValue = 0, outOpenValue = 0;

            //if (st_Store.InventoryAcc.HasValue)
            //{
            //    MyHelper.GetCloseInventory(fromDate, toDate, out outCloseValue, out outOpenValue, costCenter);
            //    tradeCredit += outCloseValue;
            //    tradeDebit += outOpenValue;
            //}
            //closeInvBalance = outCloseValue;
            //openInvBalance = outOpenValue;
            #endregion

            //calculate summary
            tradeProfit = tradeCredit - tradeDebit;
        }



        public static void GetRevExpSummary_archive(DateTime fromDate, DateTime toDate, decimal tradeDebit, decimal tradeCredit
            , ref decimal revExpDebit, ref decimal revExpCredit, ref decimal netProfit, string costCenterNum, int crncy)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            string mainExpAccNum = DB.ACC_Accounts.Where(x => x.AccountId == HelperAcc.ExpensesAcc).Select(x => x.AcNumber).First();
            string mainRevAccNum = DB.ACC_Accounts.Where(x => x.AccountId == HelperAcc.RevenuesAcc).Select(x => x.AcNumber).First();

            var MerchndBal = (from a in DB.ACC_Accounts.Where(a =>
                a.AcNumber.StartsWith(mainExpAccNum)
                ||
                a.AcNumber.StartsWith(mainRevAccNum)
                )
                              join d in DB.ACC_JournalDetailArchives
                              on a.AccountId equals d.AccountId

                              join j in DB.ACC_JournalArchives.Where(j => j.IsPosted)
                              on d.JournalId equals j.JournalArchiveId

                              from r in DB.ST_Currencies.Where(r => r.CrncId == d.CrncId).DefaultIfEmpty()
                              where crncy == -1 || crncy == -2 ? true : d.CrncId == crncy

                              from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                              where costCenterNum == string.Empty ? true :
                              cc.ccNumber.StartsWith(costCenterNum)

                              where j.InsertDate.Date >= fromDate
                              && j.InsertDate.Date <= toDate

                              group new { d, r } by d.AccountId into g
                              select new
                              {
                                  AccId = g.Key,
                                  Debit = crncy == 0 ? (g.Sum(c => c.d.Debit)) :
                                      (crncy == -1 ?
                                      (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.r.LastRate))) :
                                      (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  Credit = crncy == 0 ? (g.Sum(c => c.d.Credit)) :
                                      (crncy == -1 ?
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.r.LastRate))) :
                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.d.CrncRate)))),

                                  //Debit = grp.Where(d => d.Debit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Debit : 0),
                                  //Credit = grp.Where(d => d.Credit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Credit : 0),

                              }).Distinct().ToList();

            #region Commented MultCostCenter
            /*if (costCenterNum != string.Empty)
            {
                MerchndBal = MerchndBal.Union(from a in DB.ACC_Accounts.Where(a =>
                    a.AcNumber.StartsWith(mainExpAccNum)
                    ||
                    a.AcNumber.StartsWith(mainRevAccNum)
                    )
                                              join d in DB.ACC_JournalDetails
                                              on a.AccountId equals d.AccountId

                                              join j in DB.ACC_Journals.Where(j => j.IsPosted)
                                              on d.JournalId equals j.JournalId

                                              from r in DB.ST_Currencies.Where(r => r.CrncId == d.CrncId).DefaultIfEmpty()
                                              where crncy == -1 || crncy == -2 ? true : d.CrncId == crncy

                                              from cc in DB.ACC_CostCenters.Where(cc => cc.CostCenterId == d.CostCenter).DefaultIfEmpty()

                                              where costCenterNum == string.Empty ? true :
                                              cc.ccNumber.StartsWith(costCenterNum)

                                              where j.InsertDate.Date >= fromDate
                                              && j.InsertDate.Date <= toDate

                                              group new { d, r } by d.AccountId into g
                                              select new
                                              {
                                                  AccId = g.Key,
                                                  Debit = crncy == 0 ? (g.Sum(c => c.d.Debit)) :
                                                      (crncy == -1 ?
                                                      (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.r.LastRate))) :
                                                      (g.Sum(c => c.d.Debit * (c.r == null ? 1 : c.d.CrncRate)))),

                                                  Credit = crncy == 0 ? (g.Sum(c => c.d.Credit)) :
                                                      (crncy == -1 ?
                                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.r.LastRate))) :
                                                      (g.Sum(c => c.d.Credit * (c.r == null ? 1 : c.d.CrncRate)))),

                                                  //Debit = grp.Where(d => d.Debit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Debit : 0),
                                                  //Credit = grp.Where(d => d.Credit != 0).DefaultIfEmpty().Sum(d => d != null ? d.Credit : 0),

                                              }).Distinct().ToList();
            }*/
            #endregion

            if (MerchndBal != null)
            {
                revExpDebit = MerchndBal.Sum(x => x.Debit);
                revExpCredit = MerchndBal.Sum(x => x.Credit);
            }
            //calculate summary
            if (revExpDebit + tradeDebit > revExpCredit + tradeCredit)//"مجمل الخساره:" 
            {
                netProfit = revExpCredit + tradeCredit - revExpDebit - tradeDebit;

                revExpDebit = revExpDebit + tradeDebit - revExpCredit - tradeCredit;
                revExpCredit = 0;
            }
            else if (revExpCredit + tradeCredit > revExpDebit + tradeDebit)//"مجمل الربح:" 
            {
                netProfit = revExpCredit + tradeCredit - revExpDebit - tradeDebit;

                revExpCredit = revExpCredit + tradeCredit - revExpDebit - tradeDebit;
                revExpDebit = 0;
            }
            else//"لايوجد ربح أو خسارة"
            {
                revExpDebit = revExpCredit = 0;
            }

        }


        public static string AccNumGenerated(ACC_Account parent)
        {
            ERPDataContext DB = new ERPDataContext();
            string NewNumber = string.Empty;
            int level = 0;
            string NewAccNum = "";

            //set level
            level = parent.Level + 1;

            //set number
            //add parent code
            NewNumber = parent.AcNumber;
            //add zero decimals, count of zero decimals equal to level
            for (int x = 0; x < level; x++)
                NewNumber += "0";

            string lastChildAccNum = (DB.ACC_Accounts.Where(a => a.ParentActId == parent.AccountId).
                 OrderBy(a => a.AcNumber).Select(a => a.AcNumber).ToList().DefaultIfEmpty("0").LastOrDefault()).ToString();

            string accNum_ = string.Empty;
            if (lastChildAccNum != "0")
            {
                NewAccNum = (Convert.ToDecimal(lastChildAccNum) + 1).ToString();
            }
            else
            {
                accNum_ = NewNumber.Substring(parent.AcNumber.Length);

                decimal accNum_decimal = Convert.ToDecimal(accNum_) + 1;

                //test that, new account doesn't exceed allowed number of childs for this parent
                if (accNum_decimal.ToString().Length > accNum_.Length)
                    return "-1";

                NewAccNum = parent.AcNumber + MyHelper.GetNextNumberInString(accNum_);
                if (NewAccNum.Length > 55)//max allowed length for account number in Database
                    return "-1";
            }
            return NewAccNum;
        }

        //public static string AccNumGenerated(ACC_Account parent)
        //{
        //    ERPDataContext DB = new ERPDataContext();
        //    string NewNumber = string.Empty;
        //    int level = 0;

        //    //set level
        //    level = parent.Level + 1;

        //    //set number
        //    //add parent code
        //    NewNumber = parent.AcNumber;
        //    //add zero decimals, count of zero decimals equal to level
        //    for (int x = 0; x < level; x++)
        //        NewNumber += "0";

        //    string lastChildAccNum = (DB.ACC_Accounts.Where(a => a.ParentActId == parent.AccountId).
        //         OrderBy(a => a.AcNumber).Select(a => a.AcNumber).ToList().DefaultIfEmpty("0").LastOrDefault()).ToString();

        //    string accNum_ = string.Empty;
        //    if (lastChildAccNum != "0")
        //        accNum_ = lastChildAccNum.Substring((lastChildAccNum.Length - level));
        //    else
        //        accNum_ = NewNumber.Substring(parent.AcNumber.Length);

        //    decimal accNum_decimal = Convert.ToDecimal(accNum_) + 1;

        //    //test that, new account doesn't exceed allowed number of childs for this parent
        //    if (accNum_decimal.ToString().Length > accNum_.Length)
        //        return "-1";

        //    string NewAccNum = parent.AcNumber + MyHelper.GetNextNumberInString(accNum_);
        //    if (NewAccNum.Length > 55)//max allowed length for account number in Database
        //        return "-1";

        //    return NewAccNum;
        //}

        public static string CcNumGenerated(ACC_CostCenter parent)
        {
            ERPDataContext DB = new ERPDataContext();
            string NewNumber = string.Empty;
            int level = 0;

            //set level
            level = parent == null ? 1 : parent.Level + 1;

            //set number
            //add parent code
            NewNumber = parent == null ? string.Empty : parent.ccNumber;
            //add zero decimals, count of zero decimals equal to level+1
            for (int x = 0; x <= level; x++)
                NewNumber += "0";

            char[] ccNumberArr = NewNumber.ToCharArray();
            string numberOfNewAccount = string.Empty;
            if (parent != null)
            {
                numberOfNewAccount = (DB.ACC_CostCenters.Where(a => a.ParentCCId == parent.CostCenterId).Count() + 1).ToString();
            }
            else
            {
                numberOfNewAccount = (DB.ACC_CostCenters.Where(a => a.Level == 1).Count() + 1).ToString();
            }

            //test that level doesn't exceed digits limits
            if (numberOfNewAccount.Length > (level + 1))
                return "-1";

            int numbIndex = numberOfNewAccount.Length - 1;


            //add new account number to the number
            for (int x = NewNumber.Length; x > NewNumber.Length - numberOfNewAccount.Length; x--)
            {
                ccNumberArr[x - 1] = numberOfNewAccount[numbIndex];
                numbIndex--;
            }

            //get string from array of char
            string lastNumber = string.Empty;
            foreach (char r in ccNumberArr)
                lastNumber += r;

            NewNumber = lastNumber;
            return NewNumber;
        }

        public static void MoveAccount(int accId, ACC_Account New_Parent)
        {
            ERPDataContext DB = new ERPDataContext();

            if (DB.ACC_JournalDetails.Where(x => x.AccountId == New_Parent.AccountId).Count() > 0)
                return;

            var acc = DB.ACC_Accounts.Where(x => x.AccountId == accId).FirstOrDefault();

            if (acc.Level == 1)
                return;

            var all_childs = DB.ACC_Accounts.Where(x => x.AcNumber.StartsWith(acc.AcNumber));

            foreach (var child_acc in all_childs)
            {
                child_acc.AcNumber = "0";
                DB.SubmitChanges();
            }

            acc.AcNumber = AccNumGenerated(New_Parent);
            acc.Level = New_Parent.Level + 1;
            acc.ParentActId = New_Parent.AccountId;

            DB.SubmitChanges();

            var childs = DB.ACC_Accounts.Where(x => x.ParentActId == accId).Select(x => x.AccountId);
            if (childs.Count() == 0)
                return;

            foreach (var c in childs)
                MoveAccount(c, acc);
        }

        public static List<FA_FixedAssetGroup> GetChildFixedAssetsGroups()
        {
            ERPDataContext DB = new ERPDataContext();
            List<FA_FixedAssetGroup> groups =
                DB.FA_FixedAssetGroups.Where(a =>
                   DB.FA_FixedAssetGroups.Where(b => b.ParentGrpId == a.FaGrpId).Count() < 1).
                   OrderBy(b => b.FaAcNumber).ToList();

            groups.Insert(0, new FA_FixedAssetGroup
            {
                FaGrpId = 0,
                FaGrpCode = "",
                FaGrpNameAr = "",
                FaGrpNameEn = "",
                Desc = "",
                ParentGrpId = null,
                FaAccountId = 0,
                FaAcNumber = "",
                DprAccountId = 0,
                DprAcNumber = "",
            });

            return groups;
        }


        public static string GetAccNo(int AccId)
        {
            ERPDataContext DB = new ERPDataContext();
            return DB.ACC_Accounts.Where(x => x.AccountId == AccId).Select(x => x.AcNumber).First();
        }

        public static void CreatePayNoteJournal(ACC_NotesPayable paper, string IsVendorText, string DealerText)
        {
            string note = string.Empty;
            string mainNote = string.Empty, ResponseNote = string.Empty;

            if ((byte)paper.NoteType == (byte)NoteType.check)
            {
                if (Shared.IsEnglish == true)
                    note = "Check Number " + paper.NoteNumber + " outgoing to " + IsVendorText + " " + DealerText + " Maturity of " + paper.DueDate.ToShortDateString();
                else
                    note = "شيك رقم " + paper.NoteNumber + " صادر ل " + IsVendorText + " " + DealerText + " استحقاق" + paper.DueDate.ToShortDateString();
            }
            else if ((byte)paper.NoteType == (byte)NoteType.Draft)
            {
                if (Shared.IsEnglish == true)
                    note = "Bill Number " + paper.NoteNumber + " outgoing to " + IsVendorText + " " + DealerText + " Maturity of " + paper.DueDate.ToShortDateString();
                else
                    note = "كمبيالة رقم " + paper.NoteNumber + " صادرة ل " + IsVendorText + " " + DealerText + " استحقاق" + paper.DueDate.ToShortDateString();
            }
            else if ((byte)paper.NoteType == (byte)NoteType.Installment)
            {
                if (Shared.IsEnglish == true)
                    note = "Installment Number " + paper.NoteNumber + " outgoing to " + IsVendorText + " " + DealerText + " Maturity of " + paper.DueDate.ToShortDateString();
                else
                    note = "قسط رقم " + paper.NoteNumber + " صادر ل " + IsVendorText + " " + DealerText + " استحقاق" + paper.DueDate.ToShortDateString();
            }
            if (!string.IsNullOrEmpty(paper.OutTrnsCode))
            {
                if (Shared.IsEnglish == true)
                    note += "\r\n"+ "exchange code" + paper.OutTrnsCode;
                else
                    note += "\r\n" + "كو الصرف "+ paper.OutTrnsCode ;
            }
                
            mainNote = note;
            //بيان بحالة الورقة
            if (paper.ResponseType == (byte)PayNoteResponseType.Payed)
            {
                ResponseNote = Shared.IsEnglish == true ? "** Payed Note **" : "** ورقة مسددة **";
                mainNote += ("\r\n" + ResponseNote);
            }
            else if (paper.ResponseType == (byte)PayNoteResponseType.Rejected)
            {
                ResponseNote = Shared.IsEnglish == true ? "** Rejected Note**" : "** ورقة مردودة **";
                mainNote += ("\r\n" + ResponseNote);
            }

            note += ("\r\n" + paper.Notes);
            mainNote += ("\r\n" + paper.Notes);

            DAL.ERPDataContext DB = new ERPDataContext();

            if ((paper.NoteType == (byte)NoteType.check || paper.NoteType == (byte)NoteType.Draft) && paper.IsOpenBalance == false)
            {
                /*قيد عمل ورقة دفع*/
                #region CreateNote_Jornal
                ACC_Journal j;
                if (!paper.RegJournalId.HasValue || paper.RegJournalId == 0)
                {
                    j = new ACC_Journal();
                    j.JCode = HelperAcc.Get_Jornal_Code();//MyHelper.Get_Server_DateTime());
                    j.JNumber = paper.NoteNumber;
                    j.InsertDate = paper.RegDate;// MyHelper.Get_Server_DateTime();
                    j.JNotes = mainNote;
                    j.ProcessId = (int)Process.NotesPayable;
                    j.SourceId = paper.PayNoteId;
                    j.InsertUser = Shared.UserId;
                    j.IsPosted = !Shared.OfflinePostToGL;
                    j.StoreId = paper.StoreId;
                    j.CrncId = paper.CrncId;
                    j.CrncRate = paper.CrncRate;
                    j.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(j.InsertDate, j.ProcessId);

                    DB.ACC_Journals.InsertOnSubmit(j);
                }
                else
                {
                    j = DB.ACC_Journals.Where(x => x.JournalId == paper.RegJournalId.Value).FirstOrDefault();
                    j.JNumber = paper.NoteNumber;
                    j.InsertDate = paper.RegDate;// MyHelper.Get_Server_DateTime();
                    j.JNotes = mainNote;
                    j.LastUpdateUser = Shared.UserId;
                    j.LastUpdateDate = MyHelper.Get_Server_DateTime();
                    j.StoreId = paper.StoreId;
                    j.CrncId = paper.CrncId;
                    j.CrncRate = paper.CrncRate;
                }
                DB.SubmitChanges();
                paper.RegJournalId = j.JournalId;
                #endregion

                /*تفاصيل القيد*/
                #region CreateNote_Jornal_Details
                DAL.ACC_JournalDetail d1 = new DAL.ACC_JournalDetail();
                d1.JournalId = j.JournalId;
                d1.AccountId = paper.DealerAccountId;
                d1.Debit = paper.Amount;
                d1.Credit = 0;
                d1.Notes = mainNote;
                d1.CostCenter = paper.CostCenter;
                d1.CrncId = paper.CrncId;
                d1.CrncRate = paper.CrncRate;

                DB.ACC_JournalDetails.InsertOnSubmit(d1);
                if (Shared.st_Store.OutstandingRecieveNote != false)
                {
                    DAL.ACC_JournalDetail d2 = new DAL.ACC_JournalDetail();
                    d2.JournalId = j.JournalId;
                    d2.AccountId = Shared.st_Store.NotesPayableAcc.Value;//اوراق الدفع
                    d2.Debit = 0;
                    d2.Credit = paper.Amount;
                    d2.Notes = mainNote;
                    d2.CrncId = paper.CrncId;
                    d2.CrncRate = paper.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(d2);
                }
                else
                {

                    DAL.ACC_JournalDetail d2 = new DAL.ACC_JournalDetail();
                    d2.JournalId = j.JournalId;
                    d2.AccountId = paper.DrawerAccountId.Value; // حساب السداد
                    d2.Debit = 0;
                    d2.Credit = paper.Amount;
                    d2.Notes = mainNote;
                    d2.CrncId = paper.CrncId;
                    d2.CrncRate = paper.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(d2);
                }
                DB.SubmitChanges();
                #endregion
            }
            /*مستحق*/
            if (paper.ResponseType == 1)
                return;

            /*في حالة السداد*/
            if (paper.ResponseType == (byte)PayNoteResponseType.Payed && Shared.st_Store.OutstandingRecieveNote != false)
            {
                if (paper.NoteType != (byte)NoteType.Installment)
                {
                    /*قيد سداد ورقة دفع*/
                    #region Pay_Note_Jornal
                    ACC_Journal payj;
                    if (paper.PayJournalId == null || paper.PayJournalId == 0)
                    {
                        payj = new ACC_Journal();
                        payj.JCode = HelperAcc.Get_Jornal_Code(); //MyHelper.Get_Server_DateTime());
                        payj.JNumber = paper.NoteNumber;
                        payj.InsertDate = paper.RespondDate.Value;                       //MyHelper.Get_Server_DateTime();
                        payj.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        payj.ProcessId = (int)Process.NotesPayable;
                        payj.SourceId = paper.PayNoteId;
                        payj.InsertUser = Shared.UserId;
                        payj.IsPosted = !Shared.OfflinePostToGL;
                        payj.StoreId = paper.StoreId;
                        payj.CrncId = paper.CrncId;
                        payj.CrncRate = paper.CrncRate;
                        payj.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(payj.InsertDate, payj.ProcessId);

                        DB.ACC_Journals.InsertOnSubmit(payj);
                    }
                    else
                    {
                        payj = DB.ACC_Journals.Where(x => x.JournalId == paper.PayJournalId).FirstOrDefault();
                        payj.JNumber = paper.NoteNumber;
                        payj.InsertDate = paper.RespondDate.Value;// MyHelper.Get_Server_DateTime();
                        payj.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        payj.LastUpdateUser = Shared.UserId;
                        payj.LastUpdateDate = MyHelper.Get_Server_DateTime();
                        payj.StoreId = paper.StoreId;
                        payj.CrncId = paper.CrncId;
                        payj.CrncRate = paper.CrncRate;

                    }
                    DB.SubmitChanges();
                    paper.PayJournalId = payj.JournalId;
                    #endregion

                    #region PayPapers_JornalDetail
                    DAL.ACC_JournalDetail d3 = new ACC_JournalDetail();
                    d3.JournalId = payj.JournalId;
                    d3.AccountId = Shared.st_Store.NotesPayableAcc.Value;//اوراق الدفع
                    d3.Debit = paper.Amount;
                    d3.Credit = 0;
                    d3.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                    d3.CrncId = paper.CrncId;
                    d3.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d3);
                    /*if (paper.NoteType == (byte)NoteType.check)       // سداد الشيك من حساب البنك
                    {
                        DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                        d4.JournalId = payj.JournalId;
                        d4.AccountId = paper.BankAccountId.Value;
                        d4.Debit = 0;
                        d4.Credit = paper.Amount;
                        d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        DB.ACC_JournalDetails.InsertOnSubmit(d4);
                    }
                    elseif (paper.NoteType == (byte)NoteType.Draft)      //سداد الكمبيالة من الخزينة
                    */
                    {
                        DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                        d4.JournalId = payj.JournalId;
                        d4.AccountId = paper.DrawerAccountId.Value;
                        d4.Debit = 0;
                        d4.Credit = paper.Amount;
                        d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        d4.CrncId = paper.CrncId;
                        d4.CrncRate = paper.CrncRate;

                        DB.ACC_JournalDetails.InsertOnSubmit(d4);
                    }
                    DB.SubmitChanges();
                    #endregion
                }
                else
                {
                    /*قيد سداد ورقة دفع*/
                    #region Pay_Note_Jornal
                    ACC_Journal payj;
                    if (paper.PayJournalId == null || paper.PayJournalId == 0)
                    {
                        payj = new ACC_Journal();
                        payj.JCode = HelperAcc.Get_Jornal_Code(); //MyHelper.Get_Server_DateTime());
                        payj.JNumber = paper.NoteNumber;
                        payj.InsertDate = paper.RespondDate.Value;                       //MyHelper.Get_Server_DateTime();
                        payj.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        payj.ProcessId = (int)Process.NotesPayable;
                        payj.SourceId = paper.PayNoteId;
                        payj.InsertUser = Shared.UserId;
                        payj.IsPosted = !Shared.OfflinePostToGL;
                        payj.StoreId = paper.StoreId;
                        payj.CrncId = paper.CrncId;
                        payj.CrncRate = paper.CrncRate;
                        payj.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(payj.InsertDate, payj.ProcessId);

                        DB.ACC_Journals.InsertOnSubmit(payj);
                    }
                    else
                    {
                        payj = DB.ACC_Journals.Where(x => x.JournalId == paper.PayJournalId).FirstOrDefault();
                        payj.JNumber = paper.NoteNumber;
                        payj.InsertDate = paper.RespondDate.Value;// MyHelper.Get_Server_DateTime();
                        payj.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        payj.LastUpdateUser = Shared.UserId;
                        payj.LastUpdateDate = MyHelper.Get_Server_DateTime();
                        payj.StoreId = paper.StoreId;
                        payj.CrncId = paper.CrncId;
                        payj.CrncRate = paper.CrncRate;

                    }
                    DB.SubmitChanges();
                    paper.PayJournalId = payj.JournalId;
                    #endregion

                    #region PayPapers_JornalDetail
                    DAL.ACC_JournalDetail d3 = new ACC_JournalDetail();
                    d3.JournalId = payj.JournalId;
                    d3.AccountId = paper.DealerAccountId;//حساب طرف التعامل
                    d3.Debit = paper.Amount;
                    d3.Credit = 0;
                    d3.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                    d3.CrncId = paper.CrncId;
                    d3.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d3);
                    /*if (paper.NoteType == (byte)NoteType.check)       // سداد الشيك من حساب البنك
                    {
                        DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                        d4.JournalId = payj.JournalId;
                        d4.AccountId = paper.BankAccountId.Value;
                        d4.Debit = 0;
                        d4.Credit = paper.Amount;
                        d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        DB.ACC_JournalDetails.InsertOnSubmit(d4);
                    }
                    elseif (paper.NoteType == (byte)NoteType.Draft)      //سداد الكمبيالة من الخزينة
                    */
                    {
                        DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                        d4.JournalId = payj.JournalId;
                        d4.AccountId = paper.DrawerAccountId.Value;
                        d4.Debit = 0;
                        d4.Credit = paper.Amount;
                        d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        d4.CrncId = paper.CrncId;
                        d4.CrncRate = paper.CrncRate;

                        DB.ACC_JournalDetails.InsertOnSubmit(d4);
                    }
                    DB.SubmitChanges();
                    #endregion
                }
                return;
            }

            if (paper.NoteType != (byte)NoteType.Installment)
            {
                /*في حالة رد الورقة*/
                if (paper.ResponseType == (byte)PayNoteResponseType.Rejected)
                {
                    /*قيد رد ورقة دفع*/
                    #region Pay_Note_Jornal
                    ACC_Journal RejectJ;
                    if (paper.PayJournalId == null || paper.PayJournalId == 0)
                    {
                        RejectJ = new ACC_Journal();
                        RejectJ.JCode = HelperAcc.Get_Jornal_Code();  //MyHelper.Get_Server_DateTime());
                        RejectJ.JNumber = paper.NoteNumber;
                        RejectJ.InsertDate = paper.RespondDate.Value;                        //MyHelper.Get_Server_DateTime();
                        RejectJ.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNoteBounce : ResAccAr.txtNoteBounce) + note;
                        RejectJ.ProcessId = (int)Process.NotesPayable;
                        RejectJ.SourceId = paper.PayNoteId;
                        RejectJ.InsertUser = Shared.UserId;
                        RejectJ.IsPosted = !Shared.OfflinePostToGL;
                        RejectJ.StoreId = paper.StoreId;
                        RejectJ.CrncId = paper.CrncId;
                        RejectJ.CrncRate = paper.CrncRate;
                        RejectJ.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(RejectJ.InsertDate, RejectJ.ProcessId);

                        DB.ACC_Journals.InsertOnSubmit(RejectJ);
                    }
                    else
                    {
                        RejectJ = DB.ACC_Journals.Where(x => x.JournalId == paper.PayJournalId).FirstOrDefault();
                        RejectJ.JNumber = paper.NoteNumber;
                        RejectJ.InsertDate = paper.RespondDate.Value;// MyHelper.Get_Server_DateTime();
                        RejectJ.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNoteBounce : ResAccAr.txtNoteBounce) + note;
                        RejectJ.LastUpdateUser = Shared.UserId;
                        RejectJ.LastUpdateDate = MyHelper.Get_Server_DateTime();
                        RejectJ.StoreId = paper.StoreId;
                        RejectJ.CrncId = paper.CrncId;
                        RejectJ.CrncRate = paper.CrncRate;
                    }
                    DB.SubmitChanges();
                    paper.PayJournalId = RejectJ.JournalId;
                    #endregion

                    #region Reject_Papers_JornalDetail
                    DAL.ACC_JournalDetail d3 = new ACC_JournalDetail();
                    d3.JournalId = RejectJ.JournalId;
                    d3.AccountId = Shared.st_Store.NotesPayableAcc.Value;//اوراق الدفع
                    d3.Debit = paper.Amount;
                    d3.Credit = 0;
                    d3.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNoteBounce : ResAccAr.txtNoteBounce) + note;
                    d3.CrncId = paper.CrncId;
                    d3.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d3);

                    // رد الطرف الاخر دائن مرة اخرى
                    DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                    d4.JournalId = RejectJ.JournalId;
                    d4.AccountId = paper.DealerAccountId;
                    d4.Debit = 0;
                    d4.Credit = paper.Amount;
                    d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNoteBounce : ResAccAr.txtNoteBounce) + note;
                    d4.CostCenter = paper.CostCenter;
                    d4.CrncId = paper.CrncId;
                    d4.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d4);

                    DB.SubmitChanges();
                    #endregion

                    return;
                }
            }
        }

        public static void CreateReceiveNoteJournal(ACC_NotesReceivable paper, string IsVendorText, string DealerText, List<ACC_CashExpense> expCash)
        {
            string note = string.Empty;
            string mainNote = string.Empty, ResponseNote = string.Empty;
            string undercollect = "";
            if (paper.Is_Undercollect)
                undercollect = Shared.IsEnglish == true ? " Under Collection " : " تحت التحصيل ";

            if (paper.NoteType == (byte)NoteType.check)
            {
                if (Shared.IsEnglish == true)
                    note = "Check Number " + paper.NoteNumber + undercollect + " ingoing from " + IsVendorText + " " + DealerText + " Maturity of " + paper.DueDate.ToShortDateString();
                else
                    note = "شيك رقم " + paper.NoteNumber + undercollect + " واردة من " + IsVendorText + " " + DealerText + " استحقاق" + paper.DueDate.ToShortDateString();
            }
            else if (paper.NoteType == (byte)NoteType.Draft)
            {
                if (Shared.IsEnglish == true)
                    note = "Bill Number " + paper.NoteNumber + undercollect + " ingoing from " + IsVendorText + " " + DealerText + " Maturity of " + paper.DueDate.ToShortDateString();
                else
                    note = "كمبيالة رقم " + paper.NoteNumber + undercollect + " واردة من " + IsVendorText + " " + DealerText + " استحقاق" + paper.DueDate.ToShortDateString();
            }
            else if (paper.NoteType == (byte)NoteType.Installment)
            {
                if (Shared.IsEnglish == true)
                    note = "Installment Number " + paper.NoteNumber + undercollect + " ingoing from " + IsVendorText + " " + DealerText + " Maturity of " + paper.DueDate.ToShortDateString();
                else
                    note = "قسط رقم " + paper.NoteNumber + undercollect + " وارد من " + IsVendorText + " " + DealerText + " استحقاق" + paper.DueDate.ToShortDateString();
            }
            if (!string.IsNullOrEmpty(paper.OutTrnsCode))
            {
                if (Shared.IsEnglish == true)
                    note += "\r\n" + "exchange code " + paper.OutTrnsCode;
                else
                    note += "\r\n" + " كود الصرف"+ paper.OutTrnsCode;
            }
            mainNote = note;
            //بيان بحالة الورقة
            if (paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToBank
                || paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToDrawer
                || paper.ResponseType == (byte)ReceiveNoteResponseType.Diverted)
            {
                ResponseNote = Shared.IsEnglish == true ? "** Payed Note **" : "** ورقة مسددة **";
                mainNote += ("\r\n" + ResponseNote);
            }
            else if (paper.ResponseType == (byte)ReceiveNoteResponseType.Rejected)
            {
                ResponseNote = Shared.IsEnglish == true ? "** Rejected Note**" : "** ورقة مردودة **";
                mainNote += ("\r\n" + ResponseNote);
            }

            note += ("\r\n" + paper.Notes);
            mainNote += ("\r\n" + paper.Notes);

            DAL.ERPDataContext DB = new ERPDataContext();

            if
            ((paper.NoteType == (byte)NoteType.check || paper.NoteType == (byte)NoteType.Draft) && paper.IsOpenBalance == false)
            {
                /*قيد ورقة قبض*/
                #region CreateNote_Jornal
                ACC_Journal j;
                if (!paper.RegJournalId.HasValue || paper.RegJournalId == 0)
                {
                    j = new ACC_Journal();
                    j.JCode = HelperAcc.Get_Jornal_Code();//MyHelper.Get_Server_DateTime());
                    j.JNumber = paper.NoteNumber;
                    j.InsertDate = paper.RegDate;//MyHelper.Get_Server_DateTime();
                    j.JNotes = mainNote;
                    j.ProcessId = (int)Process.NotesReceivable;
                    j.SourceId = paper.ReceiveId;
                    j.InsertUser = Shared.UserId;
                    j.StoreId = paper.StoreId;
                    j.IsPosted = !Shared.OfflinePostToGL;
                    j.CrncId = paper.CrncId;
                    j.CrncRate = paper.CrncRate;
                    j.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(j.InsertDate, j.ProcessId);

                    DB.ACC_Journals.InsertOnSubmit(j);
                }
                else
                {
                    j = DB.ACC_Journals.Where(x => x.JournalId == paper.RegJournalId.Value).FirstOrDefault();
                    if (j == null)
                        paper.RegJournalId = 0;
                    else
                    {
                        j.JNumber = paper.NoteNumber;
                        j.JNotes = mainNote;
                        j.LastUpdateUser = Shared.UserId;
                        j.LastUpdateDate = MyHelper.Get_Server_DateTime();
                        j.InsertDate = paper.RegDate;//MyHelper.Get_Server_DateTime();
                        j.StoreId = paper.StoreId;
                        j.CrncId = paper.CrncId;
                        j.CrncRate = paper.CrncRate;
                    }
                }
                DB.SubmitChanges();
                paper.RegJournalId = j.JournalId;
                #endregion

                /*تفاصيل القيد*/
                #region CreateNote_Jornal_Details

                //======================Samar======================//
              
                DAL.ACC_JournalDetail _d = new DAL.ACC_JournalDetail();
                decimal cashAmount = Convert.ToDecimal(paper.Amount);
                //if (expCash != null && expCash.Count > 0)
                //{
                //    cashAmount = Convert.ToDecimal(paper.Amount) - Convert.ToDecimal(expCash.Select(x => x.Amount).Sum());

                //    _d.JournalId = j.JournalId;

                //    _d.AccountId = Shared.st_Store.NotesReceivableAcc.Value;
                //    _d.Credit = 0;
                //    _d.Debit = Convert.ToDecimal(cashAmount);
                //    _d.Notes = note;
                //    _d.CostCenter = paper.CostCenter;
                //    _d.CrncId = paper.CrncId;
                //    _d.CrncRate = paper.CrncRate;

                //    DB.ACC_JournalDetails.InsertOnSubmit(_d);

                //    foreach (var expense in expCash)
                //    {
                //        DAL.ACC_JournalDetail aCC_JournalDetail = new DAL.ACC_JournalDetail();
                //        aCC_JournalDetail.JournalId = j.JournalId;
                //        aCC_JournalDetail.AccountId = expense.AccountId;
                //        aCC_JournalDetail.Credit = 0;
                //        aCC_JournalDetail.Debit = Convert.ToDecimal(expense.Amount);
                //        aCC_JournalDetail.Notes = "";
                //        aCC_JournalDetail.CrncId = paper.CrncId;
                //        aCC_JournalDetail.CrncRate = paper.CrncRate;
                //        aCC_JournalDetail.CostCenter = expense.CostCenterId;

                //        aCC_JournalDetail.CrncRate = paper.CrncRate;
                //        DB.ACC_JournalDetails.InsertOnSubmit(aCC_JournalDetail);
                       
                //    }
                //    DAL.ACC_JournalDetail d1 = new DAL.ACC_JournalDetail();
                //    d1.JournalId = j.JournalId;
                //    d1.AccountId = paper.DealerAccountId;
                //    d1.Debit = 0;
                //    d1.Credit = paper.Amount;
                //    d1.Notes = mainNote;
                //    d1.CostCenter = paper.CostCenter;
                //    d1.CrncId = paper.CrncId;
                //    d1.CrncRate = paper.CrncRate;

                //    DB.ACC_JournalDetails.InsertOnSubmit(d1);
                //}
                //else
                //{
                    DAL.ACC_JournalDetail d2 = new DAL.ACC_JournalDetail();
                    d2.JournalId = j.JournalId;
                    d2.AccountId = Shared.st_Store.NotesReceivableAcc.Value;// DB.ACC_Accounts.Where(a => a.OldName == "اوراق القبض").First().AccountId;
                    d2.Debit = paper.Amount;
                    d2.Credit = 0;
                    d2.Notes = mainNote;
                    d2.CrncId = paper.CrncId;
                    d2.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d2);

                    DAL.ACC_JournalDetail d1 = new DAL.ACC_JournalDetail();
                    d1.JournalId = j.JournalId;
                    d1.AccountId = paper.DealerAccountId;
                    d1.Debit = 0;
                    d1.Credit = paper.Amount;
                    d1.Notes = mainNote;
                    d1.CostCenter = paper.CostCenter;
                    d1.CrncId = paper.CrncId;
                    d1.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d1);

                    
                //}
                //=================================================//
              
                DB.SubmitChanges();
                #endregion
            }

            else if (paper.NoteType == (byte)NoteType.check && paper.IsOpenBalance && paper.RegDate < Shared.st_comp.FiscalYearStartDate)
            {
                DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(x => x.JournalId == paper.RegJournalId));
                DB.ACC_Journals.DeleteAllOnSubmit(DB.ACC_Journals.Where(x => x.JournalId == paper.RegJournalId));
                DB.SubmitChanges();
            }

            /*قيد تحت التحصيل*/
            if (paper.Is_Undercollect)
            {
                #region UnderCollection_Jornal
                ACC_Journal UnderCollectionJ;
                if (paper.UndercollectJournalId == null && !(paper.UndercollectDate <= Shared.st_comp.FiscalYearStartDate && paper.IsOpenBalance))
                {
                    UnderCollectionJ = new ACC_Journal();
                    UnderCollectionJ.JCode = HelperAcc.Get_Jornal_Code();//MyHelper.Get_Server_DateTime());
                    UnderCollectionJ.JNumber = paper.NoteNumber;
                    UnderCollectionJ.InsertDate = paper.UndercollectDate.Value.Date;
                    UnderCollectionJ.JNotes = mainNote;
                    UnderCollectionJ.ProcessId = (int)Process.NotesReceivable;
                    UnderCollectionJ.SourceId = paper.ReceiveId;
                    UnderCollectionJ.InsertUser = Shared.UserId;
                    UnderCollectionJ.StoreId = paper.StoreId;
                    UnderCollectionJ.IsPosted = !Shared.OfflinePostToGL;
                    UnderCollectionJ.CrncId = paper.CrncId;
                    UnderCollectionJ.CrncRate = paper.CrncRate;
                    UnderCollectionJ.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(UnderCollectionJ.InsertDate, UnderCollectionJ.ProcessId);
                    DB.ACC_Journals.InsertOnSubmit(UnderCollectionJ);
                }
                else
                {
                    UnderCollectionJ = DB.ACC_Journals.Where(x => x.JournalId == paper.UndercollectJournalId).FirstOrDefault();

                    //added by mohammad 07-04-2021
                    if (UnderCollectionJ != null && !(UnderCollectionJ.InsertDate <= Shared.st_comp.FiscalYearStartDate && paper.IsOpenBalance))//لو الورقة رصيد افتتاحي وتاريخ تحت التحصيل قبل بداية العام المالي اذا قيد تحت التحصيل من ضمن القيد الافتتاحي
                    {
                        UnderCollectionJ.JNumber = paper.NoteNumber;
                        UnderCollectionJ.InsertDate = paper.UndercollectDate.Value.Date;
                        UnderCollectionJ.JNotes = mainNote;
                        UnderCollectionJ.LastUpdateUser = Shared.UserId;
                        UnderCollectionJ.LastUpdateDate = MyHelper.Get_Server_DateTime();
                        UnderCollectionJ.InsertDate = paper.UndercollectDate.Value.Date;
                        UnderCollectionJ.StoreId = paper.StoreId;
                        UnderCollectionJ.CrncId = paper.CrncId;
                        UnderCollectionJ.CrncRate = paper.CrncRate;
                    }
                    else if (UnderCollectionJ != null && (UnderCollectionJ.InsertDate <= Shared.st_comp.FiscalYearStartDate && paper.IsOpenBalance))
                    {
                        DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(x => x.JournalId == UnderCollectionJ.JournalId));
                        DB.ACC_Journals.DeleteAllOnSubmit(DB.ACC_Journals.Where(x => x.JournalId == UnderCollectionJ.JournalId));
                        DB.SubmitChanges();
                    }
                }

                DB.SubmitChanges();
                paper.UndercollectJournalId = UnderCollectionJ?.JournalId;
                #endregion

                /*تفاصيل القيد*/
                if (UnderCollectionJ != null && !(UnderCollectionJ.InsertDate <= Shared.st_comp.FiscalYearStartDate && paper.IsOpenBalance))
                {
                    #region CreateUnderCollectNote_Jornal_Details

                    DAL.ACC_JournalDetail d_undercollect1 = new DAL.ACC_JournalDetail();
                    d_undercollect1.JournalId = UnderCollectionJ.JournalId;
                    d_undercollect1.AccountId = Shared.st_Store.RecieveNotesUnderCollectAccId.Value;   //شيكات تحت التحصيل
                    d_undercollect1.Debit = paper.Amount;
                    d_undercollect1.Credit = 0;
                    d_undercollect1.Notes = mainNote;
                    d_undercollect1.CrncId = paper.CrncId;
                    d_undercollect1.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d_undercollect1);

                    DAL.ACC_JournalDetail d_undercollect2 = new DAL.ACC_JournalDetail();
                    d_undercollect2.JournalId = UnderCollectionJ.JournalId;
                    d_undercollect2.AccountId = Shared.st_Store.NotesReceivableAcc.Value;              // اوراق القبض
                    d_undercollect2.Debit = 0;
                    d_undercollect2.Credit = paper.Amount;
                    d_undercollect2.Notes = mainNote;
                    d_undercollect2.CostCenter = paper.CostCenter;
                    d_undercollect2.CrncId = paper.CrncId;
                    d_undercollect2.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d_undercollect2);
                    DB.SubmitChanges();
                    #endregion
                }
            }

            /*مستحق*/
            if (paper.ResponseType == 1)
                return;

            /*في حالة السداد*/
            if (paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToBank
                || paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToDrawer
                || paper.ResponseType == (byte)ReceiveNoteResponseType.Diverted)
            {
                if (paper.NoteType != (byte)NoteType.Installment)
                {
                    /*قيد سداد ورقة قبض*/
                    #region Recieve_Note_Jornal
                    ACC_Journal payj;
                    if (paper.PayJournalId == null || paper.PayJournalId == 0)
                    {
                        payj = new ACC_Journal();
                        payj.JCode = HelperAcc.Get_Jornal_Code(); //MyHelper.Get_Server_DateTime());
                        payj.JNumber = paper.NoteNumber;
                        payj.InsertDate = paper.RespondDate.Value;                       //MyHelper.Get_Server_DateTime();
                        payj.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        payj.ProcessId = (int)Process.NotesReceivable;
                        payj.SourceId = paper.ReceiveId;
                        payj.InsertUser = Shared.UserId;
                        payj.IsPosted = !Shared.OfflinePostToGL;
                        payj.StoreId = paper.StoreId;
                        payj.CrncId = paper.CrncId;
                        payj.CrncRate = paper.CrncRate;

                        DB.ACC_Journals.InsertOnSubmit(payj);
                    }
                    else
                    {
                        payj = DB.ACC_Journals.Where(x => x.JournalId == paper.PayJournalId).FirstOrDefault();
                        payj.JNumber = paper.NoteNumber;
                        payj.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        payj.LastUpdateUser = Shared.UserId;
                        payj.LastUpdateDate = MyHelper.Get_Server_DateTime();
                        payj.InsertDate = paper.RespondDate.Value;                       //MyHelper.Get_Server_DateTime();
                        payj.StoreId = paper.StoreId;
                        payj.CrncId = paper.CrncId;
                        payj.CrncRate = paper.CrncRate;
                    }
                    payj.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(payj.InsertDate, payj.ProcessId);
                    DB.SubmitChanges();
                    paper.PayJournalId = payj.JournalId;
                    #endregion

                    #region RecieveNote_JornalDetail

                    var cashAmount = Convert.ToDecimal(paper.Amount);
                    var expensesAm = Convert.ToDecimal(expCash.Select(x => x.Amount).Sum());

                    // سداد الشيك الى حساب البنك
                    if (paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToBank)
                    {
                        DAL.ACC_JournalDetail d5 = new DAL.ACC_JournalDetail();
                        
                        d5.JournalId = payj.JournalId;

                        d5.AccountId = paper.BankAccountId.Value;
                        d5.Credit = 0;
                        d5.Debit = Convert.ToDecimal(cashAmount);
                        d5.Notes = note;
                        d5.CostCenter = paper.CostCenter;
                        d5.CrncId = paper.CrncId;
                        d5.CrncRate = paper.CrncRate;

                        DB.ACC_JournalDetails.InsertOnSubmit(d5);
                        
                       
                    }
                    // سداد الشيك الى حساب الخزينة
                    if (paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToDrawer)
                    {
                        
                        DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                        d4.JournalId = payj.JournalId;
                        d4.AccountId = paper.DrawerAccountId.Value;
                        d4.Debit = paper.Amount;
                        d4.Credit = 0;
                        d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        d4.CrncId = paper.CrncId;
                        d4.CrncRate = paper.CrncRate;

                        DB.ACC_JournalDetails.InsertOnSubmit(d4);
                       
                    }
                    // تظهير الشيك الى حساب اخر
                    if (paper.ResponseType == (byte)ReceiveNoteResponseType.Diverted)
                    {
                        DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                        d4.JournalId = payj.JournalId;
                        d4.AccountId = paper.DivertedDealerAccountId.Value;
                        d4.Debit = paper.Amount;
                        d4.Credit = 0;
                        d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNoteEndorse : ResAccAr.txtNoteEndorse) + note;
                        d4.CrncId = paper.CrncId;
                        d4.CrncRate = paper.CrncRate;

                        DB.ACC_JournalDetails.InsertOnSubmit(d4);
                    }

                
                        DAL.ACC_JournalDetail d3 = new ACC_JournalDetail();
                        d3.JournalId = payj.JournalId;
                        if (paper.Is_Undercollect)
                            d3.AccountId = Shared.st_Store.RecieveNotesUnderCollectAccId.Value;    // شيكات تحت التحصيل
                        else
                            d3.AccountId = Shared.st_Store.NotesReceivableAcc.Value;               // اوراق القبض
                        d3.Debit = 0;
                        d3.Credit = paper.Amount;
                        d3.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        d3.CrncId = paper.CrncId;
                        d3.CrncRate = paper.CrncRate;

                        DB.ACC_JournalDetails.InsertOnSubmit(d3);


                    if (expCash != null && expCash.Count > 0 && (paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToBank
                || paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToDrawer))
                    {
                        foreach (var expense in expCash)
                        {
                            DAL.ACC_JournalDetail aCC_JournalDetail = new DAL.ACC_JournalDetail();
                            aCC_JournalDetail.JournalId = payj.JournalId;
                            aCC_JournalDetail.AccountId = expense.AccountId;
                            aCC_JournalDetail.Credit = 0;
                            aCC_JournalDetail.Debit = Convert.ToDecimal(expense.Amount);
                            aCC_JournalDetail.Notes = "";
                            aCC_JournalDetail.CrncId = paper.CrncId;
                            aCC_JournalDetail.CrncRate = paper.CrncRate;
                            aCC_JournalDetail.CostCenter = expense.CostCenterId;

                            aCC_JournalDetail.CrncRate = paper.CrncRate;
                            DB.ACC_JournalDetails.InsertOnSubmit(aCC_JournalDetail);

                        }

                        DAL.ACC_JournalDetail d5 = new DAL.ACC_JournalDetail();

                        d5.JournalId = payj.JournalId;

                        d5.AccountId = paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToBank ? 
                            paper.BankAccountId.Value : paper.DrawerAccountId.Value;
                        d5.Debit = 0;
                        d5.Credit = Convert.ToDecimal(expensesAm);
                        d5.Notes = note;
                        d5.CostCenter = paper.CostCenter;
                        d5.CrncId = paper.CrncId;
                        d5.CrncRate = paper.CrncRate;

                        DB.ACC_JournalDetails.InsertOnSubmit(d5);

                    }


                    DB.SubmitChanges();
                    #endregion
                }
                else
                {
                    /*قيد سداد ورقة قبض*/
                    #region Recieve_Note_Jornal
                    ACC_Journal payj;
                    if (paper.PayJournalId == null || paper.PayJournalId == 0)
                    {
                        payj = new ACC_Journal();
                        payj.JCode = HelperAcc.Get_Jornal_Code(); //MyHelper.Get_Server_DateTime());
                        payj.JNumber = paper.NoteNumber;
                        payj.InsertDate = paper.RespondDate.Value;                       //MyHelper.Get_Server_DateTime();
                        payj.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        payj.ProcessId = (int)Process.NotesReceivable;
                        payj.SourceId = paper.ReceiveId;
                        payj.InsertUser = Shared.UserId;
                        payj.IsPosted = !Shared.OfflinePostToGL;
                        payj.StoreId = paper.StoreId;
                        payj.CrncId = paper.CrncId;
                        payj.CrncRate = paper.CrncRate;

                        DB.ACC_Journals.InsertOnSubmit(payj);
                    }
                    else
                    {
                        payj = DB.ACC_Journals.Where(x => x.JournalId == paper.PayJournalId).FirstOrDefault();
                        payj.JNumber = paper.NoteNumber;
                        payj.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                        payj.LastUpdateUser = Shared.UserId;
                        payj.LastUpdateDate = MyHelper.Get_Server_DateTime();
                        payj.InsertDate = paper.RespondDate.Value;                       //MyHelper.Get_Server_DateTime();
                        payj.StoreId = paper.StoreId;
                        payj.CrncId = paper.CrncId;
                        payj.CrncRate = paper.CrncRate;
                    }
                    payj.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(payj.InsertDate, payj.ProcessId);
                    DB.SubmitChanges();
                    paper.PayJournalId = payj.JournalId;
                    #endregion

                    #region RecieveNote_JornalDetail

                    // سداد الشيك الى حساب البنك
                    if (paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToBank)
                    {
                        //==========samar=============//
                        if (expCash != null && expCash.Count > 0)
                        {
                            DAL.ACC_JournalDetail d5 = new DAL.ACC_JournalDetail();
                            var cashAmount = Convert.ToDecimal(paper.Amount) - Convert.ToDecimal(expCash.Select(x => x.Amount).Sum());

                            d5.JournalId = payj.JournalId;

                            d5.AccountId = paper.BankAccountId.Value;
                            d5.Credit = 0;
                            d5.Debit = Convert.ToDecimal(cashAmount);
                            d5.Notes = note;
                            d5.CostCenter = paper.CostCenter;
                            d5.CrncId = paper.CrncId;
                            d5.CrncRate = paper.CrncRate;

                            DB.ACC_JournalDetails.InsertOnSubmit(d5);

                            foreach (var expense in expCash)
                            {
                                DAL.ACC_JournalDetail aCC_JournalDetail = new DAL.ACC_JournalDetail();
                                aCC_JournalDetail.JournalId = payj.JournalId;
                                aCC_JournalDetail.AccountId = expense.AccountId;
                                aCC_JournalDetail.Credit = 0;
                                aCC_JournalDetail.Debit = Convert.ToDecimal(expense.Amount);
                                aCC_JournalDetail.Notes = "";
                                aCC_JournalDetail.CrncId = paper.CrncId;
                                aCC_JournalDetail.CrncRate = paper.CrncRate;
                                aCC_JournalDetail.CostCenter = expense.CostCenterId;

                                aCC_JournalDetail.CrncRate = paper.CrncRate;
                                DB.ACC_JournalDetails.InsertOnSubmit(aCC_JournalDetail);

                            }

                        }
                        //=======================//
                        else
                        {
                            DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                            d4.JournalId = payj.JournalId;
                            d4.AccountId = paper.BankAccountId.Value;
                            d4.Debit = paper.Amount;
                            d4.Credit = 0;
                            d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                            d4.CrncId = paper.CrncId;
                            d4.CrncRate = paper.CrncRate;

                            DB.ACC_JournalDetails.InsertOnSubmit(d4);
                        }
                       
                    }
                    // سداد الشيك الى حساب الخزينة
                    if (paper.ResponseType == (byte)ReceiveNoteResponseType.PayedToDrawer)
                    {
                        //==========samar=============//
                        if (expCash != null && expCash.Count > 0)
                        {
                            DAL.ACC_JournalDetail d5 = new DAL.ACC_JournalDetail();
                            var cashAmount = Convert.ToDecimal(paper.Amount) - Convert.ToDecimal(expCash.Select(x => x.Amount).Sum());

                            d5.JournalId = payj.JournalId;

                            d5.AccountId = paper.BankAccountId.Value;
                            d5.Credit = 0;
                            d5.Debit = Convert.ToDecimal(cashAmount);
                            d5.Notes = note;
                            d5.CostCenter = paper.CostCenter;
                            d5.CrncId = paper.CrncId;
                            d5.CrncRate = paper.CrncRate;

                            DB.ACC_JournalDetails.InsertOnSubmit(d5);

                            foreach (var expense in expCash)
                            {
                                DAL.ACC_JournalDetail aCC_JournalDetail = new DAL.ACC_JournalDetail();
                                aCC_JournalDetail.JournalId = payj.JournalId;
                                aCC_JournalDetail.AccountId = expense.AccountId;
                                aCC_JournalDetail.Credit = 0;
                                aCC_JournalDetail.Debit = Convert.ToDecimal(expense.Amount);
                                aCC_JournalDetail.Notes = "";
                                aCC_JournalDetail.CrncId = paper.CrncId;
                                aCC_JournalDetail.CrncRate = paper.CrncRate;
                                aCC_JournalDetail.CostCenter = expense.CostCenterId;

                                aCC_JournalDetail.CrncRate = paper.CrncRate;
                                DB.ACC_JournalDetails.InsertOnSubmit(aCC_JournalDetail);

                            }

                        }

                        //=======================//
                        else
                        {
                            DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                            d4.JournalId = payj.JournalId;
                            d4.AccountId = paper.DrawerAccountId.Value;
                            d4.Debit = paper.Amount;
                            d4.Credit = 0;
                            d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                            d4.CrncId = paper.CrncId;
                            d4.CrncRate = paper.CrncRate;

                            DB.ACC_JournalDetails.InsertOnSubmit(d4);
                        }
                        
                    }

                    DAL.ACC_JournalDetail d3 = new ACC_JournalDetail();
                    d3.JournalId = payj.JournalId;

                    d3.AccountId = paper.DealerAccountId;//حساب المتعامل

                    d3.Debit = 0;
                    d3.Credit = paper.Amount;
                    d3.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNotePay : ResAccAr.txtNotePay) + note;
                    d3.CrncId = paper.CrncId;
                    d3.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d3);

                    DB.SubmitChanges();
                    #endregion
                }
                return;
            }

            if (paper.NoteType != (byte)NoteType.Installment)
            {
                /*في حالة رد الورقة*/
                if (paper.ResponseType == (byte)ReceiveNoteResponseType.Rejected)
                {
                    /*قيد رد ورقة قبض*/
                    #region Pay_Note_Jornal
                    ACC_Journal RejectJ;
                    if (paper.PayJournalId == null || paper.PayJournalId == 0)
                    {
                        RejectJ = new ACC_Journal();
                        RejectJ.JCode = HelperAcc.Get_Jornal_Code();  //MyHelper.Get_Server_DateTime());
                        RejectJ.JNumber = paper.NoteNumber;
                        RejectJ.InsertDate = paper.RespondDate.Value;                        //MyHelper.Get_Server_DateTime();
                        RejectJ.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNoteBounce : ResAccAr.txtNoteBounce) + note;
                        RejectJ.ProcessId = (int)Process.NotesReceivable;
                        RejectJ.SourceId = paper.ReceiveId;
                        RejectJ.InsertUser = Shared.UserId;
                        RejectJ.IsPosted = !Shared.OfflinePostToGL;
                        RejectJ.StoreId = paper.StoreId;
                        RejectJ.CrncId = paper.CrncId;
                        RejectJ.CrncRate = paper.CrncRate;
                        RejectJ.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(RejectJ.InsertDate, RejectJ.ProcessId);

                        DB.ACC_Journals.InsertOnSubmit(RejectJ);
                    }
                    else
                    {
                        RejectJ = DB.ACC_Journals.Where(x => x.JournalId == paper.PayJournalId).FirstOrDefault();
                        RejectJ.JNumber = paper.NoteNumber;
                        RejectJ.JNotes = (Shared.IsEnglish == true ? ResAccEn.txtNoteBounce : ResAccAr.txtNoteBounce) + note;
                        RejectJ.LastUpdateUser = Shared.UserId;
                        RejectJ.LastUpdateDate = MyHelper.Get_Server_DateTime();
                        RejectJ.InsertDate = paper.RespondDate.Value;                        //MyHelper.Get_Server_DateTime();
                        RejectJ.StoreId = paper.StoreId;
                        RejectJ.CrncId = paper.CrncId;
                        RejectJ.CrncRate = paper.CrncRate;
                    }
                    DB.SubmitChanges();
                    paper.PayJournalId = RejectJ.JournalId;
                    #endregion

                    #region Reject_Papers_JornalDetail
                    // رد الطرف الاخر مدين مرة اخرى
                    DAL.ACC_JournalDetail d4 = new DAL.ACC_JournalDetail();
                    d4.JournalId = RejectJ.JournalId;
                    d4.AccountId = paper.DealerAccountId;
                    d4.Debit = paper.Amount;
                    d4.Credit = 0;
                    d4.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNoteBounce : ResAccAr.txtNoteBounce) + note;
                    d4.CostCenter = paper.CostCenter;
                    d4.CrncId = paper.CrncId;
                    d4.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d4);

                    DAL.ACC_JournalDetail d3 = new ACC_JournalDetail();
                    d3.JournalId = RejectJ.JournalId;
                    if (paper.Is_Undercollect)
                        d3.AccountId = Shared.st_Store.RecieveNotesUnderCollectAccId.Value;    // شيكات تحت التحصيل
                    else
                        d3.AccountId = Shared.st_Store.NotesReceivableAcc.Value;               // اوراق القبض                
                    d3.Debit = 0;
                    d3.Credit = paper.Amount;
                    d3.Notes = (Shared.IsEnglish == true ? ResAccEn.txtNoteBounce : ResAccAr.txtNoteBounce) + note;
                    d3.CrncId = paper.CrncId;
                    d3.CrncRate = paper.CrncRate;

                    DB.ACC_JournalDetails.InsertOnSubmit(d3);

                    DB.SubmitChanges();
                    #endregion

                    return;
                }
            }
        }

    }

    public class acc
    {
        int accId;
        int accSecurityLevel;
        string accName;
        int? parentId;
        int nodeLevel;
        bool? costCenter;
        string accNumber;
        bool? acType;
        string accNameEn;

        public string AccNameEn
        {
            get { return accNameEn; }
            set { accNameEn = value; }
        }
        public string AccNumber
        {
            get { return accNumber; }
            set { accNumber = value; }
        }
        public bool? CostCenter
        {
            get { return costCenter; }
            set { costCenter = value; }
        }

        public int NodeLevel
        {
            get { return nodeLevel; }
            set { nodeLevel = value; }
        }
        public int? ParentId
        {
            get { return parentId; }
            set { parentId = value; }
        }
        public int AccId
        {
            get { return accId; }
            set { accId = value; }
        }

        public string AccName
        {
            get { return accName; }
            set { accName = value; }
        }

        public int AccSecurityLevel
        {
            get { return accSecurityLevel; }
            set { accSecurityLevel = value; }
        }

        public bool? AcType
        {
            get { return acType; }
            set { acType = value; }
        }
    }

    public class Trial
    {
        int accountId;
        string accountName;
        decimal beforeDebit;
        decimal beforeCredit;
        decimal debit;
        decimal credit;
        int? parentId;
        int nodeLevel;
        bool isParent;

        decimal totalDebit;

        public decimal TotalDebit
        {
            get { return totalDebit; }
            set { totalDebit = value; }
        }
        decimal totalCredit;

        public decimal TotalCredit
        {
            get { return totalCredit; }
            set { totalCredit = value; }
        }
        decimal netDebit;

        public decimal NetDebit
        {
            get { return netDebit; }
            set { netDebit = value; }
        }
        decimal netCredit;

        public decimal NetCredit
        {
            get { return netCredit; }
            set { netCredit = value; }
        }

        public bool IsParent
        {
            get { return isParent; }
            set { isParent = value; }
        }

        public int NodeLevel
        {
            get { return nodeLevel; }
            set { nodeLevel = value; }
        }

        public int? ParentId
        {
            get { return parentId; }
            set { parentId = value; }
        }
        public decimal Credit
        {
            get { return credit; }
            set { credit = value; }
        }
        public decimal Debit
        {
            get { return debit; }
            set { debit = value; }
        }
        public decimal BeforeCredit
        {
            get { return beforeCredit; }
            set { beforeCredit = value; }
        }
        public decimal BeforeDebit
        {
            get { return beforeDebit; }
            set { beforeDebit = value; }
        }
        public string AccountName
        {
            get { return accountName; }
            set { accountName = value; }
        }
        public int AccountId
        {
            get { return accountId; }
            set { accountId = value; }
        }

        public string AcNumber { get; set; }
    }

    public class TrialMonths
    {
        int accountId;
        string accountName, acNumber;


        decimal openDebit;

        public string AcNumber
        {
            get { return acNumber; }
            set { acNumber = value; }
        }

        public decimal OpenDebit
        {
            get { return openDebit; }
            set { openDebit = value; }
        }
        decimal openCredit;

        public decimal OpenCredit
        {
            get { return openCredit; }
            set { openCredit = value; }
        }

        decimal janDebit;

        public decimal JanDebit
        {
            get { return janDebit; }
            set { janDebit = value; }
        }
        decimal janCredit;

        public decimal JanCredit
        {
            get { return janCredit; }
            set { janCredit = value; }
        }

        decimal febDebit;

        public decimal FebDebit
        {
            get { return febDebit; }
            set { febDebit = value; }
        }
        decimal febCredit;

        public decimal FebCredit
        {
            get { return febCredit; }
            set { febCredit = value; }
        }

        decimal marchDebit;

        public decimal MarchDebit
        {
            get { return marchDebit; }
            set { marchDebit = value; }
        }
        decimal marchCredit;

        public decimal MarchCredit
        {
            get { return marchCredit; }
            set { marchCredit = value; }
        }

        decimal aprilDebit;

        public decimal AprilDebit
        {
            get { return aprilDebit; }
            set { aprilDebit = value; }
        }
        decimal aprilCredit;

        public decimal AprilCredit
        {
            get { return aprilCredit; }
            set { aprilCredit = value; }
        }

        decimal mayDebit;

        public decimal MayDebit
        {
            get { return mayDebit; }
            set { mayDebit = value; }
        }
        decimal mayCredit;

        public decimal MayCredit
        {
            get { return mayCredit; }
            set { mayCredit = value; }
        }

        decimal juneDebit;

        public decimal JuneDebit
        {
            get { return juneDebit; }
            set { juneDebit = value; }
        }
        decimal juneCredit;

        public decimal JuneCredit
        {
            get { return juneCredit; }
            set { juneCredit = value; }
        }

        decimal julyDebit;

        public decimal JulyDebit
        {
            get { return julyDebit; }
            set { julyDebit = value; }
        }
        decimal julyCredit;

        public decimal JulyCredit
        {
            get { return julyCredit; }
            set { julyCredit = value; }
        }

        decimal augDebit;

        public decimal AugDebit
        {
            get { return augDebit; }
            set { augDebit = value; }
        }
        decimal augCredit;

        public decimal AugCredit
        {
            get { return augCredit; }
            set { augCredit = value; }
        }

        decimal sepDebit;

        public decimal SepDebit
        {
            get { return sepDebit; }
            set { sepDebit = value; }
        }
        decimal sepCredit;

        public decimal SepCredit
        {
            get { return sepCredit; }
            set { sepCredit = value; }
        }

        decimal octDebit;

        public decimal OctDebit
        {
            get { return octDebit; }
            set { octDebit = value; }
        }
        decimal octCredit;

        public decimal OctCredit
        {
            get { return octCredit; }
            set { octCredit = value; }
        }

        decimal novDebit;

        public decimal NovDebit
        {
            get { return novDebit; }
            set { novDebit = value; }
        }
        decimal novCredit;

        public decimal NovCredit
        {
            get { return novCredit; }
            set { novCredit = value; }
        }

        decimal decDebit;

        public decimal DecDebit
        {
            get { return decDebit; }
            set { decDebit = value; }
        }
        decimal decCredit;

        public decimal DecCredit
        {
            get { return decCredit; }
            set { decCredit = value; }
        }


        int? parentId;
        int nodeLevel;
        bool isParent;

        decimal totalDebit;

        public decimal TotalDebit
        {
            get { return totalDebit; }
            set { totalDebit = value; }
        }
        decimal totalCredit;

        public decimal TotalCredit
        {
            get { return totalCredit; }
            set { totalCredit = value; }
        }
        decimal netDebit;

        public decimal NetDebit
        {
            get { return netDebit; }
            set { netDebit = value; }
        }
        decimal netCredit;

        public decimal NetCredit
        {
            get { return netCredit; }
            set { netCredit = value; }
        }

        public bool IsParent
        {
            get { return isParent; }
            set { isParent = value; }
        }

        public int NodeLevel
        {
            get { return nodeLevel; }
            set { nodeLevel = value; }
        }

        public int? ParentId
        {
            get { return parentId; }
            set { parentId = value; }
        }

        public string AccountName
        {
            get { return accountName; }
            set { accountName = value; }
        }
        public int AccountId
        {
            get { return accountId; }
            set { accountId = value; }
        }
    }

    /// <summary>
    /// used in account statement
    /// </summary>
    class stRow
    {
        double debit;

        public double Debit
        {
            get { return debit; }
            set { debit = value; }
        }
        double credit;

        public double Credit
        {
            get { return credit; }
            set { credit = value; }
        }
        string notes;

        public string Notes
        {
            get { return notes; }
            set { notes = value; }
        }
        int jCode;

        public int JCode
        {
            get { return jCode; }
            set { jCode = value; }
        }
        DateTime insertDate;

        public DateTime InsertDate
        {
            get { return insertDate; }
            set { insertDate = value; }
        }
        string processName;

        public string ProcessName
        {
            get { return processName; }
            set { processName = value; }
        }
        int processId;

        public int ProcessId
        {
            get { return processId; }
            set { processId = value; }
        }
        int sourceId;

        public int SourceId
        {
            get { return sourceId; }
            set { sourceId = value; }
        }
        int journalId;

        public int JournalId
        {
            get { return journalId; }
            set { journalId = value; }
        }
        DateTime? dueDate;

        public DateTime? DueDate
        {
            get { return dueDate; }
            set { dueDate = value; }
        }
        bool? acType;

        public bool? AcType
        {
            get { return acType; }
            set { acType = value; }
        }
    }

    /// <summary>
    /// used in account statement
    /// </summary>
    public class DueAmounts
    {
        decimal p0;
        public decimal P0
        {
            get { return p0; }
            set { p0 = value; }
        }

        decimal p1;
        public decimal P1
        {
            get { return p1; }
            set { p1 = value; }
        }

        decimal p2;
        public decimal P2
        {
            get { return p2; }
            set { p2 = value; }
        }

        decimal p3;
        public decimal P3
        {
            get { return p3; }
            set { p3 = value; }
        }

        decimal p4;
        public decimal P4
        {
            get { return p4; }
            set { p4 = value; }
        }

        decimal p5;
        public decimal P5
        {
            get { return p5; }
            set { p5 = value; }
        }
    }

    /// <summary>
    /// used in accounts statements
    /// </summary>
    public class period
    {
        int periodId;
        public int PeriodId
        {
            get { return periodId; }
            set { periodId = value; }
        }

        DateTime periodDate;
        public DateTime PeriodDate
        {
            get { return periodDate; }
            set { periodDate = value; }
        }
    }

    public enum NotePayOrReceive
    {
        Payable = 1,            //ورق دفع
        Receivable = 2          //ورق قبض
    }
    public enum NoteType
    {
        check = 1,              //شيك
        Draft = 2,               //كمبيالة
        Installment = 3
    }
    public enum PayNoteResponseType
    {
        Still = 1,              //مستحق
        Payed = 2,              //مسدد
        Rejected = 3            //مردود
    }
    public enum ReceiveNoteResponseType
    {
        Still = 1,              //مستحق
        Rejected = 2,           //مردود
        PayedToDrawer = 3,      //مسدد بالخزينة
        PayedToBank = 4,        //مسدد لحساب بنك
        Diverted = 5,            //مظهر لشخص اخر
        ParialPayment = 7            //سداد جزئي
    }

    public enum ItemType
    {
        Inventory = 0,
        Service = 1,
        Assembly = 2,
        MatrixParent = 3,
        MatrixDetail = 4,
        Subtotal = 5
    }

    public enum CostCenter
    {
        Optional = 0,
        Mandatory = 1
    }

    public enum SrcBtn //used in statement screen
    {
        Account = 0,
        CostCenter = 1,
        CustomList = 2
    }

    public enum AccountType
    {
        Debit = 0,
        Credit = 1,
        None = 2
    }

}