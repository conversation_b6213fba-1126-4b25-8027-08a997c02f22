﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>لينكيت لتخطيط موارد الشركات</value>
  </data>
  <data name="mi_CloseAll" xml:space="preserve">
    <value>غلق الكل</value>
  </data>
  <data name="mi_HideCharts" xml:space="preserve">
    <value>اخفاء الاحصائيات</value>
  </data>
  <data name="mi_NewExpenses" xml:space="preserve">
    <value>تسجيل مصروفات</value>
  </data>
  <data name="mi_NewPayNote" xml:space="preserve">
    <value>سند دفع نقدي</value>
  </data>
  <data name="mi_NewReceiveNote" xml:space="preserve">
    <value>سند قبض نقدي</value>
  </data>
  <data name="mi_NewRevenue" xml:space="preserve">
    <value>تسجيل ايرادات</value>
  </data>
  <data name="mi_ShowAll" xml:space="preserve">
    <value>عرض الكل</value>
  </data>
  <data name="mi_ShowCharts" xml:space="preserve">
    <value>عرض الاحصائيات</value>
  </data>
  <data name="MsgFileNotExist" xml:space="preserve">
    <value>الملف غير موجود</value>
  </data>
  <data name="Fri" xml:space="preserve">
    <value>الجمعه</value>
  </data>
  <data name="Mon" xml:space="preserve">
    <value>الاتنين</value>
  </data>
  <data name="Sat" xml:space="preserve">
    <value>السبت</value>
  </data>
  <data name="Sun" xml:space="preserve">
    <value>الاحد</value>
  </data>
  <data name="Thu" xml:space="preserve">
    <value>الخميس</value>
  </data>
  <data name="Tus" xml:space="preserve">
    <value>الثلاثاء</value>
  </data>
  <data name="Wed" xml:space="preserve">
    <value>الاربعاء</value>
  </data>
  <data name="Fortnightly" xml:space="preserve">
    <value>كل اسبوعين</value>
  </data>
  <data name="Hourly" xml:space="preserve">
    <value>بالساعه</value>
  </data>
  <data name="Monthly" xml:space="preserve">
    <value>بالشهر</value>
  </data>
  <data name="Pertask" xml:space="preserve">
    <value>بالقطعه</value>
  </data>
  <data name="Weekly" xml:space="preserve">
    <value>بالاسبوع</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا</value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>تم الحذف بنجاح</value>
  </data>
  <data name="MsgDelExpenses" xml:space="preserve">
    <value>هل تريد حذف المصاريف</value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>حذف صف ؟</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>تأكد من صحة البيانات</value>
  </data>
  <data name="Msgmanfend" xml:space="preserve">
    <value>تم انهاء التشغيل بنجاح</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية تعديل هذا البيان</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية اضافة بيان جديد</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>تم الحفظ بنجاح</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>معلومة</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>سؤال</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="MsgValidatemanfnumber" xml:space="preserve">
    <value>تأكد من ادخال رقم التشغيلة</value>
  </data>
  <data name="MsgValidatemanfstartdate" xml:space="preserve">
    <value>تأكد من ادخال تاريخ بدء التشغيل</value>
  </data>
  <data name="MsgValidatemanfstore" xml:space="preserve">
    <value>تأكد من ادخال مخزن التشغيل</value>
  </data>
  <data name="MsgValidatePitem" xml:space="preserve">
    <value>تأكد من اختيار المنتج المشغل</value>
  </data>
  <data name="MsgWdeletemanf" xml:space="preserve">
    <value>سيتم حذف التشغيلة و كل القيود و محتوياتها في المخازن؟</value>
  </data>
  <data name="MsgWsavemanf" xml:space="preserve">
    <value>سيتم تسجيل انهاء التشغيل و حفظ المنتج المشغل في المخزن متابعة؟</value>
  </data>
  <data name="txtmanfExpense" xml:space="preserve">
    <value>مصاريف التشغيلة رقم</value>
  </data>
  <data name="txtValidateDate" xml:space="preserve">
    <value>يجب اختيار التاريخ</value>
  </data>
  <data name="txtValidateDrawer" xml:space="preserve">
    <value>يجب اختيار الخزينة</value>
  </data>
  <data name="txtValidateEmp" xml:space="preserve">
    <value>يجب اختيار الموظف</value>
  </data>
  <data name="txtValidateExpensesText" xml:space="preserve">
    <value>يجب كتابة بيانات المصروفات</value>
  </data>
  <data name="txtValidateExpensesValue" xml:space="preserve">
    <value>يجب كتابة قيمة المصروفات</value>
  </data>
  <data name="txtValidateItem" xml:space="preserve">
    <value>يجب اختيار الصنف</value>
  </data>
  <data name="txtValidateQty" xml:space="preserve">
    <value>الكمية يجب أن تكون أكبر من الصفر</value>
  </data>
  <data name="txtValidateStore" xml:space="preserve">
    <value>يجب اختيار المخزن</value>
  </data>
  <data name="txtValidateWorkedhours" xml:space="preserve">
    <value>يجب كتابة عدد ساعات العمل</value>
  </data>
  <data name="custOpen" xml:space="preserve">
    <value>رصيد افتتاحي عميل</value>
  </data>
  <data name="VendOpen" xml:space="preserve">
    <value>رصيد افتتاحي مورد</value>
  </data>
  <data name="accountCreationBy" xml:space="preserve">
    <value>تم انشاء الحساب بواسطة النظام</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>موظف</value>
  </data>
  <data name="CloseInventory" xml:space="preserve">
    <value>نهاية المده</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>عميل</value>
  </data>
  <data name="OpenInventory" xml:space="preserve">
    <value>اول المده</value>
  </data>
  <data name="Purchases" xml:space="preserve">
    <value>مشتريات</value>
  </data>
  <data name="PurchasesReturn" xml:space="preserve">
    <value>مردود مشتريات</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>مبيعات</value>
  </data>
  <data name="SalesReturn" xml:space="preserve">
    <value>مردود مبيعات</value>
  </data>
  <data name="Vendor" xml:space="preserve">
    <value>مورد</value>
  </data>
  <data name="txt_Date" xml:space="preserve">
    <value>تاريخ التشغيلة</value>
  </data>
  <data name="txt_prod" xml:space="preserve">
    <value>المنتج</value>
  </data>
  <data name="txt_Qty" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="MsgValidatemanfprodUOM" xml:space="preserve">
    <value>تأكد من اختيار وحدة قياس المنتج</value>
  </data>
  <data name="txt_V_Manf_EndDate" xml:space="preserve">
    <value>تأكد من ادخال تاريخ انتهاء التشغيل</value>
  </data>
  <data name="txt_V_manf_EndDate_Larger_StartDate" xml:space="preserve">
    <value>تاريخ الانتهاء يجب ان يكون بعد تاريخ بدء التشغيل</value>
  </data>
  <data name="MsgPostedSuccessfully" xml:space="preserve">
    <value>تم ترحيل فواتير البيع بنجاح</value>
  </data>
  <data name="MsgPostedFailed" xml:space="preserve">
    <value>خطأ لم يتم ترحيل الفواتير </value>
  </data>
  <data name="txtSLPosting" xml:space="preserve">
    <value>ترحيل فواتير بيع  تاريخ</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>اسم المستخدم </value>
  </data>
  <data name="MsgAskPost" xml:space="preserve">
    <value>هل انت متأكد من أنك تريد ترحيل فواتير البيع و اقفال اليومية ؟</value>
  </data>
  <data name="MsgCapitalAcc" xml:space="preserve">
    <value>برجاء تحديد حساب رأس المال من شاشة الإعدادات</value>
  </data>
  <data name="MsgCustomersAcc" xml:space="preserve">
    <value>برجاء تحديد حساب العملاء الرئيسي من شاشة الإعدادات</value>
  </data>
  <data name="MsgDiscountAcc" xml:space="preserve">
    <value>برجاء تحديد حساب الخصم من شاشة الإعدادات</value>
  </data>
  <data name="MsgManufacturingExpAcc" xml:space="preserve">
    <value>برجاء تحديد حساب مصروفات التشغيل من شاشة الإعدادات</value>
  </data>
  <data name="MsgSalesTaxAcc" xml:space="preserve">
    <value>برجاء تحديد حساب ضريبة المبيعات من شاشة الإعدادات</value>
  </data>
  <data name="MsgVendorsAcc" xml:space="preserve">
    <value>برجاء تحديد حساب الموردون الرئيسي من شاشة الإعدادات</value>
  </data>
  <data name="MsgSelectCustomer" xml:space="preserve">
    <value>برجاء إختيار عميل</value>
  </data>
  <data name="MsgSelectVendor" xml:space="preserve">
    <value>برجاء إختيار مورد</value>
  </data>
  <data name="MsgPostedBill" xml:space="preserve">
    <value>عفوا ، لا يمكنك تعديل او حذف بيان تم ترحيله</value>
  </data>
  <data name="MsgNameRequired" xml:space="preserve">
    <value>برجاء إدخال الإسم</value>
  </data>
  <data name="MsgAskPostJournals" xml:space="preserve">
    <value>هل انت متأكد من أنك تريد ترحيل هذه القيود المالية ؟</value>
  </data>
  <data name="MsgAskUnPostJournals" xml:space="preserve">
    <value>هل انت متأكد من أنك تريد إالغاء ترحيل هذه القيود المالية ؟</value>
  </data>
  <data name="MsgEnterCode" xml:space="preserve">
    <value>يجب إدخال الكود</value>
  </data>
  <data name="MsgJournalPostedSuccessfully" xml:space="preserve">
    <value>تم ترحيل القيود بنجاح</value>
  </data>
  <data name="DeselectAll" xml:space="preserve">
    <value>الغاء اختيار الكل</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>اختيار الكل</value>
  </data>
  <data name="MsgFaAccRequired" xml:space="preserve">
    <value>يجب إختيار حساب مصروفات الإهلاك</value>
  </data>
  <data name="MsgFaDeprAccRequired" xml:space="preserve">
    <value>يجب إدخال حساب مجمع الإهلاك</value>
  </data>
  <data name="MsgFixedAsetsAcc" xml:space="preserve">
    <value>برجاء تحديد حساب الأصول الثابتة من شاشة الإعدادات</value>
  </data>
  <data name="MsgAskDelFA" xml:space="preserve">
    <value>هل انت متأكد انك تريد حذف هذا الأصل الثابت</value>
  </data>
  <data name="DelEntryDenied" xml:space="preserve">
    <value>عفوا، لايمكن حذف هذا البند فهو مستخدم بالفعل</value>
  </data>
  <data name="NoChange" xml:space="preserve">
    <value>لا تغيير</value>
  </data>
  <data name="DelJO" xml:space="preserve">
    <value>عفوا، لايمكن حذف أمر العمل، يوجد فواتير بيع مرتبطة به</value>
  </data>
  <data name="EditInClosedPeriodDenie" xml:space="preserve">
    <value>عفوا، لايمكنك تعديل بيانات في فترة مالية مغلقة</value>
  </data>
  <data name="EditInClosedPeriodWarning" xml:space="preserve">
    <value>انت تقوم بتعديلات في فترة مالية مغلقة، هل تريد الاستمرار</value>
  </data>
  <data name="Expenses" xml:space="preserve">
    <value>مصروفات</value>
  </data>
  <data name="Revenue" xml:space="preserve">
    <value>ايرادات</value>
  </data>
  <data name="InvBookUsedDel" xml:space="preserve">
    <value>عفوا، لايمكن حذف الدفتر، فقد تم استخدامه بالفعل</value>
  </data>
  <data name="MsgAskDelInvBook" xml:space="preserve">
    <value>هل انت متأكد انك تريد حذف هذا الأصل الدفتر</value>
  </data>
  <data name="linkAccountParent" xml:space="preserve">
    <value>لايمكن الربط بحساب رئيسي</value>
  </data>
  <data name="DetailIdBrcodPrntd" xml:space="preserve">
    <value>عفوا، لايمكن تعديل الفاتورة بعد طباعة ملصقات الباركود</value>
  </data>
  <data name="DetailIdBrcodPrntdDel" xml:space="preserve">
    <value>حذف الفاتورة سيؤدي لتلف ملصقات الباركود المطبوعة لها، هل تريد الإستمرار</value>
  </data>
  <data name="MsgWarnningSelectManfActualRaws" xml:space="preserve">
    <value>لا يوجد استهلاكات فعليه للتشغيلة، هل تريد الاستمرار ؟</value>
  </data>
  <data name="AppPOSName" xml:space="preserve">
    <value>لينكيت لنقاط البيع</value>
  </data>
  <data name="ValPass" xml:space="preserve">
    <value>الرقم السري غير متطابق</value>
  </data>
  <data name="valUserName" xml:space="preserve">
    <value>يجب ادخال اسم المستخدم</value>
  </data>
  <data name="MsgSelectComp" xml:space="preserve">
    <value>يجب اختيار الشركة</value>
  </data>
  <data name="MsgSelectCustGrp" xml:space="preserve">
    <value>يجب اختيار فئة العميل</value>
  </data>
  <data name="txtValidateNoRows" xml:space="preserve">
    <value>يجب تسجيل صنف على الأقل</value>
  </data>
  <data name="txtValidateNoteNum" xml:space="preserve">
    <value>يجب تسجيل رقم الاستمارة</value>
  </data>
  <data name="DelIndirectComp" xml:space="preserve">
    <value>عفوا، لايمكن حذف الشركة، يوجد استمارات بيع غير مباشر لهذه الشركة</value>
  </data>
  <data name="DelMrItem" xml:space="preserve">
    <value>عفوا، لايمكن ازالة الصنف، فهو مازال مخصص لأحد فئات العملاء</value>
  </data>
  <data name="DelMrItemWarning" xml:space="preserve">
    <value>إزالة صنف؟</value>
  </data>
  <data name="LastRate" xml:space="preserve">
    <value>تقييم كل العملات بأخر معامل</value>
  </data>
  <data name="TrnsRate" xml:space="preserve">
    <value>تقييم كل العملات بمعامل الحركة</value>
  </data>
  <data name="NetSales" xml:space="preserve">
    <value>صافي المبيعات</value>
  </data>
  <data name="PurchaseDiscount" xml:space="preserve">
    <value>خصم مكتسب</value>
  </data>
  <data name="SalesCost" xml:space="preserve">
    <value>تكلفة البضاعة المباعة</value>
  </data>
  <data name="SalesDiscount" xml:space="preserve">
    <value>خصم مسموح به</value>
  </data>
  <data name="MsgDelManfOrder" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف أمر الإنتاج؟</value>
  </data>
  <data name="ValMsgManfOrderNo" xml:space="preserve">
    <value>تأكد من إدخال رقم امر الإنتاج</value>
  </data>
  <data name="MsgJournalUnPostedSuccessfully" xml:space="preserve">
    <value>تم الغاء ترحيل القيود بنجاح</value>
  </data>
  <data name="ManfNo" xml:space="preserve">
    <value>تشغيلة رقم : </value>
  </data>
  <data name="txt_QtyText" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="txt_ValueText" xml:space="preserve">
    <value>قيمة</value>
  </data>
  <data name="txt_ICInTransNo" xml:space="preserve">
    <value>اذن اضافة رقم </value>
  </data>
  <data name="txt_ICOutTransNo" xml:space="preserve">
    <value>اذن صرف رقم </value>
  </data>
  <data name="txt_ICStoreMoveNo" xml:space="preserve">
    <value>سند نقل من مخزن رقم </value>
  </data>
  <data name="txt_ManfQCNo" xml:space="preserve">
    <value>سند مراقبة جودة رقم</value>
  </data>
  <data name="MsgAskToSave" xml:space="preserve">
    <value>يجب حفظ المستند أولا</value>
  </data>
  <data name="DelLC" xml:space="preserve">
    <value>هل انت متأكد انك تريد حذف هذا الإعتماد المستندي</value>
  </data>
  <data name="LCSettingAcc" xml:space="preserve">
    <value>يجب تحديد الحساب الرئيسي للإعتمادات المستندية من شاشة الإعدادات</value>
  </data>
  <data name="DelLCDenied" xml:space="preserve">
    <value>عفوا، لايمكن حذف الاعتماد المستندي فهو مستخدم بفاتورة مشتريات رقم </value>
  </data>
  <data name="DelLCDenied2" xml:space="preserve">
    <value>عفوا، لايمكن حذف الاعتماد المستندي يوجد قيود على الحساب الخاص به</value>
  </data>
  <data name="openBlncDate" xml:space="preserve">
    <value>برجاء تحديد تاريخ الرصيد الإفتتاحي</value>
  </data>
  <data name="MsgAskDelRows" xml:space="preserve">
    <value>هل انت متأكد انك تريد حذف البيانات المختاره</value>
  </data>
  <data name="DelBomDenied" xml:space="preserve">
    <value>عفوا لايمكن حذف قائمة الخامات، فهي مستخدمة بالفعل في التشغيلات</value>
  </data>
  <data name="invDeleted" xml:space="preserve">
    <value>الفاتورة غير موجودة، سيتم إنشاء فاتورة جديدة</value>
  </data>
  <data name="MsgContinue" xml:space="preserve">
    <value>هل تريد الإستمرار ؟</value>
  </data>
  <data name="SelectrevExpAcc" xml:space="preserve">
    <value>يجب اختيار حسابات الايرادات والمصروفات</value>
  </data>
  <data name="AccessDenied" xml:space="preserve">
    <value>عفوا, غير مصرح لك فتح البرنامج, يرجي مراجعة مدير النظام</value>
  </data>
  <data name="ValidUser" xml:space="preserve">
    <value>عفوا, الاسم أو الرقم السري غير صحيح</value>
  </data>
  <data name="DelBillDenied" xml:space="preserve">
    <value>لايمكن حذف الإذن، هذه الفواتير مرتبطة به</value>
  </data>
  <data name="DelInvoiceDenied" xml:space="preserve">
    <value>لايمكن حذف الفاتورة، هذه الأذون مرتبطة بها</value>
  </data>
  <data name="DelOrderDeniedIC" xml:space="preserve">
    <value>لايمكن حذف امر التوريد، هذه الأذون مرتبطة به</value>
  </data>
  <data name="DelOrderDeniedInv" xml:space="preserve">
    <value>لايمكن حذف امر التوريد، هذه الفواتير مرتبطة به</value>
  </data>
  <data name="SelectBill" xml:space="preserve">
    <value>يجب تحميل اذن الاستلام أولا</value>
  </data>
  <data name="SelectInv" xml:space="preserve">
    <value>يجب تحميل الفاتورة أولا</value>
  </data>
  <data name="ItemPosting" xml:space="preserve">
    <value>يجب تسجيل حسابات الترحيل لفئات الأصناف بشكل كامل</value>
  </data>
  <data name="AppClose" xml:space="preserve">
    <value>هل تريد الخروج من النظام ؟</value>
  </data>
  <data name="MsgBlockedCustomer" xml:space="preserve">
    <value>هذا العميل محظور</value>
  </data>
  <data name="MsgCusTaxAcc" xml:space="preserve">
    <value>برجاء تحديد حساب ضريبة الجدول من شاشة الإعدادات</value>
  </data>
  <data name="txtmanfIndirectExpense" xml:space="preserve">
    <value>مصاريف غير مباشرة للتشغيلة رقم </value>
  </data>
  <data name="LastEvaluationDateError" xml:space="preserve">
    <value>عفواَ,  تاريخ الحفظ يتخطى أقصى موعد يمكن ترحيله</value>
  </data>
  <data name="Daily" xml:space="preserve">
    <value>باليوم</value>
  </data>
  <data name="InermediateAccount" xml:space="preserve">
    <value>يرجى تحديد حساب المخزون الوسيط من الإعدادات</value>
  </data>
  <data name="ValidAdvancedPayment" xml:space="preserve">
    <value>يجب ادخال الدفعة المقدمة</value>
  </data>
  <data name="ValidAreaSize" xml:space="preserve">
    <value>يجب ادخال مساحة الدور</value>
  </data>
  <data name="ValidCostMeter" xml:space="preserve">
    <value> يجب ادخال تكلفة المتر</value>
  </data>
  <data name="ValidCustomer" xml:space="preserve">
    <value>يجب ادخال العميل</value>
  </data>
  <data name="ValidFloor" xml:space="preserve">
    <value>يجب ادخال الدور</value>
  </data>
  <data name="ValidDueName" xml:space="preserve">
    <value>لا يمكن تكرار الاسم</value>
  </data>
  <data name="validCode" xml:space="preserve">
    <value>ادخل الكود</value>
  </data>
  <data name="validDate" xml:space="preserve">
    <value>عفوا: بداية الفترة بعد نهايتها</value>
  </data>
  <data name="validDateFrom" xml:space="preserve">
    <value>ادخل بداية الفترة</value>
  </data>
  <data name="validDateTo" xml:space="preserve">
    <value>ادخل   نهاية الفترة</value>
  </data>
  <data name="ValidRowInMontlyDue" xml:space="preserve">
    <value>يجب تسجل صف واحد على الاقل في الاستحقاق</value>
  </data>
  <data name="validEndDateContract" xml:space="preserve">
    <value>يجب ادخال تاريخ الانتهاء</value>
  </data>
  <data name="validContractDate" xml:space="preserve">
    <value>يجب ادخال تاريخ التعاقد</value>
  </data>
  <data name="validMonthlyDue" xml:space="preserve">
    <value>يجب تسجيل الاسنجقاق الشهري</value>
  </data>
  <data name="ValidTotalValueMonthlyDue" xml:space="preserve">
    <value>القيمة الاجمالية المستحقه يجب ان تكون اكبر من صفر</value>
  </data>
  <data name="ValidAreaSizeGreaterThanMallArea" xml:space="preserve">
    <value>مساحة المحل اكبر من مساحة المول</value>
  </data>
  <data name="validmonthlyDues" xml:space="preserve">
    <value>يوجد استحقاقات شهرية في هذه الفتره ..ادخل فتره اخري</value>
  </data>
  <data name="validDateContract" xml:space="preserve">
    <value>تاريخ انتهاء التعاقد يجب ان يكون بعد نهايته</value>
  </data>
  <data name="ValidUom" xml:space="preserve">
    <value>لا يمكن ايقاف الوحدة</value>
  </data>
  <data name="validAreaNumber" xml:space="preserve">
    <value>رقم المحل مطلوب</value>
  </data>
</root>