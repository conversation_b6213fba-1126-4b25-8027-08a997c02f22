﻿using AutoMapper;
using EInvoice.Models;
using Models_1.ViewModels.DatabaseVM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EInvoice.BL
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<StCompanyInfo, StCompanyInfoVM>();
            CreateMap<IcStore, IcStoreVM>().ForMember(dest => dest.CountryCode, src => src.MapFrom(source => source.Country.Ecode));
            CreateMap<SlCustomer, SlCustomerVM>().ForMember(dest => dest.CountryCode, src => src.MapFrom(source => source.Country.Ecode));
            CreateMap<IcItem, IcItemVM>();
            CreateMap<StCurrency, StCurrencyVM>();
            CreateMap<IcUom, IcUomVM>();
        }
    }
}
