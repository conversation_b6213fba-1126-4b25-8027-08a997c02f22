﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResEn {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResEn() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResEn", typeof(ResEn).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you are not allowd to access the system, please contact system administrator.
        /// </summary>
        public static string AccessDenied {
            get {
                return ResourceManager.GetString("AccessDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account created by the system.
        /// </summary>
        public static string accountCreationBy {
            get {
                return ResourceManager.GetString("accountCreationBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to exit the system?.
        /// </summary>
        public static string AppClose {
            get {
                return ResourceManager.GetString("AppClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LinkIT ERP System.
        /// </summary>
        public static string AppName {
            get {
                return ResourceManager.GetString("AppName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LinkIT POS System.
        /// </summary>
        public static string AppPOSName {
            get {
                return ResourceManager.GetString("AppPOSName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Inventory.
        /// </summary>
        public static string CloseInventory {
            get {
                return ResourceManager.GetString("CloseInventory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this entry, it&apos;s already in use..
        /// </summary>
        public static string CrncyDel {
            get {
                return ResourceManager.GetString("CrncyDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        public static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Open Balance.
        /// </summary>
        public static string custOpen {
            get {
                return ResourceManager.GetString("custOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily.
        /// </summary>
        public static string Daily {
            get {
                return ResourceManager.GetString("Daily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t delete this bill, these invoices are linked.
        /// </summary>
        public static string DelBillDenied {
            get {
                return ResourceManager.GetString("DelBillDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this BOM, it&apos;s already in use by Job Orders..
        /// </summary>
        public static string DelBomDenied {
            get {
                return ResourceManager.GetString("DelBomDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this entry, it&apos;s already in use..
        /// </summary>
        public static string DelEntryDenied {
            get {
                return ResourceManager.GetString("DelEntryDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this company, you&apos;ve to delete it&apos;s indirect sales notes first.
        /// </summary>
        public static string DelIndirectComp {
            get {
                return ResourceManager.GetString("DelIndirectComp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t delete this invoice, these bills are linked.
        /// </summary>
        public static string DelInvoiceDenied {
            get {
                return ResourceManager.GetString("DelInvoiceDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this Job Order, it&apos;s already in use by sales invoices..
        /// </summary>
        public static string DelJO {
            get {
                return ResourceManager.GetString("DelJO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this Letter of Credit.
        /// </summary>
        public static string DelLC {
            get {
                return ResourceManager.GetString("DelLC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this letter of credit it&apos;s already in use by purchase invoice no. .
        /// </summary>
        public static string DelLCDenied {
            get {
                return ResourceManager.GetString("DelLCDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this letter of credit, you&apos;ve to delete it&apos;s jpurnals first.
        /// </summary>
        public static string DelLCDenied2 {
            get {
                return ResourceManager.GetString("DelLCDenied2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t remove this item, it&apos;s already in use by a customer group.
        /// </summary>
        public static string DelMrItem {
            get {
                return ResourceManager.GetString("DelMrItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Item?.
        /// </summary>
        public static string DelMrItemWarning {
            get {
                return ResourceManager.GetString("DelMrItemWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t delete this order, these bills are linked.
        /// </summary>
        public static string DelOrderDeniedIC {
            get {
                return ResourceManager.GetString("DelOrderDeniedIC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t delete this order, these invoices are linked.
        /// </summary>
        public static string DelOrderDeniedInv {
            get {
                return ResourceManager.GetString("DelOrderDeniedInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deselect All.
        /// </summary>
        public static string DeselectAll {
            get {
                return ResourceManager.GetString("DeselectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t edit this invoice, after you printed barcode stickers.
        /// </summary>
        public static string DetailIdBrcodPrntd {
            get {
                return ResourceManager.GetString("DetailIdBrcodPrntd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleting this invoice will ruin barcode stickers printed, are you sure you wan&apos;t to continue.
        /// </summary>
        public static string DetailIdBrcodPrntdDel {
            get {
                return ResourceManager.GetString("DetailIdBrcodPrntdDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t edit data in a closed period.
        /// </summary>
        public static string EditInClosedPeriodDenie {
            get {
                return ResourceManager.GetString("EditInClosedPeriodDenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you are saving data in a closed period, do you want to continue.
        /// </summary>
        public static string EditInClosedPeriodWarning {
            get {
                return ResourceManager.GetString("EditInClosedPeriodWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee.
        /// </summary>
        public static string Employee {
            get {
                return ResourceManager.GetString("Employee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expenses.
        /// </summary>
        public static string Expenses {
            get {
                return ResourceManager.GetString("Expenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fortnightly.
        /// </summary>
        public static string Fortnightly {
            get {
                return ResourceManager.GetString("Fortnightly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Friday.
        /// </summary>
        public static string Fri {
            get {
                return ResourceManager.GetString("Fri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hourly.
        /// </summary>
        public static string Hourly {
            get {
                return ResourceManager.GetString("Hourly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select the intermediate sotck account from settings.
        /// </summary>
        public static string InermediateAccount {
            get {
                return ResourceManager.GetString("InermediateAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this Book, it&apos;s already in use by invoices.
        /// </summary>
        public static string InvBookUsedDel {
            get {
                return ResourceManager.GetString("InvBookUsedDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice doesn&apos;t exist, a new one will be created.
        /// </summary>
        public static string invDeleted {
            get {
                return ResourceManager.GetString("invDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please set gl posting accounts for items categories.
        /// </summary>
        public static string ItemPosting {
            get {
                return ResourceManager.GetString("ItemPosting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t save after last evalutaion date.
        /// </summary>
        public static string LastEvaluationDateError {
            get {
                return ResourceManager.GetString("LastEvaluationDateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evaluete All Currencies by Last Rate.
        /// </summary>
        public static string LastRate {
            get {
                return ResourceManager.GetString("LastRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Letter Of Credit account in settings screen.
        /// </summary>
        public static string LCSettingAcc {
            get {
                return ResourceManager.GetString("LCSettingAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you can&apos;t link to a parent account.
        /// </summary>
        public static string linkAccountParent {
            get {
                return ResourceManager.GetString("linkAccountParent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manf No : .
        /// </summary>
        public static string ManfNo {
            get {
                return ResourceManager.GetString("ManfNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close All.
        /// </summary>
        public static string mi_CloseAll {
            get {
                return ResourceManager.GetString("mi_CloseAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hide Charts.
        /// </summary>
        public static string mi_HideCharts {
            get {
                return ResourceManager.GetString("mi_HideCharts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Expenses.
        /// </summary>
        public static string mi_NewExpenses {
            get {
                return ResourceManager.GetString("mi_NewExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Payment Note.
        /// </summary>
        public static string mi_NewPayNote {
            get {
                return ResourceManager.GetString("mi_NewPayNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Receive Note.
        /// </summary>
        public static string mi_NewReceiveNote {
            get {
                return ResourceManager.GetString("mi_NewReceiveNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Revenue.
        /// </summary>
        public static string mi_NewRevenue {
            get {
                return ResourceManager.GetString("mi_NewRevenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show All.
        /// </summary>
        public static string mi_ShowAll {
            get {
                return ResourceManager.GetString("mi_ShowAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Charts.
        /// </summary>
        public static string mi_ShowCharts {
            get {
                return ResourceManager.GetString("mi_ShowCharts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monday.
        /// </summary>
        public static string Mon {
            get {
                return ResourceManager.GetString("Mon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monthly.
        /// </summary>
        public static string Monthly {
            get {
                return ResourceManager.GetString("Monthly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this Fixed Asset.
        /// </summary>
        public static string MsgAskDelFA {
            get {
                return ResourceManager.GetString("MsgAskDelFA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this Invoice book.
        /// </summary>
        public static string MsgAskDelInvBook {
            get {
                return ResourceManager.GetString("MsgAskDelInvBook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete selected rows ? .
        /// </summary>
        public static string MsgAskDelRows {
            get {
                return ResourceManager.GetString("MsgAskDelRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to post sales invoices ?.
        /// </summary>
        public static string MsgAskPost {
            get {
                return ResourceManager.GetString("MsgAskPost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to post these financial Journals .
        /// </summary>
        public static string MsgAskPostJournals {
            get {
                return ResourceManager.GetString("MsgAskPostJournals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You&apos;ve to save this form first.
        /// </summary>
        public static string MsgAskToSave {
            get {
                return ResourceManager.GetString("MsgAskToSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to unpost these financial Journals .
        /// </summary>
        public static string MsgAskUnPostJournals {
            get {
                return ResourceManager.GetString("MsgAskUnPostJournals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Customer Is Blocked.
        /// </summary>
        public static string MsgBlockedCustomer {
            get {
                return ResourceManager.GetString("MsgBlockedCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Capital account in settings screen.
        /// </summary>
        public static string MsgCapitalAcc {
            get {
                return ResourceManager.GetString("MsgCapitalAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to continue.
        /// </summary>
        public static string MsgContinue {
            get {
                return ResourceManager.GetString("MsgContinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Custom Tax account in settings screen.
        /// </summary>
        public static string MsgCusTaxAcc {
            get {
                return ResourceManager.GetString("MsgCusTaxAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Customers main account in settings screen.
        /// </summary>
        public static string MsgCustomersAcc {
            get {
                return ResourceManager.GetString("MsgCustomersAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You made some changes, do you want to save.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted Successfully.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete expenses ?.
        /// </summary>
        public static string MsgDelExpenses {
            get {
                return ResourceManager.GetString("MsgDelExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this production order?.
        /// </summary>
        public static string MsgDelManfOrder {
            get {
                return ResourceManager.GetString("MsgDelManfOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to delete row ?.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Discount account in settings screen.
        /// </summary>
        public static string MsgDiscountAcc {
            get {
                return ResourceManager.GetString("MsgDiscountAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter code.
        /// </summary>
        public static string MsgEnterCode {
            get {
                return ResourceManager.GetString("MsgEnterCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Fixed Asset Expeneses Account.
        /// </summary>
        public static string MsgFaAccRequired {
            get {
                return ResourceManager.GetString("MsgFaAccRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Depreciation Account.
        /// </summary>
        public static string MsgFaDeprAccRequired {
            get {
                return ResourceManager.GetString("MsgFaDeprAccRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File doesn&apos;t exist.
        /// </summary>
        public static string MsgFileNotExist {
            get {
                return ResourceManager.GetString("MsgFileNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Fixed Assets main account in settings screen.
        /// </summary>
        public static string MsgFixedAsetsAcc {
            get {
                return ResourceManager.GetString("MsgFixedAsetsAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to some data are incorrect.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jounals Posted Successfully.
        /// </summary>
        public static string MsgJournalPostedSuccessfully {
            get {
                return ResourceManager.GetString("MsgJournalPostedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Journals post cacelled successfully.
        /// </summary>
        public static string MsgJournalUnPostedSuccessfully {
            get {
                return ResourceManager.GetString("MsgJournalUnPostedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Order ended successfully.
        /// </summary>
        public static string Msgmanfend {
            get {
                return ResourceManager.GetString("Msgmanfend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Manufacturing Expenses account in settings screen.
        /// </summary>
        public static string MsgManufacturingExpAcc {
            get {
                return ResourceManager.GetString("MsgManufacturingExpAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter name.
        /// </summary>
        public static string MsgNameRequired {
            get {
                return ResourceManager.GetString("MsgNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry , you can&apos;t edit or delete posted bills.
        /// </summary>
        public static string MsgPostedBill {
            get {
                return ResourceManager.GetString("MsgPostedBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error , Sales Invoices not posted.
        /// </summary>
        public static string MsgPostedFailed {
            get {
                return ResourceManager.GetString("MsgPostedFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Invoices Posted Successfully.
        /// </summary>
        public static string MsgPostedSuccessfully {
            get {
                return ResourceManager.GetString("MsgPostedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to edit record.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to add new record.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Sales Tax account in settings screen.
        /// </summary>
        public static string MsgSalesTaxAcc {
            get {
                return ResourceManager.GetString("MsgSalesTaxAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved Successfully.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Company.
        /// </summary>
        public static string MsgSelectComp {
            get {
                return ResourceManager.GetString("MsgSelectComp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Customer Group.
        /// </summary>
        public static string MsgSelectCustGrp {
            get {
                return ResourceManager.GetString("MsgSelectCustGrp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select customer.
        /// </summary>
        public static string MsgSelectCustomer {
            get {
                return ResourceManager.GetString("MsgSelectCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select vendor.
        /// </summary>
        public static string MsgSelectVendor {
            get {
                return ResourceManager.GetString("MsgSelectVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Job order Number.
        /// </summary>
        public static string MsgValidatemanfnumber {
            get {
                return ResourceManager.GetString("MsgValidatemanfnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select product UOM.
        /// </summary>
        public static string MsgValidatemanfprodUOM {
            get {
                return ResourceManager.GetString("MsgValidatemanfprodUOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Job Order Start Date.
        /// </summary>
        public static string MsgValidatemanfstartdate {
            get {
                return ResourceManager.GetString("MsgValidatemanfstartdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter the Produced Item Store .
        /// </summary>
        public static string MsgValidatemanfstore {
            get {
                return ResourceManager.GetString("MsgValidatemanfstore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Product Item.
        /// </summary>
        public static string MsgValidatePitem {
            get {
                return ResourceManager.GetString("MsgValidatePitem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Vendors main account in settings screen.
        /// </summary>
        public static string MsgVendorsAcc {
            get {
                return ResourceManager.GetString("MsgVendorsAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no actual raws for this job order , do you want to continue ?.
        /// </summary>
        public static string MsgWarnningSelectManfActualRaws {
            get {
                return ResourceManager.GetString("MsgWarnningSelectManfActualRaws", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job order and all its details and jornals will be deleted .. Continue ?.
        /// </summary>
        public static string MsgWdeletemanf {
            get {
                return ResourceManager.GetString("MsgWdeletemanf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Order will be ended and produced item will be stored .. Continue ?.
        /// </summary>
        public static string MsgWsavemanf {
            get {
                return ResourceManager.GetString("MsgWsavemanf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net Sales.
        /// </summary>
        public static string NetSales {
            get {
                return ResourceManager.GetString("NetSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Change.
        /// </summary>
        public static string NoChange {
            get {
                return ResourceManager.GetString("NoChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select open balance date.
        /// </summary>
        public static string openBlncDate {
            get {
                return ResourceManager.GetString("openBlncDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Inventory.
        /// </summary>
        public static string OpenInventory {
            get {
                return ResourceManager.GetString("OpenInventory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Per-Task.
        /// </summary>
        public static string Pertask {
            get {
                return ResourceManager.GetString("Pertask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Discount.
        /// </summary>
        public static string PurchaseDiscount {
            get {
                return ResourceManager.GetString("PurchaseDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchases.
        /// </summary>
        public static string Purchases {
            get {
                return ResourceManager.GetString("Purchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchases Return.
        /// </summary>
        public static string PurchasesReturn {
            get {
                return ResourceManager.GetString("PurchasesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue.
        /// </summary>
        public static string Revenue {
            get {
                return ResourceManager.GetString("Revenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales.
        /// </summary>
        public static string Sales {
            get {
                return ResourceManager.GetString("Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Cost.
        /// </summary>
        public static string SalesCost {
            get {
                return ResourceManager.GetString("SalesCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Discount.
        /// </summary>
        public static string SalesDiscount {
            get {
                return ResourceManager.GetString("SalesDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Return.
        /// </summary>
        public static string SalesReturn {
            get {
                return ResourceManager.GetString("SalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saturday.
        /// </summary>
        public static string Sat {
            get {
                return ResourceManager.GetString("Sat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select All.
        /// </summary>
        public static string SelectAll {
            get {
                return ResourceManager.GetString("SelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Receive Bill first.
        /// </summary>
        public static string SelectBill {
            get {
                return ResourceManager.GetString("SelectBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Receive Invoice first.
        /// </summary>
        public static string SelectInv {
            get {
                return ResourceManager.GetString("SelectInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select revenue and expenses accounts.
        /// </summary>
        public static string SelectrevExpAcc {
            get {
                return ResourceManager.GetString("SelectrevExpAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sunday.
        /// </summary>
        public static string Sun {
            get {
                return ResourceManager.GetString("Sun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thursday.
        /// </summary>
        public static string Thu {
            get {
                return ResourceManager.GetString("Thu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Evaluete All Currencies by Transaction Rate.
        /// </summary>
        public static string TrnsRate {
            get {
                return ResourceManager.GetString("TrnsRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tusday.
        /// </summary>
        public static string Tus {
            get {
                return ResourceManager.GetString("Tus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Date.
        /// </summary>
        public static string txt_Date {
            get {
                return ResourceManager.GetString("txt_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Bill Number .
        /// </summary>
        public static string txt_ICInTransNo {
            get {
                return ResourceManager.GetString("txt_ICInTransNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outgoing Bill Number .
        /// </summary>
        public static string txt_ICOutTransNo {
            get {
                return ResourceManager.GetString("txt_ICOutTransNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move From Store Bill Number.
        /// </summary>
        public static string txt_ICStoreMoveNo {
            get {
                return ResourceManager.GetString("txt_ICStoreMoveNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Control Bill Number .
        /// </summary>
        public static string txt_ManfQCNo {
            get {
                return ResourceManager.GetString("txt_ManfQCNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        public static string txt_prod {
            get {
                return ResourceManager.GetString("txt_prod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty.
        /// </summary>
        public static string txt_Qty {
            get {
                return ResourceManager.GetString("txt_Qty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty.
        /// </summary>
        public static string txt_QtyText {
            get {
                return ResourceManager.GetString("txt_QtyText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Enter Job Order End Date.
        /// </summary>
        public static string txt_V_Manf_EndDate {
            get {
                return ResourceManager.GetString("txt_V_Manf_EndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Order End Date must be After Job Start Date.
        /// </summary>
        public static string txt_V_manf_EndDate_Larger_StartDate {
            get {
                return ResourceManager.GetString("txt_V_manf_EndDate_Larger_StartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value.
        /// </summary>
        public static string txt_ValueText {
            get {
                return ResourceManager.GetString("txt_ValueText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expenses of Job Order No..
        /// </summary>
        public static string txtmanfExpense {
            get {
                return ResourceManager.GetString("txtmanfExpense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indirect expense of Job Order No. .
        /// </summary>
        public static string txtmanfIndirectExpense {
            get {
                return ResourceManager.GetString("txtmanfIndirectExpense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Post Sales Invoices Date :.
        /// </summary>
        public static string txtSLPosting {
            get {
                return ResourceManager.GetString("txtSLPosting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Date.
        /// </summary>
        public static string txtValidateDate {
            get {
                return ResourceManager.GetString("txtValidateDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select drawer.
        /// </summary>
        public static string txtValidateDrawer {
            get {
                return ResourceManager.GetString("txtValidateDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Empolyee.
        /// </summary>
        public static string txtValidateEmp {
            get {
                return ResourceManager.GetString("txtValidateEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter expenses notes.
        /// </summary>
        public static string txtValidateExpensesText {
            get {
                return ResourceManager.GetString("txtValidateExpensesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter expenses value.
        /// </summary>
        public static string txtValidateExpensesValue {
            get {
                return ResourceManager.GetString("txtValidateExpensesValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        public static string txtValidateItem {
            get {
                return ResourceManager.GetString("txtValidateItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter at least one item.
        /// </summary>
        public static string txtValidateNoRows {
            get {
                return ResourceManager.GetString("txtValidateNoRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Note Number.
        /// </summary>
        public static string txtValidateNoteNum {
            get {
                return ResourceManager.GetString("txtValidateNoteNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty must be larger than 0.
        /// </summary>
        public static string txtValidateQty {
            get {
                return ResourceManager.GetString("txtValidateQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selext Store.
        /// </summary>
        public static string txtValidateStore {
            get {
                return ResourceManager.GetString("txtValidateStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Worked Hours.
        /// </summary>
        public static string txtValidateWorkedhours {
            get {
                return ResourceManager.GetString("txtValidateWorkedhours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Name.
        /// </summary>
        public static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Advanced Payment.
        /// </summary>
        public static string ValidAdvancedPayment {
            get {
                return ResourceManager.GetString("ValidAdvancedPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Area Number Is Required.
        /// </summary>
        public static string validAreaNumber {
            get {
                return ResourceManager.GetString("validAreaNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Area Size.
        /// </summary>
        public static string ValidAreaSize {
            get {
                return ResourceManager.GetString("ValidAreaSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Area Size is greater than Mall Area.
        /// </summary>
        public static string ValidAreaSizeGreaterThanMallArea {
            get {
                return ResourceManager.GetString("ValidAreaSizeGreaterThanMallArea", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Code.
        /// </summary>
        public static string validCode {
            get {
                return ResourceManager.GetString("validCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Contract Date.
        /// </summary>
        public static string validContractDate {
            get {
                return ResourceManager.GetString("validContractDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Cost Meter.
        /// </summary>
        public static string ValidCostMeter {
            get {
                return ResourceManager.GetString("ValidCostMeter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Customer.
        /// </summary>
        public static string ValidCustomer {
            get {
                return ResourceManager.GetString("ValidCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date From Is Biger Than Date To....
        /// </summary>
        public static string validDate {
            get {
                return ResourceManager.GetString("validDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Date Must Be After Start Date.
        /// </summary>
        public static string validDateContract {
            get {
                return ResourceManager.GetString("validDateContract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Date From.
        /// </summary>
        public static string validDateFrom {
            get {
                return ResourceManager.GetString("validDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Date To.
        /// </summary>
        public static string validDateTo {
            get {
                return ResourceManager.GetString("validDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cant repeat name.
        /// </summary>
        public static string ValidDueName {
            get {
                return ResourceManager.GetString("ValidDueName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter End Date.....
        /// </summary>
        public static string validEndDateContract {
            get {
                return ResourceManager.GetString("validEndDateContract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Floor.
        /// </summary>
        public static string ValidFloor {
            get {
                return ResourceManager.GetString("ValidFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Monthly Due.
        /// </summary>
        public static string validMonthlyDue {
            get {
                return ResourceManager.GetString("validMonthlyDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There Are Monthly Dues In this Duration ,Please Insert anthor Duration...
        /// </summary>
        public static string validmonthlyDues {
            get {
                return ResourceManager.GetString("validmonthlyDues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Must insert one row at least.
        /// </summary>
        public static string ValidRowInMontlyDue {
            get {
                return ResourceManager.GetString("ValidRowInMontlyDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value Must Be More Than 0.
        /// </summary>
        public static string ValidTotalValueMonthlyDue {
            get {
                return ResourceManager.GetString("ValidTotalValueMonthlyDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can`t Stop Unit....
        /// </summary>
        public static string ValidUom {
            get {
                return ResourceManager.GetString("ValidUom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter user name and password correctly.
        /// </summary>
        public static string ValidUser {
            get {
                return ResourceManager.GetString("ValidUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Production order Number.
        /// </summary>
        public static string ValMsgManfOrderNo {
            get {
                return ResourceManager.GetString("ValMsgManfOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Passwords doesn&apos;t match.
        /// </summary>
        public static string ValPass {
            get {
                return ResourceManager.GetString("ValPass", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Name Required.
        /// </summary>
        public static string valUserName {
            get {
                return ResourceManager.GetString("valUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor Open Balance.
        /// </summary>
        public static string VendOpen {
            get {
                return ResourceManager.GetString("VendOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor.
        /// </summary>
        public static string Vendor {
            get {
                return ResourceManager.GetString("Vendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wednesday.
        /// </summary>
        public static string Wed {
            get {
                return ResourceManager.GetString("Wed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weekly.
        /// </summary>
        public static string Weekly {
            get {
                return ResourceManager.GetString("Weekly", resourceCulture);
            }
        }
    }
}
