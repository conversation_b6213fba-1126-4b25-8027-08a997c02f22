﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EInvoice.ViewModels
{
    public class TokenVM
    {
        public TokenVM()
        {
            ExpiresDate = DateTime.UtcNow;
        }
        public string access_token { get; set; }
        public int expires_in { get; set; }
        public DateTime ExpiresDate { get; set; }
        public bool Expired => (expires_in == 0?true: ExpiresDate >= DateTime.UtcNow);
    }
}
