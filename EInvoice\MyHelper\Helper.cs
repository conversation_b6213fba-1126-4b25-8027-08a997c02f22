﻿using EInvoice.Models;
using Models_1.ViewModels;
using Models_1.ViewModels.SignuatureVM;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Utilities;
using Models_1.ViewModels.DatabaseVM;

namespace EInvoice.MyHelper
{
    public static class Utilities
    {
        public static List<string> TaxableFees()
        {
            List<string> taxableFees = new List<string>
            {
                "T5","T7","T8","T9","T10","T11","T12"
            };
            return taxableFees;
        }

        public static List<string> NonTaxableFees()
        {
            List<string> nonTaxableFees = new List<string>
            {
                "T13","T14","T15","T16","T17","T18","T19","T20"
            };
            return nonTaxableFees;
        }

        public static List<string> TaxableandNonTaxableFees()
        {
            List<string> nonTaxableFees = new List<string>
            {
                "T5","T7","T8","T9","T10","T11","T12","T13","T14","T15","T16","T17","T18","T19","T20"
            };
            return nonTaxableFees;
        }

        public static Issuer GetIssuerData(StCompanyInfoVM companyData, IcStoreVM storeData)
        {

            var issuer = new Issuer();
            var issuerAddress = new Address();
            issuer.name = companyData.CmpNameAr;
            issuer.id = companyData.TaxCard;
            issuer.type = Enum.GetName(typeof(Type), (int)Type.B);

            issuerAddress.branchID = storeData.Ecode;
            issuerAddress.country = storeData.CountryCode;
            issuerAddress.governate = storeData.Governate;
            issuerAddress.regionCity = storeData.RegionCity;
            issuerAddress.street = storeData.Street;
            issuerAddress.buildingNumber = storeData.BuildingNumber;
            issuer.address = issuerAddress;
            return issuer;

        }

        public static Receiver GetReceiverData(SlCustomerVM receiverData)
        {

            var receiver = new Receiver();
            var receiverAddress = new Address();
            receiver.name = receiverData.CusNameAr;
            receiver.id = (receiverData.CsType == (int)Type.P || receiverData.CsType == (int)Type.F) ? (receiverData.IdNumber ?? string.Empty) : receiverData.TaxCardNumber;
            receiver.type = Enum.GetName(typeof(Type), receiverData.CsType);

            receiverAddress.country = receiverData.CountryCode;
            receiverAddress.governate = receiverData.Governate;
            receiverAddress.regionCity = receiverData.City;
            receiverAddress.street = receiverData.Street;
            receiverAddress.buildingNumber = receiverData.BuildingNumber;
            receiverAddress.branchID = "1";
            receiver.address = receiverAddress;

            return receiver;
        }


        public static UnitValue GetUnitValue(int currencyId, decimal amount, decimal exchangeRate, int roundValue, ERPEinvoiceContext DB)
        {
            UnitValue unitValue = new UnitValue()
            {
                currencySold = DB.StCurrency.FirstOrDefault(x => x.CrncId == currencyId).Ecode,
                amountEGP = Math.Round((amount * exchangeRate), roundValue),
                amountSold = (exchangeRate == 1) ? null : (decimal?)Math.Round(amount, roundValue),
                currencyExchangeRate = (exchangeRate == 1) ? 0 : (decimal?)Math.Round(exchangeRate, roundValue)
            };
            return unitValue;
        }
        //[DllImport("ChilkatDotNet45.dll", EntryPoint = "GetSigBase64", CharSet = CharSet.Ansi, CallingConvention = CallingConvention.Cdecl)]
        //public static extern string GetSigBase64(string json, string donglePIN, string cretSerial);
        //[DllImport("ChilkatDotNet45.dll")]
        //public static extern string hello(string json, string donglePIN, string cretSerial);

        public static void save_Log(string message, Exception exception)
        {
            try
            {
                //get connected db name
                System.Data.SqlClient.SqlConnectionStringBuilder builder = new System.Data.SqlClient.SqlConnectionStringBuilder();
                builder.ConnectionString = DAL.Config.ConnectionString;

                string LinkITERP_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\LinkITERP";
                if (!System.IO.Directory.Exists(LinkITERP_path))
                    System.IO.Directory.CreateDirectory(LinkITERP_path);

                string logFileName = LinkITERP_path + "\\" + builder.InitialCatalog + "_Log.txt";
                if (!System.IO.File.Exists(logFileName))
                    System.IO.File.CreateText(logFileName);

                StreamWriter sw = File.AppendText(logFileName);
                sw.WriteLine(DateTime.Now.ToString() + ">> " + message);
                if (exception != null)
                {
                    sw.WriteLine(DateTime.Now.ToString() + "Inner Exception: " + exception.InnerException?.Message);
                    sw.WriteLine(DateTime.Now.ToString() + "Stacke Trace: " + exception.InnerException?.StackTrace);

                }
                sw.Flush();
                sw.Close();
            }
            catch { }
        }

        public static string GetNextSlInvoiceCode(int? bookId, ERPEinvoiceContext DB)
        {

            var lastNumber = (from x in DB.SlInvoice
                              where x.InvoiceBookId == bookId
                              orderby x.InvoiceDate descending
                              orderby x.SlInvoiceId descending
                              select x.InvoiceCode).FirstOrDefault();
            var codeList = DB.SlInvoice.Where(p => p.InvoiceBookId == bookId).Select(p => p.InvoiceCode).ToList();

            var nextcode = GetNextNumberInString(lastNumber, codeList);
            return nextcode;
        }

        public static string GetNextNumberInString(string Number, List<string> list)
        {

            if (Number == string.Empty || Number == null)
                return "1";

            string NumPart = "";
            foreach (char c in Number)
            {
                if (char.IsDigit(c))
                    NumPart += c;
                else
                    NumPart = "";
            }
            if (NumPart == string.Empty)
                return Number + "1";

            string new_numpart = NumPart.Insert(0, "1");
            new_numpart = (Convert.ToInt64(new_numpart) + 1).ToString();
            if (new_numpart[0] == '1')
                new_numpart = new_numpart.Remove(0, 1);
            else
            {
                new_numpart = new_numpart.Remove(0, 1);
                new_numpart = new_numpart.Insert(0, "1");
            }
            int index = Number.LastIndexOf(NumPart);
            Number = Number.Remove(index);
            Number = Number.Insert(index, new_numpart);
            string x = "";
            if (list.Contains(Number))
            {
                x = GetNextNumberInString(Number, list);
            }
            else { x = Number; }
            return x;
            //return Number;
        }

        public static void UpdateST_UserLog(ERPEinvoiceContext DB, string code, string NotesAr, int processId, int ScreenId)
        {
            StUserLog ul = new StUserLog();
            ul.ActionDate = DateTime.Now;
            ul.Code = code;
            ul.NotesAr = NotesAr;
            ul.ProcessId = processId;
            ul.ScreenId = ScreenId;
            ul.UserId = 0;
            DB.Add(ul);
            DB.SaveChanges();
        }

        public static SignuatureVM GetDocumentToSync(Document document, ERPEinvoiceContext DB)
        {
            var model = new SignuatureVM();
            try
            {
                var certSerial = DAL.Shared.Dongle_SN;
                Utilities.UpdateST_UserLog(DB, "1", $"Get cert. Serial - {certSerial}", (int)FormAction.Add, 1);
                var donglePinDec = DB.StCompanyInfo.FirstOrDefault().DonglePin;
                var donglePinEnc = Crypto.DecryptStringAES(donglePinDec, Crypto.Key);
                Utilities.UpdateST_UserLog(DB, "1", $"Get dongle Pin", (int)FormAction.Add, 1);
                List<string> IgnoreProperties = new List<string>();
                IgnoreProperties.Add("signatures");
                IgnoreProperties.Add("invoiceId");
                //IgnoreProperties.Add("taxableItems");

                if (document.documentType == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
                {
                    IgnoreProperties.Add("references");
                }
                string canonicalDocument = JsonConvert.SerializeObject(document, Formatting.Indented, new JsonSerializerSettings { ContractResolver = new JsonIgnoreResolver(IgnoreProperties) });

                model.CretSerial = certSerial;
                model.DonglePIN = donglePinEnc;
                model.DocumentJson = canonicalDocument;

            }
            catch (Exception ex)
            {
                Utilities.UpdateST_UserLog(DB, "1", $"Exception from (GetDocumentToSync) - {ex.Message}", (int)FormAction.Add, 1);
            }
            return model;
        }

        public static void writeToFile(string msg, string action)
        {
            string path = "c:\\log\\log.txt";
            //if (File.Exists(path))
            //    File.WriteAllText(path, "scmd.DeleteContext" + msg);
            //else
            File.AppendAllText(path, action + " : " + msg);
        }

        public enum currencySold
        {
            EGP = 1,
            EUR = 2,
            USD = 3,
        }

        public enum Type
        {
            P = 0,
            B = 1,
            F = 2,
        }



        //public enum InvoiceType
        //{
        //    SalesInvoice = 1,
        //    SalesReturn= 2,

        //}

        public enum DocumentStatus
        {
            Submitted = 0,
            Valid = 1,
            Invalid = 2,
            Rejected = 3,
            Cancelled = 4
        }

        public enum FormAction
        {
            None = 0,
            Add = 1,
            Edit = 2,
            Delete = 3,
            Print = 4
        }

    }
}
