﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models_1.ViewModels
{
   public  class QueryParameters
    {
        public DateTime dateFrom { get; set; }
        public DateTime dateTo { get; set; }
        public List<string> documentTypeNames { get; set; }
        public List<string> statuses { get; set; }
        public List<string> productsInternalCodes { get; set; }
        public string receiverSenderType { get; set; }
        public string receiverSenderId { get; set; }
        public string branchNumber { get; set; }
        public List<ItemCodes> itemCodes { get; set; }
    }
}
