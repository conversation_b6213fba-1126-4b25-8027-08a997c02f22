﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models_1.ViewModels.DatabaseVM
{
    public class StCurrencyVM
    {
        public int CrncId { get; set; }
        public string CrncName { get; set; }
        public string CurrencyPound1 { get; set; }
        public string C<PERSON>rencyPound2 { get; set; }
        public string CurrencyPound3 { get; set; }
        public string CurrencyPiaster1 { get; set; }
        public string CurrencyPiaster2 { get; set; }
        public string CurrencyPiaster3 { get; set; }
        public int? CurrencyDigitsCount { get; set; }
        public decimal LastRate { get; set; }
        public string Ecode { get; set; }
        public bool? IsDefualt { get; set; }
    }
}
