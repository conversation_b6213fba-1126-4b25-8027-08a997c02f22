﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResRptAr {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResRptAr() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResRptAr", typeof(ResRptAr).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  باتش: .
        /// </summary>
        public static string Batch {
            get {
                return ResourceManager.GetString("Batch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دائن.
        /// </summary>
        public static string Credit {
            get {
                return ResourceManager.GetString("Credit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف زيارات العملاء.
        /// </summary>
        public static string CustomerVisits {
            get {
                return ResourceManager.GetString("CustomerVisits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مدين.
        /// </summary>
        public static string Debit {
            get {
                return ResourceManager.GetString("Debit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مستحق.
        /// </summary>
        public static string Due {
            get {
                return ResourceManager.GetString("Due", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سابق.
        /// </summary>
        public static string DueEarlierPeriod {
            get {
                return ResourceManager.GetString("DueEarlierPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لاحق.
        /// </summary>
        public static string DueLaterPeriod {
            get {
                return ResourceManager.GetString("DueLaterPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الحساب: .
        /// </summary>
        public static string Faccount {
            get {
                return ResourceManager.GetString("Faccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الفئه: .
        /// </summary>
        public static string Fcat {
            get {
                return ResourceManager.GetString("Fcat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الفئه: الكل .
        /// </summary>
        public static string FcatAll {
            get {
                return ResourceManager.GetString("FcatAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المجموعة: .
        /// </summary>
        public static string Fcomp {
            get {
                return ResourceManager.GetString("Fcomp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المجموعة: الكل .
        /// </summary>
        public static string FcompAll {
            get {
                return ResourceManager.GetString("FcompAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مركز التكلفة: .
        /// </summary>
        public static string FcostCenter {
            get {
                return ResourceManager.GetString("FcostCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فئة العميل: .
        /// </summary>
        public static string FCustGroup {
            get {
                return ResourceManager.GetString("FCustGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  فئة العميل: الكل .
        /// </summary>
        public static string FcustGroupAll {
            get {
                return ResourceManager.GetString("FcustGroupAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to العميل: .
        /// </summary>
        public static string Fcustomer {
            get {
                return ResourceManager.GetString("Fcustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to العميل: الكل .
        /// </summary>
        public static string FcustomerAll {
            get {
                return ResourceManager.GetString("FcustomerAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قائمة حسابات مخصصة: .
        /// </summary>
        public static string FcustomList {
            get {
                return ResourceManager.GetString("FcustomList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بتاريخ: .
        /// </summary>
        public static string FDate {
            get {
                return ResourceManager.GetString("FDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قبل تاريخ: .
        /// </summary>
        public static string FdateBefore {
            get {
                return ResourceManager.GetString("FdateBefore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  مجموعة الموظف: .
        /// </summary>
        public static string FempGroup {
            get {
                return ResourceManager.GetString("FempGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  مجموعة الموظف: الكل .
        /// </summary>
        public static string FempGroupAll {
            get {
                return ResourceManager.GetString("FempGroupAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من .
        /// </summary>
        public static string FFrom {
            get {
                return ResourceManager.GetString("FFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to من تاريخ: .
        /// </summary>
        public static string FfromDate {
            get {
                return ResourceManager.GetString("FfromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دفتر الفواتير:.
        /// </summary>
        public static string FInvBook {
            get {
                return ResourceManager.GetString("FInvBook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دفتر الفواتير: الكل .
        /// </summary>
        public static string FInvBookAll {
            get {
                return ResourceManager.GetString("FInvBookAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم الصنف: .
        /// </summary>
        public static string FitemName {
            get {
                return ResourceManager.GetString("FitemName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم الصنف: الكل .
        /// </summary>
        public static string FitemsAll {
            get {
                return ResourceManager.GetString("FitemsAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نوع الصنف:.
        /// </summary>
        public static string FitemType {
            get {
                return ResourceManager.GetString("FitemType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نوع الصنف: الكل.
        /// </summary>
        public static string FitemTypeAll {
            get {
                return ResourceManager.GetString("FitemTypeAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الجهة:.
        /// </summary>
        public static string FjoDept {
            get {
                return ResourceManager.GetString("FjoDept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الجهة: الكل.
        /// </summary>
        public static string FjoDeptAll {
            get {
                return ResourceManager.GetString("FjoDeptAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاهمية:.
        /// </summary>
        public static string FjoPriority {
            get {
                return ResourceManager.GetString("FjoPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاهمية: الكل.
        /// </summary>
        public static string FjoPriorityAll {
            get {
                return ResourceManager.GetString("FjoPriorityAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الحالة:.
        /// </summary>
        public static string FjoStatus {
            get {
                return ResourceManager.GetString("FjoStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الحالة: الكل.
        /// </summary>
        public static string FjoStatusAll {
            get {
                return ResourceManager.GetString("FjoStatusAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف حساب عميل تفصيلي.
        /// </summary>
        public static string frm_Acc_AccountDetails {
            get {
                return ResourceManager.GetString("frm_Acc_AccountDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف حساب مورد تفصيلي.
        /// </summary>
        public static string frm_Acc_PR_AccountDetails {
            get {
                return ResourceManager.GetString("frm_Acc_PR_AccountDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مديونية العملاء.
        /// </summary>
        public static string frm_Customers_Debit {
            get {
                return ResourceManager.GetString("frm_Customers_Debit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رواتب متوقعة.
        /// </summary>
        public static string frm_HR_AllExpectedPays {
            get {
                return ResourceManager.GetString("frm_HR_AllExpectedPays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف المرتبات.
        /// </summary>
        public static string frm_HR_AllPays {
            get {
                return ResourceManager.GetString("frm_HR_AllPays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الحضور والانصراف.
        /// </summary>
        public static string frm_HR_Att {
            get {
                return ResourceManager.GetString("frm_HR_Att", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأمينات الموظفين.
        /// </summary>
        public static string frm_HR_Insurance {
            get {
                return ResourceManager.GetString("frm_HR_Insurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أرصدة الاجازات.
        /// </summary>
        public static string frm_HR_VacationBal {
            get {
                return ResourceManager.GetString("frm_HR_VacationBal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف الأجازات.
        /// </summary>
        public static string frm_HR_Vacations {
            get {
                return ResourceManager.GetString("frm_HR_Vacations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أرصدة الأصناف بالمخازن الأفقي.
        /// </summary>
        public static string frm_IC_ItemsQtyH {
            get {
                return ResourceManager.GetString("frm_IC_ItemsQtyH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقرير أرصدة الأصناف أفقى بدون تكلفة.
        /// </summary>
        public static string frm_IC_ItemsQtyHNoCost {
            get {
                return ResourceManager.GetString("frm_IC_ItemsQtyHNoCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to معدل دوران المخزون.
        /// </summary>
        public static string frm_IC_ItemsTurnOver {
            get {
                return ResourceManager.GetString("frm_IC_ItemsTurnOver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبيعات ومشتريات الأصناف.
        /// </summary>
        public static string frm_ItemsPr_and_Sl {
            get {
                return ResourceManager.GetString("frm_ItemsPr_and_Sl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to منتجات و خامات عمليات الانتاج.
        /// </summary>
        public static string frm_ManfItems {
            get {
                return ResourceManager.GetString("frm_ManfItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to توزيع الأصناف حسب فئة العميل.
        /// </summary>
        public static string frm_MrAllSales {
            get {
                return ResourceManager.GetString("frm_MrAllSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مستخلص مقاول.
        /// </summary>
        public static string frm_PR_ContractorExtract {
            get {
                return ResourceManager.GetString("frm_PR_ContractorExtract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أوامر الشراء ونسب التحقيق.
        /// </summary>
        public static string frm_PurchaseOrderAndAchievement {
            get {
                return ResourceManager.GetString("frm_PurchaseOrderAndAchievement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أوامر البيع ونسب التحقيق.
        /// </summary>
        public static string frm_SalesOrderAndAchievement {
            get {
                return ResourceManager.GetString("frm_SalesOrderAndAchievement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يومية مندوب.
        /// </summary>
        public static string frm_SalesRep_DaySummary {
            get {
                return ResourceManager.GetString("frm_SalesRep_DaySummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حمولات السيارات.
        /// </summary>
        public static string frm_SL_Car_Weights {
            get {
                return ResourceManager.GetString("frm_SL_Car_Weights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقرير مبيعات المندوبين ومجموعات الأصناف.
        /// </summary>
        public static string frm_SL_DelegatesSales_ItemCategory {
            get {
                return ResourceManager.GetString("frm_SL_DelegatesSales_ItemCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقرير صافي المشتريات ومردودات المشتريات.
        /// </summary>
        public static string frm_SL_ItemsNetPurchaseDetails {
            get {
                return ResourceManager.GetString("frm_SL_ItemsNetPurchaseDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقرير صافي مييعات ومردود مبيعات.
        /// </summary>
        public static string frm_SL_ItemsNetSalesDetails {
            get {
                return ResourceManager.GetString("frm_SL_ItemsNetSalesDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إجمالي مبيعات ومردودات الاصناف.
        /// </summary>
        public static string frm_SL_ItemsSalesDetails {
            get {
                return ResourceManager.GetString("frm_SL_ItemsSalesDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مبيعات الأصناف.
        /// </summary>
        public static string frm_SL_ItemsSalesTotals {
            get {
                return ResourceManager.GetString("frm_SL_ItemsSalesTotals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبيعات أوامر عمل.
        /// </summary>
        public static string frm_SL_JobOrderInv {
            get {
                return ResourceManager.GetString("frm_SL_JobOrderInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف أوامر بيع - محجوزة.
        /// </summary>
        public static string frm_SL_SalesOrderItems {
            get {
                return ResourceManager.GetString("frm_SL_SalesOrderItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف أوامر بيع بالأرصدة الحالية.
        /// </summary>
        public static string frm_SL_SalesOrderItemsAndBalance {
            get {
                return ResourceManager.GetString("frm_SL_SalesOrderItemsAndBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to انتهاء ضمان الأصناف.
        /// </summary>
        public static string frm_SL_Warranty {
            get {
                return ResourceManager.GetString("frm_SL_Warranty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مندوب المبيعات:.
        /// </summary>
        public static string FsalesEmp {
            get {
                return ResourceManager.GetString("FsalesEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مندوب المبيعات: الكل.
        /// </summary>
        public static string FsalesEmpAll {
            get {
                return ResourceManager.GetString("FsalesEmpAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المخزن: .
        /// </summary>
        public static string Fstore {
            get {
                return ResourceManager.GetString("Fstore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المخزن: الكل  .
        /// </summary>
        public static string FstoreAll {
            get {
                return ResourceManager.GetString("FstoreAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى .
        /// </summary>
        public static string FTo {
            get {
                return ResourceManager.GetString("FTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حتي تاريخ: .
        /// </summary>
        public static string FtoDate {
            get {
                return ResourceManager.GetString("FtoDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  المستخدم: .
        /// </summary>
        public static string Fuser {
            get {
                return ResourceManager.GetString("Fuser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  المستخدم: الكل .
        /// </summary>
        public static string FuserAll {
            get {
                return ResourceManager.GetString("FuserAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المورد: .
        /// </summary>
        public static string Fvendor {
            get {
                return ResourceManager.GetString("Fvendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المورد: الكل .
        /// </summary>
        public static string FvendorAll {
            get {
                return ResourceManager.GetString("FvendorAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فئة المورد: .
        /// </summary>
        public static string FvenGroup {
            get {
                return ResourceManager.GetString("FvenGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  فئة المورد: الكل .
        /// </summary>
        public static string FvenGroupAll {
            get {
                return ResourceManager.GetString("FvenGroupAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خسارة.
        /// </summary>
        public static string Loss {
            get {
                return ResourceManager.GetString("Loss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، غير مصرح لك فتح هذا التقرير.
        /// </summary>
        public static string MsgPrv {
            get {
                return ResourceManager.GetString("MsgPrv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تصنيف1.
        /// </summary>
        public static string mtrx1 {
            get {
                return ResourceManager.GetString("mtrx1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تصنيف2.
        /// </summary>
        public static string mtrx2 {
            get {
                return ResourceManager.GetString("mtrx2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تصنيف3.
        /// </summary>
        public static string mtrx3 {
            get {
                return ResourceManager.GetString("mtrx3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد مسار صحيح لمجلد تصاميم التقارير، في شاشة اعدادات البرنامج.
        /// </summary>
        public static string path {
            get {
                return ResourceManager.GetString("path", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بعد الانتهاء من تعديل تصميم التقرير، يجب عمل حفظ للتقرير بدون تغير اسم التقرير، في المجلد.
        /// </summary>
        public static string path2 {
            get {
                return ResourceManager.GetString("path2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ربح.
        /// </summary>
        public static string Profit {
            get {
                return ResourceManager.GetString("Profit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف مجمع لمراكز تكلفة حساب.
        /// </summary>
        public static string rpt_Acc_Account_CostCenters {
            get {
                return ResourceManager.GetString("rpt_Acc_Account_CostCenters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المركز المالي.
        /// </summary>
        public static string rpt_Acc_Balance {
            get {
                return ResourceManager.GetString("rpt_Acc_Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف حساب مجمع لمركز التكلفة.
        /// </summary>
        public static string rpt_Acc_CostCenter_AccDetails {
            get {
                return ResourceManager.GetString("rpt_Acc_CostCenter_AccDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إجمالي أرصدة مراكز التكلفة.
        /// </summary>
        public static string rpt_Acc_CostCenterTotalBalances {
            get {
                return ResourceManager.GetString("rpt_Acc_CostCenterTotalBalances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف اجمالي قوائم مخصصة.
        /// </summary>
        public static string rpt_Acc_CustomAccListBalances {
            get {
                return ResourceManager.GetString("rpt_Acc_CustomAccListBalances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف تفصيلي لقائمة مخصصة.
        /// </summary>
        public static string rpt_Acc_CustomAccListDetails {
            get {
                return ResourceManager.GetString("rpt_Acc_CustomAccListDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to وارد يومية.
        /// </summary>
        public static string rpt_Acc_DailyIncome {
            get {
                return ResourceManager.GetString("rpt_Acc_DailyIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مدفوعات يومية.
        /// </summary>
        public static string rpt_Acc_DailyPayments {
            get {
                return ResourceManager.GetString("rpt_Acc_DailyPayments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف مدفوعات و ايرادات يومية.
        /// </summary>
        public static string rpt_Acc_DailyPaymentsAndIncomePayments {
            get {
                return ResourceManager.GetString("rpt_Acc_DailyPaymentsAndIncomePayments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قائمة الدخل.
        /// </summary>
        public static string rpt_Acc_Income {
            get {
                return ResourceManager.GetString("rpt_Acc_Income", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المدفوعات.
        /// </summary>
        public static string rpt_Acc_Payments {
            get {
                return ResourceManager.GetString("rpt_Acc_Payments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف حساب تفصيلي.
        /// </summary>
        public static string rpt_Acc_PR_AccountDetails {
            get {
                return ResourceManager.GetString("rpt_Acc_PR_AccountDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالى حسابات الموردين.
        /// </summary>
        public static string rpt_Acc_PR_AccountsBalances {
            get {
                return ResourceManager.GetString("rpt_Acc_PR_AccountsBalances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالى حسابات الموردين بالأوراق التجارية.
        /// </summary>
        public static string rpt_Acc_PR_AccountsBalancesWithNotes {
            get {
                return ResourceManager.GetString("rpt_Acc_PR_AccountsBalancesWithNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف حساب تفصيلي.
        /// </summary>
        public static string rpt_Acc_SL_AccountDetails {
            get {
                return ResourceManager.GetString("rpt_Acc_SL_AccountDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالى حسابات العملاء.
        /// </summary>
        public static string rpt_Acc_SL_AccountsBalances {
            get {
                return ResourceManager.GetString("rpt_Acc_SL_AccountsBalances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالى حسابات العملاء بالأوراق التجارية.
        /// </summary>
        public static string rpt_Acc_SL_AccountsBalancesWithNotes {
            get {
                return ResourceManager.GetString("rpt_Acc_SL_AccountsBalancesWithNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقرير استاذ مساعد.
        /// </summary>
        public static string rpt_ACC_SubLedger {
            get {
                return ResourceManager.GetString("rpt_ACC_SubLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقرير مبيعات المندوبين.
        /// </summary>
        public static string rpt_DelegatesSales {
            get {
                return ResourceManager.GetString("rpt_DelegatesSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عمولات أصناف أذون الصرف.
        /// </summary>
        public static string rpt_HR_DeliveryCommision {
            get {
                return ResourceManager.GetString("rpt_HR_DeliveryCommision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to انتاجية الموظف.
        /// </summary>
        public static string rpt_HR_ManfCommision {
            get {
                return ResourceManager.GetString("rpt_HR_ManfCommision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عمولات مباشرة علي فواتير البيع.
        /// </summary>
        public static string rpt_HR_SalesCommision {
            get {
                return ResourceManager.GetString("rpt_HR_SalesCommision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عمولات مندوب حسب الهدف والتحصيل.
        /// </summary>
        public static string rpt_HR_SalesEmpCommission {
            get {
                return ResourceManager.GetString("rpt_HR_SalesEmpCommission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عمولات أصناف فواتير البيع.
        /// </summary>
        public static string rpt_HR_SalesInvItemsCommision {
            get {
                return ResourceManager.GetString("rpt_HR_SalesInvItemsCommision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to وارد وصادر عمليات الصنف بالمخزن.
        /// </summary>
        public static string rpt_IC_Item_In_Out_Balance {
            get {
                return ResourceManager.GetString("rpt_IC_Item_In_Out_Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقرير وارد و صادر أصناف.
        /// </summary>
        public static string rpt_IC_ItemOpenInOutClose {
            get {
                return ResourceManager.GetString("rpt_IC_ItemOpenInOutClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إجمالى حركات الأصناف بالمخازن.
        /// </summary>
        public static string rpt_IC_ItemOpenInOutCloseQty {
            get {
                return ResourceManager.GetString("rpt_IC_ItemOpenInOutCloseQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف تنتهي صلاحيتها.
        /// </summary>
        public static string rpt_IC_ItemsExpired {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاصناف الاكثر مبيعا.
        /// </summary>
        public static string rpt_IC_ItemsMaxSell {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsMaxSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف وصلت للحد الأدنى للطلب.
        /// </summary>
        public static string rpt_IC_ItemsMinLevel {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsMinLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاصناف الاقل مبيعا.
        /// </summary>
        public static string rpt_IC_ItemsMinSell {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsMinSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف لم تباع مطلقا.
        /// </summary>
        public static string rpt_IC_ItemsNotSold {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsNotSold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الارصده الافتتاحيه.
        /// </summary>
        public static string rpt_IC_ItemsOpenBalance {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أرصدة الأصناف.
        /// </summary>
        public static string rpt_IC_ItemsQty {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أرصدة الأصناف تفصيلي.
        /// </summary>
        public static string rpt_IC_ItemsQtyDetails {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsQtyDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقييم المخزون.
        /// </summary>
        public static string rpt_IC_ItemsQtyWithPrices {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsQtyWithPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقييم المخزون بسعر البيع.
        /// </summary>
        public static string rpt_IC_ItemsQtyWithSalesPrice {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsQtyWithSalesPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف وصلت لحد الطلب.
        /// </summary>
        public static string rpt_IC_ItemsReorder {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إجماليات حركة الاصناف.
        /// </summary>
        public static string rpt_IC_ItemsTotals {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsTotals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حركة صنف تفصيلي بالتكلفة.
        /// </summary>
        public static string rpt_IC_ItemTransactions {
            get {
                return ResourceManager.GetString("rpt_IC_ItemTransactions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to جرد محتويات مخزن.
        /// </summary>
        public static string rpt_IC_ItemTransactionsDetails {
            get {
                return ResourceManager.GetString("rpt_IC_ItemTransactionsDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حركة صنف تفصيلي.
        /// </summary>
        public static string rpt_IC_ItemTransactionsNoCost {
            get {
                return ResourceManager.GetString("rpt_IC_ItemTransactionsNoCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تكلفة البضاعـــة المبـــاعة.
        /// </summary>
        public static string rpt_IC_SoldItemsCost {
            get {
                return ResourceManager.GetString("rpt_IC_SoldItemsCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تفاصيل الفواتير.
        /// </summary>
        public static string rpt_InvoiceDetails {
            get {
                return ResourceManager.GetString("rpt_InvoiceDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تغييرات أسعار الاصناف.
        /// </summary>
        public static string rpt_ItemPriceChangings {
            get {
                return ResourceManager.GetString("rpt_ItemPriceChangings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نسبة رأس المال في فواتير الآجل.
        /// </summary>
        public static string rpt_PercentageOfCapitalInDeferredInvoices {
            get {
                return ResourceManager.GetString("rpt_PercentageOfCapitalInDeferredInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نموذج ٤١ ضرائب.
        /// </summary>
        public static string rpt_PR_InvoicesDiscountTaxHeaders {
            get {
                return ResourceManager.GetString("rpt_PR_InvoicesDiscountTaxHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي فواتير المشتريات.
        /// </summary>
        public static string rpt_PR_InvoicesHeaders {
            get {
                return ResourceManager.GetString("rpt_PR_InvoicesHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كميات شراء وأرصدة الأصناف.
        /// </summary>
        public static string rpt_PR_ItemsPurchases {
            get {
                return ResourceManager.GetString("rpt_PR_ItemsPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كميات مردود شراء وأرصدة الأصناف.
        /// </summary>
        public static string rpt_PR_ItemsReturns {
            get {
                return ResourceManager.GetString("rpt_PR_ItemsReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي فواتير مردود المشتريات.
        /// </summary>
        public static string rpt_PR_ReturnHeaders {
            get {
                return ResourceManager.GetString("rpt_PR_ReturnHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مشتريات الأصناف من الموردين.
        /// </summary>
        public static string rpt_PR_VendorItemsPurchases {
            get {
                return ResourceManager.GetString("rpt_PR_VendorItemsPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مشتريات و اذونات اضافة.
        /// </summary>
        public static string rpt_PR_VendorItemsPurchases_InTrns {
            get {
                return ResourceManager.GetString("rpt_PR_VendorItemsPurchases_InTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مردود مشتريات الأصناف من الموردين.
        /// </summary>
        public static string rpt_PR_VendorItemsPurchasesReturns {
            get {
                return ResourceManager.GetString("rpt_PR_VendorItemsPurchasesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبيعات الأصناف للعملاء.
        /// </summary>
        public static string rpt_SL_CustomerItemsSales {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerItemsSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبيعات وأذونات صرف.
        /// </summary>
        public static string rpt_SL_CustomerItemsSales_OutTrns {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerItemsSales_OutTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مردود مبيعات الأصناف من العملاء.
        /// </summary>
        public static string rpt_SL_CustomerItemsSalesReturn {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerItemsSalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبيعات و مردودات الأصناف تفصيلي.
        /// </summary>
        public static string rpt_SL_CustomerItemsSalesReturns {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerItemsSalesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بيان عمليات عميل.
        /// </summary>
        public static string rpt_SL_CustomerTrans {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerTrans", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبيعات مسؤولي التسليم.
        /// </summary>
        public static string rpt_SL_DeliveryOfficialsSales {
            get {
                return ResourceManager.GetString("rpt_SL_DeliveryOfficialsSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ استحقاق فواتير المبيعات الآجلة.
        /// </summary>
        public static string rpt_SL_Invoices_Due {
            get {
                return ResourceManager.GetString("rpt_SL_Invoices_Due", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي فواتير المبيعات.
        /// </summary>
        public static string rpt_SL_InvoicesHeaders {
            get {
                return ResourceManager.GetString("rpt_SL_InvoicesHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كميات مردود بيع وأرصدة الأصناف.
        /// </summary>
        public static string rpt_SL_ItemsReturn {
            get {
                return ResourceManager.GetString("rpt_SL_ItemsReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كميات بيع وأرصدة الأصناف.
        /// </summary>
        public static string rpt_SL_ItemsSales {
            get {
                return ResourceManager.GetString("rpt_SL_ItemsSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ربح او خسارة صنف توزيع.
        /// </summary>
        public static string rpt_SL_ItemTrade {
            get {
                return ResourceManager.GetString("rpt_SL_ItemTrade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ربح أو خسارة فواتير البيع.
        /// </summary>
        public static string rpt_SL_Profit_Loss {
            get {
                return ResourceManager.GetString("rpt_SL_Profit_Loss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي فواتير مردود المبيعات.
        /// </summary>
        public static string rpt_SL_ReturnHeaders {
            get {
                return ResourceManager.GetString("rpt_SL_ReturnHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الحساب.
        /// </summary>
        public static string ValAccount {
            get {
                return ResourceManager.GetString("ValAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار مركز التكلفة.
        /// </summary>
        public static string ValCostCenter {
            get {
                return ResourceManager.GetString("ValCostCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار العميل.
        /// </summary>
        public static string ValCustomer {
            get {
                return ResourceManager.GetString("ValCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار قائمة الحسابات المخصصة.
        /// </summary>
        public static string ValCustomList {
            get {
                return ResourceManager.GetString("ValCustomList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار تاريخ البداية والنهاية.
        /// </summary>
        public static string ValDateFromTo {
            get {
                return ResourceManager.GetString("ValDateFromTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الصنف.
        /// </summary>
        public static string ValItem {
            get {
                return ResourceManager.GetString("ValItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب إختيار الموظف.
        /// </summary>
        public static string valSalesEmp {
            get {
                return ResourceManager.GetString("valSalesEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار المخزن.
        /// </summary>
        public static string ValStore {
            get {
                return ResourceManager.GetString("ValStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار المورد.
        /// </summary>
        public static string ValVendor {
            get {
                return ResourceManager.GetString("ValVendor", resourceCulture);
            }
        }
    }
}
