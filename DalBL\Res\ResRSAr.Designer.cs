﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResRSAr {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResRSAr() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResRSAr", typeof(ResRSAr).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا العقد.
        /// </summary>
        public static string DelContract {
            get {
                return ResourceManager.GetString("DelContract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يمكن حذف الوحدة يوجد عقد بيع مسجل لها.
        /// </summary>
        public static string DelFlat {
            get {
                return ResourceManager.GetString("DelFlat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد انك تريد حذف هذا العقار.
        /// </summary>
        public static string MsgAskConfirmDeletePrj {
            get {
                return ResourceManager.GetString("MsgAskConfirmDeletePrj", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حفظ العقد أولا.
        /// </summary>
        public static string MsgAskToSaveCntr {
            get {
                return ResourceManager.GetString("MsgAskToSaveCntr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف القيود الخاصة بهذا المشروع أولا.
        /// </summary>
        public static string MsgDeleteJornalsFirst {
            get {
                return ResourceManager.GetString("MsgDeleteJornalsFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه الوحدة.
        /// </summary>
        public static string MsgDelFlat {
            get {
                return ResourceManager.GetString("MsgDelFlat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف فئة المشاريع هذه ؟.
        /// </summary>
        public static string MsgDelProjectGrp {
            get {
                return ResourceManager.GetString("MsgDelProjectGrp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف فئة المشروعات، يوجد عقارات مرتبطة بها.
        /// </summary>
        public static string MsgDelProjectGrpDenied1 {
            get {
                return ResourceManager.GetString("MsgDelProjectGrpDenied1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حفظ العقار أولا.
        /// </summary>
        public static string MsgSavePrj {
            get {
                return ResourceManager.GetString("MsgSavePrj", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عقد بيع عقار رقم.
        /// </summary>
        public static string SellContract {
            get {
                return ResourceManager.GetString("SellContract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب إختيار حساب ايرادات عقود بيع العقارات من شاشة الإعدادات.
        /// </summary>
        public static string SellRevenueAcc {
            get {
                return ResourceManager.GetString("SellRevenueAcc", resourceCulture);
            }
        }
    }
}
