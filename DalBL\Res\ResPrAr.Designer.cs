﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResPrAr {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResPrAr() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResPrAr", typeof(ResPrAr).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف حساب تفصيلي.
        /// </summary>
        public static string accDetail {
            get {
                return ResourceManager.GetString("accDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الوضع الإفتراضي لحسابات موردين هذه الفئة.
        /// </summary>
        public static string defaultCustGroupAcc {
            get {
                return ResourceManager.GetString("defaultCustGroupAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to غرامة رقم.
        /// </summary>
        public static string FineNumber {
            get {
                return ResourceManager.GetString("FineNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد تحديث قوائم أسعار العملاء أيضا بنفس نسبة زيادة الأســعار ؟.
        /// </summary>
        public static string MsgAskChangePList {
            get {
                return ResourceManager.GetString("MsgAskChangePList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد انك تريد حذف هذ المورد ?.
        /// </summary>
        public static string MsgAskConfirmDeleteVen {
            get {
                return ResourceManager.GetString("MsgAskConfirmDeleteVen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد انشاء اذن اضافة للمخزن الان؟.
        /// </summary>
        public static string MsgAskCreateInTrns {
            get {
                return ResourceManager.GetString("MsgAskCreateInTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد انشاء اذن صرف من المخزن الان ؟.
        /// </summary>
        public static string MsgAskCreateOutTrns {
            get {
                return ResourceManager.GetString("MsgAskCreateOutTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف.
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد طباعة ملصقات باركود لفاتورة المشتريات.
        /// </summary>
        public static string MsgAskPrintBarCode {
            get {
                return ResourceManager.GetString("MsgAskPrintBarCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء حفظ الفاتورة اولا.
        /// </summary>
        public static string MsgAskToSaveInv {
            get {
                return ResourceManager.GetString("MsgAskToSaveInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود 1 موجود مسبقا.
        /// </summary>
        public static string MsgChkCodeDuplication {
            get {
                return ResourceManager.GetString("MsgChkCodeDuplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحذف بنجاح.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف فئة الموردين هذه.
        /// </summary>
        public static string MsgDelCustGroup {
            get {
                return ResourceManager.GetString("MsgDelCustGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف فئة الموردين، يوجد موردين مرتبطين بها.
        /// </summary>
        public static string MsgDelCustGroup2 {
            get {
                return ResourceManager.GetString("MsgDelCustGroup2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف المستند.
        /// </summary>
        public static string MsgDeleteInv {
            get {
                return ResourceManager.GetString("MsgDeleteInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف القيود الخاصه بهذا المورد أولا.
        /// </summary>
        public static string MsgDeleteJornalsFirst {
            get {
                return ResourceManager.GetString("MsgDeleteJornalsFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكنك حذف هذا الحساب، يوجد حسابات اخرى مرتبطة به.
        /// </summary>
        public static string msgDelLinked {
            get {
                return ResourceManager.GetString("msgDelLinked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف صف ؟.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال كود المورد.
        /// </summary>
        public static string MsgEnterVendorCode {
            get {
                return ResourceManager.GetString("MsgEnterVendorCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال اسم المورد.
        /// </summary>
        public static string MsgEnterVendorName {
            get {
                return ResourceManager.GetString("MsgEnterVendorName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من صحة البيانات.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكنك تغيير هذا الحساب، عليك حذف القيود الخاصة به أولا.
        /// </summary>
        public static string msgLinkAcc {
            get {
                return ResourceManager.GetString("msgLinkAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن انشاء رصيد افتتاحي لهذا الحساب.
        /// </summary>
        public static string msgLinkAccOpen {
            get {
                return ResourceManager.GetString("msgLinkAccOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لم يتم اعتماد أمر الشراء بعد.
        /// </summary>
        public static string msgMustApprovePrOrder {
            get {
                return ResourceManager.GetString("msgMustApprovePrOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لم يتم اعتماد عرض سعر الشراء بعد.
        /// </summary>
        public static string msgMustApprovePrQuote {
            get {
                return ResourceManager.GetString("msgMustApprovePrQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الاسم مسجل من قبل.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد حساب لهذا المورد.
        /// </summary>
        public static string MsgNoAccount {
            get {
                return ResourceManager.GetString("MsgNoAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد كميه كافيه من أحد الاصناف, هل تريد الاستمرار  ؟.
        /// </summary>
        public static string MsgNoEnoughQty_continue {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty_continue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الرقم مسجل من قبل.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية تعديل هذا البيان.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية اضافة بيان جديد.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحفظ بنجاح.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to معلومة.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سؤال.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تحذير.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود المورد لايمكن أن يساوي صفر.
        /// </summary>
        public static string MsgValidateVendorCode {
            get {
                return ResourceManager.GetString("MsgValidateVendorCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to غير مسموح بتغير نوع البروفورمة.
        /// </summary>
        public static string preformaType {
            get {
                return ResourceManager.GetString("preformaType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string PreInvoicesAdvanced {
            get {
                return ResourceManager.GetString("PreInvoicesAdvanced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خصم.
        /// </summary>
        public static string txt_Discount {
            get {
                return ResourceManager.GetString("txt_Discount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سداد.
        /// </summary>
        public static string txt_Paid {
            get {
                return ResourceManager.GetString("txt_Paid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مصاريف مشتريات.
        /// </summary>
        public static string txt_PurchaseExpenses {
            get {
                return ResourceManager.GetString("txt_PurchaseExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ضريبة.
        /// </summary>
        public static string txt_Tax {
            get {
                return ResourceManager.GetString("txt_Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تام.
        /// </summary>
        public static string txtAssembly {
            get {
                return ResourceManager.GetString("txtAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف المورد العام.
        /// </summary>
        public static string txtCantDeleteVen {
            get {
                return ResourceManager.GetString("txtCantDeleteVen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد تعديل المورد العام.
        /// </summary>
        public static string txtCantEditVen {
            get {
                return ResourceManager.GetString("txtCantEditVen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الفئه: .
        /// </summary>
        public static string txtCategory {
            get {
                return ResourceManager.GetString("txtCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الشركة: .
        /// </summary>
        public static string txtComapny {
            get {
                return ResourceManager.GetString("txtComapny", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to متابعة ?.
        /// </summary>
        public static string txtContinue {
            get {
                return ResourceManager.GetString("txtContinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دائن.
        /// </summary>
        public static string txtCredit {
            get {
                return ResourceManager.GetString("txtCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to العميل.
        /// </summary>
        public static string txtCustomer {
            get {
                return ResourceManager.GetString("txtCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  اسم المتعامل.
        /// </summary>
        public static string txtDealerNm {
            get {
                return ResourceManager.GetString("txtDealerNm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مدين.
        /// </summary>
        public static string txtDebit {
            get {
                return ResourceManager.GetString("txtDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خ ن :.
        /// </summary>
        public static string txtDiscRatio {
            get {
                return ResourceManager.GetString("txtDiscRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خ ق :.
        /// </summary>
        public static string txtDiscValue {
            get {
                return ResourceManager.GetString("txtDiscValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  خزينه .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مصاريف :.
        /// </summary>
        public static string txtExpenses {
            get {
                return ResourceManager.GetString("txtExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من تاريخ .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to التاريخ.
        /// </summary>
        public static string txtInvDate {
            get {
                return ResourceManager.GetString("txtInvDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رقم الفاتورة.
        /// </summary>
        public static string txtInvNumber {
            get {
                return ResourceManager.GetString("txtInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نوع السداد.
        /// </summary>
        public static string txtInvPayMethod {
            get {
                return ResourceManager.GetString("txtInvPayMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الصنف:.
        /// </summary>
        public static string txtItem {
            get {
                return ResourceManager.GetString("txtItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حركة الاصناف.
        /// </summary>
        public static string txtItemMovement {
            get {
                return ResourceManager.GetString("txtItemMovement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أرصدة الأصناف.
        /// </summary>
        public static string txtItemsBalance {
            get {
                return ResourceManager.GetString("txtItemsBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاصناف الاكثر مبيعا.
        /// </summary>
        public static string txtItemsBestSell {
            get {
                return ResourceManager.GetString("txtItemsBestSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاصناف الاقل مبيعا.
        /// </summary>
        public static string txtItemsLeastSell {
            get {
                return ResourceManager.GetString("txtItemsLeastSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف وصلت لحد الطلب.
        /// </summary>
        public static string txtItemsReorder {
            get {
                return ResourceManager.GetString("txtItemsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مشتريات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalPurchases {
            get {
                return ResourceManager.GetString("txtItemTotalPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مردود مشتريات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalPurchasesReturns {
            get {
                return ResourceManager.GetString("txtItemTotalPurchasesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مبيعات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalSales {
            get {
                return ResourceManager.GetString("txtItemTotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مردود مبيعات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalSalesReturn {
            get {
                return ResourceManager.GetString("txtItemTotalSalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اخر اسعار شراء.
        /// </summary>
        public static string txtLastPPrices {
            get {
                return ResourceManager.GetString("txtLastPPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اخر اسعار شراء من مورد.
        /// </summary>
        public static string txtLastVendorPPrices {
            get {
                return ResourceManager.GetString("txtLastVendorPPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to صافي :.
        /// </summary>
        public static string txtNet {
            get {
                return ResourceManager.GetString("txtNet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ملاحظات.
        /// </summary>
        public static string txtNotes {
            get {
                return ResourceManager.GetString("txtNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  رصيد افتتاحي .
        /// </summary>
        public static string txtOpenBalance {
            get {
                return ResourceManager.GetString("txtOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مدفوع :.
        /// </summary>
        public static string txtPaid {
            get {
                return ResourceManager.GetString("txtPaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فاتورة مشتريات رقم.
        /// </summary>
        public static string txtPRInvoiceNumber {
            get {
                return ResourceManager.GetString("txtPRInvoiceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فاتورة مردود مشتريات رقم.
        /// </summary>
        public static string txtPRReturnNumber {
            get {
                return ResourceManager.GetString("txtPRReturnNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to متبقي :.
        /// </summary>
        public static string txtRemains {
            get {
                return ResourceManager.GetString("txtRemains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تسلسل.
        /// </summary>
        public static string txtSerial {
            get {
                return ResourceManager.GetString("txtSerial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المخزن:.
        /// </summary>
        public static string txtStore {
            get {
                return ResourceManager.GetString("txtStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  ض ع ن:.
        /// </summary>
        public static string txtTaxRatio {
            get {
                return ResourceManager.GetString("txtTaxRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ض ع ق:.
        /// </summary>
        public static string txtTaxValue {
            get {
                return ResourceManager.GetString("txtTaxValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى تاريخ .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاجمــالي :.
        /// </summary>
        public static string txtTotal {
            get {
                return ResourceManager.GetString("txtTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تحديد الخصم.
        /// </summary>
        public static string txtValidateDiscount {
            get {
                return ResourceManager.GetString("txtValidateDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل رقم الفاتوره.
        /// </summary>
        public static string txtValidateInvNumber {
            get {
                return ResourceManager.GetString("txtValidateInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الصنف.
        /// </summary>
        public static string txtValidateItem {
            get {
                return ResourceManager.GetString("txtValidateItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية المشتراه و الكمية الموجودة حاليا في المخزن أكبر من الحد الأقصى للكمية.
        /// </summary>
        public static string txtValidateItemMaxLimit {
            get {
                return ResourceManager.GetString("txtValidateItemMaxLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نسبة الخصم لايمكن ان تتجاوز المائة.
        /// </summary>
        public static string txtValidateMaxDiscount {
            get {
                return ResourceManager.GetString("txtValidateMaxDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل صنف علي الاقل في الفاتوره.
        /// </summary>
        public static string txtValidateNoRows {
            get {
                return ResourceManager.GetString("txtValidateNoRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to من فضلك ادخل تاريخ تسليم المخزن.
        /// </summary>
        public static string txtValidatePostDate {
            get {
                return ResourceManager.GetString("txtValidatePostDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سعر الشراء يجب أن يكون أكبر من الصفر.
        /// </summary>
        public static string txtValidatePPrice {
            get {
                return ResourceManager.GetString("txtValidatePPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية يجب ان تكون اكبر من الصفر.
        /// </summary>
        public static string txtValidateQty {
            get {
                return ResourceManager.GetString("txtValidateQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار وحدة القياس.
        /// </summary>
        public static string txtValidateUom {
            get {
                return ResourceManager.GetString("txtValidateUom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المورد تخطى حد الائتمان كدائن.
        /// </summary>
        public static string txtValidateVendorMaxCredit {
            get {
                return ResourceManager.GetString("txtValidateVendorMaxCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  المورد: .
        /// </summary>
        public static string txtVendor {
            get {
                return ResourceManager.GetString("txtVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الفرع.
        /// </summary>
        public static string ValBranch {
            get {
                return ResourceManager.GetString("ValBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود المورد لايمكن أن يكون اكبر من المحدد في نموذج طباعة الباركود.
        /// </summary>
        public static string ValVendorCodeLength {
            get {
                return ResourceManager.GetString("ValVendorCodeLength", resourceCulture);
            }
        }
    }
}
