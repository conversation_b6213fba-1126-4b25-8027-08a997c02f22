﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class StCurrency
    {
        public int CrncId { get; set; }
        public string CrncName { get; set; }
        public string CurrencyPound1 { get; set; }
        public string CurrencyPound2 { get; set; }
        public string C<PERSON>rencyPound3 { get; set; }
        public string <PERSON><PERSON>rencyPiaster1 { get; set; }
        public string C<PERSON>rencyPiaster2 { get; set; }
        public string CurrencyPiaster3 { get; set; }
        public int? CurrencyDigitsCount { get; set; }
        public decimal LastRate { get; set; }
        public string Ecode { get; set; }
        public bool? IsDefualt { get; set; }
    }
}
