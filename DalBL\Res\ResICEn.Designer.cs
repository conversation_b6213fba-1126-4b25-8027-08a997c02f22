﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResICEn {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResICEn() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResICEn", typeof(ResICEn).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category Code.
        /// </summary>
        public static string CategoryCode {
            get {
                return ResourceManager.GetString("CategoryCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category F Name.
        /// </summary>
        public static string categoryFname {
            get {
                return ResourceManager.GetString("categoryFname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category Name.
        /// </summary>
        public static string categoryName {
            get {
                return ResourceManager.GetString("categoryName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Code.
        /// </summary>
        public static string CompanyCode {
            get {
                return ResourceManager.GetString("CompanyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group F Name.
        /// </summary>
        public static string companyFname {
            get {
                return ResourceManager.GetString("companyFname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Name.
        /// </summary>
        public static string companyName {
            get {
                return ResourceManager.GetString("companyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t delete this branch, you&apos;ve to delete it&apos;s stores first.
        /// </summary>
        public static string delBranchDenied {
            get {
                return ResourceManager.GetString("delBranchDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this item, it&apos;s in use by marketting plans.
        /// </summary>
        public static string DelItemOnMr {
            get {
                return ResourceManager.GetString("DelItemOnMr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fixed Ratio%.
        /// </summary>
        public static string FixedRatio {
            get {
                return ResourceManager.GetString("FixedRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Bills.
        /// </summary>
        public static string frmInTrnsList {
            get {
                return ResourceManager.GetString("frmInTrnsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select subcategory.
        /// </summary>
        public static string IcSubCat {
            get {
                return ResourceManager.GetString("IcSubCat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Codes are duplicated.
        /// </summary>
        public static string ItemCodesDuplicate {
            get {
                return ResourceManager.GetString("ItemCodesDuplicate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Are you sure you want to delete .
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t add  more than two levels for branches and stores..
        /// </summary>
        public static string MsgBranch {
            get {
                return ResourceManager.GetString("MsgBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t make a sub-store to that branch, because it used also as a store.
        /// </summary>
        public static string MsgBranchItems {
            get {
                return ResourceManager.GetString("MsgBranchItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter category code.
        /// </summary>
        public static string MsgCatCode {
            get {
                return ResourceManager.GetString("MsgCatCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter category name.
        /// </summary>
        public static string MsgCatName {
            get {
                return ResourceManager.GetString("MsgCatName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This code already exists.
        /// </summary>
        public static string MsgCodeExist {
            get {
                return ResourceManager.GetString("MsgCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter Group code.
        /// </summary>
        public static string MsgCompCode {
            get {
                return ResourceManager.GetString("MsgCompCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter Group name.
        /// </summary>
        public static string MsgCompName {
            get {
                return ResourceManager.GetString("MsgCompName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please complete data correctly.
        /// </summary>
        public static string MsgData {
            get {
                return ResourceManager.GetString("MsgData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You made some changes, do you want to save.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted Successfully.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this international barcode.
        /// </summary>
        public static string MsgDelBarcode {
            get {
                return ResourceManager.GetString("MsgDelBarcode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete the last international barcode.
        /// </summary>
        public static string MsgDelBarcode2 {
            get {
                return ResourceManager.GetString("MsgDelBarcode2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this category.
        /// </summary>
        public static string MsgDelCat {
            get {
                return ResourceManager.GetString("MsgDelCat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this category, there is some items related to it.
        /// </summary>
        public static string MsgDelCatDenied {
            get {
                return ResourceManager.GetString("MsgDelCatDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this Group.
        /// </summary>
        public static string MsgDelComp {
            get {
                return ResourceManager.GetString("MsgDelComp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this Group , there is some items related to it.
        /// </summary>
        public static string MsgDelCompDenied {
            get {
                return ResourceManager.GetString("MsgDelCompDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete Bill and all its details .
        /// </summary>
        public static string MsgDeleteInv {
            get {
                return ResourceManager.GetString("MsgDeleteInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete expenses?.
        /// </summary>
        public static string MsgDelExpenseAsk {
            get {
                return ResourceManager.GetString("MsgDelExpenseAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete invoice book.
        /// </summary>
        public static string MsgDelInvoiceBook {
            get {
                return ResourceManager.GetString("MsgDelInvoiceBook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this item.
        /// </summary>
        public static string MsgDelItem {
            get {
                return ResourceManager.GetString("MsgDelItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete item?.
        /// </summary>
        public static string MsgDelItemAsk {
            get {
                return ResourceManager.GetString("MsgDelItemAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this item, it exists in Bill Of Material of other item.
        /// </summary>
        public static string MsgDelItemDenied1 {
            get {
                return ResourceManager.GetString("MsgDelItemDenied1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this item, there exist some quantity of it in store.
        /// </summary>
        public static string MsgDelItemDenied2 {
            get {
                return ResourceManager.GetString("MsgDelItemDenied2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this item, it exists in a price level list.
        /// </summary>
        public static string MsgDelItemDenied3 {
            get {
                return ResourceManager.GetString("MsgDelItemDenied3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this item, it exists in some invoices.
        /// </summary>
        public static string MsgDelItemDenied4 {
            get {
                return ResourceManager.GetString("MsgDelItemDenied4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this matrix, it&apos;s already in use.
        /// </summary>
        public static string MsgDelMtrx {
            get {
                return ResourceManager.GetString("MsgDelMtrx", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have to delete child matrix items first.
        /// </summary>
        public static string MsgDelMtxItems {
            get {
                return ResourceManager.GetString("MsgDelMtxItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this Price List.
        /// </summary>
        public static string MsgDelPriceList {
            get {
                return ResourceManager.GetString("MsgDelPriceList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this Price List, there is some customers use it.
        /// </summary>
        public static string MsgDelPriceListDenied {
            get {
                return ResourceManager.GetString("MsgDelPriceListDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this Price List, there is some vendors use it.
        /// </summary>
        public static string MsgDelPriceListDenied1 {
            get {
                return ResourceManager.GetString("MsgDelPriceListDenied1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to delete row ?.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this store.
        /// </summary>
        public static string MsgDelStore {
            get {
                return ResourceManager.GetString("MsgDelStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Dimensions can&apos;t be less than or equal to zero.
        /// </summary>
        public static string MsgDimension {
            get {
                return ResourceManager.GetString("MsgDimension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Foreign name already exists .
        /// </summary>
        public static string MsgFNameExist {
            get {
                return ResourceManager.GetString("MsgFNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to some data are incorrect.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter international code correctly.
        /// </summary>
        public static string MsgInterCode {
            get {
                return ResourceManager.GetString("MsgInterCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This International code is used before.
        /// </summary>
        public static string MsgInterCodeExist {
            get {
                return ResourceManager.GetString("MsgInterCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item doesn&apos;t exist.
        /// </summary>
        public static string MsgItemdoesntExist {
            get {
                return ResourceManager.GetString("MsgItemdoesntExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can not print more than 500 items .
        /// </summary>
        public static string MsgItemsPrint {
            get {
                return ResourceManager.GetString("MsgItemsPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete the main branch.
        /// </summary>
        public static string MsgMainStoreDel {
            get {
                return ResourceManager.GetString("MsgMainStoreDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Merchandising accounts in settings screen.
        /// </summary>
        public static string MsgMerchendaisingAcc {
            get {
                return ResourceManager.GetString("MsgMerchendaisingAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter matrix code.
        /// </summary>
        public static string MsgMtCode {
            get {
                return ResourceManager.GetString("MsgMtCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter matrix name.
        /// </summary>
        public static string MsgMtName {
            get {
                return ResourceManager.GetString("MsgMtName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter matrix attributes.
        /// </summary>
        public static string MsgMtRows {
            get {
                return ResourceManager.GetString("MsgMtRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review dimensions data for matrix items.
        /// </summary>
        public static string MsgMtrxData {
            get {
                return ResourceManager.GetString("MsgMtrxData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete item, some matrix items are already used.
        /// </summary>
        public static string MsgMtrxDelDenied {
            get {
                return ResourceManager.GetString("MsgMtrxDelDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please check data entered correctly.
        /// </summary>
        public static string MsgMtrxValid {
            get {
                return ResourceManager.GetString("MsgMtrxValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This name already exists.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no enough Qty of some items.
        /// </summary>
        public static string MsgNoEnoughQty {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no enough Qty of some items, Continue ?.
        /// </summary>
        public static string MsgNoEnoughQty_continue {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty_continue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This number already exists.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to disbursed before.
        /// </summary>
        public static string MsgOutBefore {
            get {
                return ResourceManager.GetString("MsgOutBefore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter Price List name.
        /// </summary>
        public static string MsgPLNAme {
            get {
                return ResourceManager.GetString("MsgPLNAme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter Ratio correctly.
        /// </summary>
        public static string MsgPLRatio {
            get {
                return ResourceManager.GetString("MsgPLRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to edit record.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to add new record.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review item sales prices per quantity.
        /// </summary>
        public static string MsgSalePerQty {
            get {
                return ResourceManager.GetString("MsgSalePerQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved Successfully.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save item first.
        /// </summary>
        public static string MsgSaveItemFirst {
            get {
                return ResourceManager.GetString("MsgSaveItemFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sory , you cant edit or delete a commited stocktaking.
        /// </summary>
        public static string Msgstocktaking_cant_upadte {
            get {
                return ResourceManager.GetString("Msgstocktaking_cant_upadte", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commit stocktaking cant be edited or backward after commit , Are you Sure ?.
        /// </summary>
        public static string Msgstocktaking_commit {
            get {
                return ResourceManager.GetString("Msgstocktaking_commit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have a deficit in the inventory by Value :.
        /// </summary>
        public static string Msgstocktaking_lose {
            get {
                return ResourceManager.GetString("Msgstocktaking_lose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save stocktaking doesn&apos;t mean commit it in the store , to commit the stocktaking please press Commit.
        /// </summary>
        public static string Msgstocktaking_save {
            get {
                return ResourceManager.GetString("Msgstocktaking_save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have an increase in inventory by Value :.
        /// </summary>
        public static string Msgstocktaking_win {
            get {
                return ResourceManager.GetString("Msgstocktaking_win", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stocktalking Commited Successfuly .
        /// </summary>
        public static string MsgstocktakingCommited {
            get {
                return ResourceManager.GetString("MsgstocktakingCommited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter code.
        /// </summary>
        public static string MsgStoreCode {
            get {
                return ResourceManager.GetString("MsgStoreCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to sorry, you can&apos;t delete this store, it contains items inside.
        /// </summary>
        public static string MsgStoreItems {
            get {
                return ResourceManager.GetString("MsgStoreItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have to delete this branch transactions first.
        /// </summary>
        public static string MsgStoreJournals {
            get {
                return ResourceManager.GetString("MsgStoreJournals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter name.
        /// </summary>
        public static string MsgStoreName {
            get {
                return ResourceManager.GetString("MsgStoreName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t make a sub-store to that branch, try to contact system administrator.
        /// </summary>
        public static string MsgSubStore {
            get {
                return ResourceManager.GetString("MsgSubStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This store is assigned as default store for some users.
        /// </summary>
        public static string msgUserDefaultStore {
            get {
                return ResourceManager.GetString("msgUserDefaultStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Store and Date.
        /// </summary>
        public static string MsgValidateDateandStore {
            get {
                return ResourceManager.GetString("MsgValidateDateandStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outgoing qty is larger than current store qty.
        /// </summary>
        public static string Msgvalidateoutqty {
            get {
                return ResourceManager.GetString("Msgvalidateoutqty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outgoing qty can&apos;t be larger than the current store qty.
        /// </summary>
        public static string Msgvalidateoutqty_forbid {
            get {
                return ResourceManager.GetString("Msgvalidateoutqty_forbid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Source Store and Destination Store.
        /// </summary>
        public static string MsgValidateStore {
            get {
                return ResourceManager.GetString("MsgValidateStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source Store cant be the same as Destination Store.
        /// </summary>
        public static string MsgValidateStore1 {
            get {
                return ResourceManager.GetString("MsgValidateStore1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code can&apos;t be zero.
        /// </summary>
        public static string MsgZeroCode {
            get {
                return ResourceManager.GetString("MsgZeroCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Per Item.
        /// </summary>
        public static string PerItem {
            get {
                return ResourceManager.GetString("PerItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Goods.
        /// </summary>
        public static string ReceiveGood {
            get {
                return ResourceManager.GetString("ReceiveGood", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Goods List.
        /// </summary>
        public static string ReceiveGoodList {
            get {
                return ResourceManager.GetString("ReceiveGoodList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Store With zero Qty.
        /// </summary>
        public static string rpt_IC_ItemsZeroQty {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsZeroQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select vendor.
        /// </summary>
        public static string SelectVendor {
            get {
                return ResourceManager.GetString("SelectVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select all Branch/Stock Accounts.
        /// </summary>
        public static string StockAccs {
            get {
                return ResourceManager.GetString("StockAccs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t change Branch or Store Level.
        /// </summary>
        public static string storeLevel {
            get {
                return ResourceManager.GetString("storeLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t add subcategory, there&apos;s items already registered.
        /// </summary>
        public static string subCat {
            get {
                return ResourceManager.GetString("subCat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code 1.
        /// </summary>
        public static string txt_Code1 {
            get {
                return ResourceManager.GetString("txt_Code1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code 2.
        /// </summary>
        public static string txt_Code2 {
            get {
                return ResourceManager.GetString("txt_Code2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Q.
        /// </summary>
        public static string txt_CurrentQty {
            get {
                return ResourceManager.GetString("txt_CurrentQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number .
        /// </summary>
        public static string txt_Number {
            get {
                return ResourceManager.GetString("txt_Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty.
        /// </summary>
        public static string txt_Qty {
            get {
                return ResourceManager.GetString("txt_Qty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assembly.
        /// </summary>
        public static string txtAssembly {
            get {
                return ResourceManager.GetString("txtAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category: .
        /// </summary>
        public static string txtCategory {
            get {
                return ResourceManager.GetString("txtCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comapny: .
        /// </summary>
        public static string txtComapny {
            get {
                return ResourceManager.GetString("txtComapny", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit.
        /// </summary>
        public static string txtCredit {
            get {
                return ResourceManager.GetString("txtCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        public static string txtCustomer {
            get {
                return ResourceManager.GetString("txtCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dealer Name.
        /// </summary>
        public static string txtDealerNm {
            get {
                return ResourceManager.GetString("txtDealerNm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        public static string txtDebit {
            get {
                return ResourceManager.GetString("txtDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Drawer .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New quantity should be larger than or equal Zero.
        /// </summary>
        public static string txtEditItemQty {
            get {
                return ResourceManager.GetString("txtEditItemQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter new quantity for at least one item.
        /// </summary>
        public static string txtEditItemQty2 {
            get {
                return ResourceManager.GetString("txtEditItemQty2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exceeds Reorder level , Continue ?.
        /// </summary>
        public static string txtExceedsReorder {
            get {
                return ResourceManager.GetString("txtExceedsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From Date .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Damage Bill.
        /// </summary>
        public static string txtIC_damage {
            get {
                return ResourceManager.GetString("txtIC_damage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Bill.
        /// </summary>
        public static string txtIC_intrns {
            get {
                return ResourceManager.GetString("txtIC_intrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Bills.
        /// </summary>
        public static string txtIC_intrnsList {
            get {
                return ResourceManager.GetString("txtIC_intrnsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Balance.
        /// </summary>
        public static string txtIC_openbalance {
            get {
                return ResourceManager.GetString("txtIC_openbalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Balances.
        /// </summary>
        public static string txtIC_openbalanceList {
            get {
                return ResourceManager.GetString("txtIC_openbalanceList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outgoing Bill.
        /// </summary>
        public static string txtIC_outtrns {
            get {
                return ResourceManager.GetString("txtIC_outtrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replacement.
        /// </summary>
        public static string txtIC_replacement {
            get {
                return ResourceManager.GetString("txtIC_replacement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stocktaking .
        /// </summary>
        public static string txtIC_stocktaking {
            get {
                return ResourceManager.GetString("txtIC_stocktaking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer Bill.
        /// </summary>
        public static string txtIC_storemove {
            get {
                return ResourceManager.GetString("txtIC_storemove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        public static string txtInvDate {
            get {
                return ResourceManager.GetString("txtInvDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item: .
        /// </summary>
        public static string txtItem {
            get {
                return ResourceManager.GetString("txtItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Already exist.
        /// </summary>
        public static string txtItemExist {
            get {
                return ResourceManager.GetString("txtItemExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Movement.
        /// </summary>
        public static string txtItemMovement {
            get {
                return ResourceManager.GetString("txtItemMovement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Balance.
        /// </summary>
        public static string txtItemsBalance {
            get {
                return ResourceManager.GetString("txtItemsBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Best-Selling Items.
        /// </summary>
        public static string txtItemsBestSell {
            get {
                return ResourceManager.GetString("txtItemsBestSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Least-Selling Items.
        /// </summary>
        public static string txtItemsLeastSell {
            get {
                return ResourceManager.GetString("txtItemsLeastSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Not Sold.
        /// </summary>
        public static string txtItemsNotSold {
            get {
                return ResourceManager.GetString("txtItemsNotSold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items On Reorder.
        /// </summary>
        public static string txtItemsReorder {
            get {
                return ResourceManager.GetString("txtItemsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Purchases.
        /// </summary>
        public static string txtItemTotalPurchases {
            get {
                return ResourceManager.GetString("txtItemTotalPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Purchases Returns.
        /// </summary>
        public static string txtItemTotalPurchasesReturns {
            get {
                return ResourceManager.GetString("txtItemTotalPurchasesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Sales.
        /// </summary>
        public static string txtItemTotalSales {
            get {
                return ResourceManager.GetString("txtItemTotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Sales Returns.
        /// </summary>
        public static string txtItemTotalSalesReturn {
            get {
                return ResourceManager.GetString("txtItemTotalSalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open balance Journal.
        /// </summary>
        public static string txtJornalOpenBalance {
            get {
                return ResourceManager.GetString("txtJornalOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Matrix.
        /// </summary>
        public static string txtMatrix {
            get {
                return ResourceManager.GetString("txtMatrix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string txtNotes {
            get {
                return ResourceManager.GetString("txtNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Open Balance .
        /// </summary>
        public static string txtOpenBalance {
            get {
                return ResourceManager.GetString("txtOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory.
        /// </summary>
        public static string txtRaw {
            get {
                return ResourceManager.GetString("txtRaw", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial.
        /// </summary>
        public static string txtSerial {
            get {
                return ResourceManager.GetString("txtSerial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        public static string txtService {
            get {
                return ResourceManager.GetString("txtService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Store: .
        /// </summary>
        public static string txtStore {
            get {
                return ResourceManager.GetString("txtStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subtotal.
        /// </summary>
        public static string txtSubTotal {
            get {
                return ResourceManager.GetString("txtSubTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To Date .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select discount.
        /// </summary>
        public static string txtValidateDiscount {
            get {
                return ResourceManager.GetString("txtValidateDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please record bill number.
        /// </summary>
        public static string txtValidateInvNumber {
            get {
                return ResourceManager.GetString("txtValidateInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Item.
        /// </summary>
        public static string txtValidateItem {
            get {
                return ResourceManager.GetString("txtValidateItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchased qty and store qty more than max limit of the item.
        /// </summary>
        public static string txtValidateItemMaxLimit {
            get {
                return ResourceManager.GetString("txtValidateItemMaxLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount ratio must be less than 100.
        /// </summary>
        public static string txtValidateMaxDiscount {
            get {
                return ResourceManager.GetString("txtValidateMaxDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current qty after decrease outgoing qty is less than item minimum qty .
        /// </summary>
        public static string txtValidateMinQty {
            get {
                return ResourceManager.GetString("txtValidateMinQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter at least one item .
        /// </summary>
        public static string txtValidateNoRows {
            get {
                return ResourceManager.GetString("txtValidateNoRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase price must be larger than 0.
        /// </summary>
        public static string txtValidatePPrice {
            get {
                return ResourceManager.GetString("txtValidatePPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty must be larger than 0.
        /// </summary>
        public static string txtValidateQty {
            get {
                return ResourceManager.GetString("txtValidateQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current qty after decrease outgoing qty is less than item reorder qty.
        /// </summary>
        public static string txtValidateReorderQty {
            get {
                return ResourceManager.GetString("txtValidateReorderQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell price must be larger than 0.
        /// </summary>
        public static string txtValidateSPrice {
            get {
                return ResourceManager.GetString("txtValidateSPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Price must be larger than purchase price.
        /// </summary>
        public static string txtValidateSpriceLargerPprice {
            get {
                return ResourceManager.GetString("txtValidateSpriceLargerPprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sold qty cant be larger than store qty.
        /// </summary>
        public static string txtValidateStoreQty {
            get {
                return ResourceManager.GetString("txtValidateStoreQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select unit of measure.
        /// </summary>
        public static string txtValidateUom {
            get {
                return ResourceManager.GetString("txtValidateUom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor.
        /// </summary>
        public static string txtVendor {
            get {
                return ResourceManager.GetString("txtVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter expenses.
        /// </summary>
        public static string ValExpenses {
            get {
                return ResourceManager.GetString("ValExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expenses must be larger than zero.
        /// </summary>
        public static string ValExpensesZero {
            get {
                return ResourceManager.GetString("ValExpensesZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item code length cann&apos;t exceed item length in barcode template.
        /// </summary>
        public static string ValItemCodeLength {
            get {
                return ResourceManager.GetString("ValItemCodeLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this item, it&apos;s already in use by bills of materials.
        /// </summary>
        public static string ValTxtItemBOMDel {
            get {
                return ResourceManager.GetString("ValTxtItemBOMDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter item code1.
        /// </summary>
        public static string ValTxtItemCode1 {
            get {
                return ResourceManager.GetString("ValTxtItemCode1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code1 is used by another item.
        /// </summary>
        public static string ValTxtItemCode1Exist {
            get {
                return ResourceManager.GetString("ValTxtItemCode1Exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Codes are used by another item.
        /// </summary>
        public static string ValTxtItemCode2Exist {
            get {
                return ResourceManager.GetString("ValTxtItemCode2Exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item already exits.
        /// </summary>
        public static string ValTxtItemExist {
            get {
                return ResourceManager.GetString("ValTxtItemExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter item name.
        /// </summary>
        public static string ValTxtItemName {
            get {
                return ResourceManager.GetString("ValTxtItemName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter item purchase price.
        /// </summary>
        public static string ValTxtItemPurchasePrice {
            get {
                return ResourceManager.GetString("ValTxtItemPurchasePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase price can&apos;t be larger than sales price of retail unit.
        /// </summary>
        public static string ValTxtItemSalePrice {
            get {
                return ResourceManager.GetString("ValTxtItemSalePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter retail unit sales price.
        /// </summary>
        public static string ValTxtItemSellPrice {
            get {
                return ResourceManager.GetString("ValTxtItemSellPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can&apos;t enter wholesale unit2 without entering wholesale unit1 before.
        /// </summary>
        public static string ValTxtItemUOM1 {
            get {
                return ResourceManager.GetString("ValTxtItemUOM1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select item.
        /// </summary>
        public static string ValTxtSelectItem {
            get {
                return ResourceManager.GetString("ValTxtSelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select UOM.
        /// </summary>
        public static string ValTxtSelectUOM {
            get {
                return ResourceManager.GetString("ValTxtSelectUOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to varies per item.
        /// </summary>
        public static string variesPerItem {
            get {
                return ResourceManager.GetString("variesPerItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity can&apos;t be zero.
        /// </summary>
        public static string VatTxtZeroQty {
            get {
                return ResourceManager.GetString("VatTxtZeroQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor Exists Before.
        /// </summary>
        public static string VendorExist {
            get {
                return ResourceManager.GetString("VendorExist", resourceCulture);
            }
        }
    }
}
