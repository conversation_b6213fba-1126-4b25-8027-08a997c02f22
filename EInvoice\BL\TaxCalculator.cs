﻿using Models_1.ViewModels;
using Models_1.ViewModels.TaxCalculatorVM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EInvoice.BL
{
    public class TaxCalculator
    {
        int round = 3;
        public static TaxCalculatorVM CalculateTaxes(List<Models_1.ViewModels.InvoiceVM.Tax> taxes, decimal netTotal, decimal itemsDiscount ,decimal valueDifference,int roundValue)
        {
            var model = new TaxCalculatorVM();
            var taxableFees = EInvoice.MyHelper.Utilities.TaxableFees();
            var nonTaxableFees = EInvoice.MyHelper.Utilities.NonTaxableFees();


            //Calculate fixed Amount Taxs T3-T6
            List<TaxableItem> fixedAmountTaxsItems = CaluculateFixedAmountTaxFees(taxes,roundValue);
            var totalfixedAmountTaxsT3 = fixedAmountTaxsItems.Where(x=>x.taxType=="T3").Sum(z => z.amount);
            var totalfixedAmountTaxsT6 = fixedAmountTaxsItems.Where(x => x.taxType == "T6").Sum(z => z.amount);
            model.totalfixedAmountTaxsT3 = totalfixedAmountTaxsT3;
            //model.totalfixedAmountTaxsT6 = totalfixedAmountTaxsT6;
            List<TaxableItem> taxableItems = fixedAmountTaxsItems;
            

            //Calculate taxable and NonTaxable Items
            List<TaxableItem> taxableNonTaxableItems = CaluculateTaxableandNonTaxableFees(taxes, netTotal, roundValue);
            var totalTaxableFees = taxableNonTaxableItems.Where(x => taxableFees.Contains(x.taxType)).Sum(x => x.amount);
            var totalNonTaxableFees = taxableNonTaxableItems.Where(x => nonTaxableFees.Contains(x.taxType)).Sum(x => x.amount);
            model.totalTaxableFees = Math.Round(totalTaxableFees + totalfixedAmountTaxsT6, roundValue);
            model.totalNonTaxableFees = totalNonTaxableFees;
            taxableItems.AddRange(taxableNonTaxableItems);

            //Calculate withHolding Tax T4
            List<TaxableItem> withHoldingTax = CaluculateWithHoldingTax(taxes, netTotal, itemsDiscount , roundValue);
            var totalWithHoldingTax = withHoldingTax.Sum(x => x.amount);
            taxableItems.AddRange(withHoldingTax);
            model.totalWithHoldingTax = totalWithHoldingTax;

            // Calculate Table Tax T2
            List<TaxableItem> tableTaxPercentage = CaluculateTableTax(taxes, netTotal, valueDifference, model.totalTaxableFees, totalfixedAmountTaxsT3 , roundValue);
            var totaltableTax = tableTaxPercentage.Sum(x => x.amount);
            taxableItems.AddRange(tableTaxPercentage);
            model.totaltableTax = totaltableTax;

            // Calculate Value Added Service T1
            List<TaxableItem> valueAddedService = CaluculateValueAddedService(taxes, netTotal, valueDifference, model.totalTaxableFees, totaltableTax,totalfixedAmountTaxsT3 , roundValue);
            taxableItems.AddRange(valueAddedService);
            model.totalVATTax = valueAddedService.Sum(x => x.amount);


            model.taxableItem = taxableItems;

            return model;
        }

        private static List<TaxableItem> CaluculateTaxableandNonTaxableFees(List<Models_1.ViewModels.InvoiceVM.Tax> taxes, decimal netTotal ,int roundValue)
        {
            List<TaxableItem> model = new List<TaxableItem>();
            var taxableandNonTaxableFees = EInvoice.MyHelper.Utilities.TaxableandNonTaxableFees();
            var taxesToCalculate = taxes.Where(x => taxableandNonTaxableFees.Contains(x.taxName)).ToList();

            taxesToCalculate.ForEach(x => model.Add(new TaxableItem()
            {
                taxType = x.taxName,
                rate = Math.Round(x.rate, 2),
                subType = x.subTaxName,
                amount = Math.Round((x.rate * netTotal) / 100, roundValue)
            }));

            return model;
        }

        public static List<TaxableItem> CaluculateFixedAmountTaxFees(List<Models_1.ViewModels.InvoiceVM.Tax> taxes, int roundValue)
        {
            //T3 - T6 
            List<TaxableItem> model = new List<TaxableItem>();
            var taxesToCalculate = taxes.Where(x => x.taxName == "T3" || x.taxName == "T6").ToList();

            taxesToCalculate.ForEach(x => model.Add(new TaxableItem()
            {
                taxType = x.taxName,
                rate = 0,
                subType = x.subTaxName,
                amount = Math.Round(x.rate, roundValue)
            }));

            return model;
        }

        public static List<TaxableItem> CaluculateWithHoldingTax(List<Models_1.ViewModels.InvoiceVM.Tax> taxes, decimal netTotal, decimal itemsDiscount ,int roundValue)
        {
            //Withholding tax (WHT) (T4)
            var eqConstant = (netTotal - itemsDiscount);
            List<TaxableItem> model = new List<TaxableItem>();
            var taxesToCalculate = taxes.Where(x => x.taxName == "T4").ToList();

            taxesToCalculate.ForEach(x => model.Add(new TaxableItem()
            {
                taxType = x.taxName,
                rate = Math.Round(x.rate,2),
                subType = x.subTaxName,
                amount = Math.Round(x.rate * eqConstant / 100, roundValue)
            }));

            return model;
        }

        public static List<TaxableItem> CaluculateValueAddedService(List<Models_1.ViewModels.InvoiceVM.Tax> taxes, decimal netTotal, decimal valueDifference, decimal totalTaxableFees ,decimal totaltableTax ,decimal totalfixedAmountTaxsT3 , int roundValue)
        {
            // Equation Not Clear
            //Value Added Service (VAT) (T1)
            var eqConstant = netTotal + valueDifference + totalTaxableFees + totaltableTax + totalfixedAmountTaxsT3;
            List<TaxableItem> model = new List<TaxableItem>();
            var taxesToCalculate = taxes.Where(x => x.taxName == "T1").ToList();

            taxesToCalculate.ForEach(x => model.Add(new TaxableItem()
            {
                taxType = x.taxName,
                rate = Math.Round(x.rate,2),
                subType = x.subTaxName,
                amount = Math.Round(x.rate * eqConstant / 100, roundValue)
            }));

            return model;
        }

        public static List<TaxableItem> CaluculateTableTax(List<Models_1.ViewModels.InvoiceVM.Tax> taxes, decimal netTotal, decimal valueDifference, decimal totalTaxableFees , decimal totalfixedAmountTaxsT3 ,int roundValue)
        {
            //Table tax(percentage) (T2)
            var eqConstant = (netTotal + valueDifference + totalTaxableFees + totalfixedAmountTaxsT3);
            List<TaxableItem> model = new List<TaxableItem>();
            var taxesToCalculate = taxes.Where(x => x.taxName == "T2").ToList();

            taxesToCalculate.ForEach(x => model.Add(new TaxableItem()
            {
                taxType = x.taxName,
                rate = Math.Round(x.rate, 2),
                subType = x.subTaxName,
                amount = /*208.22M//*/Math.Round(x.rate * eqConstant / 100, roundValue)
            }));

            return model;
        }

        public static List<TaxTotal> CalculateInvoiceTotalTaxes(List<InvoiceLine> invoiceLines, int roundValue)
        {
            var invocieLineTaxes = invoiceLines.Select(x => x.taxableItems).ToList();
            var allTaxes = new List<TaxTotal>();
            foreach (var tax in invocieLineTaxes)
            {
                tax.ForEach(x => allTaxes.Add(new TaxTotal
                {
                    amount = x.amount,
                    taxType = x.taxType
                }));
            };

            List<TaxTotal> taxTotals = (from t in allTaxes
                                        group t by t.taxType into grp
                                        select new TaxTotal
                                        {
                                            taxType = grp.Key,
                                            amount = Math.Round(grp.Sum(z => z.amount), roundValue)
                                        }).ToList();
            return taxTotals;
        }
    }
}
