﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class StStore
    {
        public int StStoreId { get; set; }
        public bool SellRawMaterial { get; set; }
        public bool BuyAssembly { get; set; }
        public bool SerialForAssembly { get; set; }
        public bool ExpireDate { get; set; }
        public int? HrLoansAccount { get; set; }
        public int? HrSalaryExpensesAccount { get; set; }
        public bool? AutoInvSerialForStore { get; set; }
        public bool AutoPostSales { get; set; }
        public int? HrAccruedSalaryAccount { get; set; }
        public bool UseLengthDimension { get; set; }
        public bool UseWidthDimension { get; set; }
        public bool UseHeightDimension { get; set; }
        public string CurrencyPound1 { get; set; }
        public string CurrencyPound2 { get; set; }
        public string CurrencyPound3 { get; set; }
        public string CurrencyPiaster1 { get; set; }
        public string CurrencyPiaster2 { get; set; }
        public string CurrencyPiaster3 { get; set; }
        public int? CurrencyDigitsCount { get; set; }
        public string MyDocReportPathFolder { get; set; }
        public string SecondReportPath { get; set; }
        public bool Batch { get; set; }
        public int? DrawersAcc { get; set; }
        public int? BanksAcc { get; set; }
        public int? CustomersAcc { get; set; }
        public int? NotesReceivableAcc { get; set; }
        public int? InventoryAcc { get; set; }
        public int? VendorsAcc { get; set; }
        public int? CapitalAcc { get; set; }
        public int? NotesPayableAcc { get; set; }
        public int? TaxAcc { get; set; }
        public int? ManufacturingExpAcc { get; set; }
        public int? MerchandisingAcc { get; set; }
        public int? PurchasesAcc { get; set; }
        public int? PurchasesReturnAcc { get; set; }
        public int? SalesAcc { get; set; }
        public int? SalesReturnAcc { get; set; }
        public int? OpenInventoryAcc { get; set; }
        public int? CloseInventoryAcc { get; set; }
        public int? PurchaseDiscountAcc { get; set; }
        public int? SalesDiscountAcc { get; set; }
        public int? FixedAssets { get; set; }
        public bool PurchaseAutoSerialBatch { get; set; }
        public bool PiecesCount { get; set; }
        public byte MultiplyDimensions { get; set; }
        public bool UseMediumUom { get; set; }
        public bool UseLargeUom { get; set; }
        public bool ManufactureProductsOnly { get; set; }
        public int? SalesDeductTaxAccount { get; set; }
        public int? PurchaseDeductTaxAccount { get; set; }
        public bool PriceIncludeSalesTax { get; set; }
        public DateTime? ClosePeriodDate { get; set; }
        public int? RecieveNotesUnderCollectAccId { get; set; }
        public int? PurchaseAddTaxAccount { get; set; }
        public int? SalesAddTaxAccount { get; set; }
        public bool PriceIncludePurchaseTax { get; set; }
        public bool CalcPurchaseTaxPerItem { get; set; }
        public bool IsFutureDueDate { get; set; }
        public bool? InvoicesCodeRedundancy { get; set; }
        public bool AttendanceIs2Shifts { get; set; }
        public bool UseQc { get; set; }
        public bool UseRegisteredNotes { get; set; }
        public int? DebitNoteAcc { get; set; }
        public int? CreditNoteAcc { get; set; }
        public string MainCurrencyName { get; set; }
        public int? CostOfSoldGoodsAcc { get; set; }
        public int? LetterOfCreditAcc { get; set; }
        public bool SalesOrderReserveGood { get; set; }
        public int? DepreciationAcc { get; set; }
        public int? RealStateSellRvnuAcc { get; set; }
        public bool DelayOnAttendTimeOnly { get; set; }
        public bool GenerateNewInvCodeOnSave { get; set; }
        public int? ExpensesAcc { get; set; }
        public int? RevenueAcc { get; set; }
        public bool GulfHravailable { get; set; }
        public bool IncomeTaxAvailable { get; set; }
        public bool EncodeItemsPerCategory { get; set; }
        public int InvoiceWorkflow { get; set; }
        public bool EncodePrInvPerVendor { get; set; }
        public bool PrintBarcodePerInventory { get; set; }
        public string BarcodePrefix { get; set; }
        public int? BarcodeItemCodeLength { get; set; }
        public int? BarcodeQtyLength { get; set; }
        public int? BarcodeBatchCodeLength { get; set; }
        public bool GroupPositems { get; set; }
        public bool UseBarcodeMatchTable { get; set; }
        public bool Serial { get; set; }
        public string BatchNameAr { get; set; }
        public string BatchNameEn { get; set; }
        public string SerialNameAr { get; set; }
        public string SerialNameEn { get; set; }
        public string DescNameAr { get; set; }
        public string DescNameEn { get; set; }
        public string DescEnNameAr { get; set; }
        public string DescEnNameEn { get; set; }
        public int PrInvoiceWorkflow { get; set; }
        public bool SellAsRestaurant { get; set; }
        public bool SalesEmpMandatory { get; set; }
        public bool IsStoreOnEachSellRecord { get; set; }
        public bool IsStoreOnEachPurchRecord { get; set; }
        public bool MustApproveSalesOrder { get; set; }
        public string PriorityNameEn { get; set; }
        public string SalesEmployeeNameAr { get; set; }
        public string DeliveryEmployeeNameAr { get; set; }
        public string DepartmentNameAr { get; set; }
        public string StatusNameAr { get; set; }
        public string PriorityNameAr { get; set; }
        public string SalesEmployeeNameEn { get; set; }
        public string DeliveryEmployeeNameEn { get; set; }
        public string DepartmentNameEn { get; set; }
        public string StatusNameEn { get; set; }
        public int? CustomTaxAcc { get; set; }
        public string AttachmentPath { get; set; }
        public int? MtrxColor { get; set; }
        public int? MtrxLiter { get; set; }
        public int? MtrxGeir { get; set; }
        public string ExpireDateNameEn { get; set; }
        public string ExpireDateNameAr { get; set; }
        public int? VisaAccount { get; set; }
        public int? AdvancePaymentAcc { get; set; }
        public int? RetentionAcc { get; set; }
        public int? CostDistributionMethod { get; set; }
        public string Serial2NameAr { get; set; }
        public string Serial2NameEn { get; set; }
        public int? BcIdentifier { get; set; }
        public int? BcItemCount { get; set; }
        public int? BcScaleCount { get; set; }
        public bool? HasChkSum { get; set; }
        public int? BcScalePrice { get; set; }
        public bool? IsPrice { get; set; }
        public int? LibraUomId { get; set; }
        public int? KgPrUomId { get; set; }
        public int? KgSlUomId { get; set; }
        public string KgPrFactor { get; set; }
        public string KgSlFactor { get; set; }
        public int? LaborRevenue { get; set; }
        public int? TransferRevenue { get; set; }
        public string PiecesCountNameAr { get; set; }
        public string PiecesCountNameEn { get; set; }
        public int? TotalMonthDaysHr { get; set; }
        public bool? SlInvoiceMustBApproved { get; set; }
        public DateTime? LastEvaluationDate { get; set; }
        public bool? ChAuthorize { get; set; }
        public bool? ExpireDisplay { get; set; }
        public int? CapitalProfitLoss { get; set; }
        public bool? ChkCsTypeValidation { get; set; }
        public bool? IsMaxSalesOrder { get; set; }
        public bool? IsMaxSalesInvoice { get; set; }
        public bool? ExcludeLeavesNGl { get; set; }
        public decimal? NetSalaryTax { get; set; }
        public bool? PackCount { get; set; }
        public bool? OutstandingRecieveNote { get; set; }
        public int? IntermediateInventoryAcc { get; set; }
        public decimal? MinInsureSalary { get; set; }
        public decimal? MaxInsureSalary { get; set; }
        public decimal? CompanyShare { get; set; }
        public decimal? EmpShare { get; set; }
        public decimal? ExemptionRatio { get; set; }
        public bool? TotalSalaryType { get; set; }
        public bool? EgyptionLaw { get; set; }
        public int? IntermediatePrInventoryAcc { get; set; }
        public int? InsuranceAcc { get; set; }
        public int? BusinessGainAcc { get; set; }
        public int? NetTaxAcc { get; set; }
        public int? HrSubtractAcc { get; set; }
        public int? CustodyAcc { get; set; }
        public int? ReturnCostAcc { get; set; }
        public bool? MustApprovePurchaseOrder { get; set; }
        public bool? MustApprovePurchaseQuote { get; set; }
        public string CompanyAr { get; set; }
        public string CompanyEn { get; set; }
        public string CategoryAr { get; set; }
        public string CategoryEn { get; set; }
        public bool? SalesOrderforClient { get; set; }
        public bool? UseLastCostPrice { get; set; }
        public decimal? TotalMallSize { get; set; }
        public int? AccVacationAcount { get; set; }
        public int? AccAbsenceAcount { get; set; }
        public int? AccPenaltyAcount { get; set; }
        public int? RoundValue { get; set; }
        public decimal? DocumentThreshold { get; set; }
        public bool? EAllowMoreThanTax { get; set; }
        public byte? DefaultRoundingPoints { get; set; }
    }
}
