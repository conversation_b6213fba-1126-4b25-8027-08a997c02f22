﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="txtFrom" xml:space="preserve">
    <value> From </value>
  </data>
  <data name="txtFromDate" xml:space="preserve">
    <value> From Date </value>
  </data>
  <data name="MsgNameExist" xml:space="preserve">
    <value>This name already exists</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>some data are incorrect</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>You made some changes, do you want to save</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>Sorry, you don't have privilege to edit record</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>Sorry, you don't have privilege to add new record</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>Saved Successfully</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="txtTo" xml:space="preserve">
    <value> To </value>
  </data>
  <data name="txtToDate" xml:space="preserve">
    <value> To Date </value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>Deleted Successfully</value>
  </data>
  <data name="txtOpenBalance" xml:space="preserve">
    <value> Open Balance </value>
  </data>
  <data name="txtDrawer" xml:space="preserve">
    <value> Drawer </value>
  </data>
  <data name="txtCredit" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="txtCustomer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="txtDebit" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="txtVendor" xml:space="preserve">
    <value>Vendor: </value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>delete row ?</value>
  </data>
  <data name="MsgNumExist" xml:space="preserve">
    <value>This number already exists</value>
  </data>
  <data name="MsgAskDel" xml:space="preserve">
    <value> Are you sure you want to delete </value>
  </data>
  <data name="txtDealerNm" xml:space="preserve">
    <value>Dealer Name</value>
  </data>
  <data name="txtItemsBalance" xml:space="preserve">
    <value>Items Balance</value>
  </data>
  <data name="txtItemsBestSell" xml:space="preserve">
    <value>Best-Selling Items</value>
  </data>
  <data name="txtItemsLeastSell" xml:space="preserve">
    <value>Least-Selling Items</value>
  </data>
  <data name="txtItemsReorder" xml:space="preserve">
    <value>Items On Reorder</value>
  </data>
  <data name="txtAssembly" xml:space="preserve">
    <value>Assembly</value>
  </data>
  <data name="txtCategory" xml:space="preserve">
    <value>Category: </value>
  </data>
  <data name="txtComapny" xml:space="preserve">
    <value>Comapny: </value>
  </data>
  <data name="txtItem" xml:space="preserve">
    <value>Item: </value>
  </data>
  <data name="txtItemMovement" xml:space="preserve">
    <value>Items Movement</value>
  </data>
  <data name="txtItemTotalPurchases" xml:space="preserve">
    <value>Item Total Purchases</value>
  </data>
  <data name="txtItemTotalPurchasesReturns" xml:space="preserve">
    <value>Item Total Purchases Returns</value>
  </data>
  <data name="txtItemTotalSales" xml:space="preserve">
    <value>Item Total Sales</value>
  </data>
  <data name="txtItemTotalSalesReturn" xml:space="preserve">
    <value>Item Total Sales Returns</value>
  </data>
  <data name="txtStore" xml:space="preserve">
    <value>Store: </value>
  </data>
  <data name="MsgNoAccount" xml:space="preserve">
    <value>This vendor has no account</value>
  </data>
  <data name="MsgChkCodeDuplication" xml:space="preserve">
    <value>Code 1 Already Exist</value>
  </data>
  <data name="MsgAskToSaveInv" xml:space="preserve">
    <value>Please save invoice first</value>
  </data>
  <data name="MsgDeleteInv" xml:space="preserve">
    <value>Are you sure you want to delete document</value>
  </data>
  <data name="txtContinue" xml:space="preserve">
    <value>Continue ?</value>
  </data>
  <data name="txtDiscRatio" xml:space="preserve">
    <value>D R :</value>
  </data>
  <data name="txtDiscValue" xml:space="preserve">
    <value>D V :</value>
  </data>
  <data name="txtExpenses" xml:space="preserve">
    <value>Expenses :</value>
  </data>
  <data name="txtInvDate" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="txtInvNumber" xml:space="preserve">
    <value>Invoice Number</value>
  </data>
  <data name="txtInvPayMethod" xml:space="preserve">
    <value>Payment Method</value>
  </data>
  <data name="txtLastPPrices" xml:space="preserve">
    <value>View Last Prices</value>
  </data>
  <data name="txtLastVendorPPrices" xml:space="preserve">
    <value>View Last Vendor Prices</value>
  </data>
  <data name="txtNet" xml:space="preserve">
    <value>Net :</value>
  </data>
  <data name="txtNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="txtPaid" xml:space="preserve">
    <value>Paid :</value>
  </data>
  <data name="txtPRInvoiceNumber" xml:space="preserve">
    <value>Purchase invoice number</value>
  </data>
  <data name="txtRemains" xml:space="preserve">
    <value>Remains</value>
  </data>
  <data name="txtSerial" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="txtTotal" xml:space="preserve">
    <value>Total :</value>
  </data>
  <data name="txtValidateDiscount" xml:space="preserve">
    <value>Please select discount</value>
  </data>
  <data name="txtValidateInvNumber" xml:space="preserve">
    <value>Please record invoice number</value>
  </data>
  <data name="txtValidateItem" xml:space="preserve">
    <value>Please select Item</value>
  </data>
  <data name="txtValidateItemMaxLimit" xml:space="preserve">
    <value>Purchased qty and store qty more than max limit of the item</value>
  </data>
  <data name="txtValidateMaxDiscount" xml:space="preserve">
    <value>Discount ratio must be less than 100</value>
  </data>
  <data name="txtValidateNoRows" xml:space="preserve">
    <value>Please enter at least one item to the invoice</value>
  </data>
  <data name="txtValidatePPrice" xml:space="preserve">
    <value>Purchase price must be larger than 0</value>
  </data>
  <data name="txtValidateQty" xml:space="preserve">
    <value>Qty must be larger than 0</value>
  </data>
  <data name="txtValidateUom" xml:space="preserve">
    <value>Please select unit of measure</value>
  </data>
  <data name="txtValidateVendorMaxCredit" xml:space="preserve">
    <value>Vendor exceeds max credit</value>
  </data>
  <data name="MsgNoEnoughQty_continue" xml:space="preserve">
    <value>There is no enough Qty of some items, Continue ?</value>
  </data>
  <data name="txtPRReturnNumber" xml:space="preserve">
    <value>Purchase Return Invoice Number</value>
  </data>
  <data name="MsgAskConfirmDeleteVen" xml:space="preserve">
    <value>Are you sure you want to delete this vendor ?</value>
  </data>
  <data name="MsgDeleteJornalsFirst" xml:space="preserve">
    <value>Please delete all vendor journals first </value>
  </data>
  <data name="MsgEnterVendorCode" xml:space="preserve">
    <value>Please enter vendor code</value>
  </data>
  <data name="MsgEnterVendorName" xml:space="preserve">
    <value>Please enter vendor name </value>
  </data>
  <data name="MsgValidateVendorCode" xml:space="preserve">
    <value>Vendor code must be larger than 0</value>
  </data>
  <data name="txtCantDeleteVen" xml:space="preserve">
    <value>Are you sure you want to delete General Vendor</value>
  </data>
  <data name="txtCantEditVen" xml:space="preserve">
    <value>Are you sure you want to edit General Vendor</value>
  </data>
  <data name="txt_Discount" xml:space="preserve">
    <value>Discount</value>
  </data>
  <data name="txt_Paid" xml:space="preserve">
    <value>Pay</value>
  </data>
  <data name="txt_Tax" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="txt_PurchaseExpenses" xml:space="preserve">
    <value>Purchases Expenses</value>
  </data>
  <data name="accDetail" xml:space="preserve">
    <value>Detailed Statement of Account</value>
  </data>
  <data name="txtTaxRatio" xml:space="preserve">
    <value>Tax R:</value>
  </data>
  <data name="txtTaxValue" xml:space="preserve">
    <value>Tax V:</value>
  </data>
  <data name="txtValidatePostDate" xml:space="preserve">
    <value>Please enter post date</value>
  </data>
  <data name="MsgAskChangePList" xml:space="preserve">
    <value>Do you want to update customers price levels with the same increasing ratio ?</value>
  </data>
  <data name="msgLinkAcc" xml:space="preserve">
    <value>Sorry, you can't change this account, you have to delete it's journals first</value>
  </data>
  <data name="msgDelLinked" xml:space="preserve">
    <value>Sorry, you can't delete this acount, there's other accounts linked to it</value>
  </data>
  <data name="msgLinkAccOpen" xml:space="preserve">
    <value>Sorry, you can't set open balance for this account</value>
  </data>
  <data name="MsgAskPrintBarCode" xml:space="preserve">
    <value>Do you want to print barcode stickers for Purchase Invoice</value>
  </data>
  <data name="MsgPostedBill" xml:space="preserve">
    <value>Sorry, you can't edit or delete posted Bill</value>
  </data>
  <data name="MsgAskCreateInTrns" xml:space="preserve">
    <value>Do you want to create Store Receving Bill Now ?</value>
  </data>
  <data name="MsgAskCreateOutTrns" xml:space="preserve">
    <value>Do you want to create Store Outgoing Bill Now ?</value>
  </data>
  <data name="defaultCustGroupAcc" xml:space="preserve">
    <value>Default accounts settings for this group's vendors</value>
  </data>
  <data name="MsgDelCustGroup" xml:space="preserve">
    <value>Are you sure you want to delete this vendors group</value>
  </data>
  <data name="MsgDelCustGroup2" xml:space="preserve">
    <value>Sorry, you can't delete this vendos group, there's some vendors related to it</value>
  </data>
  <data name="ValVendorCodeLength" xml:space="preserve">
    <value>Vendor code length cann't exceed batch length in barcode template</value>
  </data>
  <data name="ValBranch" xml:space="preserve">
    <value>Please Choose Branch</value>
  </data>
  <data name="msgMustApprovePrOrder" xml:space="preserve">
    <value>Purchase order is not approved yet</value>
  </data>
  <data name="msgMustApprovePrQuote" xml:space="preserve">
    <value>Purchase quotation is not approved yet</value>
  </data>
  <data name="FineNumber" xml:space="preserve">
    <value>Fine Number</value>
  </data>
  <data name="preformaType" xml:space="preserve">
    <value>Can`t allow to change preformaType</value>
  </data>
  <data name="PreInvoicesAdvanced" xml:space="preserve">
    <value>Must Choose InvoicesAdvanced</value>
  </data>
</root>