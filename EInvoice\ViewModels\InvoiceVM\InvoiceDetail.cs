﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EInvoice.ViewModels.InvoiceVM
{
    public class InvoiceDetail
    {
        public int Id { get; set; }
        public int itemId { get; set; }
        public string itemName { get; set; }
        public decimal quantity { get; set; }
        public int currencyId { get; set; }
        public string currencyName { get; set; }
        public decimal exchangeRate { get; set; }
        public decimal amount { get; set; } // amount with currency entered
        public int uomId { get; set; }
        public string uomName { get; set; }
        public decimal discountAfterTax { get; set; }
        public Discount discount { get; set; }
        public List<Tax> taxes { get; set; }
    }



    public class Discount
    {
        public int type { get; set; } // 1 percent --- 0  amount
        public decimal amount { get; set; }
        public decimal rate { get; set; }
    }


    public class Tax 
    {
        public int taxId { get; set; } //ضريبة ..
        public string taxName { get; set; } //ضريبة ..
        public int subTaxId { get; set; }
        public string subTaxName { get; set; }
        public int type { get; set; } // 1 percent --- 0  amount                                     
        public decimal rate { get; set; }
        public decimal amount { get; set; }
    }





}
