﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EInvoice.ViewModels.TaxCalculatorVM
{
    public class TaxCalculatorVM
    {
       public List<TaxableItem> taxableItem { get; set; }
       public decimal totalTaxableFees { get; set; }
       public decimal totalNonTaxableFees { get; set; }
       public decimal totalfixedAmountTaxsT3 { get; set; }
       public decimal totalfixedAmountTaxsT6 { get; set; }
       public decimal totalWithHoldingTax { get; set; }
       public decimal totaltableTax { get; set; }
        public decimal totalVATTax { get; set; }

    }
}
