﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Diagnostics;
using DAL;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Net.Sockets;
using System.Net;
using System.Data;
using Microsoft.Win32;
using System.Threading;
using System.Globalization;

namespace DAL
{
    public class Utilities
    {
        public static void EnCulture(bool isEnglish)
        {
            if (isEnglish)
            {
                Thread.CurrentThread.CurrentCulture = new CultureInfo("en-US");
                Thread.CurrentThread.CurrentUICulture = new CultureInfo("en-US");
            }
            else
            {
                //string defaultLanguage = Thread.CurrentThread.CurrentUICulture.ToString();
                Thread.CurrentThread.CurrentCulture = new CultureInfo("ar-EG");
                Thread.CurrentThread.CurrentUICulture = new CultureInfo("ar-EG");
            }
        }

        /// <summary>
        /// Clean single string that replace charchters with the default one ,        
        /// </summary>
        /// <param name="message"></param>
        public static string CleanSingle(string orignal)
        {
            string cleaned_string = orignal;
            cleaned_string = cleaned_string.Replace('أ', 'ا');
            cleaned_string = cleaned_string.Replace('إ', 'ا');
            cleaned_string = cleaned_string.Replace('آ', 'ا');

            cleaned_string = cleaned_string.Replace('ى', 'ي');
            cleaned_string = cleaned_string.Replace('ة', 'ه');

            return cleaned_string;
        }

        public static string get_arabic_days(DayOfWeek dayOfWeek)
        {
            if (dayOfWeek == DayOfWeek.Saturday)
                return "السبت";
            if (dayOfWeek == DayOfWeek.Sunday)
                return "الأحد";
            if (dayOfWeek == DayOfWeek.Monday)
                return "الإثنين";
            if (dayOfWeek == DayOfWeek.Tuesday)
                return "الثلاثاء";
            if (dayOfWeek == DayOfWeek.Wednesday)
                return "الأربعاء";
            if (dayOfWeek == DayOfWeek.Thursday)
                return "الخميس";
            else
                return "الجمعة";
        }

        public static decimal Calc_DiscountValue(decimal DiscR1, decimal DiscR2, decimal DiscR3, decimal TotalPrice)
        {
            return (1 - (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3)) * TotalPrice;
        }

        public static bool ValuesNotEqual(decimal val1, decimal val2)
        {
            decimal diff_limit = Convert.ToDecimal(3);
            if (val1 <= 10)
                diff_limit = 1;
            else if (val1 > 10 && val1 <= 100)
                diff_limit = 2;
            else if (val1 > 100)
                diff_limit = 3;

            if (val1 < diff_limit || val2 < diff_limit)
                return true;

            decimal diff = val1 - val2;
            if (diff >= (diff_limit * -1) && diff <= diff_limit)        //Closed values
                return false;
            else
                return true;                   //Far Values
        }

        public static void Get_ChkLst_Items(object chklst_EditValue, List<int> lst)
        {
            if (chklst_EditValue != null && chklst_EditValue.ToString() != string.Empty)
            {
                foreach (string l in chklst_EditValue.ToString().Split(','))
                {
                    int id = 0;
                    int.TryParse(l, out id);
                    lst.Add(id);
                }
            }
        }

        public static string Format_Date(DateTime date)
        {
            string _date = date.Year + "/" + date.Month + "/" + date.Day;
            string temp = Utilities.ConvertToArabicNumerals(_date);
            return temp;
        }

        public static string ConvertToArabicNumerals(string input)
        {
            System.Text.UTF8Encoding utf8Encoder = new UTF8Encoding();
            System.Text.Decoder utf8Decoder = utf8Encoder.GetDecoder();
            System.Text.StringBuilder convertedChars = new System.Text.StringBuilder();
            char[] convertedChar = new char[1];
            byte[] bytes = new byte[] { 217, 160 };
            char[] inputCharArray = input.ToCharArray();
            foreach (char c in inputCharArray)
            {
                if (char.IsDigit(c))
                {
                    bytes[1] = Convert.ToByte(160 + char.GetNumericValue(c));
                    utf8Decoder.GetChars(bytes, 0, 2, convertedChar, 0);
                    convertedChars.Append(convertedChar[0]);
                }
                else
                {
                    convertedChars.Append(c);
                }
            }
            return convertedChars.ToString();
        }

        public static void save_Log(string message, Exception exception)
        {
            try
            {
                //get connected db name
                System.Data.SqlClient.SqlConnectionStringBuilder builder = new System.Data.SqlClient.SqlConnectionStringBuilder();
                builder.ConnectionString = DAL.Config.ConnectionString;

                string LinkITERP_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\LinkITERP";
                if (!System.IO.Directory.Exists(LinkITERP_path))
                    System.IO.Directory.CreateDirectory(LinkITERP_path);

                string logFileName = LinkITERP_path + "\\" + builder.InitialCatalog + "_Log.txt";
                if (!System.IO.File.Exists(logFileName))
                    System.IO.File.CreateText(logFileName);

                StreamWriter sw = File.AppendText(logFileName);
                sw.WriteLine(DateTime.Now.ToString() + ">> " + message);
                if (exception != null)
                {
                    sw.WriteLine(DateTime.Now.ToString() + "Inner Exception: " + exception.InnerException?.Message);
                    sw.WriteLine(DateTime.Now.ToString() + "Stacke Trace: " + exception.InnerException?.StackTrace);
                }
                sw.Flush();
                sw.Close();
            }
            catch { }
        }
    }

    public enum FormAction
    {
        None = 0,
        Add = 1,
        Edit = 2,
        Delete = 3,
        Print = 4
    }

    public enum FormActionAR
    {
        لا = 0,
        جديد = 1,
        تعديل = 2,
        حذف = 3,
        طباعة = 4
    }

    public enum SlQouteStatus
    {
        Standing = 0,
        Canceled = 1,
        Done = 2
    }
    public enum PeriodType
    {
        Day = 0,
        Month = 1,
      
    }
    public enum Floor
    {
        Ground = 0,
        First = 1,
        Second = 2,
        Third=3
    }
    public enum FormsNames
    {
        ST_CompInfo = 1,
        Styles = 2,
        ST_Store = 3,
        ST_Print = 4,
        ST_Barcode = 5,
        Item = 6,
        Company = 7,
        Category = 8,
        Store = 9,
        AccDrawer = 10,
        ACCBank = 11,
        IC_StockTaking = 12,
        IC_EditItemQty = 13,
        IC_OpenBalance = 14,
        SL_Invoice = 15,
        SL_Return = 16,
        SL_Customer = 17,
        PR_Invoice = 18,
        PR_Return = 19,
        PR_Vendor = 20,
        AccountTree = 21,
        AccJournal = 22,
        AccStatement = 23,
        tradeStatement = 24,
        IncomeStatement = 25,
        ACC_BalanceSheet = 26,
        ACCRevenue = 27,
        ACCExpenses = 28,
        CashIn = 29,
        CashOut = 31,
        NewYear = 34,
        rpt_Ic_ItemsQty = 36,
        rpt_Ic_ItemsReorder = 37,
        rpt_IC_ItemsOpenBalance = 38,
        rpt_IC_ItemsNotSold = 39,
        rpt_IC_ItemsMinSell = 40,
        rpt_IC_ItemsMaxSell = 41,
        //rpt_IC_ItemsTotals = 42,
        rpt_PR_InvoicesHeaders = 43,
        rpt_PR_ReturnHeaders = 44,
        rpt_PR_ItemsPurchases = 45,
        rpt_PR_ItemsReturns = 46,
        rpt_SL_InvoicesHeaders = 47,
        rpt_SL_ReturnHeaders = 48,
        rpt_SL_ItemsSales = 49,
        rpt_SL_ItemsReturn = 50,
        frm_Manf_List = 51,
        MainStatistics = 53,
        rpt_IC_ItemTransactions = 54,
        IC_InTrns = 55,
        IC_OutTrns = 56,
        SL_POS = 57,
        IC_Damaged = 58,
        SL_Quote = 59,
        IC_StoreMove = 60,
        HR_Dept = 61,
        HR_Group = 62,
        HR_Job = 63,
        HR_FormalVacation = 64,
        HR_Benefit = 65,
        HR_Employee = 66,
        HR_Attendance = 67,
        HR_Vacation = 68,
        HR_Absence = 69,
        HR_Delay = 70,
        HR_OverTime = 71,
        HR_Penalty = 72,
        HR_Reward = 73,
        HR_WorkLeave = 74,
        HR_WorkReturn = 75,
        HR_Pay = 76,
        ACC_CashTransfer = 77,
        ACC_PayNote = 78,
        ACC_RecieveNote = 79,
        PR_InvoiceListBarCodePrint = 80,
        rpt_SL_ItemTrade = 81,
        rpt_ItemPriceChangings = 82,
        rpt_IC_ItemOpenInOutClose = 83,
        rpt_IC_SoldItemsCost = 84,
        rpt_Acc_Income = 85,
        rpt_Acc_Balance = 86,
        IC_PriceLevelList = 87,
        rpt_IC_ItemTransactionsDetails = 90,
        rpt_IC_ItemsExpired = 91,
        frm_ACC_CostCenter = 92,
        rpt_Acc_SL_AccountsBalances = 93,
        rpt_Acc_PR_AccountsBalances = 94,
        Acc_CustomAccountsList = 95,
        Acc_TrialBalance = 96,
        rpt_Acc_CostCenter_AccDetails = 97,
        rpt_Acc_Account_CostCenters = 98,
        rpt_Acc_CostCenterTotalBalances = 99,
        rpt_Acc_CustomAccListDetails = 100,
        rpt_Acc_CustomAccListBalances = 101,
        frm_Manf_Evaluation = 102,

        rpt_PR_VendorItemsPurchases = 103,
        rpt_SL_CustomerItemsSales = 104,
        frm_ACC_Statement_Chart = 105,
        frm_SL_PostInvoices = 106,
        frm_IC_MatrixList = 107,
        frm_ContractList = 108,
        PR_PurchaseOrder = 109,
        rpt_SL_SalesEmpTargetCommission = 110,
        IC_PrPriceLevelList = 111,
        SL_SalesOrder = 112,
        rpt_IC_ItemsQtyDetails = 113,
        rpt_IC_ItemsQtyWithPrices = 114,
        frm_SL_CustomerGroup = 115,
        rpt_SL_SalesCommision = 116,
        rpt_SL_DeliveryCommision = 117,
        rpt_SL_CustomerTrans = 118,
        rpt_Acc_DailyIncome = 119,
        rpt_Acc_DailyPayments = 120,
        rpt_SL_CustomerItemsSales_OutTrns = 121,
        frm_ACC_DrawerDaySummary = 122,
        frm_Manf_Planning = 123,
        rpt_IC_ManfCommision = 124,
        rpt_Acc_SL_AccountsBalancesWithNotes = 125,
        rpt_Acc_PR_AccountsBalancesWithNotes = 126,
        FA_FixedAsset = 127,
        rpt_IC_ItemsQtyWithSalesPrice = 128,
        rpt_IC_ItemTransactionsNoCost = 129,
        rpt_frm_IC_ItemQtyBatch = 130,
        rpt_frm_Acc_AccountDetails_Vend = 131,
        rpt_frm_Acc_AccountDetails_Cust = 132,
        frm_JO_JobOrder = 133,
        frm_SL_JobOrderInv = 134,
        rpt_frm_SL_SalesOrderItems = 135,
        rpt_HR_SalesInvItemsCommision = 136,
        frm_SL_ItemsSalesDetails = 137,
        frm_SL_ItemsSalesTotals = 138,
        rpt_SL_CustomerItemsSalesReturn = 139,
        rpt_IC_ItemOpenInOutCloseQty = 140,
        frm_Hr_Mission = 141,
        rpt_PR_ItemsPurchasesReturn = 142,
        DebitNote = 143,
        CreditNote = 144,
        Manf_QC = 145,
        frm_HR_AllPays = 146,
        frm_Mr_MainData = 147,
        frm_Mr_InDirectSales = 148,
        frm_MrAllSales = 149,
        frm_ACC_Income2 = 150,
        rpt_SL_CustomerItemsSalesReturns = 151,
        frm_HR_Loan = 152,
        frm_PR_Quote = 153,
        frm_PR_LC = 154,
        frm_PR_Request = 155,
        frm_IC_BOM = 156,
        frm_OrderAndAchievement_Cust = 157,
        frm_OrderAndAchievement_Ven = 158,
        frm_ItemsPr_and_Sl = 159,

        HR_Country = 160,
        HR_Nationality = 161,
        HR_Religion = 162,
        HR_Qualification = 163,
        HR_Sponsor = 164,
        HR_Training = 165,
        HR_SponsorTransfer = 166,
        HR_Promotion = 167,
        Weight = 168,
        HR_Evaluation = 169,
        HR_EvalItem = 170,
        HR_VacationType = 171,
        HR_AttendanceMachine = 172,
        frm_PR_VendorGroup = 173,
        HR_DelayOverTimeRule = 174,
        FA_FixedAssetGroup = 175,
        frm_Assembly = 176,
        HR_TimeTable = 177,
        frm_RS_ProjectGroup = 178,
        frm_RS_Project = 179,
        frm_RS_SellContract = 180,
        frm_PR_ContractorExtract = 181,
        HR_Shift = 182,
        rpt_frm_SL_SalesOrderItemsAndBalance = 183,
        rpt_frm_ManfItems = 184,
        rpt_PR_VendorItemsPurchases_InTrns = 185,
        frm_HR_Insurance = 186,
        frm_HR_AllExpectedPays = 187,
        frm_SL_Warranty = 188,
        frm_HR_VacationBal = 189,
        frm_HR_ShiftReplace = 190,
        frm_IC_ItemsQtyH = 191,
        frm_Acc_SubLedger = 192,
        frm_IC_ItemsTurnOver = 193,
        frm_CapitalInDeferredInvoices = 194,
        frm_SL_DeliveryOfficialsSales = 195,
        frm_SL_DelegatesSales = 196,
        rpt_IC_ItemsMinLevel = 197,
        frm_SL_Profit_Loss = 198,
        frm_ACC_Archive = 199,
        frm_Pr_InvoiceArchive = 200,
        frm_Pr_ReturnArchive = 201,
        frm_Sl_InvoiceArchive = 202,
        frm_Sl_ReturnArchive = 203,
        frm_HR_EmpPermissiontoattendandleave = 204,
        frm_IC_UOM = 205,
        frm_Acc_YearlyBudget = 206,
        rpt_PR_InvoicesDiscountTaxHeaders = 207,
        rpt_Acc_DailyPaymentsAndIncomePayments = 208,
        rpt_Acc_Payments = 209,
        rpt_ACC_SubLedger = 210,
        rpt_IC_SoldItemsandReturnCost = 211,
        rpt_SL_averagesellingpriceoftheitems = 212,
        frm_ACC_Visa = 213,
        VisaNote = 214,
        SL_Group_Customer = 215,
        frm_ACC_Budget = 216,
        frm_ACC_Budget_List = 217,
        frm_ManfExp_List = 218,
        frm_Sales_profitability = 219,
        frm_Profitability_of_items = 220,
        frm_MaxCustomerSalesCategory = 221,
        frm_MaxItemsSalesCategory = 222,
        frm_SalesRate = 223,
        frm_SalesDelegatesForItems = 224,
        frm_ItemSales_toeach_delegate = 225,
        frm_Accounts_statistics = 226,
        frm_HR_Att = 227, // تقرير الحضور والانصراف
        frm_HR_Vacations = 228,// تقرير الاجازات
        frm_SalesRep_DaySummary = 229,
        frm_Customers_Debit = 230,//مديونية العملاء
        frm_SL_Car_Weights = 231,
        rpt_UserDaySummary = 232,//user's shift
        frm_HR_Emloyee_Nationality = 233,
        frm_HR_Employee_Report = 234,
        IC_ItemReplacement = 235,
        frm_SL_Customer_Visits = 236, // customer visits
        frm_SL_InvoiceRevision = 237, // Invoice Review
        rpt_IC_InOutItems = 238, // تقرير حركات اضافة وصرف الاصناف
        FA_SL_FixedAsset = 239, // بيع أصل ثابت
        rpt_IC_Item_In_Out_Balance = 240, // وارد وصادر عمليات الصنف بالمخزن
        rpt_SL_CustomerDiscount = 241, //تقرير خصومات العملاء
        rpt_SL_CustomerTotal_Invoices = 242,//تقري اجمالي مبيعات العملاء
        frm_IC_ItemsQtyHWithoutCost = 243, // تقرير أرصدة الأصناف أفقى بدون تكلفة
        frm_SL_ItemsNetSalesDetails = 244, // تقرير صافي مبعات ومردودات المبيعات
        frm_SL_ItemsNetPurchaseDetails = 245, // تقرير صافي مشتريات ومردودات المشتريات
        frm_IC_StoreSales = 246, // تقرير صافي مبيعات المخازن
        frm_IC_StorePurchase = 247, // تقرير صافي مشتريات المخازن
        frm_SL_Delivery = 248, // طرق التسليم
        frm_FA_Exclude = 249, // استبعاد أصل ثابت
        frm_SL_CustomerGroupItemsNet = 250,
        frm_HR_QualificationBenfits = 251,
        frm_HR_WorkEntities = 252,
        frm_HR_Add_WorkingDays = 253,
        frm_HR_Branch = 254,
        frm_HR_BenfitGroup = 255,
        frm_ReceivingAndAchievement = 256, //Receiving And Achievement
        rpt_SL_Invoices_Due=257,//تقرير تاريخ استحقاق فواتير البيع
        frm_Acc_Account_CostCentersPivot = 258,
        frm_Acc_CostCenterOper = 259,
        SL_SalesOrderArchive = 260,
        PR_PurchaseOrderArchive = 261,
        frm_ACC_IncomeMain = 262,
        ACC_CrncTransfer = 263,
        frm_ACC_IncomeSub = 264,
        HR_BusinessTax = 265,
        frm_SL_Employee_Item_Target = 266,
        frm_HR_AbsenceBenfit = 267,
        frm_IC_InternalRequest=268,
        rpt_SL_CustomerInvoicesHeaders = 269,
        rpt_CashFlow = 270,
        rpt_ACC_CCTrialBalance=271,
        rpt_Acc_TrialBalance=272,
        rpt_ACC_Statement = 273,
        ImExp_PreInvoice = 274,
        frm_ImExp_Fines=275,
        frm_ImExp_Baucher=276,
        frm_ImExp_BoucherList=277,
        frm_ImExp_Brand=278,
        frm_ImExp_CertifectionList= 279,
        frm_ImExp_Container=280,
        frm_ImExp_ContainerList=281,
        frm_ImExp_ContractType=282,
        frm_ImExp_Customs_Certifectie_List=283,
        frm_ImExp_Customs_Certificate=284,
        frm_ImExp_ExportConfirmation=285,
        frm_ImExp_Fine_List=286,
        frm_ImExp_HalalCertifection=287,
        frm_ImExp_Import_Approval_List=288,
        frm_ImExp_Import_Approvall = 289,
        frm_ImExp_Lab=290,
        frm_ImExp_Policy=291,
        frm_ImExp_PolicyList=292,
        frm_ImExp_Port=293,
        frm_ImExp_ProFormaType=294,
        frm_ACC_CashNote_MultiSource=295,
        frm_ACC_RecieveNote_MultiSource=296,
        frm_ImExp_ExportConfirmationList = 297,
        frm_ImExp_Container_Report =298,
        frm_ImExp_FinesReport =299,
        frm_ImExp_ExportConfirmat_Report=300,
        frm_ImExp_Commission_Report=301,
        frm_ImExp_PreInvoice_Report = 302,
        frm_Sl_JobOrderStatus = 303,
        frm_ACC_Capital_Distribute = 304,
        IC_Customer_Items = 305,
        frm_HR_Incentives = 306,
        frm_Mall_Due=307,
        frm_Mall_Contract=308,
        frm_Mall_MonthlyDue=309,
        frm_Mall_Invoice=310,
        frm_CustomsCertifecationWarning=311,
        frm_Manf_Planning_Offer=312,
        frm_ACC_CustomizedIncomeReport=313,
        frm_ACC_RevExpEntry=314,
        frm_ACC_CurrencyTransfer=315,
        rpt_InvoiceDetails=316,
        frm_HR_Todo = 317,
        Dongle_SN=800,
        SL_Add=318,
        frm_SubTaxNet=319,
        frm_SubTaxTotal=320,
        frm_SL_E_Invoice=321
    }

    public enum HR_SubtractType
    {
        Default = 0,
        Vacation = 1,
        Absence = 2,
        Penalty = 3,
        Loan = 4
    }

    [Serializable]
    public class UserPriv
    {
        int pId;
        string formNameAr;
        string formNameEn;
        bool canAdd;
        bool canDel;
        bool canEdit;
        bool canPrint;

        public int PId
        {
            get { return pId; }
            set { pId = value; }
        }

        public string FormNameAr
        {
            get { return formNameAr; }
            set { formNameAr = value; }
        }

        public string FormNameEn
        {
            get { return formNameEn; }
            set { formNameEn = value; }
        }

        public bool CanAdd
        {
            get { return canAdd; }
            set { canAdd = value; }
        }

        public bool CanDel
        {
            get { return canDel; }
            set { canDel = value; }
        }

        public bool CanEdit
        {
            get { return canEdit; }
            set { canEdit = value; }
        }

        public bool CanPrint
        {
            get { return canPrint; }
            set { canPrint = value; }
        }

    }

    public class Crypto
    {
        public static string Key = "NeoTech@Damietta";
        private static byte[] _salt = Encoding.ASCII.GetBytes(Key);

        /// <summary>
        /// Encrypt the given string using AES.  The string can be decrypted using 
        /// DecryptStringAES().  The sharedSecret parameters must match.
        /// </summary>
        /// <param name="plainText">The text to encrypt.</param>
        /// <param name="sharedSecret">A password used to generate a key for encryption.</param>
        public static string EncryptStringAES(string plainText, string sharedSecret)
        {
            if (string.IsNullOrEmpty(plainText))
                throw new ArgumentNullException("plainText");
            if (string.IsNullOrEmpty(sharedSecret))
                throw new ArgumentNullException("sharedSecret");

            string outStr = null;                       // Encrypted string to return
            RijndaelManaged aesAlg = null;              // RijndaelManaged object used to encrypt the data.

            try
            {
                // generate the key from the shared secret and the salt
                Rfc2898DeriveBytes key = new Rfc2898DeriveBytes(sharedSecret, _salt);

                // Create a RijndaelManaged object
                // with the specified key and IV.
                aesAlg = new RijndaelManaged();
                aesAlg.Key = key.GetBytes(aesAlg.KeySize / 8);
                aesAlg.IV = key.GetBytes(aesAlg.BlockSize / 8);

                // Create a decrytor to perform the stream transform.
                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                // Create the streams used for encryption.
                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {

                            //Write all data to the stream.
                            swEncrypt.Write(plainText);
                        }
                    }
                    outStr = Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
            finally
            {
                // Clear the RijndaelManaged object.
                if (aesAlg != null)
                    aesAlg.Clear();
            }

            // Return the encrypted bytes from the memory stream.
            return outStr;
        }

        /// <summary>
        /// Decrypt the given string.  Assumes the string was encrypted using 
        /// EncryptStringAES(), using an identical sharedSecret.
        /// </summary>
        /// <param name="cipherText">The text to decrypt.</param>
        /// <param name="sharedSecret">A password used to generate a key for decryption.</param>
        public static string DecryptStringAES(string cipherText, string sharedSecret)
        {
            if (string.IsNullOrEmpty(cipherText))
                throw new ArgumentNullException("cipherText");
            if (string.IsNullOrEmpty(sharedSecret))
                throw new ArgumentNullException("sharedSecret");

            // Declare the RijndaelManaged object
            // used to decrypt the data.
            RijndaelManaged aesAlg = null;

            // Declare the string used to hold
            // the decrypted text.
            string plaintext = null;

            try
            {
                // generate the key from the shared secret and the salt
                Rfc2898DeriveBytes key = new Rfc2898DeriveBytes(sharedSecret, _salt);

                // Create a RijndaelManaged object
                // with the specified key and IV.
                aesAlg = new RijndaelManaged();
                aesAlg.Key = key.GetBytes(aesAlg.KeySize / 8);
                aesAlg.IV = key.GetBytes(aesAlg.BlockSize / 8);

                // Create a decrytor to perform the stream transform.
                ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);
                // Create the streams used for decryption.                
                byte[] bytes = Convert.FromBase64String(cipherText);
                using (MemoryStream msDecrypt = new MemoryStream(bytes))
                {
                    using (CryptoStream csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (StreamReader srDecrypt = new StreamReader(csDecrypt))

                            // Read the decrypted bytes from the decrypting stream
                            // and place them in a string.
                            plaintext = srDecrypt.ReadToEnd();
                    }
                }
            }
            finally
            {
                // Clear the RijndaelManaged object.
                if (aesAlg != null)
                    aesAlg.Clear();
            }

            return plaintext;
        }
    }
}
