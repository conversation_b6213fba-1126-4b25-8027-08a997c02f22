﻿using Models_1.ViewModels.Receipt;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models_1.ViewModels.ValidationVM
{
    public class ReceiptValidation
    {
        // Validation messages
        public List<string> ValidationMessages { get; set; }

        // Indicates if the model is valid or not
        public bool IsValid { get; set; }

        public ReceiptValidation()
        {
            ValidationMessages = new List<string>();
            IsValid = true;
        }

        // Validate the header data
        public void ValidateHeader(Header header)
        {
            if (header.DateTimeIssued == DateTime.MinValue)
            {
                ValidationMessages.Add("DateTimeIssued is required.");
                IsValid = false;
            }

            if (string.IsNullOrEmpty(header.ReceiptNumber))
            {
                ValidationMessages.Add("ReceiptNumber is required.");
                IsValid = false;
            }

            // Add more validations for other header properties...
        }

        // Validate the seller data
        public void ValidateSeller(Seller seller)
        {
            if (string.IsNullOrEmpty(seller.RIN))
            {
                ValidationMessages.Add("RIN is required.");
                IsValid = false;
            }

            if (string.IsNullOrEmpty(seller.CompanyTradeName))
            {
                ValidationMessages.Add("CompanyTradeName is required.");
                IsValid = false;
            }

            // Add more validations for other seller properties...
        }

        // Validate the buyer data
        public void ValidateBuyer(Buyer buyer)
        {
            if (string.IsNullOrEmpty(buyer.Type))
            {
                ValidationMessages.Add("Buyer Type is required.");
                IsValid = false;
            }

            // Add more validations for other buyer properties...
        }

        // Validate the item data
        public void ValidateItemData(List<ItemData> itemDataList)
        {
            foreach (var itemData in itemDataList)
            {
                if (string.IsNullOrEmpty(itemData.InternalCode))
                {
                    ValidationMessages.Add("InternalCode is required for all items.");
                    IsValid = false;
                }

                if (string.IsNullOrEmpty(itemData.Description))
                {
                    ValidationMessages.Add("Description is required for all items.");
                    IsValid = false;
                }

                if (string.IsNullOrEmpty(itemData.ItemType) ||
                    (itemData.ItemType != "GS1" && itemData.ItemType != "EGS"))
                {
                    ValidationMessages.Add("Invalid ItemType. Must be 'GS1' or 'EGS'.");
                    IsValid = false;
                }

                if (string.IsNullOrEmpty(itemData.ItemCode))
                {
                    ValidationMessages.Add("ItemCode is required for all items.");
                    IsValid = false;
                }

                if (itemData.Quantity <= 0)
                {
                    ValidationMessages.Add("Quantity must be greater than 0 for all items.");
                    IsValid = false;
                }

                if (itemData.UnitPrice <= 0)
                {
                    ValidationMessages.Add("UnitPrice must be greater than 0 for all items.");
                    IsValid = false;
                }

                // Add more validations for other item properties...
            }
        }

        // Validate the return receipt specific data
        public void ValidateReturnReceiptData(ReturnReceipt receipt)
        {
            // Add specific validations for return receipt properties...
        }

        // Add other validation methods for remaining properties...
    }
}
