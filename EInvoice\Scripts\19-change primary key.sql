﻿if not exists(select * from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME='sl_invoicedetailsubtaxvalue' and column_name='invoicedetailSubTaxId')
Begin
if exists(select * from INFORMATION_SCHEMA.TABLE_CONSTRAINTS where TABLE_NAME='sl_invoicedetailsubtaxvalue' and constraint_name='PK_dbo.SL_InvoiceDetailSubTaxValue')
begin
alter table sl_invoicedetailsubtaxvalue drop constraint [PK_dbo.SL_InvoiceDetailSubTaxValue]
end

if exists(select * from INFORMATION_SCHEMA.TABLE_CONSTRAINTS where TABLE_NAME='sl_invoicedetailsubtaxvalue' and constraint_name='PK_SL_InvoiceDetailSubTaxValue')
begin
alter table sl_invoicedetailsubtaxvalue drop constraint [PK_SL_InvoiceDetailSubTaxValue]
end
alter table sl_invoicedetailsubtaxvalue drop column id
alter table sl_invoicedetailsubtaxvalue add [invoicedetailSubTaxId] int Identity(1,1) Primary key
End