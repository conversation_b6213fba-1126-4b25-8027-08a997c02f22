﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResSLEn {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResSLEn() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResSLEn", typeof(ResSLEn).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detailed Statement of Account.
        /// </summary>
        public static string accDetail {
            get {
                return ResourceManager.GetString("accDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Accounts Settings for this Group&apos;s Customers.
        /// </summary>
        public static string defaultCustGroupAcc {
            get {
                return ResourceManager.GetString("defaultCustGroupAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this group, it&apos;s in use by marketting plans.
        /// </summary>
        public static string DelCustGrpOnMr {
            get {
                return ResourceManager.GetString("DelCustGrpOnMr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t update customers data for this customer group.
        /// </summary>
        public static string DfltCustGrp {
            get {
                return ResourceManager.GetString("DfltCustGrp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to False.
        /// </summary>
        public static string False {
            get {
                return ResourceManager.GetString("False", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This month already exists.
        /// </summary>
        public static string MonthExists {
            get {
                return ResourceManager.GetString("MonthExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this month plan ?.
        /// </summary>
        public static string MrDelTarget {
            get {
                return ResourceManager.GetString("MrDelTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter the date of this month plan.
        /// </summary>
        public static string MrEnterDate {
            get {
                return ResourceManager.GetString("MrEnterDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data loaded successfully.
        /// </summary>
        public static string MrMonthLoad {
            get {
                return ResourceManager.GetString("MrMonthLoad", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There&apos;s no plan for this Month.
        /// </summary>
        public static string MrPlanNotfound {
            get {
                return ResourceManager.GetString("MrPlanNotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to proceed approving selected sales orders?.
        /// </summary>
        public static string MsgAskApproveSalesOrders {
            get {
                return ResourceManager.GetString("MsgAskApproveSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to create Store Receving Bill Now ?.
        /// </summary>
        public static string MsgAskCreateInTrns {
            get {
                return ResourceManager.GetString("MsgAskCreateInTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to create Store Outgoing Bill Now ?.
        /// </summary>
        public static string MsgAskCreateOutTrns {
            get {
                return ResourceManager.GetString("MsgAskCreateOutTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Are you sure you want to delete .
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this customer ?.
        /// </summary>
        public static string MsgAskDeleteCust {
            get {
                return ResourceManager.GetString("MsgAskDeleteCust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to exit ?.
        /// </summary>
        public static string MsgAskExit {
            get {
                return ResourceManager.GetString("MsgAskExit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to proceed rejecting selected sales orders?.
        /// </summary>
        public static string MsgAskRejectSalesOrders {
            get {
                return ResourceManager.GetString("MsgAskRejectSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save invoice first.
        /// </summary>
        public static string MsgAskToSaveInv {
            get {
                return ResourceManager.GetString("MsgAskToSaveInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save sales order first.
        /// </summary>
        public static string MsgAskToSaveOrder {
            get {
                return ResourceManager.GetString("MsgAskToSaveOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t change customer group, it&apos;s account is used know.
        /// </summary>
        public static string MsgChangeGroup {
            get {
                return ResourceManager.GetString("MsgChangeGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to change sub categories code?.
        /// </summary>
        public static string MsgChangeSubCode {
            get {
                return ResourceManager.GetString("MsgChangeSubCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sub cateory has code smaller Category.
        /// </summary>
        public static string msgCheckSubCatCode {
            get {
                return ResourceManager.GetString("msgCheckSubCatCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This code already exists.
        /// </summary>
        public static string MsgCodeExist {
            get {
                return ResourceManager.GetString("MsgCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t change group code.
        /// </summary>
        public static string MsgCustGrpCode {
            get {
                return ResourceManager.GetString("MsgCustGrpCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You made some changes, do you want to save.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted Successfully.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this customer, you have to delete his transactions first.
        /// </summary>
        public static string MsgDelCustDenied {
            get {
                return ResourceManager.GetString("MsgDelCustDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this customers group.
        /// </summary>
        public static string MsgDelCustGroup {
            get {
                return ResourceManager.GetString("MsgDelCustGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this group, there&apos;s customers related to this group..
        /// </summary>
        public static string MsgDelCustGroup2 {
            get {
                return ResourceManager.GetString("MsgDelCustGroup2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this document.
        /// </summary>
        public static string MsgDeleteInv {
            get {
                return ResourceManager.GetString("MsgDeleteInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please delete all customer journals first.
        /// </summary>
        public static string MsgDeleteJornalsFirst {
            get {
                return ResourceManager.GetString("MsgDeleteJornalsFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this acount, there&apos;s other accounts linked to it.
        /// </summary>
        public static string msgDelLinked {
            get {
                return ResourceManager.GetString("msgDelLinked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to delete row ?.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter customer code.
        /// </summary>
        public static string MsgEnterCustomerCode {
            get {
                return ResourceManager.GetString("MsgEnterCustomerCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter customer name.
        /// </summary>
        public static string MsgEnterCustomerName {
            get {
                return ResourceManager.GetString("MsgEnterCustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to some data are incorrect.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t change this account, you have to delete it&apos;s journals first.
        /// </summary>
        public static string msgLinkAcc {
            get {
                return ResourceManager.GetString("msgLinkAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t set open balance for this account.
        /// </summary>
        public static string msgLinkAccOpen {
            get {
                return ResourceManager.GetString("msgLinkAccOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales order is not approved yet.
        /// </summary>
        public static string msgMustApproveSalesOrder {
            get {
                return ResourceManager.GetString("msgMustApproveSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This name already exists.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This customer has no account.
        /// </summary>
        public static string MsgNoAccount {
            get {
                return ResourceManager.GetString("MsgNoAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no enough Qty of some items.
        /// </summary>
        public static string MsgNoEnoughQty {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no enough Qty of some items, Continue ?.
        /// </summary>
        public static string MsgNoEnoughQty_continue {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty_continue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry , There is no sales invoices to post.
        /// </summary>
        public static string msgnoNonArchivedSLInvoices {
            get {
                return ResourceManager.GetString("msgnoNonArchivedSLInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This number already exists.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to Delete  record.
        /// </summary>
        public static string MsgPrvDel {
            get {
                return ResourceManager.GetString("MsgPrvDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to edit record.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to add new record.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is Items has qty less than or equal Zero.
        /// </summary>
        public static string MsgQtyLessZero {
            get {
                return ResourceManager.GetString("MsgQtyLessZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approving sales orders were completed successfully.
        /// </summary>
        public static string MsgSalesOrdersApprovedSuccessfully {
            get {
                return ResourceManager.GetString("MsgSalesOrdersApprovedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejecting sales orders were  completed successfully.
        /// </summary>
        public static string MsgSalesOrdersRejectedSuccessfully {
            get {
                return ResourceManager.GetString("MsgSalesOrdersRejectedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved Successfully.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer code must be larger than 0.
        /// </summary>
        public static string MsgValidateCustomerCode {
            get {
                return ResourceManager.GetString("MsgValidateCustomerCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t repeat Telehone Numbers.
        /// </summary>
        public static string repeatPhoneNumber {
            get {
                return ResourceManager.GetString("repeatPhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select sales employee.
        /// </summary>
        public static string SalesEmpMandatory {
            get {
                return ResourceManager.GetString("SalesEmpMandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to for sales order no..
        /// </summary>
        public static string soCode {
            get {
                return ResourceManager.GetString("soCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to True.
        /// </summary>
        public static string True {
            get {
                return ResourceManager.GetString("True", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount.
        /// </summary>
        public static string txt_Discount {
            get {
                return ResourceManager.GetString("txt_Discount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay.
        /// </summary>
        public static string txt_Paid {
            get {
                return ResourceManager.GetString("txt_Paid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Commission .
        /// </summary>
        public static string txt_SalesCommission {
            get {
                return ResourceManager.GetString("txt_SalesCommission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax.
        /// </summary>
        public static string txt_Tax {
            get {
                return ResourceManager.GetString("txt_Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assembly.
        /// </summary>
        public static string txtAssembly {
            get {
                return ResourceManager.GetString("txtAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete general customer.
        /// </summary>
        public static string txtCantDeleteGCustomer {
            get {
                return ResourceManager.GetString("txtCantDeleteGCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to edit general customer.
        /// </summary>
        public static string txtCantEditGCustomer {
            get {
                return ResourceManager.GetString("txtCantEditGCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category: .
        /// </summary>
        public static string txtCategory {
            get {
                return ResourceManager.GetString("txtCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comapny: .
        /// </summary>
        public static string txtComapny {
            get {
                return ResourceManager.GetString("txtComapny", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit.
        /// </summary>
        public static string txtCredit {
            get {
                return ResourceManager.GetString("txtCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer: .
        /// </summary>
        public static string txtCustomer {
            get {
                return ResourceManager.GetString("txtCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dealer Name.
        /// </summary>
        public static string txtDealerNm {
            get {
                return ResourceManager.GetString("txtDealerNm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        public static string txtDebit {
            get {
                return ResourceManager.GetString("txtDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please choose discount account in branch form.
        /// </summary>
        public static string txtDiscountAccount {
            get {
                return ResourceManager.GetString("txtDiscountAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to D R :.
        /// </summary>
        public static string txtDiscRatio {
            get {
                return ResourceManager.GetString("txtDiscRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to D V :.
        /// </summary>
        public static string txtDiscValue {
            get {
                return ResourceManager.GetString("txtDiscValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Drawer .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ended.
        /// </summary>
        public static string txtEnded {
            get {
                return ResourceManager.GetString("txtEnded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exceeds Reorder level , Continue ?.
        /// </summary>
        public static string txtExceedsReorder {
            get {
                return ResourceManager.GetString("txtExceedsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expenses :.
        /// </summary>
        public static string txtExpenses {
            get {
                return ResourceManager.GetString("txtExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From Date .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        public static string txtInvDate {
            get {
                return ResourceManager.GetString("txtInvDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment Method.
        /// </summary>
        public static string txtInvPayMethod {
            get {
                return ResourceManager.GetString("txtInvPayMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item: .
        /// </summary>
        public static string txtItem {
            get {
                return ResourceManager.GetString("txtItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item already exists.
        /// </summary>
        public static string txtItemExist {
            get {
                return ResourceManager.GetString("txtItemExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Movement.
        /// </summary>
        public static string txtItemMovement {
            get {
                return ResourceManager.GetString("txtItemMovement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Balance.
        /// </summary>
        public static string txtItemsBalance {
            get {
                return ResourceManager.GetString("txtItemsBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Best-Selling Items.
        /// </summary>
        public static string txtItemsBestSell {
            get {
                return ResourceManager.GetString("txtItemsBestSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Least-Selling Items.
        /// </summary>
        public static string txtItemsLeastSell {
            get {
                return ResourceManager.GetString("txtItemsLeastSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Not Sold.
        /// </summary>
        public static string txtItemsNotSold {
            get {
                return ResourceManager.GetString("txtItemsNotSold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items On Reorder.
        /// </summary>
        public static string txtItemsReorder {
            get {
                return ResourceManager.GetString("txtItemsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Purchases.
        /// </summary>
        public static string txtItemTotalPurchases {
            get {
                return ResourceManager.GetString("txtItemTotalPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Purchases Returns.
        /// </summary>
        public static string txtItemTotalPurchasesReturns {
            get {
                return ResourceManager.GetString("txtItemTotalPurchasesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Sales.
        /// </summary>
        public static string txtItemTotalSales {
            get {
                return ResourceManager.GetString("txtItemTotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Sales Returns.
        /// </summary>
        public static string txtItemTotalSalesReturn {
            get {
                return ResourceManager.GetString("txtItemTotalSalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Last Customer Prices.
        /// </summary>
        public static string txtLastCustomerPPrices {
            get {
                return ResourceManager.GetString("txtLastCustomerPPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Last Prices.
        /// </summary>
        public static string txtLastPPrices {
            get {
                return ResourceManager.GetString("txtLastPPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net :.
        /// </summary>
        public static string txtNet {
            get {
                return ResourceManager.GetString("txtNet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string txtNotes {
            get {
                return ResourceManager.GetString("txtNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Open Balance .
        /// </summary>
        public static string txtOpenBalance {
            get {
                return ResourceManager.GetString("txtOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paid :.
        /// </summary>
        public static string txtPaid {
            get {
                return ResourceManager.GetString("txtPaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remains.
        /// </summary>
        public static string txtRemains {
            get {
                return ResourceManager.GetString("txtRemains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restore F10.
        /// </summary>
        public static string txtRestore {
            get {
                return ResourceManager.GetString("txtRestore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial.
        /// </summary>
        public static string txtSerial {
            get {
                return ResourceManager.GetString("txtSerial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Invoice Number.
        /// </summary>
        public static string txtSLInvNumber {
            get {
                return ResourceManager.GetString("txtSLInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Number.
        /// </summary>
        public static string txtSLQuoteNumber {
            get {
                return ResourceManager.GetString("txtSLQuoteNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Return Invoice Number.
        /// </summary>
        public static string txtSLReturnInvNumber {
            get {
                return ResourceManager.GetString("txtSLReturnInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standing.
        /// </summary>
        public static string txtStanding {
            get {
                return ResourceManager.GetString("txtStanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Store: .
        /// </summary>
        public static string txtStore {
            get {
                return ResourceManager.GetString("txtStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suspend F10.
        /// </summary>
        public static string txtSuspend {
            get {
                return ResourceManager.GetString("txtSuspend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax R:.
        /// </summary>
        public static string txtTaxRatio {
            get {
                return ResourceManager.GetString("txtTaxRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax V:.
        /// </summary>
        public static string txtTaxValue {
            get {
                return ResourceManager.GetString("txtTaxValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To Date .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total :.
        /// </summary>
        public static string txtTotal {
            get {
                return ResourceManager.GetString("txtTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Invoices.
        /// </summary>
        public static string txtTotalSales {
            get {
                return ResourceManager.GetString("txtTotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Group exceeds max debit.
        /// </summary>
        public static string txtValidateCustGrpMaxDebit {
            get {
                return ResourceManager.GetString("txtValidateCustGrpMaxDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Group exceeds max debit, do you want to continue?.
        /// </summary>
        public static string txtValidateCustGrpMaxDebitWarn {
            get {
                return ResourceManager.GetString("txtValidateCustGrpMaxDebitWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer exceeds max debit.
        /// </summary>
        public static string txtValidateCustomerMaxDebit {
            get {
                return ResourceManager.GetString("txtValidateCustomerMaxDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer exceeds max debit, do you want to continue?.
        /// </summary>
        public static string txtValidateCustomerMaxDebitWarn {
            get {
                return ResourceManager.GetString("txtValidateCustomerMaxDebitWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select discount.
        /// </summary>
        public static string txtValidateDiscount {
            get {
                return ResourceManager.GetString("txtValidateDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please record invoice number.
        /// </summary>
        public static string txtValidateInvNumber {
            get {
                return ResourceManager.GetString("txtValidateInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Item.
        /// </summary>
        public static string txtValidateItem {
            get {
                return ResourceManager.GetString("txtValidateItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount ratio must be less than 100.
        /// </summary>
        public static string txtValidateMaxDiscount {
            get {
                return ResourceManager.GetString("txtValidateMaxDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current qty after decrease sold qty is less than item minimum qty .
        /// </summary>
        public static string txtValidateMinQty {
            get {
                return ResourceManager.GetString("txtValidateMinQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter at least one item to the invoice.
        /// </summary>
        public static string txtValidateNoRows {
            get {
                return ResourceManager.GetString("txtValidateNoRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to Save On Credit Invoice.
        /// </summary>
        public static string txtValidateOnCreditInv {
            get {
                return ResourceManager.GetString("txtValidateOnCreditInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty must be larger than 0.
        /// </summary>
        public static string txtValidateQty {
            get {
                return ResourceManager.GetString("txtValidateQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current qty after decrease sold qty is less than item reorder qty.
        /// </summary>
        public static string txtValidateReorderQty {
            get {
                return ResourceManager.GetString("txtValidateReorderQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell price must be larger than 0.
        /// </summary>
        public static string txtValidateSPrice {
            get {
                return ResourceManager.GetString("txtValidateSPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Price must be larger than purchase price.
        /// </summary>
        public static string txtValidateSpriceLargerPprice {
            get {
                return ResourceManager.GetString("txtValidateSpriceLargerPprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sold qty cant be larger than store qty.
        /// </summary>
        public static string txtValidateStoreQty {
            get {
                return ResourceManager.GetString("txtValidateStoreQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select unit of measure.
        /// </summary>
        public static string txtValidateUom {
            get {
                return ResourceManager.GetString("txtValidateUom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please,Select Tax Type............
        /// </summary>
        public static string txtvalidationTaxType {
            get {
                return ResourceManager.GetString("txtvalidationTaxType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor.
        /// </summary>
        public static string txtVendor {
            get {
                return ResourceManager.GetString("txtVendor", resourceCulture);
            }
        }
    }
}
