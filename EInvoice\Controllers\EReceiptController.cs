﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Configuration;
using EInvoice.BL;
using EInvoice.Models;
using Microsoft.AspNetCore.Mvc;
using System.Net.Http;
using Newtonsoft.Json;
using Models_1.ViewModels;
using System.Net.Http.Headers;
using EInvoice.MyHelper;
using System.Text;

namespace EInvoice.Controllers
{
    [Route("api/[controller]")]
    [ApiController]

    public class EReceiptController : ControllerBase
    {
        private readonly ERPEinvoiceContext DB;
        private readonly IMapper mapper;
        string BaseUrl;
        string SignuaturenUrl;
        string publicKey;
        string tokenSerial;
        string libraryPath;
        string TokenName;
        static Service service;
        public static Service Service => service ?? (service = new Service());

        private IConfiguration Configuration;
        public EReceiptController(IConfiguration _Configuration, ERPEinvoiceContext _DB, IMapper _mapper)
        {
            Configuration = _Configuration;
            BaseUrl = Configuration.GetSection("BaseUrl").Value;
            SignuaturenUrl = Configuration.GetSection("SignuaturenUrl").Value;
            publicKey = Configuration.GetSection("publicKey").Value;
            tokenSerial = Configuration.GetSection("tokenSerial").Value;
            libraryPath = Configuration.GetSection("libraryPath").Value;
            TokenName = Configuration.GetSection("tokenName").Value;
            DB = _DB;
            mapper = _mapper;
        }

        public IActionResult Index()
        {
            var bbb = AccessToken.GetAccessToken(DB);
            try
            {
//                var validate = Validation.Validate(invoices, DB, mapper);
//                if (validate.IsValid)
//                {
//                    var documentsList = validate.ValidDocumnents.Select(x => MapInvoice(x)).ToList();
//                    var document = new Documents()
//                    {
//                        documents = documentsList
//                    };
//                    List<string> IgnoreProperties = new List<string>();
//                    if (documentsList.FirstOrDefault().documentType == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
//                    {
//                        IgnoreProperties.Add("references");
//                    }
//                    var serialzeddocuments = JsonConvert.SerializeObject(document, new JsonSerializerSettings { ContractResolver = new JsonIgnoreResolver(IgnoreProperties) });
//                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" Final Object - {serialzeddocuments}", 1, 1);
//                    var companyData = DB.StCompanyInfo.FirstOrDefault();
//                    using (HttpClient client = new HttpClient())
//                    {
//                        client.Timeout = TimeSpan.FromHours(2);
//                        client.DefaultRequestHeaders.Accept.Clear();
//                        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
//                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
//                        var content = new StringContent(serialzeddocuments, Encoding.UTF8, "application/json");
//                        UriBuilder builder = new UriBuilder(BaseUrl);
//                        builder.Path = "/api/v1.0/receiptsubmissions";
//                        HttpResponseMessage APIResponse = client.PostAsync(builder.Uri, content).Result;
//                        if (APIResponse.IsSuccessStatusCode)
//                        {
//                            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
//                            var result = JsonConvert.DeserializeObject<Models_1.ViewModels.ResponseVM.SubmitDocumentVM.SubmitDocumentResponse>(JsonContent);
                           
//;
//                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"result.acceptedDocuments " + result.acceptedDocuments, 1, 1);
//                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"result.rejectedDocuments " + result.rejectedDocuments, 1, 1);

//                            save_Log("Success APIResponse.Content: " + JsonContent, null);
//                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Success APIResponse.Content " + JsonContent, 1, 1);

//                            return StatusCode((int)APIResponse.StatusCode, result);
//                        }
//                        else
//                        {
//                            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
//                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"APIResponse.Content " + JsonContent, 1, 1);
//                            save_Log("APIResponse.Content: " + JsonContent, null);
//                            return StatusCode((int)APIResponse.StatusCode, JsonContent);
//                        }
//                    }
//                }
//                else
//                {
//                    return BadRequest(validate.Message);
//                }
            }
            catch (Exception ex)
            {
               // return BadRequest(ex.Message + " " + ex.InnerException);
            }
            return Ok();

        }
    }
}