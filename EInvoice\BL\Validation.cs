﻿using DAL;
using EInvoice.Models;
using Microsoft.EntityFrameworkCore;
using Models_1.ViewModels.ValidationVM;
using System;
using System.Collections.Generic;
using System.Linq;
using Models_1.ViewModels.InvoiceVM;
using AutoMapper;
using Models_1.ViewModels.DatabaseVM;


namespace EInvoice.BL
{
    public static class Validation
    {
        public static int row = 0;
        public static ValidationMessages validationMessages = new ValidationMessages();
        public static ValidateModel Validate(List<Invoice> invoices, ERPEinvoiceContext DB,IMapper _mapper)
        {
            ValidateModel model = new ValidateModel();
            model.UnValidDocumnents = validationMessages;
            validationMessages.Messages = new List<ValidationMessage>();

            if (!invoices.Any())
            {
                var LogMessage = "List of document should have at least one document.";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, "", LogMessage, (int)FormAction.Add, 1);

                var LogMessageAr = "يجب ان يكون هناك مستند واحد علي الاقل";
                List<string> messagesList = new List<string>() { LogMessageAr };

                AddValidationMessage(string.Empty, messagesList);
                model.IsValid = false;
                model.Message = LogMessage;
                //model.UnValidDocumnents = validationMessages;
                return model;
            }

            model.ValidDocumnents = new List<InvoiceData>();
            foreach (var invoice in invoices)
            {
                row = 0;
                var messagesList = new List<string>();
                InvoiceData invoiceData = new InvoiceData();
                invoiceData.InvoiceDetailData = new List<InvoiceDetailData>();
                invoiceData.PurchaseOrderNumber = invoice.PurchaseOrderNumber;
                invoiceData.DeliveryDate = invoice.DeliveryDate;
                var companyData = Validation.ValidateCompany(invoice.companyId, invoice.invoiceCode, DB);
                if (companyData.IsValid)
                {
                    var result =_mapper.Map<StCompanyInfoVM>(companyData.Data);
                    invoiceData.CompanyData = result;
                }
                else
                {
                    messagesList.AddRange(companyData.MessagesAr);
                }

                var storeData = Validation.ValidateStore(invoice.storeId, invoice.invoiceCode, DB);
                if (storeData.IsValid)
                {
                    var result = _mapper.Map<IcStoreVM>(storeData.Data);
                    invoiceData.StoreData = result;
                }
                else
                {
                    messagesList.AddRange(storeData.MessagesAr);
                }

                var receiverData = Validation.ValidateCustomer(invoice.customerId, invoice.invoiceCode, DB);
                if (receiverData.IsValid)
                {
                    var result = _mapper.Map<SlCustomerVM>(receiverData.Data);
                    invoiceData.CustomerData = result;
                }
                else
                {
                    messagesList.AddRange(receiverData.MessagesAr);
                }

                var DateData = Validation.ValidateDate(invoice.date, invoice.invoiceCode, DB);
                if (DateData.IsValid)
                {
                    invoiceData.InvoiceDate = DateData.Data;
                }
                else
                {
                    messagesList.AddRange(DateData.MessagesAr);
                }

                invoiceData.invoiceId = invoice.invoiceId ?? 0;
                invoiceData.invoiceCode = invoice.invoiceCode;

                var documentTypeData = Validation.ValidateDocumentType(invoice.documentType, invoice.invoiceCode, DB);
                if (documentTypeData.IsValid)
                {
                    invoiceData.DocumentType = invoice.documentType;
                }
                else
                {
                    messagesList.AddRange(documentTypeData.MessagesAr);
                }

                foreach (var detail in invoice.invoiceDetails)
                {
                    row++;
                    InvoiceDetailData invoiceDetail = new InvoiceDetailData();
                    invoiceDetail.IsValid = true;
                    var itemData = Validation.ValidateItem(detail.itemId, invoice.invoiceCode, DB);
                    if (itemData.IsValid)
                    {
                        var result = _mapper.Map<IcItemVM>(itemData.Data);
                        invoiceDetail.ItemData = result;
                    }
                    else
                    {
                        invoiceDetail.IsValid = false;
                        messagesList.AddRange(itemData.MessagesAr);
                    }

                    var currencyData = Validation.ValidateCurrency(detail.currencyId, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                    if (currencyData.IsValid)
                    {
                        var result = _mapper.Map<StCurrencyVM>(currencyData.Data);
                        invoiceDetail.CurrencyData = result;
                    }
                    else
                    {
                        invoiceDetail.IsValid = false;
                        messagesList.AddRange(currencyData.MessagesAr);
                    }

                    var uomData = Validation.ValidateUOM(detail.uomId, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                    if (uomData.IsValid)
                    {
                        var result = _mapper.Map<IcUomVM>(uomData.Data);
                        invoiceDetail.UomData = result;
                    }
                    else
                    {
                        invoiceDetail.IsValid = false;
                        messagesList.AddRange(uomData.MessagesAr);
                    }

                    var quantityData = Validation.ValidateQuantity(detail.quantity, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                    if (quantityData.IsValid)
                    {
                        invoiceDetail.Quantity = quantityData.Data;
                    }
                    else
                    {
                        invoiceDetail.IsValid = false;
                        messagesList.AddRange(quantityData.MessagesAr);
                    }

                    var totalAmountData = Validation.ValidateTotalAmount(detail.totalAmount, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                    if (!totalAmountData.IsValid)
                    {
                        invoiceDetail.IsValid = false;
                        messagesList.AddRange(totalAmountData.MessagesAr);
                    }
                    var taxList = new List<Tax>();
                    if (detail.taxes != null)
                    {
                        foreach (var tax in detail.taxes)
                        {
                            var taxData = ValidateTax(tax, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                            if (taxData.IsValid)
                            {
                                var taxmodel = taxData.Data;
                                taxList.Add(taxmodel);
                            }
                            else
                            {
                                invoiceDetail.IsValid = false;
                                continue;
                            }
                        }
                    }

                    if (invoice.documentType.ToUpper() == "D" || invoice.documentType.ToUpper() == "C")
                    {
                        if (invoice.Uuid != null)
                        {
                            var ReferenceInvioces = RefernceInvoices(invoice.Uuid);

                            var ValidationData = Validation.ValidateItemForDebitCredit(ReferenceInvioces, detail.itemId);
                            if (!ValidationData.IsValid)
                            {
                                model.IsValid = false;
                                model.Message = ValidationData.Message;
                                continue;
                            }

                        }


                    }
                
                    invoiceDetail.Taxes = taxList;
                    invoiceDetail.Amount = detail.amount;
                    invoiceDetail.ExchangeRate = detail.exchangeRate;
                    invoiceDetail.Discount = detail.discount;
                    invoiceDetail.ItemsDiscount = detail.discountAfterTax;
                    invoiceDetail.Description = detail.Description;
                    invoiceData.InvoiceDetailData.Add(invoiceDetail);
                }

                if (invoice.documentType.ToUpper() == "D" || invoice.documentType.ToUpper() == "C")
                {
                    if (invoice.Uuid != null)
                    {
                        var ReferenceInvioces = RefernceInvoices(invoice.Uuid);


                        var ValidateCustomer = Validation.ValidateCustomerForDebitCredite(ReferenceInvioces);

                        if (!ValidateCustomer.IsValid)
                        {
                            model.IsValid = false;
                            model.Message = ValidateCustomer.Message;
                            break;
                        }

                        if (invoice.documentType.ToUpper() == "C")
                        {
                            var ValidateAmount = Validation.ValidateAmountForInvoiceReference(invoice.invoiceDetails, ReferenceInvioces);
                            if (!ValidateAmount.IsValid)
                            {
                                model.IsValid = false;
                                model.Message = ValidateAmount.Message;
                                break;
                            }
                        }
                    }

                    invoiceData.Uuid = invoice.Uuid;

                }

                invoiceData.TotalDiscount = invoice.TotalDiscount;
                if (invoiceData.InvoiceDetailData.Any() && invoiceData.InvoiceDetailData.All(x => x.IsValid == true && !messagesList.Any()))
                {
                    model.ValidDocumnents.Add(invoiceData);
                }

                if (messagesList.Any()) {
                    AddValidationMessage(invoice.invoiceCode, messagesList);
                }
            }

            model.IsValid = model.ValidDocumnents.Any() ? true : false;
            
            return model;
        }

        public static Validation<StCompanyInfo> ValidateCompany(int companyId, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<StCompanyInfo> model = new Validation<StCompanyInfo>();
            model.MessagesAr = new List<string>();
            var companyData = DB.StCompanyInfo.FirstOrDefault(x => x.CompanyId == companyId);
            if (companyData == null)
            {
                var LogMessage = $"CompanyId {companyId} Not Valid In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"بيانات الشركة غير صحيحة";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(companyData.ClientId))
                {
                    var LogMessage = $"Company Client Id Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"1 بيانات الشركة غير موجودة";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(companyData.ClientSecret))
                {
                    var LogMessage = $"Company Client Secret Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"2 بيانات الشركة غير موجودة";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(companyData.DonglePin))
                {
                    var LogMessage = $"Company Dongle Pin Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"بيانات التوكن غير موجودة";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(companyData.CmpNameAr))
                {
                    var LogMessage = $"Company Name Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"اسم الشركة غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(companyData.TaxCard))
                {
                    var LogMessage = $"Company Tax Registration Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"رقم التسجيل للشركة غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }
                else
                {
                    if (companyData.TaxCard.Count() != 9)
                    {
                        var LogMessage = $"Company Tax Registration Should be 9 numbers";
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                        var LogMessageAr = $"رقم التسجيل للشركة يجب ان يكون 9 ارقام";
                        model.MessagesAr.Add(LogMessageAr);
                    }
                }



                if (string.IsNullOrEmpty(companyData.ActivityType))
                {
                    var LogMessage = $"Company Activity Type Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود نشاط الشركة غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                model.Data = companyData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<IcStore> ValidateStore(int storeId, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<IcStore> model = new Validation<IcStore>();
            model.MessagesAr = new List<string>();
            var storeData = DB.IcStore.Include(x => x.Country).FirstOrDefault(x => x.StoreId == storeId);
            if (storeData == null)
            {
                var LogMessage = $"StoreId {storeId} Not Valid In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"بيانات الفرع غير صحيحة";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(storeData.Ecode))
                {
                    var LogMessage = $"Branch Id Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود الفرع غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.Country.Ecode))
                {
                    var LogMessage = $"Company Country Ecode Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (الدولة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.Governate))
                {
                    var LogMessage = $"Company Governate Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (المحافظة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.Street))
                {
                    var LogMessage = $"Company Street Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (الشارع) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.RegionCity))
                {
                    var LogMessage = $"Company Region City Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (المدينة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.BuildingNumber))
                {
                    var LogMessage = $"Company Building Number Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (رقم العمارة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = storeData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<SlCustomer> ValidateCustomer(int customerId, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<SlCustomer> model = new Validation<SlCustomer>();
            model.MessagesAr = new List<string>();
            var customerData = DB.SlCustomer.Include(x => x.Country).FirstOrDefault(x => x.CustomerId == customerId);
            if (customerData == null)
            {
                var LogMessage = $"CustomerId {customerId} Not Valid In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"بيانات المشتري غير صحيحة";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(customerData.CusNameAr))
                {
                    var LogMessage = $"Customer Name Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"اسم العميل غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (customerData.CsType == (int)MyHelper.Utilities.Type.B)
                {
                    if (string.IsNullOrEmpty(customerData.TaxCardNumber))
                    {
                        var LogMessage = $"Customer Tax Card Number Is Null Or Empty";
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                        var LogMessageAr = $"رقم التسجيل للعميل غير موجود";
                        model.MessagesAr.Add(LogMessageAr);
                    }else
                    { 
                        if (customerData.TaxCardNumber.Count() != 9)
                        {
                         var LogMessage = $"Customer Tax Card Number Should be 9 numbers";
                         MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                         var LogMessageAr = $"رقم التسجيل للعميل يجب ان يكون 9 ارقام";
                         model.MessagesAr.Add(LogMessageAr);
                        }
                    }
                }
                if(customerData.CsType == (int)MyHelper.Utilities.Type.F)
                {
                    if (string.IsNullOrEmpty(customerData.IdNumber))
                    {
                        var LogMessage = $"Customer Id Number Is Null Or Empty";
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                        var LogMessageAr = $"الرقم المدني للعميل غير موجود";
                        model.MessagesAr.Add(LogMessageAr);
                    }
                }
                if (string.IsNullOrEmpty(customerData.Country.Ecode))
                {
                    var LogMessage = $"Customer Country Ecode Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان العميل (الدولة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(customerData.Governate))
                {
                    var LogMessage = $"Customer Governate Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان العميل (المحافظة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(customerData.Street))
                {
                    var LogMessage = $"Customer Street Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان العميل (الشارع) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(customerData.City))
                {
                    var LogMessage = $"Customer Region City Is Null Or Empty";
                    var LogMessageAr = $"عنوان العميل (المدينة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(customerData.BuildingNumber))
                {
                    var LogMessage = $"Customer Building Number Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان العميل (رقم العمارة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = customerData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<DateTime> ValidateDate(DateTime invoiceDate, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<DateTime> model = new Validation<DateTime>();
            model.MessagesAr = new List<string>();
            var InvoiceValidationDays = DB.StCompanyInfo.FirstOrDefault().InvoiceDateValidationDays;

            if (invoiceDate.ToUniversalTime() > DateTime.UtcNow)
            {
                var LogMessage = $"Date and Time {invoiceDate} cannot be in future. In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"تاريخ المستند لا يجب ان يكون اكبر من تاريخ اليوم";
                model.MessagesAr.Add(LogMessageAr);
            }
            else if (invoiceDate.Date <DateTime.Now.AddDays(InvoiceValidationDays.GetValueOrDefault()*-1))
            {
                var LogMessage = $"Date should not exceed {InvoiceValidationDays} days In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr =$"تاريخ المستند لا يجب ان يتجاوزايام{InvoiceValidationDays}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                model.IsValid = true;
                var date = invoiceDate.ToUniversalTime();
                date = date.Date + new TimeSpan(date.TimeOfDay.Hours, date.TimeOfDay.Minutes, date.TimeOfDay.Seconds);
                model.Data = date;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<string> ValidateDocumentType(string documentType, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<string> model = new Validation<string>();
            model.MessagesAr = new List<string>();
            if (documentType.ToUpper() != "I" && documentType.ToUpper() != "D" && documentType.ToUpper() != "C")
            {
                var LogMessage = $"Document tType {documentType} Not Valid In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"نوع المستند غير صحيح";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                model.Data = documentType;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<IcItem> ValidateItem(int itemId, string invoiceCode, ERPEinvoiceContext DB)
        {

            Validation<IcItem> model = new Validation<IcItem>();
            model.MessagesAr = new List<string>();
            var itemData = DB.IcItem.FirstOrDefault(x => x.ItemId == itemId);
            if (itemData == null)
            {
                var LogMessage = $"ItemId {itemId} Not Valid In Document : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"الصنف رقم {row} غير صحيح";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(itemData.ItemNameAr))
                {
                    var LogMessage = $"Item Name Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"اسم الصنف رقم {row} غير صحيح";
                    model.MessagesAr.Add(LogMessageAr);
                }
                if (string.IsNullOrEmpty(itemData.ItemEtype))
                {
                    var LogMessage = $"Item Type Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"نوع كود الصنف رقم {row} غير صحيح";
                    model.MessagesAr.Add(LogMessageAr);
                }
                if (string.IsNullOrEmpty(itemData.ItemEcode))
                {
                    var LogMessage = $"Item Code Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود الصنف رقم {row} غير صحيح";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = itemData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }


        public static List<SlInvoice> RefernceInvoices(List<string> Uuid)
        {
            using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
            {
                var model = (from slInvoice in DB.SlInvoice
                             join uuid in Uuid on slInvoice.Uuid equals uuid
                             select slInvoice).ToList();


                return model;

            }

        }


        public static Validation<SlInvoiceDetail> ValidateAmountForInvoiceReference(List<InvoiceDetail> InvoiceDetails, List<SlInvoice> InvoiceReferences)
        {
            using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
            {

                Validation<SlInvoiceDetail> model = new Validation<SlInvoiceDetail>();

                var SlInvoiceDetails = (from d in DB.SlInvoiceDetail
                                        join slinvoice in InvoiceReferences on d.SlInvoiceId equals slinvoice.SlInvoiceId
                                        select d).ToList();
                var ReferenceAmount = SlInvoiceDetails.Sum(c => c.TotalSellPrice);
                var Amount = InvoiceDetails.Sum(c => c.amount * c.quantity);

                if (Amount > ReferenceAmount)
                {
                    model.Message = $"Total Amount Larger Than Refernce Amount";
                }
                else
                {
                    model.IsValid = true;

                }
                return model;
            }
        }


        public static Validation<SlInvoiceDetail> ValidateItemForDebitCredit(List<SlInvoice> SlInvoice, int ItemId)
        {
            using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
            {
                var SlInvoiceDetails = (from d in DB.SlInvoiceDetail
                                        join slinvoice in SlInvoice on d.SlInvoiceId equals slinvoice.SlInvoiceId
                                        select d).ToList();
                Validation<SlInvoiceDetail> model = new Validation<SlInvoiceDetail>();
                var itemData = SlInvoiceDetails.FirstOrDefault(x => x.ItemId == ItemId);
                if (itemData == null)
                {
                    model.Message = $"Item {ItemId} Not Exist in the original Invoice";
                }
                else
                {
                    model.IsValid = true;

                }
                return model;
            }
        }



        public static Validation<SlInvoice> ValidateCustomerForDebitCredite(List<SlInvoice> invoices)
        {
            using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
            {
                Validation<SlInvoice> model = new Validation<SlInvoice>();
                var Data = (from invoice in invoices
                            group invoices by invoice.CustomerId
                           into ii
                            select ii.Key).ToList();
                if (Data.Count() > 1)
                {
                    model.Message = $"Reference Invoices Donot Have The Same Client";
                }
                else
                {
                    model.IsValid = true;

                }
                return model;
            }
        }





        public static Validation<StCurrency> ValidateCurrency(int currencyId, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {
            Validation<StCurrency> model = new Validation<StCurrency>();
            model.MessagesAr = new List<string>();
            var currencyData = DB.StCurrency.FirstOrDefault(x => x.CrncId == currencyId);
            if (currencyData == null)
            {
                var LogMessage = $"CurrencyId {currencyId} Not Valid In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"العملة غير صحيحة في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(currencyData.Ecode))
                {
                    var LogMessage = $"Currency Ecode Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود العملة غير صحيح في الصنف {itemName} رقم {row}";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = currencyData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<IcUom> ValidateUOM(int uomId, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {

            Validation<IcUom> model = new Validation<IcUom>();
            model.MessagesAr = new List<string>();
            var uomData = DB.IcUom.FirstOrDefault(x => x.Uomid == uomId);
            if (uomData == null)
            {
                var LogMessage = $"uomId {uomId} Not Valid In In Item {itemName} Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"كود وحدةالقياس غير صحيحة في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(uomData.Ecode))
                {
                    var LogMessage = $"UOM Ecode Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود وحدة القياس غير صحيح في الصنف {itemName} رقم {row}";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = uomData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<decimal> ValidateQuantity(decimal quantity, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {
            Validation<decimal> model = new Validation<decimal>();
            model.MessagesAr = new List<string>();
            if (quantity <= 0)
            {
                var LogMessage = $"Quantity is {quantity}  should be larger than 0 In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"الكمية يجب ان تكون اكبر من صفر في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                model.Data = quantity;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<decimal> ValidateTotalAmount(decimal totalAmount, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {
            Validation<decimal> model = new Validation<decimal>();
            model.MessagesAr = new List<string>();
            if (totalAmount < 0)
            {
                var LogMessage = $"Total Amount is {totalAmount} should be larger than 0 In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"الاجمالي يجب ان يكون اكبر من صفر في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<Tax> ValidateTax(Tax tax, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {

            Validation<Tax> model = new Validation<Tax>();
            model.MessagesAr = new List<string>();
            var subTaxData = DB.ETaxableType.FirstOrDefault(x => x.ETaxableTypeId == tax.subTaxId);
            if (subTaxData == null)
            {
                var LogMessage = $"SubTaxId {tax.subTaxId} Not Valid In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"نوع الضريبة الفرعية غير صحيح في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else if (subTaxData.ParentTaxId == null)
            {
                var LogMessage = $"{tax.subTaxId} is Not SubTaxId In Item {itemName} In Invoice : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, (int)FormAction.Add, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"نوع الضريبة غير صحيح في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(subTaxData.Code))
                {
                    var LogMessage = $"Sub Tax Code Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود الضريبة الفرعية غير صحيح في الصنف {itemName} رقم {row}";
                    model.MessagesAr.Add(LogMessageAr);
                }
                var taxData = DB.ETaxableType.FirstOrDefault(x => x.ETaxableTypeId == subTaxData.ParentTaxId);
                if (string.IsNullOrEmpty(taxData.Code))
                {
                    var LogMessage = $"Tax Code Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود الضريبة غير صحيح في الصنف {itemName} رقم {row}";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.IsValid = true;
                model.Data = new Tax()
                {
                    taxId = subTaxData.ETaxableTypeId,
                    taxName = taxData.Code,
                    subTaxId = tax.subTaxId,
                    subTaxName = subTaxData.Code,
                    amount = tax.amount,
                    type = tax.type,
                    rate = tax.rate
                };
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static void AddValidationMessage(string documnetCode, List<string> messages)
        {
            var validationMessage = new ValidationMessage() { invoiceCode = documnetCode, Message = messages, Islocal = true };
            validationMessages.Messages.Add(validationMessage);
        }

        public static ValidationMessages ValidateTotalDocument(List<Models_1.ViewModels.Document> docs, ValidationMessages validationMessage , ERPEinvoiceContext DB)
        {
            var pDocs = docs.Where(x => x.receiver.type == Enum.GetName(typeof(MyHelper.Utilities.Type), MyHelper.Utilities.Type.P)).ToList();
            var documentThreshold = DB.StStore.FirstOrDefault()?.DocumentThreshold ?? 50000;
            List<string> messages = new List<string>();
            messages.Add("اجمالي الفاتورة تخطي الحد الاقصي , يجب ادخال الرقم القومي للعميل");
            foreach (var doc in pDocs)
            {
                if (doc.totalAmount >= documentThreshold && string.IsNullOrEmpty(doc.receiver.id))
                {
                    validationMessage.Messages.Add(new ValidationMessage() { Islocal = true, invoiceCode = doc.internalID, Message = messages });
                }
            }
            return validationMessage;
        }


            }

        }

   




   



