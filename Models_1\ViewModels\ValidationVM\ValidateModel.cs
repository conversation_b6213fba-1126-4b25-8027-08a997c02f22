﻿using Models_1.ViewModels.DatabaseVM;
using Models_1.ViewModels.InvoiceVM;
using System;
using System.Collections.Generic;


namespace Models_1.ViewModels.ValidationVM
{
    public class ValidateModel
    {
        public bool IsValid { get; set; }
        public string Message { get; set; }
        public List<InvoiceData> ValidDocumnents { get; set; }
        public ValidationMessages UnValidDocumnents { get; set; }
    }

    public class InvoiceData
    {
        public int invoiceId { get; set; }
        public string invoiceCode { get; set; }
        public string PurchaseOrderNumber { get; set; }
        public StCompanyInfoVM CompanyData { get; set;}
        public IcStoreVM StoreData { get; set;}
        public SlCustomerVM CustomerData { get; set;}
        public DateTime InvoiceDate { get; set; }
        public string DocumentType { get; set; } //I - C - D
        public decimal TotalDiscount { get; set; }
        public decimal TotalTax { get; set; }
        public decimal Net { get; set; }
        public List<InvoiceDetailData> InvoiceDetailData { get; set; }
        public List<string> Uuid { get; set; }
        public string DeliveryDate { get; set; }

    }

    public class InvoiceDetailData
    {
        public int SlInvoiceId { get; set; }
        public IcItemVM ItemData { get; set; }
        public StCurrencyVM CurrencyData { get; set; }
        public IcUomVM UomData { get; set; }
        public decimal Quantity { get; set; }
        public decimal ExchangeRate { get; set; }
        public decimal Amount { get; set; } // amount with currency entered
        public string Description { get; set; }
        public DetailDiscount Discount { get; set; }
        public List<DetailDiscount> Discounts { get; set; }
        public List<Tax> Taxes { get; set; }
        public decimal ItemsDiscount { get; set; }
        public bool IsValid { get; set; }
        public decimal discountAfterTax { get; set; }
    }

    public class Validation<T>
    {
        public bool IsValid { get; set; }
        public string Message { get; set; }
        public List<string> MessagesAr { get; set; }
        public T Data { get; set; }
    }
}
