﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class SlCustomer
    {
        public int CustomerId { get; set; }
        public int CusCode { get; set; }
        public string CusNameAr { get; set; }
        public string CusNameEn { get; set; }
        public string Tel { get; set; }
        public string Mobile { get; set; }
        public string Address { get; set; }
        public decimal MaxCredit { get; set; }
        public int? AccountId { get; set; }
        public decimal DiscountRatio { get; set; }
        public int? PriceLevel { get; set; }
        public string City { get; set; }
        public bool HasSeparateAccount { get; set; }
        public string Email { get; set; }
        public string Fax { get; set; }
        public string Zip { get; set; }
        public string Shipping { get; set; }
        public string Manager { get; set; }
        public int? DueDaysCount { get; set; }
        public int? SalesEmpId { get; set; }
        public string Representative { get; set; }
        public string RepresentativeJob { get; set; }
        public string RepFname { get; set; }
        public string RepFjob { get; set; }
        public int? CategoryId { get; set; }
        public string TaxCardNumber { get; set; }
        public string TaxFileNumber { get; set; }
        public string TradeRegistry { get; set; }
        public string TaxDepartment { get; set; }
        public bool IsTaxable { get; set; }
        public string IdNumber { get; set; }
        public string BankName { get; set; }
        public string BankAccNum { get; set; }
        public bool IsBlocked { get; set; }
        public bool IsActive { get; set; }
        public int? GroupId { get; set; }
        public string RepMobile { get; set; }
        public string RepId { get; set; }
        public int? Delivery { get; set; }
        public int? IdRegion { get; set; }
        public int? CsType { get; set; }
        public int? CollectEmpId { get; set; }
        public string Street { get; set; }
        public string Neighborhood { get; set; }
        public int CountryId { get; set; }
        public string Governate { get; set; }
        public string BuildingNumber { get; set; }

        public virtual HrCountry Country { get; set; }
    }
}
