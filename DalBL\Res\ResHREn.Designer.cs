﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResHREn {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResHREn() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResHREn", typeof(ResHREn).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Absence.
        /// </summary>
        public static string Absence {
            get {
                return ResourceManager.GetString("Absence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        public static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Annual.
        /// </summary>
        public static string Annual {
            get {
                return ResourceManager.GetString("Annual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this document ?.
        /// </summary>
        public static string AppDelete {
            get {
                return ResourceManager.GetString("AppDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t edit this Vacation, it is archived.
        /// </summary>
        public static string ArchivedVac {
            get {
                return ResourceManager.GetString("ArchivedVac", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attendance.
        /// </summary>
        public static string attendance {
            get {
                return ResourceManager.GetString("attendance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ratio of Basic.
        /// </summary>
        public static string BasicRatio {
            get {
                return ResourceManager.GetString("BasicRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Casual.
        /// </summary>
        public static string Casual {
            get {
                return ResourceManager.GetString("Casual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commission.
        /// </summary>
        public static string Commission {
            get {
                return ResourceManager.GetString("Commission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customize Access Rights.
        /// </summary>
        public static string customizeAccess {
            get {
                return ResourceManager.GetString("customizeAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily.
        /// </summary>
        public static string Daily {
            get {
                return ResourceManager.GetString("Daily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must choose Date FROM and TO.
        /// </summary>
        public static string DateFromTo {
            get {
                return ResourceManager.GetString("DateFromTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day.
        /// </summary>
        public static string Day {
            get {
                return ResourceManager.GetString("Day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days.
        /// </summary>
        public static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter degree.
        /// </summary>
        public static string DegreeRequired {
            get {
                return ResourceManager.GetString("DegreeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delay.
        /// </summary>
        public static string Delay {
            get {
                return ResourceManager.GetString("Delay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this document.
        /// </summary>
        public static string DeleteConfirm {
            get {
                return ResourceManager.GetString("DeleteConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete the Evaluation Note.
        /// </summary>
        public static string DelEval {
            get {
                return ResourceManager.GetString("DelEval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t complete this operation,  this FP is already in use by Delya/Overtime document.
        /// </summary>
        public static string DelFpUsed {
            get {
                return ResourceManager.GetString("DelFpUsed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this rule.
        /// </summary>
        public static string DelRule {
            get {
                return ResourceManager.GetString("DelRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t delete this rule, it&apos;s already in use.
        /// </summary>
        public static string DelRuleDenied {
            get {
                return ResourceManager.GetString("DelRuleDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this sponsor.
        /// </summary>
        public static string DelSponsor {
            get {
                return ResourceManager.GetString("DelSponsor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to due to employee.
        /// </summary>
        public static string empDue {
            get {
                return ResourceManager.GetString("empDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to loan payment for employee.
        /// </summary>
        public static string empLoadPay {
            get {
                return ResourceManager.GetString("empLoadPay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee.
        /// </summary>
        public static string Employee {
            get {
                return ResourceManager.GetString("Employee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to paid to employee.
        /// </summary>
        public static string empPay {
            get {
                return ResourceManager.GetString("empPay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attendance machine enroll Number is aready in use for another employee.
        /// </summary>
        public static string enrollNo {
            get {
                return ResourceManager.GetString("enrollNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select evaluation item.
        /// </summary>
        public static string EvalItem {
            get {
                return ResourceManager.GetString("EvalItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fixed Amount.
        /// </summary>
        public static string FixedAmount {
            get {
                return ResourceManager.GetString("FixedAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Main Payslip.
        /// </summary>
        public static string frmPayMain {
            get {
                return ResourceManager.GetString("frmPayMain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Secondary Payslip.
        /// </summary>
        public static string frmPaySecond {
            get {
                return ResourceManager.GetString("frmPaySecond", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter hour value.
        /// </summary>
        public static string hourvalue {
            get {
                return ResourceManager.GetString("hourvalue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HRMS.
        /// </summary>
        public static string hr {
            get {
                return ResourceManager.GetString("hr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee didn&apos;t pass required appointment period yet.
        /// </summary>
        public static string hrVacReqMonth {
            get {
                return ResourceManager.GetString("hrVacReqMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Tax.
        /// </summary>
        public static string incomeTax {
            get {
                return ResourceManager.GetString("incomeTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to please make sure you inserted insurance settings..
        /// </summary>
        public static string InsuranceSettings {
            get {
                return ResourceManager.GetString("InsuranceSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter times correctly.
        /// </summary>
        public static string InvalidRuleRow {
            get {
                return ResourceManager.GetString("InvalidRuleRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left Work.
        /// </summary>
        public static string leftwork {
            get {
                return ResourceManager.GetString("leftwork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loan.
        /// </summary>
        public static string loan {
            get {
                return ResourceManager.GetString("loan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this loan.
        /// </summary>
        public static string LoanDel {
            get {
                return ResourceManager.GetString("LoanDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this loan, there is installments paid.
        /// </summary>
        public static string LoanDelDenied {
            get {
                return ResourceManager.GetString("LoanDelDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loan No..
        /// </summary>
        public static string LoanJrnl {
            get {
                return ResourceManager.GetString("LoanJrnl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Main Payslip.
        /// </summary>
        public static string MainPayslip {
            get {
                return ResourceManager.GetString("MainPayslip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mission .
        /// </summary>
        public static string Mission {
            get {
                return ResourceManager.GetString("Mission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is an absence for the employee in this day, do you want to contiunue.
        /// </summary>
        public static string MsgAbsenceExistAsk {
            get {
                return ResourceManager.GetString("MsgAbsenceExistAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select employees accrued salary account from settings .
        /// </summary>
        public static string MsgAccruedAcc {
            get {
                return ResourceManager.GetString("MsgAccruedAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Are you sure you want to delete .
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter benefit name.
        /// </summary>
        public static string MsgBenefitName {
            get {
                return ResourceManager.GetString("MsgBenefitName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, check this benefit for the employees.
        /// </summary>
        public static string MsgBenefitRecheck {
            get {
                return ResourceManager.GetString("MsgBenefitRecheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Business Gain Tax account from settings.
        /// </summary>
        public static string MsgBusinessGainAcc {
            get {
                return ResourceManager.GetString("MsgBusinessGainAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This code exists before.
        /// </summary>
        public static string MsgCodeExist {
            get {
                return ResourceManager.GetString("MsgCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter sales commission correctly.
        /// </summary>
        public static string MsgCommission {
            get {
                return ResourceManager.GetString("MsgCommission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Do you want to continue .
        /// </summary>
        public static string MsgContinue {
            get {
                return ResourceManager.GetString("MsgContinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You made some changes, do you want to save.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter date correctly.
        /// </summary>
        public static string MsgDateError {
            get {
                return ResourceManager.GetString("MsgDateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted Successfully.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this absence.
        /// </summary>
        public static string MsgDelAbsence {
            get {
                return ResourceManager.GetString("MsgDelAbsence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is a delay for the employee in this day.
        /// </summary>
        public static string MsgDelayExist {
            get {
                return ResourceManager.GetString("MsgDelayExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this benefit.
        /// </summary>
        public static string MsgDelBenefit {
            get {
                return ResourceManager.GetString("MsgDelBenefit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this benefit, it is used by an employee.
        /// </summary>
        public static string MsgDelBenefitDenied {
            get {
                return ResourceManager.GetString("MsgDelBenefitDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this delay.
        /// </summary>
        public static string MsgDelDelay {
            get {
                return ResourceManager.GetString("MsgDelDelay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this department.
        /// </summary>
        public static string MsgDelDept {
            get {
                return ResourceManager.GetString("MsgDelDept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Entry?.
        /// </summary>
        public static string MsgDelItem {
            get {
                return ResourceManager.GetString("MsgDelItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this Overtime.
        /// </summary>
        public static string MsgDelOvertime {
            get {
                return ResourceManager.GetString("MsgDelOvertime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to delete row ?.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this vacation.
        /// </summary>
        public static string MsgDelVacation {
            get {
                return ResourceManager.GetString("MsgDelVacation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subsidiary Departments must be deleted before.
        /// </summary>
        public static string MsgDeptDelChilds {
            get {
                return ResourceManager.GetString("MsgDeptDelChilds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t delete this department its already in use.
        /// </summary>
        public static string MsgDeptMoveEmps {
            get {
                return ResourceManager.GetString("MsgDeptMoveEmps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter department name.
        /// </summary>
        public static string MsgDeptName {
            get {
                return ResourceManager.GetString("MsgDeptName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter employee code.
        /// </summary>
        public static string MsgEmpCode {
            get {
                return ResourceManager.GetString("MsgEmpCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee code can&apos;t be zero.
        /// </summary>
        public static string MsgEmpCodeZero {
            get {
                return ResourceManager.GetString("MsgEmpCodeZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete employee.
        /// </summary>
        public static string MsgEmpDel {
            get {
                return ResourceManager.GetString("MsgEmpDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this employee.
        /// </summary>
        public static string MsgEmpDelAsk {
            get {
                return ResourceManager.GetString("MsgEmpDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Journals related to this employee should be deleted first.
        /// </summary>
        public static string MsgEmpJournal {
            get {
                return ResourceManager.GetString("MsgEmpJournal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter employee name.
        /// </summary>
        public static string MsgEmpName {
            get {
                return ResourceManager.GetString("MsgEmpName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This employee has no account.
        /// </summary>
        public static string MsgEmpNoAccount {
            get {
                return ResourceManager.GetString("MsgEmpNoAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employees in this shift Should move to another shift first.
        /// </summary>
        public static string MsgEmpShiftMove {
            get {
                return ResourceManager.GetString("MsgEmpShiftMove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review target table.
        /// </summary>
        public static string MsgEmpTarget {
            get {
                return ResourceManager.GetString("MsgEmpTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is a formal vacation in this period.
        /// </summary>
        public static string MsgFormalExist {
            get {
                return ResourceManager.GetString("MsgFormalExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this group.
        /// </summary>
        public static string MsgGroupDel {
            get {
                return ResourceManager.GetString("MsgGroupDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employees in this group Should move to another group first.
        /// </summary>
        public static string MsgGroupMoveEmp {
            get {
                return ResourceManager.GetString("MsgGroupMoveEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to some data are incorrect.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Insurance account from settings.
        /// </summary>
        public static string MsgInsuranceAcc {
            get {
                return ResourceManager.GetString("MsgInsuranceAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you can&apos;t select item twice.
        /// </summary>
        public static string MsgItemExist {
            get {
                return ResourceManager.GetString("MsgItemExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this job.
        /// </summary>
        public static string MsgJobDel {
            get {
                return ResourceManager.GetString("MsgJobDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t delete this job it&apos;s already in use.
        /// </summary>
        public static string MsgJobDelMoveEmp {
            get {
                return ResourceManager.GetString("MsgJobDelMoveEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to get the employee out of work.
        /// </summary>
        public static string MsgleaveAsk {
            get {
                return ResourceManager.GetString("MsgleaveAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete leave work note for this employee.
        /// </summary>
        public static string MsgLeaveDelAsk {
            get {
                return ResourceManager.GetString("MsgLeaveDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The employee is out of work.
        /// </summary>
        public static string MsgLeaveWork {
            get {
                return ResourceManager.GetString("MsgLeaveWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select employees loans master account from settings .
        /// </summary>
        public static string MsgMasterAccount {
            get {
                return ResourceManager.GetString("MsgMasterAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select employees salary master account from settings .
        /// </summary>
        public static string MsgMasterExpAccount {
            get {
                return ResourceManager.GetString("MsgMasterExpAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this mission.
        /// </summary>
        public static string MsgMissionDelAsk {
            get {
                return ResourceManager.GetString("MsgMissionDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This name already exists.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Net Tax account from settings.
        /// </summary>
        public static string MsgNetTax {
            get {
                return ResourceManager.GetString("MsgNetTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This number already exists.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is an overtime for the employee in this day.
        /// </summary>
        public static string MsgOvertimeExist {
            get {
                return ResourceManager.GetString("MsgOvertimeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter password.
        /// </summary>
        public static string MsgPassword {
            get {
                return ResourceManager.GetString("MsgPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  The employee received his salary form the period .
        /// </summary>
        public static string MsgPay1 {
            get {
                return ResourceManager.GetString("MsgPay1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Payslip No.Of.
        /// </summary>
        public static string MsgPay2 {
            get {
                return ResourceManager.GetString("MsgPay2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please complete earnings data.
        /// </summary>
        public static string MsgPay3 {
            get {
                return ResourceManager.GetString("MsgPay3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please complete deductions data.
        /// </summary>
        public static string MsgPay4 {
            get {
                return ResourceManager.GetString("MsgPay4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this payslip.
        /// </summary>
        public static string MsgPayDel {
            get {
                return ResourceManager.GetString("MsgPayDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Payslip number.
        /// </summary>
        public static string MsgPayNum {
            get {
                return ResourceManager.GetString("MsgPayNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this penalty.
        /// </summary>
        public static string MsgPnltDelAsk {
            get {
                return ResourceManager.GetString("MsgPnltDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to edit record.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to add new record.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to get the employee back to work.
        /// </summary>
        public static string MsgReturnAsk {
            get {
                return ResourceManager.GetString("MsgReturnAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete return work note for this employee.
        /// </summary>
        public static string MsgReturnDelAsk {
            get {
                return ResourceManager.GetString("MsgReturnDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The employee is back to work.
        /// </summary>
        public static string MsgReturnWork {
            get {
                return ResourceManager.GetString("MsgReturnWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is a reward for the employee in this day, do you want to contiunue.
        /// </summary>
        public static string MsgRwdExist {
            get {
                return ResourceManager.GetString("MsgRwdExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this reward.
        /// </summary>
        public static string MsgRwrdDelAsk {
            get {
                return ResourceManager.GetString("MsgRwrdDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved Successfully.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Department.
        /// </summary>
        public static string MsgSelectDept {
            get {
                return ResourceManager.GetString("MsgSelectDept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select period correctly.
        /// </summary>
        public static string MsgSelectFromTo {
            get {
                return ResourceManager.GetString("MsgSelectFromTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Job.
        /// </summary>
        public static string MsgSelectJob {
            get {
                return ResourceManager.GetString("MsgSelectJob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete system administrator.
        /// </summary>
        public static string MsgSysAdminDel {
            get {
                return ResourceManager.GetString("MsgSysAdminDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this user.
        /// </summary>
        public static string MsgUserDel {
            get {
                return ResourceManager.GetString("MsgUserDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter user name.
        /// </summary>
        public static string MsgUserName {
            get {
                return ResourceManager.GetString("MsgUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The employee exceeded his vacations balance, Do you want to continue.
        /// </summary>
        public static string MsgVacationAnnualBalance {
            get {
                return ResourceManager.GetString("MsgVacationAnnualBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this vacation.
        /// </summary>
        public static string MsgVacationDelAsk {
            get {
                return ResourceManager.GetString("MsgVacationDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is a vacation for the employee in this day, do you want to contiunue.
        /// </summary>
        public static string MsgVacationExistAsk {
            get {
                return ResourceManager.GetString("MsgVacationExistAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There&apos;s Working Days exist in this period, Do you want to Continue?.
        /// </summary>
        public static string MsgWDexist {
            get {
                return ResourceManager.GetString("MsgWDexist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The period can&apos;t be more than one year.
        /// </summary>
        public static string msgYearPeriod {
            get {
                return ResourceManager.GetString("msgYearPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value must be larger than Zero.
        /// </summary>
        public static string MsgZeroValue {
            get {
                return ResourceManager.GetString("MsgZeroValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter one evaluation item at least.
        /// </summary>
        public static string NeedEvalItem {
            get {
                return ResourceManager.GetString("NeedEvalItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string no {
            get {
                return ResourceManager.GetString("no", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please ensure that no vacations registered for selected employees before.
        /// </summary>
        public static string NoVacBefore {
            get {
                return ResourceManager.GetString("NoVacBefore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  % Of Basic .
        /// </summary>
        public static string OfBasic {
            get {
                return ResourceManager.GetString("OfBasic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  % Of Variable .
        /// </summary>
        public static string OfVariable {
            get {
                return ResourceManager.GetString("OfVariable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please register one employee at least to proceed.
        /// </summary>
        public static string OneEmpAtLeast {
            get {
                return ResourceManager.GetString("OneEmpAtLeast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        public static string Other {
            get {
                return ResourceManager.GetString("Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Benefits.
        /// </summary>
        public static string OtherBenefits {
            get {
                return ResourceManager.GetString("OtherBenefits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other Vacation.
        /// </summary>
        public static string OtherVacation {
            get {
                return ResourceManager.GetString("OtherVacation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overtime.
        /// </summary>
        public static string Overtime {
            get {
                return ResourceManager.GetString("Overtime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payslip Number.
        /// </summary>
        public static string Payslip {
            get {
                return ResourceManager.GetString("Payslip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payslip Payment Number.
        /// </summary>
        public static string PayslipPayment {
            get {
                return ResourceManager.GetString("PayslipPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Penalty.
        /// </summary>
        public static string Penalty {
            get {
                return ResourceManager.GetString("Penalty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The value is unknown, please enter value, or be sure that employee&apos;s day value is registered.
        /// </summary>
        public static string PenaltyValue {
            get {
                return ResourceManager.GetString("PenaltyValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this picture.
        /// </summary>
        public static string PicDel {
            get {
                return ResourceManager.GetString("PicDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to edit this picture.
        /// </summary>
        public static string PicEdit {
            get {
                return ResourceManager.GetString("PicEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Picture name aleardy exists, please use another name.
        /// </summary>
        public static string PicExist {
            get {
                return ResourceManager.GetString("PicExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a picture and write a description.
        /// </summary>
        public static string PicSelect {
            get {
                return ResourceManager.GetString("PicSelect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max picture size is 10 megabyte.
        /// </summary>
        public static string PicSize {
            get {
                return ResourceManager.GetString("PicSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        public static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Ratio .
        /// </summary>
        public static string Ratio {
            get {
                return ResourceManager.GetString("Ratio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reward.
        /// </summary>
        public static string Reward {
            get {
                return ResourceManager.GetString("Reward", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string save {
            get {
                return ResourceManager.GetString("save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save employee data first.
        /// </summary>
        public static string SaveEmpFirst {
            get {
                return ResourceManager.GetString("SaveEmpFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Secondary Payslip.
        /// </summary>
        public static string SecondaryPayslip {
            get {
                return ResourceManager.GetString("SecondaryPayslip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select shifts correctly.
        /// </summary>
        public static string SelectShift {
            get {
                return ResourceManager.GetString("SelectShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shift Replacement Add.
        /// </summary>
        public static string ShiftReplaceAdd {
            get {
                return ResourceManager.GetString("ShiftReplaceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is a registered Shift Replacement in this day, do you want to continue ?.
        /// </summary>
        public static string ShiftReplaceExist {
            get {
                return ResourceManager.GetString("ShiftReplaceExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shift Replacement withdraw.
        /// </summary>
        public static string ShiftReplaceWithDraw {
            get {
                return ResourceManager.GetString("ShiftReplaceWithDraw", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New shifts dates are overlapped with old shifts for employees.
        /// </summary>
        public static string ShiftsOverlapped {
            get {
                return ResourceManager.GetString("ShiftsOverlapped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Still Working.
        /// </summary>
        public static string StillWork {
            get {
                return ResourceManager.GetString("StillWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assembly.
        /// </summary>
        public static string txtAssembly {
            get {
                return ResourceManager.GetString("txtAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Divorced.
        /// </summary>
        public static string txtDivorced {
            get {
                return ResourceManager.GetString("txtDivorced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Drawer .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Female.
        /// </summary>
        public static string txtFemale {
            get {
                return ResourceManager.GetString("txtFemale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fortnightly.
        /// </summary>
        public static string txtFortnightly {
            get {
                return ResourceManager.GetString("txtFortnightly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From Date .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full Access.
        /// </summary>
        public static string txtFullAccess {
            get {
                return ResourceManager.GetString("txtFullAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Hour .
        /// </summary>
        public static string txtHour {
            get {
                return ResourceManager.GetString("txtHour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hourly.
        /// </summary>
        public static string txtHourly {
            get {
                return ResourceManager.GetString("txtHourly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Limited Access.
        /// </summary>
        public static string txtLimitedAccess {
            get {
                return ResourceManager.GetString("txtLimitedAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Male.
        /// </summary>
        public static string txtMale {
            get {
                return ResourceManager.GetString("txtMale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Married.
        /// </summary>
        public static string txtMarried {
            get {
                return ResourceManager.GetString("txtMarried", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monthly.
        /// </summary>
        public static string txtMonthly {
            get {
                return ResourceManager.GetString("txtMonthly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access Denied.
        /// </summary>
        public static string txtNoAccess {
            get {
                return ResourceManager.GetString("txtNoAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        public static string txtOther {
            get {
                return ResourceManager.GetString("txtOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Per Task.
        /// </summary>
        public static string txtPerTask {
            get {
                return ResourceManager.GetString("txtPerTask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Single.
        /// </summary>
        public static string txtSingle {
            get {
                return ResourceManager.GetString("txtSingle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System Administrator.
        /// </summary>
        public static string txtSysAdmin {
            get {
                return ResourceManager.GetString("txtSysAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To Date .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Annual.
        /// </summary>
        public static string txtVacAnnual {
            get {
                return ResourceManager.GetString("txtVacAnnual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Casual.
        /// </summary>
        public static string txtVacCasual {
            get {
                return ResourceManager.GetString("txtVacCasual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weekly.
        /// </summary>
        public static string txtWeekly {
            get {
                return ResourceManager.GetString("txtWeekly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Widowed.
        /// </summary>
        public static string txtWidowed {
            get {
                return ResourceManager.GetString("txtWidowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vacations.
        /// </summary>
        public static string Vacations {
            get {
                return ResourceManager.GetString("Vacations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End date should be larger than start date.
        /// </summary>
        public static string ValAbsenceDats {
            get {
                return ResourceManager.GetString("ValAbsenceDats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter end date.
        /// </summary>
        public static string ValAbsenceEndDate {
            get {
                return ResourceManager.GetString("ValAbsenceEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is an absence for the employee in this period.
        /// </summary>
        public static string ValAbsenceExist {
            get {
                return ResourceManager.GetString("ValAbsenceExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter start date.
        /// </summary>
        public static string ValAbsenceStartDate {
            get {
                return ResourceManager.GetString("ValAbsenceStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter amount.
        /// </summary>
        public static string ValAmount {
            get {
                return ResourceManager.GetString("ValAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter benefit name.
        /// </summary>
        public static string ValBenefitName {
            get {
                return ResourceManager.GetString("ValBenefitName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter company insurance share ratio.
        /// </summary>
        public static string ValCompanyInsurance {
            get {
                return ResourceManager.GetString("ValCompanyInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter date.
        /// </summary>
        public static string ValDate {
            get {
                return ResourceManager.GetString("ValDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End date should be larger than start date.
        /// </summary>
        public static string ValDates {
            get {
                return ResourceManager.GetString("ValDates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter delay period.
        /// </summary>
        public static string ValDelayPeriod {
            get {
                return ResourceManager.GetString("ValDelayPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Duration.
        /// </summary>
        public static string ValDuration {
            get {
                return ResourceManager.GetString("ValDuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select employee.
        /// </summary>
        public static string ValEmp {
            get {
                return ResourceManager.GetString("ValEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter group name.
        /// </summary>
        public static string ValGroupName {
            get {
                return ResourceManager.GetString("ValGroupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter job name.
        /// </summary>
        public static string ValJobName {
            get {
                return ResourceManager.GetString("ValJobName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Last salary paid to the employee was till .
        /// </summary>
        public static string ValLastPaySlip {
            get {
                return ResourceManager.GetString("ValLastPaySlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter leave work date.
        /// </summary>
        public static string ValLeaveDate {
            get {
                return ResourceManager.GetString("ValLeaveDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The employee already left work.
        /// </summary>
        public static string ValLeaveOut {
            get {
                return ResourceManager.GetString("ValLeaveOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is a mission for the employee in this period.
        /// </summary>
        public static string ValMissionExist {
            get {
                return ResourceManager.GetString("ValMissionExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter overtime date.
        /// </summary>
        public static string ValOvertimeDate {
            get {
                return ResourceManager.GetString("ValOvertimeDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter overtime period.
        /// </summary>
        public static string ValOvertimePeriod {
            get {
                return ResourceManager.GetString("ValOvertimePeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter penalty date.
        /// </summary>
        public static string ValPnltDate {
            get {
                return ResourceManager.GetString("ValPnltDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is a penalty for the employee in this day, do you want to contiunue.
        /// </summary>
        public static string ValPnltyExist {
            get {
                return ResourceManager.GetString("ValPnltyExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter work return date.
        /// </summary>
        public static string ValreturnDate {
            get {
                return ResourceManager.GetString("ValreturnDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The employee already in work.
        /// </summary>
        public static string ValReturnIn {
            get {
                return ResourceManager.GetString("ValReturnIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter reward date.
        /// </summary>
        public static string ValRwrdDate {
            get {
                return ResourceManager.GetString("ValRwrdDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select time table correctly.
        /// </summary>
        public static string ValTimeTable {
            get {
                return ResourceManager.GetString("ValTimeTable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End date should be larger than start date.
        /// </summary>
        public static string ValVacationDates {
            get {
                return ResourceManager.GetString("ValVacationDates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter end date.
        /// </summary>
        public static string ValVacationEndDate {
            get {
                return ResourceManager.GetString("ValVacationEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is a vacation for the employee in this period.
        /// </summary>
        public static string ValVacationExist {
            get {
                return ResourceManager.GetString("ValVacationExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter vacation name.
        /// </summary>
        public static string ValVacationName {
            get {
                return ResourceManager.GetString("ValVacationName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter start date.
        /// </summary>
        public static string ValVacationStartDate {
            get {
                return ResourceManager.GetString("ValVacationStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select vacation type.
        /// </summary>
        public static string ValVacType {
            get {
                return ResourceManager.GetString("ValVacType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ratio of Variable.
        /// </summary>
        public static string VariableRation {
            get {
                return ResourceManager.GetString("VariableRation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day Off.
        /// </summary>
        public static string Weekend {
            get {
                return ResourceManager.GetString("Weekend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string yes {
            get {
                return ResourceManager.GetString("yes", resourceCulture);
            }
        }
    }
}
