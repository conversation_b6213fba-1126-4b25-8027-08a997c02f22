﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Faccount" xml:space="preserve">
    <value>Account: </value>
  </data>
  <data name="Fcat" xml:space="preserve">
    <value>Category: </value>
  </data>
  <data name="FcatAll" xml:space="preserve">
    <value>Category:All </value>
  </data>
  <data name="Fcomp" xml:space="preserve">
    <value>Group: </value>
  </data>
  <data name="FcompAll" xml:space="preserve">
    <value>Group: All </value>
  </data>
  <data name="FcostCenter" xml:space="preserve">
    <value>Cost Center: </value>
  </data>
  <data name="Fcustomer" xml:space="preserve">
    <value>Customer: </value>
  </data>
  <data name="FcustomerAll" xml:space="preserve">
    <value>Customer:All </value>
  </data>
  <data name="FcustomList" xml:space="preserve">
    <value>Custom Accounts List: </value>
  </data>
  <data name="FDate" xml:space="preserve">
    <value>Date: </value>
  </data>
  <data name="FdateBefore" xml:space="preserve">
    <value>Before Date: </value>
  </data>
  <data name="FFrom" xml:space="preserve">
    <value> from </value>
  </data>
  <data name="FfromDate" xml:space="preserve">
    <value>from date: </value>
  </data>
  <data name="FitemName" xml:space="preserve">
    <value>Item Name: </value>
  </data>
  <data name="FitemsAll" xml:space="preserve">
    <value>Item Name: All </value>
  </data>
  <data name="Fstore" xml:space="preserve">
    <value>Store: </value>
  </data>
  <data name="FstoreAll" xml:space="preserve">
    <value>Store Name:All </value>
  </data>
  <data name="FTo" xml:space="preserve">
    <value> to </value>
  </data>
  <data name="FtoDate" xml:space="preserve">
    <value>to date: </value>
  </data>
  <data name="Fvendor" xml:space="preserve">
    <value>Vendor: </value>
  </data>
  <data name="FvendorAll" xml:space="preserve">
    <value>Vendor:All </value>
  </data>
  <data name="path" xml:space="preserve">
    <value>Please set a right path to reports designs from Settings Screen.</value>
  </data>
  <data name="path2" xml:space="preserve">
    <value>After finishing report design, you must save it without changing report name, to folder</value>
  </data>
  <data name="rpt_Acc_Account_CostCenters" xml:space="preserve">
    <value>Account's Cost Centers Balances</value>
  </data>
  <data name="rpt_Acc_Balance" xml:space="preserve">
    <value>Balance Sheet</value>
  </data>
  <data name="rpt_Acc_CostCenterTotalBalances" xml:space="preserve">
    <value>Cost Centers Total Balances</value>
  </data>
  <data name="rpt_Acc_CostCenter_AccDetails" xml:space="preserve">
    <value>Cost Centers Balances</value>
  </data>
  <data name="rpt_Acc_CustomAccListDetails" xml:space="preserve">
    <value>Cutom Accounts List Balances</value>
  </data>
  <data name="rpt_Acc_Income" xml:space="preserve">
    <value>Income Statement</value>
  </data>
  <data name="rpt_Acc_PR_AccountDetails" xml:space="preserve">
    <value>Detailed Statement of Account</value>
  </data>
  <data name="rpt_Acc_PR_AccountsBalances" xml:space="preserve">
    <value>Vendors Balances</value>
  </data>
  <data name="rpt_Acc_SL_AccountDetails" xml:space="preserve">
    <value>Detailed Statement of Account</value>
  </data>
  <data name="rpt_Acc_SL_AccountsBalances" xml:space="preserve">
    <value>Customers Balances</value>
  </data>
  <data name="rpt_IC_ItemOpenInOutClose" xml:space="preserve">
    <value>Items IN and Out Transactions</value>
  </data>
  <data name="rpt_IC_ItemsExpired" xml:space="preserve">
    <value>Items Expires</value>
  </data>
  <data name="rpt_IC_ItemsMaxSell" xml:space="preserve">
    <value>Most Sold Items</value>
  </data>
  <data name="rpt_IC_ItemsMinSell" xml:space="preserve">
    <value>Least Sold Items</value>
  </data>
  <data name="rpt_IC_ItemsNotSold" xml:space="preserve">
    <value>Items Not Sold</value>
  </data>
  <data name="rpt_IC_ItemsOpenBalance" xml:space="preserve">
    <value>Open Balances</value>
  </data>
  <data name="rpt_IC_ItemsQty" xml:space="preserve">
    <value>Items Balances</value>
  </data>
  <data name="rpt_IC_ItemsReorder" xml:space="preserve">
    <value>Items On Reorder</value>
  </data>
  <data name="rpt_IC_ItemsTotals" xml:space="preserve">
    <value>Items Total Transactions</value>
  </data>
  <data name="rpt_IC_ItemTransactions" xml:space="preserve">
    <value>Full Item Transactions Details </value>
  </data>
  <data name="rpt_IC_ItemTransactionsDetails" xml:space="preserve">
    <value>Items Stocktaking</value>
  </data>
  <data name="rpt_IC_SoldItemsCost" xml:space="preserve">
    <value>Sold Items Cost</value>
  </data>
  <data name="rpt_ItemPriceChangings" xml:space="preserve">
    <value>Changes of Items Prices </value>
  </data>
  <data name="rpt_PR_InvoicesHeaders" xml:space="preserve">
    <value>Total Purchase Invoices</value>
  </data>
  <data name="rpt_PR_ItemsPurchases" xml:space="preserve">
    <value>Items Purchases Qty and Balances</value>
  </data>
  <data name="rpt_PR_ItemsReturns" xml:space="preserve">
    <value>Items Purchases Return Qty and Balances</value>
  </data>
  <data name="rpt_PR_ReturnHeaders" xml:space="preserve">
    <value>Total Purchase Return Invoices</value>
  </data>
  <data name="rpt_SL_InvoicesHeaders" xml:space="preserve">
    <value>Total Sell Invoices</value>
  </data>
  <data name="rpt_SL_ItemsReturn" xml:space="preserve">
    <value>Items Sales Return Qty and Balances</value>
  </data>
  <data name="rpt_SL_ItemsSales" xml:space="preserve">
    <value>Items Sales Qty and Balances</value>
  </data>
  <data name="rpt_SL_ItemTrade" xml:space="preserve">
    <value>Item Profit/Loss</value>
  </data>
  <data name="rpt_SL_ReturnHeaders" xml:space="preserve">
    <value>Total Sell Return Invoices</value>
  </data>
  <data name="ValAccount" xml:space="preserve">
    <value>Please select account</value>
  </data>
  <data name="ValCostCenter" xml:space="preserve">
    <value>Please select cost center</value>
  </data>
  <data name="ValCustomer" xml:space="preserve">
    <value>Please select customer</value>
  </data>
  <data name="ValCustomList" xml:space="preserve">
    <value>Please select custom accounts list</value>
  </data>
  <data name="ValDateFromTo" xml:space="preserve">
    <value>Please select start and end dates</value>
  </data>
  <data name="ValItem" xml:space="preserve">
    <value>Please select Item</value>
  </data>
  <data name="ValStore" xml:space="preserve">
    <value>Please select Store</value>
  </data>
  <data name="ValVendor" xml:space="preserve">
    <value>Please select vendor</value>
  </data>
  <data name="Credit" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="Debit" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="MsgPrv" xml:space="preserve">
    <value>Sorry, you have no privilege to open this report</value>
  </data>
  <data name="rpt_Acc_CustomAccListBalances" xml:space="preserve">
    <value>Custom Account Lists Total Balances</value>
  </data>
  <data name="rpt_PR_VendorItemsPurchases" xml:space="preserve">
    <value>Total Purchased Items From Vendor</value>
  </data>
  <data name="rpt_SL_CustomerItemsSales" xml:space="preserve">
    <value>Items Sold To Customers</value>
  </data>
  <data name="Fuser" xml:space="preserve">
    <value> User: </value>
  </data>
  <data name="FuserAll" xml:space="preserve">
    <value> User: All </value>
  </data>
  <data name="FsalesEmp" xml:space="preserve">
    <value> Sales Employee: </value>
  </data>
  <data name="FsalesEmpAll" xml:space="preserve">
    <value> Sales Employee: All </value>
  </data>
  <data name="rpt_HR_SalesEmpCommission" xml:space="preserve">
    <value>Employee Sales Commission per Target and Payments</value>
  </data>
  <data name="rpt_IC_ItemsQtyDetails" xml:space="preserve">
    <value>Items Balances in Details</value>
  </data>
  <data name="Batch" xml:space="preserve">
    <value> Batch: </value>
  </data>
  <data name="rpt_IC_ItemsQtyWithPrices" xml:space="preserve">
    <value>Inventory Valuation</value>
  </data>
  <data name="rpt_HR_DeliveryCommision" xml:space="preserve">
    <value>Employee Commission for Outgoing bill items</value>
  </data>
  <data name="valSalesEmp" xml:space="preserve">
    <value>Please select employee</value>
  </data>
  <data name="rpt_HR_SalesCommision" xml:space="preserve">
    <value>Sales Invoices Direct Commisions</value>
  </data>
  <data name="rpt_Acc_DailyIncome" xml:space="preserve">
    <value>Daily Income</value>
  </data>
  <data name="rpt_Acc_DailyPayments" xml:space="preserve">
    <value>Daily Payments</value>
  </data>
  <data name="rpt_SL_CustomerTrans" xml:space="preserve">
    <value>Customer Transactions</value>
  </data>
  <data name="FCustGroup" xml:space="preserve">
    <value>Customer Group: </value>
  </data>
  <data name="FcustGroupAll" xml:space="preserve">
    <value>Customer Group : All </value>
  </data>
  <data name="rpt_SL_CustomerItemsSales_OutTrns" xml:space="preserve">
    <value>Sales &amp; Outgoing Bills</value>
  </data>
  <data name="rpt_HR_ManfCommision" xml:space="preserve">
    <value>Employee Production</value>
  </data>
  <data name="rpt_Acc_PR_AccountsBalancesWithNotes" xml:space="preserve">
    <value>Vendors Balances with Commercial papers</value>
  </data>
  <data name="rpt_Acc_SL_AccountsBalancesWithNotes" xml:space="preserve">
    <value>Customers Balances with Commercial papers</value>
  </data>
  <data name="Loss" xml:space="preserve">
    <value>Loss</value>
  </data>
  <data name="Profit" xml:space="preserve">
    <value>Profit</value>
  </data>
  <data name="rpt_IC_ItemsQtyWithSalesPrice" xml:space="preserve">
    <value>Inventory evaluation using sales price</value>
  </data>
  <data name="frm_Acc_AccountDetails" xml:space="preserve">
    <value>Customer Detailed Statment of Account</value>
  </data>
  <data name="frm_Acc_PR_AccountDetails" xml:space="preserve">
    <value>Vendor Detailed Statment of Account</value>
  </data>
  <data name="rpt_IC_ItemTransactionsNoCost" xml:space="preserve">
    <value>Item Transactions Details without cost</value>
  </data>
  <data name="mtrx1" xml:space="preserve">
    <value>Matrix1</value>
  </data>
  <data name="mtrx2" xml:space="preserve">
    <value>Matrix2</value>
  </data>
  <data name="mtrx3" xml:space="preserve">
    <value>Matrix3</value>
  </data>
  <data name="FjoDept" xml:space="preserve">
    <value>Department: </value>
  </data>
  <data name="FjoDeptAll" xml:space="preserve">
    <value>Department : All </value>
  </data>
  <data name="FjoPriority" xml:space="preserve">
    <value>Priority: </value>
  </data>
  <data name="FjoPriorityAll" xml:space="preserve">
    <value>Priority : All </value>
  </data>
  <data name="FjoStatus" xml:space="preserve">
    <value>Status: </value>
  </data>
  <data name="FjoStatusAll" xml:space="preserve">
    <value>Status : All </value>
  </data>
  <data name="FitemType" xml:space="preserve">
    <value>Item Type: </value>
  </data>
  <data name="FitemTypeAll" xml:space="preserve">
    <value>Item Type : All </value>
  </data>
  <data name="frm_SL_JobOrderInv" xml:space="preserve">
    <value>Job Orders Sales</value>
  </data>
  <data name="frm_SL_SalesOrderItems" xml:space="preserve">
    <value>Sales Orders Items</value>
  </data>
  <data name="rpt_HR_SalesInvItemsCommision" xml:space="preserve">
    <value>Employee Commission for Sales Invoices items</value>
  </data>
  <data name="frm_SL_ItemsSalesDetails" xml:space="preserve">
    <value>Items Total Sales &amp; Returns</value>
  </data>
  <data name="frm_SL_ItemsSalesTotals" xml:space="preserve">
    <value>Items Total Sales</value>
  </data>
  <data name="rpt_SL_CustomerItemsSalesReturn" xml:space="preserve">
    <value>Items Returned from Customers</value>
  </data>
  <data name="FInvBook" xml:space="preserve">
    <value>Invoice Book:</value>
  </data>
  <data name="FInvBookAll" xml:space="preserve">
    <value>Invoice Book: All</value>
  </data>
  <data name="FempGroup" xml:space="preserve">
    <value> Employee Group: </value>
  </data>
  <data name="FempGroupAll" xml:space="preserve">
    <value> Employee Group: All </value>
  </data>
  <data name="rpt_IC_ItemOpenInOutCloseQty" xml:space="preserve">
    <value>Items Total Transactions in Stores</value>
  </data>
  <data name="rpt_PR_VendorItemsPurchasesReturns" xml:space="preserve">
    <value>Total Purchases Returns Items To Vendor</value>
  </data>
  <data name="DueEarlierPeriod" xml:space="preserve">
    <value>Earlier </value>
  </data>
  <data name="DueLaterPeriod" xml:space="preserve">
    <value>Later</value>
  </data>
  <data name="Due" xml:space="preserve">
    <value>Due</value>
  </data>
  <data name="frm_MrAllSales" xml:space="preserve">
    <value>Items Marketing upon Customer Group</value>
  </data>
  <data name="frm_HR_AllPays" xml:space="preserve">
    <value>Payslips</value>
  </data>
  <data name="rpt_SL_CustomerItemsSalesReturns" xml:space="preserve">
    <value>Sales and returns items detailed</value>
  </data>
  <data name="frm_ItemsPr_and_Sl" xml:space="preserve">
    <value>Sold and Purchased Items</value>
  </data>
  <data name="frm_SalesOrderAndAchievement" xml:space="preserve">
    <value>Sales Orders And Achievements</value>
  </data>
  <data name="FvenGroup" xml:space="preserve">
    <value>Vendor Group:</value>
  </data>
  <data name="FvenGroupAll" xml:space="preserve">
    <value>Vendor Group : All</value>
  </data>
  <data name="frm_PR_ContractorExtract" xml:space="preserve">
    <value>Contractor Extract</value>
  </data>
  <data name="frm_SL_SalesOrderItemsAndBalance" xml:space="preserve">
    <value>Sales Order Items and Current Balances</value>
  </data>
  <data name="frm_ManfItems" xml:space="preserve">
    <value>Job Orders Products &amp; Raws</value>
  </data>
  <data name="frm_PurchaseOrderAndAchievement" xml:space="preserve">
    <value>Purchase Orders And Achievements</value>
  </data>
  <data name="rpt_PR_VendorItemsPurchases_InTrns" xml:space="preserve">
    <value>Purchases &amp; Receiving Bills</value>
  </data>
  <data name="frm_HR_Insurance" xml:space="preserve">
    <value>Employees Insurance</value>
  </data>
  <data name="frm_HR_AllExpectedPays" xml:space="preserve">
    <value>Expected Salaries</value>
  </data>
  <data name="frm_SL_Warranty" xml:space="preserve">
    <value>Items Warranty</value>
  </data>
  <data name="frm_HR_VacationBal" xml:space="preserve">
    <value>Vacations Balances</value>
  </data>
  <data name="frm_IC_ItemsQtyH" xml:space="preserve">
    <value>Items Balances Horizontal</value>
  </data>
  <data name="frm_IC_ItemsTurnOver" xml:space="preserve">
    <value>Items Turnover</value>
  </data>
  <data name="rpt_DelegatesSales" xml:space="preserve">
    <value>DelegatesSalesReport</value>
  </data>
  <data name="rpt_PercentageOfCapitalInDeferredInvoices" xml:space="preserve">
    <value>Percentage Of Capital In Deferred Invoices</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value />
  </data>
  <data name="rpt_SL_DeliveryOfficialsSales" xml:space="preserve">
    <value>Delivery Officials Sales</value>
  </data>
  <data name="rpt_IC_ItemsMinLevel" xml:space="preserve">
    <value>Items On Min Level</value>
  </data>
  <data name="rpt_SL_Profit_Loss" xml:space="preserve">
    <value>SellInvoiceProfit_Loss</value>
  </data>
  <data name="rpt_Acc_DailyPaymentsAndIncomePayments" xml:space="preserve">
    <value>DailyPayments And IncomePayments</value>
  </data>
  <data name="rpt_PR_InvoicesDiscountTaxHeaders" xml:space="preserve">
    <value>Sample 41 taxes</value>
  </data>
  <data name="rpt_Acc_Payments" xml:space="preserve">
    <value>Payments</value>
  </data>
  <data name="rpt_ACC_SubLedger" xml:space="preserve">
    <value>Sub Ledger</value>
  </data>
  <data name="frm_HR_Att" xml:space="preserve">
    <value>Attendence Report</value>
  </data>
  <data name="frm_HR_Vacations" xml:space="preserve">
    <value>Vacations Report</value>
  </data>
  <data name="frm_SalesRep_DaySummary" xml:space="preserve">
    <value>Sales Rep Day Summary</value>
  </data>
  <data name="frm_Customers_Debit" xml:space="preserve">
    <value>Customers Debit</value>
  </data>
  <data name="frm_SL_Car_Weights" xml:space="preserve">
    <value>Cars Weights</value>
  </data>
  <data name="CustomerVisits" xml:space="preserve">
    <value>Customer visits report</value>
  </data>
  <data name="rpt_IC_Item_In_Out_Balance" xml:space="preserve">
    <value>Total In &amp; Out Item Operations</value>
  </data>
  <data name="frm_IC_ItemsQtyHNoCost" xml:space="preserve">
    <value>Item Quantity Report Hor. Without Cost</value>
  </data>
  <data name="frm_SL_ItemsNetSalesDetails" xml:space="preserve">
    <value>Net Detailed Sale Report</value>
  </data>
  <data name="frm_SL_ItemsNetPurchaseDetails" xml:space="preserve">
    <value>Net Purchase and Purchase Return Report</value>
  </data>
  <data name="rpt_SL_Invoices_Due" xml:space="preserve">
    <value>Due Date of Sales Invoices</value>
  </data>
  <data name="frm_SL_DelegatesSales_ItemCategory" xml:space="preserve">
    <value>Delegates Sales per Item Category</value>
  </data>
  <data name="rpt_InvoiceDetails" xml:space="preserve">
    <value>Invoice Details</value>
  </data>
</root>