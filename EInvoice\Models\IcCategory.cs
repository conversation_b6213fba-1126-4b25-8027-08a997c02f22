﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class IcCategory
    {
        public int CategoryId { get; set; }
        public string CategoryNameAr { get; set; }
        public string CategoryNameEn { get; set; }
        public string CatNumber { get; set; }
        public int Level { get; set; }
        public int? ParentId { get; set; }
        public int? SellAcc { get; set; }
        public int? Cogsacc { get; set; }
        public int? SellReturnAcc { get; set; }
        public int? InvAcc { get; set; }
        public int? PurchaseAcc { get; set; }
        public int? PurchaseReturnAcc { get; set; }
        public int? OpenInventoryAcc { get; set; }
        public int? CloseInventoryAcc { get; set; }
        public string Region { get; set; }
        public string Subegion { get; set; }
        public string StreetName { get; set; }
        public int? StreetNo { get; set; }
        public string Notes { get; set; }
        public string Code2 { get; set; }
        public int? ExpensesAccount { get; set; }
    }
}
