﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public class RecievedDocument
    {
        public string uuid { get; set; }
        public string publicUrl { get; set; }
        public string typeName { get; set; }
        public string issuerName { get; set; }
        public string issuerId { get; set; }
        public string receiverType { get; set; }
        public DateTime dateTimeReceived { get; set; }
        public DateTime dateTimeIssued { get; set; }
        public decimal totalSales { get; set; }
        public decimal totalDiscount { get; set; }
        public decimal netAmount { get; set; }
        public decimal total { get; set; }
        public string status { get; set; }
        public string documentStatusReason { get; set; }
        public string documentTypeNamePrimaryLang { get; set; }
        public string documentTypeNameSecondaryLang { get; set; }


    }

    public class MetaData
    {
        public string continuationToken { get; set; } 
    }


    public class ReceievedDocumentsResponse
    {
        public List<RecievedDocument> result { get; set; }
        public MetaData metadata { get; set; }
    }
}
