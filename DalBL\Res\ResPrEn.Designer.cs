﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResPrEn {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResPrEn() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResPrEn", typeof(ResPrEn).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detailed Statement of Account.
        /// </summary>
        public static string accDetail {
            get {
                return ResourceManager.GetString("accDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default accounts settings for this group&apos;s vendors.
        /// </summary>
        public static string defaultCustGroupAcc {
            get {
                return ResourceManager.GetString("defaultCustGroupAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fine Number.
        /// </summary>
        public static string FineNumber {
            get {
                return ResourceManager.GetString("FineNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to update customers price levels with the same increasing ratio ?.
        /// </summary>
        public static string MsgAskChangePList {
            get {
                return ResourceManager.GetString("MsgAskChangePList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this vendor ?.
        /// </summary>
        public static string MsgAskConfirmDeleteVen {
            get {
                return ResourceManager.GetString("MsgAskConfirmDeleteVen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to create Store Receving Bill Now ?.
        /// </summary>
        public static string MsgAskCreateInTrns {
            get {
                return ResourceManager.GetString("MsgAskCreateInTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to create Store Outgoing Bill Now ?.
        /// </summary>
        public static string MsgAskCreateOutTrns {
            get {
                return ResourceManager.GetString("MsgAskCreateOutTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Are you sure you want to delete .
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to print barcode stickers for Purchase Invoice.
        /// </summary>
        public static string MsgAskPrintBarCode {
            get {
                return ResourceManager.GetString("MsgAskPrintBarCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please save invoice first.
        /// </summary>
        public static string MsgAskToSaveInv {
            get {
                return ResourceManager.GetString("MsgAskToSaveInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code 1 Already Exist.
        /// </summary>
        public static string MsgChkCodeDuplication {
            get {
                return ResourceManager.GetString("MsgChkCodeDuplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You made some changes, do you want to save.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted Successfully.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this vendors group.
        /// </summary>
        public static string MsgDelCustGroup {
            get {
                return ResourceManager.GetString("MsgDelCustGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this vendos group, there&apos;s some vendors related to it.
        /// </summary>
        public static string MsgDelCustGroup2 {
            get {
                return ResourceManager.GetString("MsgDelCustGroup2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete document.
        /// </summary>
        public static string MsgDeleteInv {
            get {
                return ResourceManager.GetString("MsgDeleteInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please delete all vendor journals first .
        /// </summary>
        public static string MsgDeleteJornalsFirst {
            get {
                return ResourceManager.GetString("MsgDeleteJornalsFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this acount, there&apos;s other accounts linked to it.
        /// </summary>
        public static string msgDelLinked {
            get {
                return ResourceManager.GetString("msgDelLinked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to delete row ?.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter vendor code.
        /// </summary>
        public static string MsgEnterVendorCode {
            get {
                return ResourceManager.GetString("MsgEnterVendorCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter vendor name .
        /// </summary>
        public static string MsgEnterVendorName {
            get {
                return ResourceManager.GetString("MsgEnterVendorName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to some data are incorrect.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t change this account, you have to delete it&apos;s journals first.
        /// </summary>
        public static string msgLinkAcc {
            get {
                return ResourceManager.GetString("msgLinkAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t set open balance for this account.
        /// </summary>
        public static string msgLinkAccOpen {
            get {
                return ResourceManager.GetString("msgLinkAccOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase order is not approved yet.
        /// </summary>
        public static string msgMustApprovePrOrder {
            get {
                return ResourceManager.GetString("msgMustApprovePrOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase quotation is not approved yet.
        /// </summary>
        public static string msgMustApprovePrQuote {
            get {
                return ResourceManager.GetString("msgMustApprovePrQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This name already exists.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This vendor has no account.
        /// </summary>
        public static string MsgNoAccount {
            get {
                return ResourceManager.GetString("MsgNoAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no enough Qty of some items, Continue ?.
        /// </summary>
        public static string MsgNoEnoughQty_continue {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty_continue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This number already exists.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t edit or delete posted Bill.
        /// </summary>
        public static string MsgPostedBill {
            get {
                return ResourceManager.GetString("MsgPostedBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to edit record.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to add new record.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved Successfully.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor code must be larger than 0.
        /// </summary>
        public static string MsgValidateVendorCode {
            get {
                return ResourceManager.GetString("MsgValidateVendorCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can`t allow to change preformaType.
        /// </summary>
        public static string preformaType {
            get {
                return ResourceManager.GetString("preformaType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Must Choose InvoicesAdvanced.
        /// </summary>
        public static string PreInvoicesAdvanced {
            get {
                return ResourceManager.GetString("PreInvoicesAdvanced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount.
        /// </summary>
        public static string txt_Discount {
            get {
                return ResourceManager.GetString("txt_Discount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay.
        /// </summary>
        public static string txt_Paid {
            get {
                return ResourceManager.GetString("txt_Paid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchases Expenses.
        /// </summary>
        public static string txt_PurchaseExpenses {
            get {
                return ResourceManager.GetString("txt_PurchaseExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax.
        /// </summary>
        public static string txt_Tax {
            get {
                return ResourceManager.GetString("txt_Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assembly.
        /// </summary>
        public static string txtAssembly {
            get {
                return ResourceManager.GetString("txtAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete General Vendor.
        /// </summary>
        public static string txtCantDeleteVen {
            get {
                return ResourceManager.GetString("txtCantDeleteVen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to edit General Vendor.
        /// </summary>
        public static string txtCantEditVen {
            get {
                return ResourceManager.GetString("txtCantEditVen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category: .
        /// </summary>
        public static string txtCategory {
            get {
                return ResourceManager.GetString("txtCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comapny: .
        /// </summary>
        public static string txtComapny {
            get {
                return ResourceManager.GetString("txtComapny", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue ?.
        /// </summary>
        public static string txtContinue {
            get {
                return ResourceManager.GetString("txtContinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit.
        /// </summary>
        public static string txtCredit {
            get {
                return ResourceManager.GetString("txtCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        public static string txtCustomer {
            get {
                return ResourceManager.GetString("txtCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dealer Name.
        /// </summary>
        public static string txtDealerNm {
            get {
                return ResourceManager.GetString("txtDealerNm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        public static string txtDebit {
            get {
                return ResourceManager.GetString("txtDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to D R :.
        /// </summary>
        public static string txtDiscRatio {
            get {
                return ResourceManager.GetString("txtDiscRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to D V :.
        /// </summary>
        public static string txtDiscValue {
            get {
                return ResourceManager.GetString("txtDiscValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Drawer .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expenses :.
        /// </summary>
        public static string txtExpenses {
            get {
                return ResourceManager.GetString("txtExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From Date .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        public static string txtInvDate {
            get {
                return ResourceManager.GetString("txtInvDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Number.
        /// </summary>
        public static string txtInvNumber {
            get {
                return ResourceManager.GetString("txtInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment Method.
        /// </summary>
        public static string txtInvPayMethod {
            get {
                return ResourceManager.GetString("txtInvPayMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item: .
        /// </summary>
        public static string txtItem {
            get {
                return ResourceManager.GetString("txtItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Movement.
        /// </summary>
        public static string txtItemMovement {
            get {
                return ResourceManager.GetString("txtItemMovement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Balance.
        /// </summary>
        public static string txtItemsBalance {
            get {
                return ResourceManager.GetString("txtItemsBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Best-Selling Items.
        /// </summary>
        public static string txtItemsBestSell {
            get {
                return ResourceManager.GetString("txtItemsBestSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Least-Selling Items.
        /// </summary>
        public static string txtItemsLeastSell {
            get {
                return ResourceManager.GetString("txtItemsLeastSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items On Reorder.
        /// </summary>
        public static string txtItemsReorder {
            get {
                return ResourceManager.GetString("txtItemsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Purchases.
        /// </summary>
        public static string txtItemTotalPurchases {
            get {
                return ResourceManager.GetString("txtItemTotalPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Purchases Returns.
        /// </summary>
        public static string txtItemTotalPurchasesReturns {
            get {
                return ResourceManager.GetString("txtItemTotalPurchasesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Sales.
        /// </summary>
        public static string txtItemTotalSales {
            get {
                return ResourceManager.GetString("txtItemTotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Total Sales Returns.
        /// </summary>
        public static string txtItemTotalSalesReturn {
            get {
                return ResourceManager.GetString("txtItemTotalSalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Last Prices.
        /// </summary>
        public static string txtLastPPrices {
            get {
                return ResourceManager.GetString("txtLastPPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Last Vendor Prices.
        /// </summary>
        public static string txtLastVendorPPrices {
            get {
                return ResourceManager.GetString("txtLastVendorPPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net :.
        /// </summary>
        public static string txtNet {
            get {
                return ResourceManager.GetString("txtNet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        public static string txtNotes {
            get {
                return ResourceManager.GetString("txtNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Open Balance .
        /// </summary>
        public static string txtOpenBalance {
            get {
                return ResourceManager.GetString("txtOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paid :.
        /// </summary>
        public static string txtPaid {
            get {
                return ResourceManager.GetString("txtPaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase invoice number.
        /// </summary>
        public static string txtPRInvoiceNumber {
            get {
                return ResourceManager.GetString("txtPRInvoiceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Return Invoice Number.
        /// </summary>
        public static string txtPRReturnNumber {
            get {
                return ResourceManager.GetString("txtPRReturnNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remains.
        /// </summary>
        public static string txtRemains {
            get {
                return ResourceManager.GetString("txtRemains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial.
        /// </summary>
        public static string txtSerial {
            get {
                return ResourceManager.GetString("txtSerial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Store: .
        /// </summary>
        public static string txtStore {
            get {
                return ResourceManager.GetString("txtStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax R:.
        /// </summary>
        public static string txtTaxRatio {
            get {
                return ResourceManager.GetString("txtTaxRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax V:.
        /// </summary>
        public static string txtTaxValue {
            get {
                return ResourceManager.GetString("txtTaxValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To Date .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total :.
        /// </summary>
        public static string txtTotal {
            get {
                return ResourceManager.GetString("txtTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select discount.
        /// </summary>
        public static string txtValidateDiscount {
            get {
                return ResourceManager.GetString("txtValidateDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please record invoice number.
        /// </summary>
        public static string txtValidateInvNumber {
            get {
                return ResourceManager.GetString("txtValidateInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Item.
        /// </summary>
        public static string txtValidateItem {
            get {
                return ResourceManager.GetString("txtValidateItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchased qty and store qty more than max limit of the item.
        /// </summary>
        public static string txtValidateItemMaxLimit {
            get {
                return ResourceManager.GetString("txtValidateItemMaxLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount ratio must be less than 100.
        /// </summary>
        public static string txtValidateMaxDiscount {
            get {
                return ResourceManager.GetString("txtValidateMaxDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter at least one item to the invoice.
        /// </summary>
        public static string txtValidateNoRows {
            get {
                return ResourceManager.GetString("txtValidateNoRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter post date.
        /// </summary>
        public static string txtValidatePostDate {
            get {
                return ResourceManager.GetString("txtValidatePostDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase price must be larger than 0.
        /// </summary>
        public static string txtValidatePPrice {
            get {
                return ResourceManager.GetString("txtValidatePPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty must be larger than 0.
        /// </summary>
        public static string txtValidateQty {
            get {
                return ResourceManager.GetString("txtValidateQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select unit of measure.
        /// </summary>
        public static string txtValidateUom {
            get {
                return ResourceManager.GetString("txtValidateUom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor exceeds max credit.
        /// </summary>
        public static string txtValidateVendorMaxCredit {
            get {
                return ResourceManager.GetString("txtValidateVendorMaxCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor: .
        /// </summary>
        public static string txtVendor {
            get {
                return ResourceManager.GetString("txtVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Choose Branch.
        /// </summary>
        public static string ValBranch {
            get {
                return ResourceManager.GetString("ValBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor code length cann&apos;t exceed batch length in barcode template.
        /// </summary>
        public static string ValVendorCodeLength {
            get {
                return ResourceManager.GetString("ValVendorCodeLength", resourceCulture);
            }
        }
    }
}
