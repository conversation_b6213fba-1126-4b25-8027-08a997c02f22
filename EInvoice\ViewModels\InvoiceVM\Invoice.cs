﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EInvoice.ViewModels.InvoiceVM
{
    public class Invoice
    {
        public int companyId { get; set; }
        public int storeId { get; set; }
        public string storeName { get; set; }
        public int customerId { get; set; }
        public string customerName { get; set; }
        public DateTime date { get; set; }
        public int? invoiceId { get; set; }
        public string documentType { get; set; } //I - C - D
        public decimal TotalDiscount { get; set; } 
        public List<InvoiceDetail> invoiceDetails { get; set; }
        public List<string> Uuid { get; set; }
        public string invoiceCode { get; set; }

    }
}
