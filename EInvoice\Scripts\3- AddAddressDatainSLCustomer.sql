﻿if COL_LENGTH('SL_Customer','CountryId') is  NULL
BEGIN
alter table  [dbo].[SL_Customer]
Add CountryId int not null DEFAULT(1)
end
go

ALTER TABLE [dbo].[SL_Customer]  WITH NOCHECK ADD  CONSTRAINT [FK_SL_Customer_HR_Country] FOREIGN KEY([CountryId])
REFERENCES [dbo].[HR_Country] ([CountryId])
GO

ALTER TABLE [dbo].[SL_Customer] NOCHECK CONSTRAINT [FK_SL_Customer_HR_Country]
GO

if COL_LENGTH('SL_Customer','Governate') is  NULL
BEGIN
alter table  [dbo].[SL_Customer]
Add Governate nvarchar(100) null
end
go

if COL_LENGTH('SL_Customer','BuildingNumber') is  NULL
BEGIN
alter table  [dbo].[SL_Customer]
Add BuildingNumber nvarchar(100) null
end
go