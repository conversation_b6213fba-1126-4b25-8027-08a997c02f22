using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using EInvoice.Models;
using System.Net.Http.Headers;
using Models_1.ViewModels.InvoiceVM;
using EInvoice.MyHelper;
using Discount = Models_1.ViewModels.Discount;
using EInvoice.BL;
using Microsoft.Extensions.Configuration;
using static EInvoice.MyHelper.Utilities;
using Models_1.ViewModels.ResponseVM.ChangeStatusErrorVM;
using Models_1.ViewModels.ValidationVM;
using Models_1.ViewModels.SignuatureVM;
using Models_1.ViewModels.ChangeDocumentStatusVM;
using Models_1.ViewModels.ResponseVM.GetDocumentVM;
using Models_1.ViewModels.ResponseVM.SubmitDocumentVM;
using Models_1.ViewModels.ResponseVM.GetRecentDocumentsVM;
using Models_1.ViewModels;
using AutoMapper;
using System.IO;
//using EInvoice.ViewModels.ValidateVM;

namespace EInvoice.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class EInvoiceController : ControllerBase
    {
        //ERPEinvoiceContext DB = new ERPEinvoiceContext();
        private readonly ERPEinvoiceContext DB;
        private readonly IMapper mapper;
        string BaseUrl;
        string SignuaturenUrl;
        string publicKey;
        string tokenSerial;
        string libraryPath;
        string TokenName;
        static Service service;
        public static Service Service => service ?? (service = new Service());

        private IConfiguration Configuration;
        public EInvoiceController(IConfiguration _Configuration, ERPEinvoiceContext _DB, IMapper _mapper)
        {
            Configuration = _Configuration;
            BaseUrl = Configuration.GetSection("BaseUrl").Value;
            SignuaturenUrl = Configuration.GetSection("SignuaturenUrl").Value;
            publicKey = Configuration.GetSection("publicKey").Value;
            tokenSerial = Configuration.GetSection("tokenSerial").Value;
            libraryPath = Configuration.GetSection("libraryPath").Value;
            TokenName = Configuration.GetSection("tokenName").Value;
            DB = _DB;
            mapper = _mapper;
        }

        #region CreateDocumnetFirstVersion
        //[HttpGet, Route("Create")]
        //public async Task<ActionResult> Create(/*/*Document EInvoice*/)
        //{

        //    var EInvoice = new Document
        //    {
        //        issuer = new Issuer
        //        {
        //            address = new Address
        //            {
        //                branchID = "1",
        //                country = "EG",
        //                governate = "Cairo",
        //                regionCity = "Nasr City",
        //                street = "580 Clementina Key",
        //                buildingNumber = "Bldg. 0",
        //                postalCode = "68030",
        //                floor = "1",
        //                room = "123",
        //                landmark = "7660 Melody Trail",
        //                additionalInformation = "beside Townhall"
        //            },

        //            type = "B",
        //            id = "*********",
        //            name = "Issuer Company"
        //        },
        //        receiver = new Receiver
        //        {
        //            address = new Address
        //            {
        //                country = "EG",
        //                governate = "Egypt",
        //                regionCity = "Mufazat al Ismlyah",
        //                street = "580 Clementina Key",
        //                buildingNumber = "Bldg. 0",
        //                postalCode = "68030",
        //                floor = "1",
        //                room = "123",
        //                landmark = "7660 Melody Trail",
        //                additionalInformation = "beside Townhall"
        //            },
        //            type = "B",
        //            id = "*********",
        //            name = "Receiver"
        //        },
        //        documentType = "I",
        //        documentTypeVersion = "0.1",
        //        dateTimeIssued = DateTime.UtcNow,
        //        taxpayerActivityCode = "4620",
        //        internalID = "IID1",
        //        purchaseOrderReference = "P-233-A6375",
        //        purchaseOrderDescription = "purchase Order description",
        //        salesOrderReference = "1231",
        //        salesOrderDescription = "Sales Order description",
        //        proformaInvoiceNumber = "SomeValue",
        //        payment = new Payment
        //        {
        //            bankName = "SomeValue",
        //            bankAddress = "SomeValue",
        //            bankAccountNo = "SomeValue",
        //            bankAccountIBAN = "",
        //            swiftCode = "",
        //            terms = "SomeValue"
        //        },
        //        delivery = new Delivery
        //        {
        //            approach = "SomeValue",
        //            packaging = "SomeValue",
        //            //dateValidity = DateTime.UtcNow,
        //            exportPort = "SomeValue",
        //            countryOfOrigin = "EG",
        //            grossWeight = 10.50,
        //            netWeight = 20.50,
        //            terms = "SomeValue"
        //        },
        //        invoiceLines = new List<InvoiceLine>
        //            {
        //                new InvoiceLine
        //                {
        //            description= "Computer1",
        //            itemType= "EGS",
        //            itemCode= "EG-14-1003228",
        //            unitType= "EA",
        //            quantity= 5,
        //            internalCode= "IC0",
        //            salesTotal= 947.00M,
        //            total= 2969.89M,
        //            valueDifference= 7.00M,
        //            totalTaxableFees= 817.42M,
        //            netTotal=880.71M,
        //            itemsDiscount= 5.00M,
        //            unitValue=new UnitValue {
        //                currencySold ="EUR",
        //                amountEGP= 189.40M,
        //                amountSold= 10.00M,
        //                currencyExchangeRate= 18.94M
        //            },
        //            discount=new Discount {
        //                rate= 7,
        //                amount= 66.29M
        //            },
        //            taxableItems=new List<TaxableItem>
        //            {
        //                new TaxableItem
        //                {
        //                     taxType= "T1",
        //                    amount= 272.07M,
        //                    subType= "T1",
        //                    rate= 14.00M
        //                },
        //                 new TaxableItem
        //                {
        //                    taxType= "T2",
        //                    amount= 208.22M,
        //                    subType= "T2",
        //                    rate= 12
        //                },
        //                   new TaxableItem
        //                {
        //                   taxType= "T3",
        //                    amount= 30.00M,
        //                    subType= "T3",
        //                    rate= 0.00M
        //                },

        //               new TaxableItem
        //                {
        //                     taxType= "T4",
        //                    amount= 43.79M,
        //                    subType= "T4",
        //                    rate= 5.00M
        //                },
        //                new TaxableItem
        //                {
        //                      taxType="T5",
        //                    amount=123.30M,
        //                    subType= "T5",
        //                    rate=14.00M
        //                },
        //                 new TaxableItem
        //                {
        //                    taxType= "T6",
        //                    amount= 60.00M,
        //                    subType= "T6",
        //                    rate= 0.00M
        //                },
        //                   new TaxableItem
        //                {
        //                    taxType= "T7",
        //                    amount= 88.07M,
        //                    subType= "T7",
        //                    rate= 10.00M
        //                },
        //                      new TaxableItem
        //                {
        //                    taxType= "T8",
        //                    amount= 123.30M,
        //                    subType= "T8",
        //                    rate= 14.00M
        //                },
        //                new TaxableItem
        //                {
        //                    taxType= "T9",
        //                    amount= 105.69M,
        //                    subType= "T9",
        //                    rate= 12.00M
        //                },
        //                       new TaxableItem
        //                {
        //                    taxType= "T10",
        //                    amount= 88.07M,
        //                    subType= "T10",
        //                    rate= 10.00M
        //                },
        //                                new TaxableItem
        //                {
        //                    taxType= "T11",
        //                    amount= 123.30M,
        //                    subType= "T11",
        //                    rate= 14.00M
        //                },
        //         new TaxableItem
        //                {
        //                    taxType= "T12",
        //                    amount=105.69M,
        //                    subType= "T12",
        //                    rate= 12.00M
        //                },

        //           new TaxableItem
        //                {
        //                    taxType= "T13",
        //                    amount=88.07M,
        //                    subType= "T13",
        //                    rate= 10.00M
        //                },

        //             new TaxableItem
        //                {
        //                    taxType= "T14",
        //                    amount=123.30M,
        //                    subType= "T14",
        //                    rate= 14.00M
        //                },
        //               new TaxableItem
        //                {
        //                    taxType= "T15",
        //                    amount=105.69M,
        //                    subType= "T15",
        //                    rate= 12.00M
        //                },
        //                 new TaxableItem
        //                {
        //                    taxType= "T16",
        //                    amount=88.07M,
        //                    subType= "T16",
        //                    rate= 10.00M
        //                },
        //                new TaxableItem  {
        //                    taxType= "T17",
        //                    amount=88.07M,
        //                    subType= "T17",
        //                    rate= 10.00M
        //                },
        //              new TaxableItem   {
        //                    taxType= "T18",
        //                    amount=123.30M,
        //                    subType= "T18",
        //                    rate= 14.00M
        //                },
        //               new TaxableItem   {
        //                    taxType= "T19",
        //                    amount=105.69M,
        //                    subType= "T19",
        //                    rate= 12.00M
        //                },
        //                new TaxableItem   {
        //                    taxType= "T20",
        //                    amount=88.07M,
        //                    subType= "T20",
        //                    rate= 10.00M
        //                }

        //            }








        //        }
        //            }

        //    };
        //    var Documents = new List<Document>();
        //    Documents.Add(EInvoice);

        //    using (HttpClient client = new HttpClient())
        //    {
        //        client.DefaultRequestHeaders.Authorization =
        //        new AuthenticationHeaderValue("Bearer", "Your Oauth token");


        //        StringContent content = new StringContent(JsonConvert.SerializeObject(Documents), Encoding.UTF8, "application/json");
        //        string endpoint = "https://api.sit.invoicing.eta.gov.eg/api/v1/documentsubmissions";

        //        using (var Response = await client.PostAsync(endpoint, content))
        //        {
        //            string result = await Response.Content.ReadAsStringAsync();
        //            if (Response.StatusCode == System.Net.HttpStatusCode.OK)
        //            {




        //            }
        //            return Ok(Response);


        //        }

        //    }

        //}

        //[HttpGet]
        //public ActionResult<IEnumerable<string>> Get()
        //{
        //    //return new string[] { "value1", "value2" };
        //    var invoiceline = new List<InvoiceLine>
        //    {
        //        new InvoiceLine
        //        {
        //    description= "Computer1",
        //    itemType= "EGS",
        //    itemCode= "EG-14-1003228",
        //    unitType= "EA",
        //    quantity= 5,
        //    internalCode= "IC0",
        //    salesTotal= 947.00m,
        //    total= 2969.89m,
        //    valueDifference= 7.00m,
        //    totalTaxableFees= 817.42m,
        //    netTotal=880.71m,
        //    itemsDiscount= 5.00m,
        //    unitValue=new UnitValue {
        //        currencySold ="EUR",
        //        amountEGP= 189.40m,
        //        amountSold= 10.00m,
        //        currencyExchangeRate= 18.94m
        //    },
        //    discount=new Discount {
        //        rate= 7,
        //        amount= 66.29m
        //    },
        //    taxableItems=new List<TaxableItem>
        //    {
        //        new TaxableItem
        //        {
        //             taxType= "T1",
        //            amount= 272.07m,
        //            subType= "T1",
        //            rate= 14.00m
        //        },
        //         new TaxableItem
        //        {
        //            taxType= "T2",
        //            amount= 208.22m,
        //            subType= "T2",
        //            rate= 12
        //        },
        //           new TaxableItem
        //        {
        //           taxType= "T3",
        //            amount= 30.00m,
        //            subType= "T3",
        //            rate= 0.00m
        //        },

        //       new TaxableItem
        //        {
        //             taxType= "T4",
        //            amount= 43.79m,
        //            subType= "T4",
        //            rate= 5.00m
        //        },
        //        new TaxableItem
        //        {
        //              taxType="T5",
        //            amount=123.30m,
        //            subType= "T5",
        //            rate=14.00m
        //        },
        //         new TaxableItem
        //        {
        //            taxType= "T6",
        //            amount= 60.00m,
        //            subType= "T6",
        //            rate= 0.00m
        //        },
        //           new TaxableItem
        //        {
        //            taxType= "T7",
        //            amount= 88.07m,
        //            subType= "T7",
        //            rate= 10.00m
        //        },
        //              new TaxableItem
        //        {
        //            taxType= "T8",
        //            amount= 123.30m,
        //            subType= "T8",
        //            rate= 14.00m
        //        },
        //        new TaxableItem
        //        {
        //            taxType= "T9",
        //            amount= 105.69m,
        //            subType= "T9",
        //            rate= 12.00m
        //        },
        //               new TaxableItem
        //        {
        //            taxType= "T10",
        //            amount= 88.07m,
        //            subType= "T10",
        //            rate= 10.00m
        //        },
        //                        new TaxableItem
        //        {
        //            taxType= "T11",
        //            amount= 123.30m,
        //            subType= "T11",
        //            rate= 14.00m
        //        },
        // new TaxableItem
        //        {
        //            taxType= "T12",
        //            amount=105.69m,
        //            subType= "T12",
        //            rate= 12.00m
        //        },

        //   new TaxableItem
        //        {
        //            taxType= "T13",
        //            amount=88.07m,
        //            subType= "T13",
        //            rate= 10.00m
        //        },

        //     new TaxableItem
        //        {
        //            taxType= "T14",
        //            amount=123.30m,
        //            subType= "T14",
        //            rate= 14.00m
        //        },
        //       new TaxableItem
        //        {
        //            taxType= "T15",
        //            amount=105.69m,
        //            subType= "T15",
        //            rate= 12.00m
        //        },
        //         new TaxableItem
        //        {
        //            taxType= "T16",
        //            amount=88.07m,
        //            subType= "T16",
        //            rate= 10.00m
        //        },
        //        new TaxableItem  {
        //            taxType= "T17",
        //            amount=88.07m,
        //            subType= "T17",
        //            rate= 10.00m
        //        },
        //      new TaxableItem   {
        //            taxType= "T18",
        //            amount=123.30m,
        //            subType= "T18",
        //            rate= 14.00m
        //        },
        //       new TaxableItem   {
        //            taxType= "T19",
        //            amount=105.69m,
        //            subType= "T19",
        //            rate= 12.00m
        //        },
        //        new TaxableItem   {
        //            taxType= "T20",
        //            amount=88.07m,
        //            subType= "T20",
        //            rate= 10.00m
        //        }

        //    }

        //}
        //    };
        //    var CheckValidation = CheckEInvoiceLine(invoiceline);
        //    return CheckValidation;
        //}


        //public List<string> CheckEInvoiceLine(List<InvoiceLine> invoiceLines)
        //{
        //    var ValidationMessages = new List<string>();
        //    foreach (var InvoiceLin in invoiceLines)
        //    {
        //        if (InvoiceLin.itemType != "GS1" || InvoiceLin.itemType == "EGS")
        //            ValidationMessages.Add(InvoiceLin.itemCode + "للصنف EGS اوGS1 برجاء ادخال نوع الصنف");
        //        var CheckitemCode = DB.IcItem.FirstOrDefault(c => c.ItemEcode == InvoiceLin.itemCode);
        //        if (CheckitemCode == null)
        //            ValidationMessages.Add(" برجاء ادخال  الصنف اولا فى السجل الضريبى");
        //        if (InvoiceLin.quantity <= 0)
        //            ValidationMessages.Add(InvoiceLin.itemCode + " برجاء ادخال كمية الصنف ");
        //        var CheckcurrencySold = DB.StCurrency.FirstOrDefault(c => c.Ecode == InvoiceLin.unitValue.currencySold);

        //        if (CheckcurrencySold == null)
        //            ValidationMessages.Add(InvoiceLin.itemCode + "  برجاء ادخال العملة بطريقة صحيحة للصنف");
        //        var CheckUnitType = DB.IcUom.FirstOrDefault(c => c.Ecode == InvoiceLin.unitType);
        //        if (CheckUnitType == null)
        //            ValidationMessages.Add(InvoiceLin.itemCode + "  برجاء ادخال نوع الصنف بطريقة صحيحة للصنف");


        //        var amountEGP = InvoiceLin.unitValue.amountEGP.ToString().Split('.');
        //        if (amountEGP.Length > 1)
        //        {
        //            if (amountEGP[1].Length > 5)
        //                ValidationMessages.Add(InvoiceLin.itemCode + "  برجاء ادخال  الارقام العشرية للمبلغ  بالمصرى  لا يتعدى 5 ارقام للصنف");


        //        }
        //        decimal amountEGPCalculate = 0;
        //        if (InvoiceLin.unitValue.currencySold == "EGP")
        //        {
        //            if (InvoiceLin.unitValue.amountSold == 0)
        //                ValidationMessages.Add(InvoiceLin.itemCode + "  برجاء ادخال الكمية المباعة للصنف");
        //            amountEGPCalculate = InvoiceLin.unitValue.amountSold.GetValueOrDefault();
        //        }
        //        if (InvoiceLin.unitValue.currencySold != "EGP")
        //        {
        //            if (InvoiceLin.unitValue.currencyExchangeRate == 0)
        //                ValidationMessages.Add(InvoiceLin.itemCode + "  برجاء ادخال سعر الصرف للصنف");
        //            amountEGPCalculate = Math.Round(Convert.ToDecimal(InvoiceLin.unitValue.amountSold * InvoiceLin.unitValue.currencyExchangeRate), 5);
        //            if (amountEGPCalculate != Convert.ToDecimal(InvoiceLin.unitValue.amountEGP))
        //                ValidationMessages.Add(InvoiceLin.itemCode + "المبلغ للكمية بالمصرى غير صحيحة للصنف");

        //        }

        //        var currencyExchangeRate = InvoiceLin.unitValue.currencyExchangeRate.ToString().Split('.');
        //        if (currencyExchangeRate.Length > 1)
        //        {
        //            if (currencyExchangeRate[1].Length > 5)
        //                ValidationMessages.Add(InvoiceLin.itemCode + "  برجاء ادخال  الارقام العشرية لسعر الصرف  لا يتعدى 5 ارقام للصنف");


        //        }
        //        var SalesTotalCalcu = InvoiceLin.quantity * amountEGPCalculate;//Quantity * Amount Description: invoice line sales total is equal to multiplication of quantity by amount.
        //        if (SalesTotalCalcu != InvoiceLin.salesTotal)
        //            ValidationMessages.Add(InvoiceLin.itemCode + "    اجمالى المبيعات غير صحيحة للصنف  ");
        //        decimal DiscountAmountCalu = 0;
        //        if (InvoiceLin.discount != null)// if there is discount
        //        {
        //            if (InvoiceLin.discount.rate > 0)
        //            {

        //                DiscountAmountCalu = (InvoiceLin.discount.rate * SalesTotalCalcu) / 100;//Discount.Rate * Invoice Line Sales Total Description: discount amount is equal to the multiplication of discount rate by invoice line sales total.If discount rate is provided as zero, then this validation equation will not be applied.
        //                if (DiscountAmountCalu != InvoiceLin.discount.amount)
        //                    ValidationMessages.Add(InvoiceLin.itemCode + "    الخصم غير صحيح للصنف  ");

        //            }
        //        }
        //        var NetCalcu = SalesTotalCalcu - DiscountAmountCalu;//Sales Total – Discount Amount
        //        if (NetCalcu != InvoiceLin.netTotal)
        //            ValidationMessages.Add(InvoiceLin.itemCode + "  الصافى غير صحيح للصنف");
        //        decimal TotalTaxableFeesCalcu = 0;
        //        decimal TotalNonTaxableFeesCalcu = 0;
        //        decimal T3AmountCalcu = 0;
        //        decimal T2TaxAmountCalaclate = 0;
        //        decimal T1TaxAmountCalaclate = 0;
        //        decimal T4TaxAmountCalcu = 0;
        //        if (InvoiceLin.taxableItems != null)
        //        {
        //            foreach (var item in InvoiceLin.taxableItems)
        //            {
        //                if (item.taxType == "T5" || item.taxType == "T6" || item.taxType == "T7" || item.taxType == "T8" || item.taxType == "T9" || item.taxType == "T10" || item.taxType == "T11" || item.taxType == "T12")
        //                {
        //                    //Description: each taxable item amount is equal to the multiplication of taxable item rate by invoice line net total. If rate is not provided, then validation equation will not be applied.
        //                    if (item.rate > 0)
        //                    {
        //                        var TaxAmountCalcu = Math.Round((item.rate / 100) * NetCalcu, 5);
        //                        TotalTaxableFeesCalcu += TaxAmountCalcu;
        //                        if (TaxAmountCalcu != item.amount)
        //                            ValidationMessages.Add(item.taxType + "ونوع ضريبة" + InvoiceLin.itemCode + "  قيمة الضريبة غير صحيحة للصنف ");

        //                    }
        //                    if (item.taxType == "T6")
        //                    {
        //                        TotalTaxableFeesCalcu += Math.Round(item.amount, 5);

        //                        if (item.rate != 0)//Rate should be equal zero (rate for this item should always be 0 as this is fixed amount taxable item)
        //                            ValidationMessages.Add(item.taxType + "ونوع ضريبة" + InvoiceLin.itemCode + "   لا يوجد نسبة للضريبة على الصنف ");
        //                    }

        //                }


        //                if (item.taxType == "T13" || item.taxType == "T14" || item.taxType == "T15" || item.taxType == "T16" || item.taxType == "T17" || item.taxType == "T18" || item.taxType == "T19" || item.taxType == "T20")
        //                {
        //                    //NonTaxableItem.Rate * InvoiceLine.NetTotal Description: each non taxable item amount is equal to the multiplication of non taxable item rate by invoice line net total. If rate is not provided, then validation equation will not be applied.
        //                    if (item.rate > 0)
        //                    {
        //                        var NonTaxAmountCalcu = Math.Round((item.rate / 100) * NetCalcu, 5);
        //                        TotalNonTaxableFeesCalcu += NonTaxAmountCalcu;
        //                        if (NonTaxAmountCalcu != item.amount)
        //                            ValidationMessages.Add(item.taxType + "ونوع ضريبة" + InvoiceLin.itemCode + "  قيمة الضريبة غير صحيحة للصنف ");


        //                    }

        //                }
        //                if (item.taxType == "T3")
        //                {
        //                    T3AmountCalcu = Math.Round(item.amount, 5);
        //                    if (item.rate != 0)//Rate should be equal zero (rate for this item should always be 0 as this is fixed amount taxable item)
        //                        ValidationMessages.Add(item.taxType + "ونوع ضريبة" + InvoiceLin.itemCode + "   لا يوجد نسبة للضريبة على الصنف ");

        //                }



        //                if (item.taxType == "T4")
        //                {

        //                    //TaxableItem<WHT-T4 + Subtype>.Rate * (Net Total – Items Discount)
        //                    T4TaxAmountCalcu = Math.Round((NetCalcu - InvoiceLin.itemsDiscount) * (item.rate / 100), 5);
        //                    if (T4TaxAmountCalcu != Convert.ToDecimal(item.amount))
        //                        ValidationMessages.Add(item.taxType + "ونوع ضريبة" + InvoiceLin.itemCode + " قيمة الضريبة غير صحيحة للصنف ");

        //                }
        //            }
        //            var T2Tax = InvoiceLin.taxableItems.FirstOrDefault(c => c.taxType == "T2");// t2 calculate
        //            if (T2Tax != null)
        //            {
        //                //( Net Total + TotalTaxableFees + Value Difference + taxableItems.taxtype(T3).Amount) * taxableItems.taxtype(T2).rate
        //                T2TaxAmountCalaclate = Math.Round((NetCalcu + TotalTaxableFeesCalcu + InvoiceLin.valueDifference + T3AmountCalcu) * (T2Tax.rate / 100), 5);
        //                if (T2TaxAmountCalaclate != T2Tax.amount)
        //                    ValidationMessages.Add(T2Tax.taxType + "ونوع ضريبة" + InvoiceLin.itemCode + "  قيمة الضريبة غير صحيحة للصنف ");

        //            }

        //            var T1Tax = InvoiceLin.taxableItems.FirstOrDefault(c => c.taxType == "T1");// t1 calculate
        //            if (T1Tax != null)
        //            {
        //                //( TaxableItem.Amount + Net Total + TotalTaxableFees + Value Difference + Taxable Item.Amount) * TaxableItem.Rate
        //                T1TaxAmountCalaclate = Math.Round((NetCalcu + TotalTaxableFeesCalcu + InvoiceLin.valueDifference + T2TaxAmountCalaclate + T3AmountCalcu) * (T1Tax.rate / 100), 5);
        //                if (T2TaxAmountCalaclate != T1Tax.amount)
        //                    ValidationMessages.Add(T2Tax.taxType + "ونوع ضريبة" + InvoiceLin.itemCode + "  قيمة الضريبة غير صحيحة للصنف ");

        //            }


        //        }

        //        if (TotalTaxableFeesCalcu != InvoiceLin.totalTaxableFees)
        //            ValidationMessages.Add(InvoiceLin.itemCode + " إجمالي الرسوم الخاضعة للضريبة غير صحيحة للصنف ");

        //        //NetTotal + Taxable Item.Amount + TotalTaxableFees + TaxableItem.Amount + TaxableItem.Amount + LineTotalNoneTaxableFees – Items Discount – sum of all (TaxableItem<WHT-T4 + Subtype>.Amount)
        //        var TotalinvoiceLineCalculate = Math.Round(NetCalcu + TotalTaxableFeesCalcu + T1TaxAmountCalaclate + T2TaxAmountCalaclate + T3AmountCalcu + TotalNonTaxableFeesCalcu - InvoiceLin.itemsDiscount - T4TaxAmountCalcu, 5);
        //        if (TotalinvoiceLineCalculate != InvoiceLin.total)
        //            ValidationMessages.Add(InvoiceLin.itemCode + "  الاجمالى غير صحيح للصنف");




        //    }
        //    return ValidationMessages;

        //}
        #endregion

        [HttpPost]
        [Route("SaveInvoice")]
        public IActionResult SaveDocument(List<Invoice> invoices)
        {
            var validate = ApiValidation.Validate(invoices, DB, mapper);
            if (validate.IsValid)
            {
                InvoiceBL.SaveDocuments(validate.ValidDocumnents, DB);
                return Ok("Document Saved");
            }
            else
            {
                return BadRequest(validate.UnValidDocumnents);
            }

        }

        [HttpPost]
        [Route("SubmitDocument")]
        [ApiExplorerSettings(IgnoreApi = true)]
        public IActionResult SubmitDocument(List<Invoice> invoices)
        {
            var bbb = AccessToken.GetAccessToken(DB);
            try
            {
                var validate = Validation.Validate(invoices, DB, mapper);
                if (validate.IsValid)
                {
                    var documentsList = validate.ValidDocumnents.Select(x => MapInvoice(x)).ToList();
                    var document = new Documents()
                    {
                        documents = documentsList
                    };
                    List<string> IgnoreProperties = new List<string>();
                    if (documentsList.FirstOrDefault().documentType == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
                    {
                        IgnoreProperties.Add("references");
                    }
                    var serialzeddocuments = JsonConvert.SerializeObject(document, new JsonSerializerSettings { ContractResolver = new JsonIgnoreResolver(IgnoreProperties) });
                    //var serialzeddocuments = JsonConvert.SerializeObject(document);
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" Final Object - {serialzeddocuments}", 1, 1);
                    var companyData = DB.StCompanyInfo.FirstOrDefault();
                    using (HttpClient client = new HttpClient())
                    {
                        client.Timeout = TimeSpan.FromHours(2);
                        client.DefaultRequestHeaders.Accept.Clear();
                        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
                        var content = new StringContent(serialzeddocuments, Encoding.UTF8, "application/json");
                        UriBuilder builder = new UriBuilder(BaseUrl);
                        builder.Path = "/api/v1.0/documentsubmissions";
                        HttpResponseMessage APIResponse = client.PostAsync(builder.Uri, content).Result;
                        if (APIResponse.IsSuccessStatusCode)
                        {
                            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                            var result = JsonConvert.DeserializeObject<Models_1.ViewModels.ResponseVM.SubmitDocumentVM.SubmitDocumentResponse>(JsonContent);
                            //result.acceptedDocuments.ForEach(async x=> await  UpdateAcceptedDocuments(x));
                            //result.acceptedDocuments.ForEach(x => UpdateAcceptedDocuments(x));
                            //result.rejectedDocuments.ForEach(x => InvoiceBL.LogRejectedDocument(x, DB));


                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"result.acceptedDocuments " + result.acceptedDocuments, 1, 1);
                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"result.rejectedDocuments " + result.rejectedDocuments, 1, 1);

                            save_Log("Success APIResponse.Content: " + JsonContent, null);
                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Success APIResponse.Content " + JsonContent, 1, 1);

                            return StatusCode((int)APIResponse.StatusCode, result);
                        }
                        else
                        {
                            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"APIResponse.Content " + JsonContent, 1, 1);
                            save_Log("APIResponse.Content: " + JsonContent, null);
                            return StatusCode((int)APIResponse.StatusCode, JsonContent);
                        }
                    }
                }
                else
                {
                    return BadRequest(validate.Message);
                }
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message + " " + ex.InnerException);
            }


        }

        #region Old Debit Note
        //[HttpPost]
        //[Route("DebitNote")] //اشعار اضافة
        //public IActionResult DebitNote(List<Invoice> invoices)
        //{
        //    //var cc = AccessToken.GetAccessToken();
        //    try
        //    {
        //        var validate = Validation.Validate(invoices, DB);
        //        if (validate.IsValid)
        //        {
        //            var documentsList = validate.ValidDocumnents.Select(x => MapDebitCrediteNoteInvoice(x)).ToList();
        //            var document = new DebitCreditDocuments()
        //            {
        //                documents = documentsList
        //            };
        //            var serialzeddocuments = JsonConvert.SerializeObject(document);
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" Final Object - {serialzeddocuments}", 1, 1);
        //            using (HttpClient client = new HttpClient())
        //            {
        //                client.Timeout = TimeSpan.FromHours(2);
        //                client.DefaultRequestHeaders.Accept.Clear();
        //                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        //                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(validate.ValidDocumnents.FirstOrDefault().CompanyData));
        //                var content = new StringContent(serialzeddocuments, Encoding.UTF8, "application/json");
        //                UriBuilder builder = new UriBuilder(BaseUrl);
        //                builder.Path = "/api/v1.0/documentsubmissions";
        //                HttpResponseMessage APIResponse = client.PostAsync(builder.Uri, content).Result;
        //                if (APIResponse.IsSuccessStatusCode)
        //                {
        //                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //                    var result = JsonConvert.DeserializeObject<SubmitDocumentResponse>(JsonContent);
        //                    return StatusCode((int)APIResponse.StatusCode, result);
        //                }
        //                else
        //                {
        //                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //                    return StatusCode((int)APIResponse.StatusCode, JsonContent);
        //                }
        //            }
        //        }
        //        else
        //        {
        //            return BadRequest(validate.Message);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(ex.Message + " " + ex.InnerException);
        //    }
        //}
        #endregion

        #region Old Credit Note
        //[HttpPost]
        //[Route("CreditNote")] //اشعار خصم
        //public IActionResult CreditNote(List<Invoice> invoices)
        //{

        //    try
        //    {
        //        var validate = Validation.Validate(invoices, DB);
        //        if (validate.IsValid)
        //        {
        //            var documentsList = validate.ValidDocumnents.Select(x => MapDebitCrediteNoteInvoice(x)).ToList();
        //            var document = new DebitCreditDocuments()
        //            {
        //                documents = documentsList
        //            };
        //            var serialzeddocuments = JsonConvert.SerializeObject(document);
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" Final Object - {serialzeddocuments}", 1, 1);
        //            using (HttpClient client = new HttpClient())
        //            {
        //                client.Timeout = TimeSpan.FromHours(2);
        //                client.DefaultRequestHeaders.Accept.Clear();
        //                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        //                //client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(validate.ValidDocumnents.FirstOrDefault().CompanyData));
        //                var content = new StringContent(serialzeddocuments, Encoding.UTF8, "application/json");
        //                UriBuilder builder = new UriBuilder(BaseUrl);
        //                builder.Path = "/api/v1.0/documentsubmissions";
        //                HttpResponseMessage APIResponse = client.PostAsync(builder.Uri, content).Result;
        //                if (APIResponse.IsSuccessStatusCode)
        //                {
        //                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //                    var result = JsonConvert.DeserializeObject<Models_1.ViewModels.ResponseVM.SubmitDocumentVM.SubmitDocumentResponse>(JsonContent);
        //                    //result.acceptedDocuments.ForEach(x => UpdateAcceptedDocuments(x, Convert.ToInt32(InvoiceType.SalesReturn)));
        //                    //result.rejectedDocuments.ForEach(x => InvoiceBL.LogRejectedDocument(x, DB, Convert.ToInt32(Models_1.ViewModels.DocumentType.C)));


        //                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"result.acceptedDocuments " + result.acceptedDocuments, 1, 1);
        //                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"result.rejectedDocuments " + result.rejectedDocuments, 1, 1);

        //                    save_Log("Success APIResponse.Content: " + JsonContent, null);
        //                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Success APIResponse.Content " + JsonContent, 1, 1);

        //                    return StatusCode((int)APIResponse.StatusCode, result);
        //                }
        //                else
        //                {
        //                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"APIResponse.Content " + JsonContent, 1, 1);
        //                    save_Log("APIResponse.Content: " + JsonContent, null);
        //                    return StatusCode((int)APIResponse.StatusCode, JsonContent);
        //                }
        //            }
        //        }
        //        else
        //        {
        //            return BadRequest(validate.Message);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        return BadRequest(ex.Message + " " + ex.InnerException);
        //    }
        //}
        #endregion

        [ApiExplorerSettings(IgnoreApi = true)]
        public Document MapInvoice(InvoiceData invoice)
        {
           
            var round = DB.StStore.FirstOrDefault().RoundValue ?? 5;
            var DefaultRoundingPoints = DB.StStore.FirstOrDefault().DefaultRoundingPoints ??0;
            #region Issuer
            var issuer = MyHelper.Utilities.GetIssuerData(invoice.CompanyData, invoice.StoreData);
            #endregion
            #region Receiver
            var receiver = MyHelper.Utilities.GetReceiverData(invoice.CustomerData);
            #endregion
            #region Payment
            Payment payment = new Payment() { bankName = "", bankAccountIBAN = "", bankAccountNo = "", bankAddress = "", swiftCode = "", terms = "" };
            #endregion
            //#region Delivery
            //Models_1.ViewModels.Delivery delivery = new Models_1.ViewModels. Delivery() { terms = "", approach = "", countryOfOrigin = "EG", dateValidity = invoice.DeliveryDate, exportPort = "", grossWeight = 0, netWeight = 0, packaging = "" };
            //#endregion
            #region InvoiceLines
            List<InvoiceLine> invoiceLines = invoice.InvoiceDetailData.Select(x => MapInvoiceDetails(x,DefaultRoundingPoints==0? round:DefaultRoundingPoints)).ToList();
            #endregion
            #region TaxTotals
            var taxTotals = TaxCalculator.CalculateInvoiceTotalTaxes(invoiceLines, DefaultRoundingPoints == 0 ? round : DefaultRoundingPoints);
            #endregion
            #region Signatures
            //List<Signature> signatures = Helper.GetSignatures(invoice.CompanyData, DB);
            #endregion

            Document document = new Document();
            document.issuer = issuer;
            document.receiver = receiver;
            document.documentType = invoice.DocumentType;
            document.documentTypeVersion = "1.0";
            document.dateTimeIssued = invoice.InvoiceDate;
            document.taxpayerActivityCode = invoice.CompanyData.ActivityType;
            document.internalID = invoice.invoiceCode;
            document.purchaseOrderReference= document.proformaInvoiceNumber = (invoice.PurchaseOrderNumber == "" || invoice.PurchaseOrderNumber == null) ? string.Empty : invoice.PurchaseOrderNumber;         //optional Dat
            document.purchaseOrderDescription = invoice.PurchaseOrderNumber;       //optional Data
            document.salesOrderReference = string.Empty;            //optional Data
            document.salesOrderDescription = string.Empty;          //optional Data
            document.payment = payment;
            document.serviceDeliveryDate = invoice.DeliveryDate; //optional Data
            //document.delivery = delivery;
            document.invoiceLines = invoiceLines;
            document.totalSalesAmount = Math.Round(invoiceLines.Sum(x => x.salesTotal), DefaultRoundingPoints == 0 ? round : DefaultRoundingPoints);
            document.totalDiscountAmount = Math.Round(invoiceLines.Sum(x => x.discount.amount), DefaultRoundingPoints == 0 ? round : DefaultRoundingPoints);
            document.netAmount = Math.Round(invoiceLines.Sum(x => x.netTotal), DefaultRoundingPoints == 0 ? round : DefaultRoundingPoints);
            document.taxTotals = taxTotals;
            document.extraDiscountAmount = Math.Round(invoice.TotalDiscount, DefaultRoundingPoints == 0 ? round : DefaultRoundingPoints);
            document.totalItemsDiscountAmount = Math.Round(invoiceLines.Sum(x => x.itemsDiscount), DefaultRoundingPoints == 0 ? round : DefaultRoundingPoints);
            document.totalAmount = Math.Round(invoiceLines.Sum(x => x.total) - document.extraDiscountAmount, DefaultRoundingPoints == 0 ? round : DefaultRoundingPoints);
            document.invoiceId = invoice.invoiceId;

            document.signatures = new List<Signature>() {
                new Signature() {
                    signatureType ="I"
                    , value= "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"}
            };
            document.references = new List<string>();
            if (invoice.Uuid != null && invoice.DocumentType.ToUpper() != Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
            {
                var references = new List<string>() { };
                //var SourceInvoiceUUID = DB.SlInvoice.FirstOrDefault(x => x.SlInvoiceId == invoice.sourceInvoiceId)?.Uuid ?? "";
                references.AddRange(invoice.Uuid);
                document.references = references;

            }

            return document;
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        public DebitCreditDocument MapDebitCrediteNoteInvoice(InvoiceData invoice)
        {
            var round = DB.StStore.FirstOrDefault().RoundValue ?? 5;
            #region Issuer
            var issuer = MyHelper.Utilities.GetIssuerData(invoice.CompanyData, invoice.StoreData);
            #endregion
            #region Receiver
            var receiver = MyHelper.Utilities.GetReceiverData(invoice.CustomerData);
            #endregion
            #region Payment
            Payment payment = new Payment();
            #endregion
            #region Delivery
            Delivery delivery = new Delivery() { };
            #endregion
            #region InvoiceLines
            List<InvoiceLine> invoiceLines = invoice.InvoiceDetailData.Select(x => MapInvoiceDetails(x, round)).ToList();
            #endregion
            #region TaxTotals
            var taxTotals = TaxCalculator.CalculateInvoiceTotalTaxes(invoiceLines, round);
            #endregion
            #region Signatures
            //var dummysignatures = "MIIGywYJKoZIhvcNAQcCoIIGvDCCBrgCAQMxDTALBglghkgBZQMEAgEwCwYJKoZIhvcNAQcFoIID/zCCA/swggLjoAMCAQICEExmZ\u002B69rsEiVjVCUUMzjcEwDQYJKoZIhvcNAQELBQAwSTELMAkGA1UEBhMCRUcxFDASBgNVBAoTC0VneXB0IFRydXN0MSQwIgYDVQQDExtFZ3lwdCBUcnVzdCBDb3Jwb3JhdGUgQ0EgRzIwHhcNMjAwMzMxMDAwMDAwWhcNMjEwMzMwMjM1OTU5WjBgMRUwEwYDVQQKFAxFZ3lwdCBUcnVzdCAxGDAWBgNVBGEUD1ZBVEVHLTMxMzcxNzkxOTELMAkGA1UEBhMCRUcxIDAeBgNVBAMMF1Rlc3QgU2VhbGluZyBEZW1vIHVzZXIxMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApmVGVJtpImeq\u002BtIJiVWSkIEEOTIcnG1XNYQOYtf5\u002BDg9eF5H5x1wkgR2G7dvWVXrTsdNv2Q\u002Bgvml9SdfWxlYxaljg2AuBrsHFjYVEAQFI37EW2K7tbMT7bfxwT1M5tbjxnkTTK12cgwxPr2LBNhHpfXp8SNyWCxpk6eyJb87DveVwCLbAGGXO9mhDj62glVTrCFit7mHC6bZ6MOMAp013B8No9c8xnrKQiOb4Tm2GxBYHFwEcfYUGZNltGZNdVUtu6ty\u002BNTrSRRC/dILeGHgz6/2pgQPk5OFYRTRHRNVNo\u002BjG\u002BnurUYkSWxA4I9CmsVt2FdeBeuvRFs/U1I\u002BieKg1wIDAQABo4HHMIHEMAkGA1UdEwQCMAAwVAYDVR0fBE0wSzBJoEegRYZDaHR0cDovL21wa2ljcmwuZWd5cHR0cnVzdC5jb20vRWd5cHRUcnVzdENvcnBvcmF0ZUNBRzIvTGF0ZXN0Q1JMLmNybDAdBgNVHQ4EFgQUqzFDImtytsUbghbmtnl2/k4d5jEwEQYJYIZIAYb4QgEBBAQDAgeAMB8GA1UdIwQYMBaAFCInP8ziUIPmu86XJUWXspKN3LsFMA4GA1UdDwEB/wQEAwIGwDANBgkqhkiG9w0BAQsFAAOCAQEAN/d9pnNi9IjMAoxi9A2FpfrWooNcejvOm0usIPj23F988uBa\u002B2vLVuWrNSC8aJJL9eLIjO9I2ecxRhRiGwp1YBoXJ25GvXvmIT4EN1B/kpZa/M/VIeSeimV/qlmdx/5Sy1ycK\u002BEFkqojEKrteNAZr5yU/v5stX/x\u002B5A0yKxrGv6GFmyLT4Mi1xtybjUpTNQXKYa/DUPtB4wO829CmPdCl8gIldIlMoyz0vQFp42PgRXoMpqVKO631wKXDBbplpC7GZ9FewGZHVwQ0ppCX1S27YGYATuhyWb9uZvuJxg9j20tLgy1BWZ2LEgbTaYQ5cZ2bggAzr5JckAqQSotU1LbFDGCApIwggKOAgEBMF0wSTELMAkGA1UEBhMCRUcxFDASBgNVBAoTC0VneXB0IFRydXN0MSQwIgYDVQQDExtFZ3lwdCBUcnVzdCBDb3Jwb3JhdGUgQ0EgRzICEExmZ\u002B69rsEiVjVCUUMzjcEwCwYJYIZIAWUDBAIBoIIBCjAYBgkqhkiG9w0BCQMxCwYJKoZIhvcNAQcFMBwGCSqGSIb3DQEJBTEPFw0yMDExMDUxNjM2MzhaMC8GCSqGSIb3DQEJBDEiBCDUP64lsCt\u002BRm\u002Blqjnl\u002BkXH2SZh5bCg55ZOBHg7/zwmUTCBngYLKoZIhvcNAQkQAi8xgY4wgYswgYgwgYUEIP64tKSYzgTXL9QKwIpQpo5Cj3Lnv7DpFDMbsh7RCAR6MGEwTaBLMEkxCzAJBgNVBAYTAkVHMRQwEgYDVQQKEwtFZ3lwdCBUcnVzdDEkMCIGA1UEAxMbRWd5cHQgVHJ1c3QgQ29ycG9yYXRlIENBIEcyAhBMZmfuva7BIlY1QlFDM43BMAsGCSqGSIb3DQEBAQSCAQCjLdLZ8JLIW2h/vKuIAf2rCmj9gJvFVK\u002BzEcu1oTOvJZ8aG7tFuWye2jvVwXobqmG8q8k8u65BwLVhOQvAZf/qcWIxsEcwTyPpOiKh2y1WWsfDsDGVQ5UBajvUotN6iUd0Y5dWfTw/SSf1lhEl8NV\u002BQeg9hCc2ZQ8lTG/7WfrE6EeZcqKb7qhC9y5rdxGu0JHb3I46h30DNiEjHIusGGpV3XIDvh5PuOhzuzvJzmb0tE0T3v4bNFdYSxhLUv\u002BFlEO\u002BrPPHYGNyzLNuZzrXhQj99bNksZcQmOKOUqyz87D\u002BhUdhepQTCorxSrgSqVDmn5chWGy4d5LJbAKcjJO1ypi2";
            //List<Signature> signatures = Helper.GetSignatures(invoice.CompanyData, DB);
            #endregion

            DebitCreditDocument document = new DebitCreditDocument();

            document.issuer = issuer;
            document.receiver = receiver;
            document.documentType = invoice.DocumentType;
            document.documentTypeVersion = "1.0";
            document.dateTimeIssued = invoice.InvoiceDate;
            document.taxpayerActivityCode = invoice.CompanyData.ActivityType;
            document.internalID = invoice.invoiceCode;
            document.purchaseOrderReference = invoice.PurchaseOrderNumber;         //optional Data
            document.purchaseOrderDescription = string.Empty;       //optional Data
            document.salesOrderReference = string.Empty;            //optional Data
            document.salesOrderDescription = string.Empty;          //optional Data
            document.payment = payment;
            document.delivery = new Models_1.ViewModels.Delivery();
            document.invoiceLines = invoiceLines;
            document.totalSalesAmount = Math.Round(invoiceLines.Sum(x => x.salesTotal), round);
            document.totalDiscountAmount = Math.Round(invoiceLines.Sum(x => x.discount.amount), round);
            document.netAmount = Math.Round(invoiceLines.Sum(x => x.netTotal), round);
            document.taxTotals = taxTotals;
            document.extraDiscountAmount = Math.Round(invoice.TotalDiscount, round);
            document.totalItemsDiscountAmount = Math.Round(invoiceLines.Sum(x => x.itemsDiscount), round);
            document.totalAmount = Math.Round(invoiceLines.Sum(x => x.total) - document.extraDiscountAmount, round);
            //document.signatures = signatures; To Be Reviewed With Eng.Asmaa Taha
            //if (!string.IsNullOrEmpty(invoice.invoiceId))
            document.references = new List<string>() { };
            if (invoice.Uuid != null)
            {
                var references = new List<string>() { };

                references.AddRange(invoice.Uuid);
                document.references = references;

            }
            return document;
        }
        [ApiExplorerSettings(IgnoreApi = true)]
        public InvoiceLine MapInvoiceDetails(InvoiceDetailData invoiceDetail, int round)
        {
            var AddItemNameToDescription = DB.StCompanyInfo.FirstOrDefault().AddItemNameToDescription;
            var itemsDiscount = Math.Round(invoiceDetail.ItemsDiscount, round);
            var valueDifference = 0;                                //From Where?
            #region UnitValue
            UnitValue unitValue = MyHelper.Utilities.GetUnitValue(invoiceDetail.CurrencyData.CrncId, invoiceDetail.Amount, invoiceDetail.ExchangeRate, round, DB);
            #endregion
            #region Discount
            Discount discount = new Discount()
            {
                amount = (invoiceDetail.Discount.type == 1) ? Math.Round(invoiceDetail.Discount.rate * invoiceDetail.ExchangeRate, round) :
                Math.Round(invoiceDetail.Discount.rate * invoiceDetail.Quantity * unitValue.amountEGP / 100, round),
                rate = invoiceDetail.Discount.type == 1 ? 0 : invoiceDetail.Discount.rate
            };
            #endregion
            #region SalesTotalandNetTotal
            var salesTotal = Math.Round(invoiceDetail.Quantity * unitValue.amountEGP, round);
            var netTotal = Math.Round(salesTotal - discount.amount, round);
            #endregion
            #region TaxbleItems
            var taxModel = TaxCalculator.CalculateTaxes(invoiceDetail.Taxes, netTotal, itemsDiscount, valueDifference, round);
            #endregion

            #region Total 
            var total = Math.Round(netTotal + taxModel.totalVATTax + taxModel.totaltableTax + taxModel.totalTaxableFees +
                                   taxModel.totalNonTaxableFees + taxModel.totalfixedAmountTaxsT3 -
                                   itemsDiscount - taxModel.totalWithHoldingTax, round);

            #endregion

            InvoiceLine invoiceLine = new InvoiceLine();

            invoiceLine.description =AddItemNameToDescription==true? invoiceDetail.Description != null ? string.Concat(invoiceDetail.ItemData.ItemNameAr, "-", invoiceDetail.Description) : invoiceDetail.ItemData.ItemNameAr: invoiceDetail.Description;
            invoiceLine.itemType = invoiceDetail.ItemData.ItemEtype;
            invoiceLine.itemCode = invoiceDetail.ItemData.ItemEcode;
            invoiceLine.unitType = invoiceDetail.UomData.Ecode;
            invoiceLine.quantity = Math.Round(invoiceDetail.Quantity, 3);
            invoiceLine.unitValue = unitValue;
            invoiceLine.salesTotal = salesTotal;
            invoiceLine.total = total;
            invoiceLine.valueDifference = valueDifference;          //From Where?
            invoiceLine.totalTaxableFees = taxModel.totalTaxableFees;
            invoiceLine.netTotal = netTotal;
            invoiceLine.itemsDiscount = itemsDiscount;
            invoiceLine.discount = discount;
            invoiceLine.taxableItems = taxModel.taxableItem;
            invoiceLine.internalCode = string.IsNullOrEmpty(invoiceDetail.ItemData.ItemCode2) ? string.Empty : invoiceDetail.ItemData.ItemCode2;
            return invoiceLine;
        }

        [HttpGet]
        [Route("GetRecentDocuments")]
        public IActionResult GetRecentDocuments(int pageNo, int pageSize)
        {
            var companyData = DB.StCompanyInfo.FirstOrDefault();
            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
                UriBuilder builder = new UriBuilder(BaseUrl);
                builder.Path = $"api/v1.0/documents/recent";
                builder.Query = $"pageNo={pageNo}&pageSize={pageSize}";
                HttpResponseMessage APIResponse = client.GetAsync(builder.Uri).Result;

                if (APIResponse.IsSuccessStatusCode)
                {
                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                    var result = JsonConvert.DeserializeObject<GetRecentDocumentsRespones>(JsonContent);
                    return StatusCode((int)APIResponse.StatusCode, result);
                }
                else
                {
                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                    return StatusCode((int)APIResponse.StatusCode, JsonContent);
                }
            }

        }

        [HttpGet]
        [Route("GetPurchaseInvoice")]
        public async Task<IActionResult> GetPurchaseInvoice()
        {
            int pageNo = 1;
            int pageSize = 1000000;
            var companyData = DB.StCompanyInfo.FirstOrDefault();
            using (HttpClient client = new HttpClient())
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"1 - ", 1, 1);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                var response = await client.GetAsync($"{Request.Scheme}://{Request.Host.Value}/api/EInvoice/GetRecentDocuments?pageNo={pageNo}&pageSize={pageSize}");
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"2 - ", 1, 1);
                HttpResponseMessage APIResponse = response;
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"GetPurchaseInvoice Response Result - " + APIResponse.IsSuccessStatusCode.ToString(), 1, 1);
                if (APIResponse.IsSuccessStatusCode)
                {
                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                    var result = JsonConvert.DeserializeObject<GetRecentDocumentsRespones>(JsonContent);
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"UpdateAcceptedDocuments Response IsSuccess - " + JsonContent, 1, 1);
                    var PurchaseInvoice = result.result.Where(c => c.receiverId == companyData.TaxCard).ToList();
                    return StatusCode((int)APIResponse.StatusCode, PurchaseInvoice);
                }
                else
                {
                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"UpdateAcceptedDocuments Response IsFailed - " + JsonContent, 1, 1);
                    return StatusCode((int)APIResponse.StatusCode);
                }
            }
            

        }

        [HttpGet]
        [Route("GetDocumentPrintout")]
        public async Task<IActionResult> GetDocumentPrintout(string uuid)
        {
            //rid = 102723;
            var companyData = DB.StCompanyInfo.FirstOrDefault();
            using (HttpClient client = new HttpClient())
            {
                client.Timeout = TimeSpan.FromHours(2);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/octet-stream"));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
                UriBuilder builder = new UriBuilder(BaseUrl);
                builder.Path = $"/api/v1.0/documents/{uuid}/pdf";

                // builder.Query = $"rid={rid}";
                HttpResponseMessage APIResponse = client.GetAsync(builder.Uri).Result;

                if (APIResponse.IsSuccessStatusCode)
                {
                    var JsonContent = APIResponse.Content.ReadAsStreamAsync();

                    using (System.IO.Stream s =await  JsonContent)
                    {
                        var fileInfo = new FileInfo("document1.pdf");
                        using (var fileStream = fileInfo.OpenWrite())
                        {
                            await s.CopyToAsync(fileStream);
                        }
                        s.Position = 0;

                        // Get the MIMEType for the File
                       

                        
                    }
                    return StatusCode(200, "pdf File has Been Download");
                    //return filename;
                }
                else
                {
                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                    return StatusCode((int)APIResponse.StatusCode, JsonContent);
                }
            }

        }


        [HttpGet]
        [Route("GetDocumentPackage")]
        public async Task<IActionResult> GetDocumentPackageAsync(int rid)
        {
            //rid = 102723;
           // var companyData = DB.StCompanyInfo.FirstOrDefault();
            using (HttpClient client = new HttpClient())
            {
                client.Timeout = TimeSpan.FromHours(2);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/octet-stream"));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
                UriBuilder builder = new UriBuilder(BaseUrl);
                builder.Path = $"api/v1.0/documentpackages/{rid}";

                // builder.Query = $"rid={rid}";
                HttpResponseMessage APIResponse = client.GetAsync(builder.Uri).Result;

                if (APIResponse.IsSuccessStatusCode)
                {
                    var JsonContent = APIResponse.Content.ReadAsStreamAsync();
                    using (System.IO.Stream s = await JsonContent)
                    {
                        var fileInfo = new FileInfo("myPackage.zip");
                        using (var fileStream = fileInfo.OpenWrite())
                        {
                            await s.CopyToAsync(fileStream);
                        }
                    }
                    return StatusCode(200, "Zip File has Been Download");
                    //return filename;
                }
                else
                {
                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                    return StatusCode((int)APIResponse.StatusCode, JsonContent);
                }
            }

        }

        [HttpGet]
        [Route("RejectDocument")]
        public async Task<IActionResult> RejectDocument(string uuid,string Reason)
        {
            var companyData = DB.StCompanyInfo.FirstOrDefault();
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" RejectDoc - client", 1, 1);
                    client.Timeout = TimeSpan.FromHours(2);
                    client.DefaultRequestHeaders.Accept.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));

                    var data = new
                    {
                        status = "rejected",
                        reason = Reason
                    };
                    var serialzeData = JsonConvert.SerializeObject(data);
                    var content = new StringContent(serialzeData, Encoding.UTF8, "application/json");
                    UriBuilder builder = new UriBuilder(BaseUrl);
                    builder.Path = $"/api/v1.0/documents/state/{uuid}/state";
                    HttpResponseMessage APIResponse = client.PutAsync(builder.Uri, content).Result;
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"RejectDoc - " + APIResponse.IsSuccessStatusCode.ToString(), 1, 1);
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"RejectDoc Response Success - " + APIResponse.StatusCode.ToString() + " - " + JsonContent, 1, 1);
                        //var result = JsonConvert.DeserializeObject<GetDocumentResponse>(JsonContent);
                        return StatusCode((int)APIResponse.StatusCode, JsonContent);
                    }
                    else
                    {
                        var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                        var result = JsonConvert.DeserializeObject<ErrorResult>(JsonContent);
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"GetDocument Response Failed - " + APIResponse.StatusCode.ToString() + " - " + JsonContent, 1, 1);
                   
                     
                        return StatusCode((int)APIResponse.StatusCode, result);
                    }
                }
            }
            catch (Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From GetDocument - " + ex.InnerException + " - " + ex.Message, 1, 1);
                return BadRequest();

            }
        }

        [HttpPost]
        [Route("RequestDocumentPackage")]
        public async Task<IActionResult> RequestDocumentPackage(documentpackage Documentpackage)
        {
            try
            {
                var documentpackage = new
                {
                    type = Documentpackage.type,
                    format = Documentpackage.format,
                    queryParameters = Documentpackage.queryParameters
                    
                };
               // var companyData = DB.StCompanyInfo.FirstOrDefault();
                using (HttpClient client = new HttpClient())
                {
                    var serialzeddocuments = JsonConvert.SerializeObject(documentpackage);
                    client.Timeout = TimeSpan.FromHours(2);
                    client.DefaultRequestHeaders.Accept.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
                    var content = new StringContent(serialzeddocuments, Encoding.UTF8, "application/json");
                    UriBuilder builder = new UriBuilder(BaseUrl);
                    builder.Path = $"api/v1/documentpackages/requests";
                    HttpResponseMessage APIResponse = client.PostAsync(builder.Uri, content).Result;

                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var result = JsonConvert.DeserializeObject<RequestResult>(APIResponse.Content.ReadAsStringAsync().Result);

                        var rid = result.RequestID; //Convert.ToInt32(result.ToString().Split(":")[1]);
                        var response = await client.GetAsync($"{Request.Scheme}://{Request.Host.Value}/api/EInvoice/GetDocumentPackage?rid={ rid}");
                        return StatusCode((int)APIResponse.StatusCode, result);
                    }
                    else
                    {
                        var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                        return StatusCode((int)APIResponse.StatusCode, JsonContent);
                    }
                }
            }
            catch(Exception ex)
            {
                return BadRequest();
            }
         

        }

        [HttpGet]
        [Route("GetDocument")]
        public async Task<IActionResult> GetDocument(string uuid)
        {
            var companyData = DB.StCompanyInfo.FirstOrDefault();
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Accept.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
                    UriBuilder builder = new UriBuilder(BaseUrl);
                    builder.Path = $"api/v1.0/documents/{uuid}/raw";
                    HttpResponseMessage APIResponse = await client.GetAsync(builder.Uri);
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"GetDocument - " + APIResponse.IsSuccessStatusCode.ToString(), 1, 1);
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"GetDocument Response Success - " + APIResponse.StatusCode.ToString() + " - " + JsonContent, 1, 1);
                        var result = JsonConvert.DeserializeObject<GetDocumentResponse>(JsonContent);
                        return StatusCode((int)APIResponse.StatusCode, result);
                    }
                    else
                    {
                        var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"GetDocument Response Failed - " + APIResponse.StatusCode.ToString() + " - " + JsonContent, 1, 1);
                        return StatusCode((int)APIResponse.StatusCode, JsonContent);
                    }
                }
            }
            catch (Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From GetDocument - " + ex.InnerException + " - " + ex.Message, 1, 1);
                return BadRequest();

            }
        }


        [HttpGet]
        [Route("ReceievedDocuments")]
        public async Task<IActionResult> GetReceievedDocuments([FromQuery]string dateFrom , [FromQuery]string dateTo)
        {
            var companyData = DB.StCompanyInfo.FirstOrDefault();
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
      
                    //Max days to get invoices is 30 days
                    var submissionDateFrom = string.IsNullOrEmpty(dateFrom) ? DateTime.Today.AddDays(-30).ToString("M/d/yyyy"): dateFrom;
                    var submissionDateTo = string.IsNullOrEmpty(dateTo) ? DateTime.Today.ToString("M/d/yyyy"): dateTo;

                    var url = $"{BaseUrl}/api/v1.0/documents/search?submissionDatefrom={submissionDateFrom}&submissionDateTo={submissionDateTo}&direction=received";                   
                    HttpResponseMessage APIResponse = await client.GetAsync(url);

                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Receieved Documents - with URL:{url}, submissionDateFrom={submissionDateFrom}, SubmissionDateTo={submissionDateTo} " + APIResponse.IsSuccessStatusCode.ToString(), 1, 1);
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Receieved Documents Response Success - " + APIResponse.StatusCode.ToString() + " - " + JsonContent, 1, 1);
                        var result = JsonConvert.DeserializeObject<ReceievedDocumentsResponse>(JsonContent);
                        return StatusCode((int)APIResponse.StatusCode, result?.result);
                    }
                    else
                    {
                        var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Receieved Documents Response Failed - " + APIResponse.StatusCode.ToString() + " - " + JsonContent, 1, 1);
                        return StatusCode((int)APIResponse.StatusCode, JsonContent);
                    }
                }
            }
            catch (Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From Receieved Documents - " + ex?.InnerException + " - " + ex.Message, 1, 1);
                return BadRequest();

            }
        }


        //[HttpGet]
        //[Route("GetDocumentPrintout")]
        //public IActionResult GetDocumentPrintout(string uuid)
        //{
        //    var companyData = DB.StCompanyInfo.FirstOrDefault();
        //    using (HttpClient client = new HttpClient())
        //    {
        //        client.DefaultRequestHeaders.Accept.Clear();
        //        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        //        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
        //        UriBuilder builder = new UriBuilder(BaseUrl);
        //        builder.Path = $"api/v1.0/documents/{uuid}/pdf";
        //        HttpResponseMessage APIResponse = client.GetAsync(builder.Uri).Result;

        //        if (APIResponse.IsSuccessStatusCode)
        //        {
        //            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //            var result = JsonConvert.DeserializeObject<GetDocumentResponse>(JsonContent);
        //            return StatusCode((int)APIResponse.StatusCode, result);
        //        }
        //        else
        //        {
        //            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //            return StatusCode((int)APIResponse.StatusCode, JsonContent);
        //        }
        //    }
        //}

        #region OldSyncInvoices
        //[HttpPost]
        //[Route("SyncInvoices")]
        //public IActionResult SyncInvoices(List<Note4Post> invoicesList)
        //{
        //    var url = Request;

        //    var invoices = InvoiceBL.GetInvoiceModel(invoicesList, DB);
        //    using (HttpClient client = new HttpClient())
        //    {
        //        client.DefaultRequestHeaders.Accept.Clear();
        //        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        //        var serialzedInvoices = JsonConvert.SerializeObject(invoices);
        //        var content = new StringContent(serialzedInvoices, Encoding.UTF8, "application/json");
        //        //HttpResponseMessage APIResponse = client.PostAsync("http://localhost:57974/api/EInvoice/SubmitDocument", content).Result;
        //        HttpResponseMessage APIResponse = client.PostAsync($"{Request.Scheme}://{Request.Host.Value}/api/EInvoice/SubmitDocument", content).Result;

        //        if (APIResponse.IsSuccessStatusCode)
        //        {
        //            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //            var result = JsonConvert.DeserializeObject<SubmitDocumentResponse>(JsonContent);
        //            MyHelper.Utilities.save_Log("SyncInvoices-Success: " + JsonContent, null);
        //            MyHelper.Utilities.UpdateST_UserLog(DB, DateTime.Now.ToString(), JsonContent, (int)Utilities.FormsNames.Dongle_SN, 1);
        //            return StatusCode((int)APIResponse.StatusCode, result);
        //        }
        //        else
        //        {
        //            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //            MyHelper.Utilities.save_Log("SyncInvoices-Failure: " + JsonContent, null);
        //            MyHelper.Utilities.UpdateST_UserLog(DB, DateTime.Now.ToString(), JsonContent, (int)Utilities.FormsNames.Dongle_SN, 1);
        //            return StatusCode((int)APIResponse.StatusCode, JsonContent);
        //        }
        //    }
        //}
        #endregion

        #region OldSyncReturnInvoices
        //[HttpPost]
        //[Route("SyncReturnInvoices")]
        //public IActionResult SyncReturnInvoices(List<Note4Post> invoicesList)
        //{
        //    var url = Request;

        //    var invoices = InvoiceBL.GetInvoiceModel(invoicesList, DB);
        //    using (HttpClient client = new HttpClient())
        //    {
        //        client.DefaultRequestHeaders.Accept.Clear();
        //        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        //        var serialzedInvoices = JsonConvert.SerializeObject(invoices);
        //        var content = new StringContent(serialzedInvoices, Encoding.UTF8, "application/json");

        //        HttpResponseMessage APIResponse = client.PostAsync($"{Request.Scheme}://{Request.Host.Value}/api/EInvoice/CreditNote", content).Result;

        //        if (APIResponse.IsSuccessStatusCode)
        //        {
        //            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //            var result = JsonConvert.DeserializeObject<SubmitDocumentResponse>(JsonContent);
        //            MyHelper.Utilities.save_Log("SyncReturn Invoices-Success: " + JsonContent, null);
        //            MyHelper.Utilities.UpdateST_UserLog(DB, DateTime.Now.ToString(), JsonContent, (int)Utilities.FormsNames.Dongle_SN, 1);
        //            return StatusCode((int)APIResponse.StatusCode, result);
        //        }
        //        else
        //        {
        //            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //            MyHelper.Utilities.save_Log("SyncInvoices-Failure: " + JsonContent, null);
        //            MyHelper.Utilities.UpdateST_UserLog(DB, DateTime.Now.ToString(), JsonContent, (int)Utilities.FormsNames.Dongle_SN, 1);
        //            return StatusCode((int)APIResponse.StatusCode, JsonContent);
        //        }
        //    }

        //}
        #endregion

        [HttpPut]
        [Route("ChangeDocumentStatus")]
        public IActionResult ChangeDocumentStatus(string UUID, int status, string reason, int documentType)
        {
            var statusString = Enum.GetName(typeof(MyHelper.Utilities.DocumentStatus), status).ToLower();
            var changeStatus = new ChangeStatus() { status = statusString, reason = reason };
            var serialzed = JsonConvert.SerializeObject(changeStatus);
            using (HttpClient client = new HttpClient())
            {
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
                UriBuilder builder = new UriBuilder(BaseUrl);
                builder.Path = $"/api/v1.0/documents/state/{UUID}/state";
                var content = new StringContent(serialzed, Encoding.UTF8, "application/json");
                HttpResponseMessage APIResponse = client.PutAsync(builder.Uri, content).Result;

                if (APIResponse.IsSuccessStatusCode)
                {
                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                    InvoiceBL.UpdateDocumentAfterChangeStatus(UUID, documentType, status, DB);
                    return StatusCode((int)APIResponse.StatusCode, true);
                }
                else
                {
                    var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                    var error = JsonConvert.DeserializeObject<ErrorResult>(JsonContent);
                    InvoiceBL.LogRejectedChangeStatusDocuments(error.error, UUID, documentType, status, DB);
                    return StatusCode((int)APIResponse.StatusCode, JsonContent);
                }
            }
        }

        [HttpPost, Route("UpdateAcceptedDocuments")]
        public async Task<IActionResult> UpdateAcceptedDocuments(List<UpdateDocumentStatus> documents)
        {
            try
            {
                foreach (var document in documents)
                {
                    using (HttpClient client = new HttpClient())
                    {
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"1 - ", 1, 1);
                        client.DefaultRequestHeaders.Accept.Clear();
                        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                        var response = await client.GetAsync($"{Request.Scheme}://{Request.Host.Value}/api/EInvoice/GetDocument?uuid={document.keyValuePairs.Value}");
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"2 - ", 1, 1);
                        HttpResponseMessage APIResponse = response;
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"UpdateAcceptedDocuments Response Result - " + APIResponse.IsSuccessStatusCode.ToString(), 1, 1);
                        if (APIResponse.IsSuccessStatusCode)
                        {
                            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                            var result = JsonConvert.DeserializeObject<GetDocumentResponse>(JsonContent);
                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"UpdateAcceptedDocuments Response IsSuccess - " + JsonContent, 1, 1);
                            InvoiceBL.UpdateInvoicesAfterSync(result, document.keyValuePairs.Key, DB, document.documentType);
                        }
                        else
                        {
                            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"UpdateAcceptedDocuments Response IsFailed - " + JsonContent, 1, 1);
                        }
                    }
                }
                return Ok();
            }
            catch (Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From UpdateAcceptedDocuments  - " + ex.Message + " - " + ex.InnerException, 1, 1);
                return BadRequest();
            }
        }

        #region OldGenerateSign
        //public List<Signature> GenerateSign(string donglePinDec, Document document/*, ERPEinvoiceContext DB*/)
        //{
        //    var signatures = new List<Signature>();
        //    var model = new SignuatureVM();
        //    try
        //    {
        //        List<string> IgnoreProperties = new List<string>();
        //        IgnoreProperties.Add("signatures");
        //        string json = JsonConvert.SerializeObject(document, new JsonSerializerSettings { ContractResolver = new JsonIgnoreResolver(IgnoreProperties) });
        //        var certSerial = DAL.Shared.Dongle_SN;
        //        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Get cert. Serial", 1, 1);
        //        //var donglePinDec = companyData.DonglePin;
        //        var donglePIN = Crypto.DecryptStringAES(donglePinDec, Crypto.Key);
        //        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Get dongle Pin", 1, 1);
        //        model.CretSerial = certSerial;
        //        model.DonglePIN = donglePIN;
        //        model.DocumentJson = json;
        //        var serialzedmodel = JsonConvert.SerializeObject(model);
        //        var content = new StringContent(serialzedmodel, Encoding.UTF8, "application/json");
        //        using (HttpClient client = new HttpClient())
        //        {
        //            client.DefaultRequestHeaders.Accept.Clear();
        //            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        //            UriBuilder builder = new UriBuilder(SignuaturenUrl);
        //            //builder.Path = $"Generator/GetSigBase64";
        //            HttpResponseMessage APIResponse = client.PostAsync(builder.Uri, content).Result;

        //            if (APIResponse.IsSuccessStatusCode)
        //            {
        //                var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
        //                var result = JsonConvert.DeserializeObject<string>(JsonContent);
        //                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Get GenerateSign Success {result}", 1, 1);

        //                var issuerSignature = new Signature() { signatureType = "I", value = result };
        //                signatures.Add(issuerSignature);
        //                return signatures;
        //            }
        //            else
        //            {
        //                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Get GenerateSign Fail - {APIResponse.ReasonPhrase} - {builder.Uri}", 1, 1);
        //                return signatures;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Get GenerateSign Fail with ex - {ex.Message}", 1, 1);
        //        throw;
        //    }
        //}
        #endregion

        [HttpPost]
        [Route("GetUnSignDoc")]
        public IActionResult GetUnSignDocuments(List<Note4Post> invoicesList)
        {
            try
            {
                var invoices = InvoiceBL.GetInvoiceModel(invoicesList, DB);
                var validate = Validation.Validate(invoices, DB, mapper);
                if (validate.IsValid)
                {
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Documents List Is Valid", 1, 1);
                    var documentsList = validate.ValidDocumnents.Select(x => MapInvoice(x)).ToList();

                    var errors = Validation.ValidateTotalDocument(documentsList, validate.UnValidDocumnents, DB);

                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document unSigned", 1, 1);

                    //var unSignDocs = documentsList.Select(x => MyHelper.Utilities.GetDocumentToSync(x, DB)).ToList();
                    documentsList = documentsList.Where(x => !errors.Messages.Select(z => z.invoiceCode).Contains(x.internalID)).ToList();
                    if (documentsList.Count > 0)
                    {
                        var unSignDocs = documentsList.Select(x => MyHelper.Utilities.GetDocumentToSync(x, DB)).ToList();
                        var documents = new DocumentsToSignVM() { Documents = unSignDocs, Errors = errors };
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document To Sign - {JsonConvert.SerializeObject(documents)}", 1, 1);
                        return Ok(documents);
                    }
                    else
                    {
                        return BadRequest(errors);
                    }

                }
                else
                {
                    return BadRequest(validate.UnValidDocumnents);
                }
            }
            catch (Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From GetUnSignDoc -  " + ex.Message + " " + ex.InnerException, 1, 1);
                return BadRequest(ex.Message + " " + ex.InnerException);
            }

        }

        [HttpPost]
        [Route("SubmitSignDoc")]
        public IActionResult SubmitSignDoc(List<Models_1.ViewModels.Document> documentsList)
        {
            try
            {

                var invoiceIds = documentsList.Select(x => x.invoiceId).ToList();
                var documentTypeString = documentsList.FirstOrDefault().documentType;
                var documentType = (int)Enum.Parse(typeof(Models_1.ViewModels.DocumentType), documentTypeString);
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"SubmitSignDoc- Enter EndPoint", 1, 1);
                List<string> IgnoreProperties = new List<string>();
                IgnoreProperties.Add("invoiceId");
                if (documentsList.FirstOrDefault().documentType == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
                {
                    IgnoreProperties.Add("references");
                    documentType = (int)Models_1.ViewModels.DocumentType.I;
                }
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"SubmitSignDocs - Ignore Properties  ", 1, 1);
                var document = new Models_1.ViewModels.Documents()
                {
                    documents = documentsList
                };
                //MyHelper.Utilities.writeToFile("documentList: " + document.ToString(), "SubmitSignDoc");


                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"SubmitSignDocs - Create document object  ", 1, 1);
                var serialzeddocuments = JsonConvert.SerializeObject(document, new JsonSerializerSettings { ContractResolver = new JsonIgnoreResolver(IgnoreProperties) });
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" Final Object - {serialzeddocuments}", 1, 1);

                using (HttpClient client = new HttpClient())
                {
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" SubmitSignDoc - client", 1, 1);
                    client.Timeout = TimeSpan.FromHours(2);
                    client.DefaultRequestHeaders.Accept.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", AccessToken.GetAccessToken(DB));
                    var content = new StringContent(serialzeddocuments, Encoding.UTF8, "application/json");
                    UriBuilder builder = new UriBuilder(BaseUrl);
                    builder.Path = "/api/v1.0/documentsubmissions";
                    HttpResponseMessage APIResponse = client.PostAsync(builder.Uri, content).Result;
                    if (APIResponse.IsSuccessStatusCode)
                    {
                        var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                        var result = JsonConvert.DeserializeObject<Models_1.ViewModels.ResponseVM.SubmitDocumentVM.SubmitDocumentResponse>(JsonContent);
                        save_Log("Success APIResponse.Content: " + JsonContent, null);
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" SubmitSignDoc APIResponse.Content - " + JsonContent, 1, 1);
                        var messages = new List<ValidationMessage>();
                        //result.rejectedDocuments.ForEach(x => c.Add(InvoiceBL.LogRejectedDocument(x, DB, invoiceIds, documentType)));
                        foreach (var x in result.rejectedDocuments)
                        {
                            var error = InvoiceBL.LogRejectedDocument(x, DB, invoiceIds, documentType);
                            messages.Add(error);
                        }
                        var validationMessages = new ValidationMessages() { Messages = messages };
                        //result.acceptedDocuments.ForEach(async x => await UpdateAcceptedDocuments(x));
                        result.acceptedDocuments.ForEach(x => InvoiceBL.UpdateInvoicesUUIDsAfterSync(x, DB, invoiceIds, documentType));
                        return StatusCode((int)APIResponse.StatusCode, validationMessages);
                    }
                    else
                    {
                        var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Documentsubmissions Response.Content -  " + (int)APIResponse.StatusCode + " " + JsonContent, 1, 1);
                        save_Log("Documentsubmissions Response.Content : " + JsonContent, null);
                        var message = new ValidationMessage() { Message = new List<string> { JsonContent } };
                        var messages = new List<ValidationMessage>();
                        messages.Add(message);
                        var validationMessages = new ValidationMessages() { Messages = messages };
                        return StatusCode((int)APIResponse.StatusCode, validationMessages);
                    }
                }
            }

            catch (Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From SubmitSignDoc: " + ex.Message + " - " + ex.InnerException, 1, 1);
                return BadRequest(ex.Message + " - " + ex.InnerException.Message);

            }

        }

        [HttpPost]
        [Route("SubmitDocuments")]
        public IActionResult SubmitDocument(List<Note4Post> invoicesList)
        {
            //HttpResponseMessage APIResponsee = Service.ExecuteClientPost(BaseUrl, "api/v1.0/documentsubmissions", "", "");

            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Enter Submit Documents Method", 1, 1);
            try
            {
                var documentsToSubmit = new List<Models_1.ViewModels.Document>();
                var invoices = InvoiceBL.GetInvoiceModel(invoicesList, DB);
                var validate = Validation.Validate(invoices, DB, mapper);
                if (validate.IsValid)
                {
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Documents List Is Valid", 1, 1);
                    var documentsList = validate.ValidDocumnents.Select(x => MapInvoice(x)).ToList();
                    var errors = Validation.ValidateTotalDocument(documentsList, validate.UnValidDocumnents, DB);
                    var validationMessages = errors;
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document unSigned", 1, 1);
                    documentsList = documentsList.Where(x => !errors.Messages.Select(z => z.invoiceCode).Contains(x.internalID)).ToList();
                    if (documentsList.Count > 0)
                    {
                        var unSignDocs = documentsList.Select(x => MyHelper.Utilities.GetDocumentToSync(x, DB)).ToList();

                        documentsToSubmit =  unSignDocs.Select(x => SignDocument(x, invoices)).ToList();

                        #region Submit Documents
                        var invoiceIds = documentsToSubmit.Select(x => x.invoiceId).ToList();
                        var documentTypeString = documentsToSubmit.FirstOrDefault().documentType;
                        var documentType = (int)Enum.Parse(typeof(Models_1.ViewModels.DocumentType), documentTypeString);
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"SubmitSignDoc- Enter EndPoint", 1, 1);
                        List<string> IgnoreProperties = new List<string>();
                        IgnoreProperties.Add("invoiceId");
                        //IgnoreProperties.Add("taxTotals");
                        if (documentsList.FirstOrDefault().documentType == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
                        {
                            IgnoreProperties.Add("references");
                            documentType = (int)Models_1.ViewModels.DocumentType.I;
                        }
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"SubmitSignDocs - Ignore Properties  ", 1, 1);
                        var document = new Models_1.ViewModels.Documents()
                        {
                            documents = documentsToSubmit
                        };
                        MyHelper.Utilities.writeToFile(Environment.NewLine + "documentList: " + document.ToString(), "SubmitSignDoc");
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"SubmitDocument - Create document object  ", 1, 1);
                        var serialzeddocuments = JsonConvert.SerializeObject(document, new JsonSerializerSettings { ContractResolver = new JsonIgnoreResolver(IgnoreProperties) });
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" Final Object - {serialzeddocuments}", 1, 1);
                        var token = AccessToken.GetAccessToken(DB);
                        HttpResponseMessage APIResponse = Service.ExecuteClientPost(BaseUrl, "api/v1.0/documentsubmissions", token, serialzeddocuments);
                        if (APIResponse.IsSuccessStatusCode)
                        {
                            MyHelper.Utilities.writeToFile(Environment.NewLine + "APIResponse.IsSuccessStatusCode: " + APIResponse, "");
                            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                            var result = JsonConvert.DeserializeObject<Models_1.ViewModels.ResponseVM.SubmitDocumentVM.SubmitDocumentResponse>(JsonContent);
                            save_Log("Success APIResponse.Content: " + JsonContent, null);
                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $" SubmitSignDoc APIResponse.Content - " + JsonContent, 1, 1);
                            var messages = new List<ValidationMessage>();
                            foreach (var x in result.rejectedDocuments)
                            {
                                var error = InvoiceBL.LogRejectedDocument(x, DB, invoiceIds, documentType);
                                messages.Add(error);
                            }

                            validationMessages.Messages.AddRange(messages);
                            // var validationMessages = new ValidationMessages() { Messages = messages };
                            result.acceptedDocuments.ForEach(x => InvoiceBL.UpdateInvoicesUUIDsAfterSync(x, DB, invoiceIds, documentType));
                            return StatusCode((int)APIResponse.StatusCode, validationMessages);
                        }
                        else
                        {
                            MyHelper.Utilities.writeToFile(Environment.NewLine + "APIResponse.IsSuccessStatusCode: " + APIResponse, "");
                            var JsonContent = APIResponse.Content.ReadAsStringAsync().Result;
                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Documentsubmissions Response.Content -  " + (int)APIResponse.StatusCode + " " + JsonContent, 1, 1);
                            save_Log("Documentsubmissions Response.Content : " + JsonContent, null);
                            var message = new ValidationMessage() { Message = new List<string> { JsonContent } };
                            var messages = new List<ValidationMessage>();
                            messages.Add(message);
                            validationMessages.Messages.AddRange(messages);
                            //var validationMessages = new ValidationMessages() { Messages = messages };
                            return StatusCode((int)APIResponse.StatusCode, validationMessages);
                        }
                        #endregion
                    }

                    else
                    {
                        return BadRequest(errors);
                    }

                }
                else
                {
                    return BadRequest(validate.UnValidDocumnents);
                }
            }
            catch (Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From SubmitDocuments -  " + ex.Message + " " + ex.InnerException, 1, 1);
                return BadRequest(ex.Message + " " + ex.InnerException);
            }
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        private Document SignDocument(SignuatureVM doc, List<Invoice> invoices)
        {

            var document = JsonConvert.DeserializeObject<Models_1.ViewModels.Document>(doc.DocumentJson);
         

            //Will Be Replaced From DB
            /*publicKey: E34F8A2EC124D49FB521656DA80B5408C027DE3F
tokenSerial: 2B1DBE598063002D
libraryPath: G:\\LinkIT\\eps2003csp1164.dll*/
            //AGa
            //var publicKey = "D493F63F15AA40E8B7B72015F36F24B4D1997D8C";
            //var tokenSerial = "2AF706D300630012";
            //var libraryPath = "E:\\eps2003csp1164.dll";
            //Yoka
            //var publicKey = "E34F8A2EC124D49FB521656DA80B5408C027DE3F";
            //var tokenSerial = "2B1DBE598063002D";
            //var libraryPath = "G:\\LinkIT\\eps2003csp1164.dll";
            //Multi
            //var publicKey = "C722A189957AF3501C0EB07ADE6E3522DD1BF423";// this.publicKey;// "D493F63F15AA40E8B7B72015F36F24B4D1997D8C";
            //var tokenSerial = "2B1F146B8063002E";// this.tokenSerial;// "2AF706D300630012";
            //var libraryPath = "C:\\LinkIT\\SIGN\\eps2003csp1164.dll";// this.libraryPath;// "D:\\Work\\eps2003csp1164.dll";

            ////Capital
            //var publicKey = "C0DEA8FB5FC7BFF835CB9D1BEADD1AF56B919D6A";// this.publicKey;// "D493F63F15AA40E8B7B72015F36F24B4D1997D8C";
            //var tokenSerial = "2B27155180058009";// this.tokenSerial;// "2AF706D300630012";
            //var libraryPath = "F:\\LInkIT\\Linkit\\SIGN\\eps2003csp1164.dll";// this.libraryPath;// "D:\\Work\\eps2003csp1164.dll";

            //Prima
            //var publicKey = "32386439666232322D616266392D343231632D396131652D6232336539613665333963643100";// this.publicKey;// "D493F63F15AA40E8B7B72015F36F24B4D1997D8C";
            //var tokenSerial = "2B8A0DF20023000E";// this.tokenSerial;// "2AF706D300630012";
            //var libraryPath = "E:\\LinkIT\\sign\\eps2003csp1164.dll";// this.libraryPath;// "D:\\Work\\eps2003csp1164.dll";

            var CompanyInfo = DB.StCompanyInfo.FirstOrDefault();
            var publicKey = CompanyInfo.PublicKey;
            var tokenSerial = CompanyInfo.TokenSerial;
            var libraryPath = CompanyInfo.LibraryPath;
            var Pin = CompanyInfo.Pin;
            var CertificateCompanyType = CompanyInfo.CertificateCompanyType;
            var sign = "";
            var TokenLabel = !string.IsNullOrEmpty(CompanyInfo.ServerName) ? CompanyInfo.ServerName : "Egypt Trust";
            //var TokenLabel = this.TokenName;
            if (CertificateCompanyType == 1) //egypt trust
            {
                SignuatuerGenerator.GetSignatuerEgyptTrust signture = new SignuatuerGenerator.GetSignatuerEgyptTrust();
                var serializeDoc = signture.SerializeJSON(doc.DocumentJson, document);
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", "Serialize JSON", 1, 1);
                string hashData = signture.ComputeSha256Hash(serializeDoc);
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", "Compute Sha256 Hash", 1, 1);
                sign = signture.Sign(hashData, libraryPath, tokenSerial,TokenLabel, doc.DonglePIN, publicKey);
            }

            //else 
            //if (CertificateCompanyType == 2)
            //{
            //    sign = McDRSigniture.MCDRSignature.GetSigniture(Pin, "MCDR 2019", libraryPath, doc.DocumentJson);

            //}
            MyHelper.Utilities.UpdateST_UserLog(DB, "1", "Sign Generated", 1, 1);
            document.signatures = new List<Models_1.ViewModels.Signature>()
                {
                new Models_1.ViewModels.Signature(){ signatureType="I",value=sign }
                };
            document.invoiceId = invoices.Where(x => x.invoiceCode == document.internalID).Select(x => x.invoiceId ?? 0).FirstOrDefault();
            return document;
        }
    }
}



