﻿IF OBJECT_ID(N'dbo.[Sl_Add_DetailSubTaxValue]', N'U') IS NULL BEGIN   

SET ANSI_NULLS ON

SET QUOTED_IDENTIFIER ON

CREATE TABLE [dbo].[Sl_Add_DetailSubTaxValue](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[Sl_AddDetailId]   [int] NOT NULL,
	[esubTypeId] [int] NOT NULL,
	[value] [decimal](20, 6) NOT NULL,
	[TaxRatio] [decimal] (20,6) null
 CONSTRAINT [PK_Sl_Add_DetailSubTaxValue] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

End

if COL_LENGTH('SL_Add_Detail','bonusDiscount') is  NULL
BEGIN
alter table [dbo].[SL_Add_Detail]
Add bonusDiscount [decimal](20, 6) null
end
go