﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class SlCustomerGroup
    {
        public int CustomerGroupId { get; set; }
        public string CustomerGroupCode { get; set; }
        public string CgnameAr { get; set; }
        public string CgnameEn { get; set; }
        public decimal MaxCredit { get; set; }
        public string Desc { get; set; }
        public int AccountId { get; set; }
        public bool CustomersHaveSeparateAccount { get; set; }
        public int? CustomersDefaultAccount { get; set; }
        public int? ParentGroupId { get; set; }
    }
}
