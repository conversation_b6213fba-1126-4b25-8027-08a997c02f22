USE [dolphin1]
GO

/****** Object:  Table [dbo].[E_RecievedDocument]    Script Date: 13/05/2024 03:25:57 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[E_RecievedDocument](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[uuid] [varchar](200) NOT NULL,
	[publicUrl] [varchar](600) NULL,
	[typeName] [varchar](50) NULL,
	[typeNameEn] [varchar](400) NULL,
	[typeNameAr] [nvarchar](400) NULL,
	[issuerName] [nvarchar](400) NOT NULL,
	[dateTimeReceived] [varchar](50) NOT NULL,
	[dateTimeIssued] [varchar](50) NOT NULL,
	[total] [decimal](20, 6) NOT NULL,
	[net] [decimal](20, 6) NOT NULL,
	[totalDiscount] [decimal](20, 6) NOT NULL,
	[status] [varchar](50) NOT NULL,
	[totalSales] [decimal](20, 6) NULL,
 CONSTRAINT [PK_dbo.E_RecievedDocument] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO


