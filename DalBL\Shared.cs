﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace DAL
{
    public class Shared
    {
        public static bool IsEnglish = false;

        public static List<UserPriv> LstUserPrvlg;
        public static string TrialVersion = "";
        //public static HR_User user;

        //licensed modules
        public static bool MaufacturingAvailable;
        public static bool AccountingAvailable;
        public static bool InventoryAvailable;
        public static bool HRAvailable;
        public static bool CarsAvailable;
        public static bool PayslipAvailable;
        public static bool POSAvailable;
        public static bool PriceListAvailable;
        public static bool TaxAvailable;
        public static bool ItemMatrixAvailable;
        public static bool ContractAvailable;
        public static bool EgyptPharmacyTaxAvailable;
        public static bool WoodTradeAvailable;
        public static bool DimensionsAvailable;
        public static bool JobOrderAvailable;
        // mohammad 22-10-2019
        public static bool LaundryAvailable;
        //////////////////////
        public static bool SalesOrderAvailable;
        public static bool MarkettingAvailable;
        public static bool CurrencyAvailable;
        public static bool ChecksAvailable;
        public static bool LcAvailable;
        public static bool BascolAvailable;
        public static bool SalesManAvailable;
        public static bool RealEstateAvailable;
        public static bool ItemsPostingAvailable;
        public static bool SysModelIsERP;
        public static bool FpDependOnInOut;
        public static bool OfflinePostToGL;
        public static bool InvoicePostToStore;
        public static bool PrInvoicePostToStore;
        public static bool StockIsPeriodic;
        public static bool MustApproveSalesOrder;
        public static bool LibraAvailabe;
        public static bool SalesOnlyAvailable;
        public static bool FA;
        public static bool Mall;
        public static bool Imp_exp;
        public static bool Shareholder;
        public static bool Pay_Rec;
        public static bool E_invoiceAvailable;
        public static string Dongle_SN;

        public static ST_Store st_Store;//user settings
        public static ST_CompanyInfo st_comp;//user settings
        public static List<ST_Currency> lstCurrency;
        public static List<ST_IncomeTaxDiscount> lstIncomeTaxDiscount;
        public static List<ST_IncomeTaxLevel> lstIncomeTaxLevel;


        public static string CompName;

        #region user profile
        public static int UserId; //assigned after login
        public static string UserName;
        public static DateTime? PR_I_FromDate, PR_I_ToDate;
        public static DateTime? PR_R_FromDate, PR_R_ToDate;

        public static DateTime? SL_I_FromDate, SL_I_ToDate;
        public static DateTime? SL_R_FromDate, SL_R_ToDate;

        public static DateTime? SL_Q_FromDate, SL_Q_ToDate;

        public static DateTime? Man_FromDate, Man_ToDate;
        public static DateTime? Man_QC_FromDate, Man_QC_ToDate;

        public static DateTime? Mr_InDrctSl_FromDate, Mr_InDrctSl_ToDate;

        public static DateTime? IC_InTrns_FromDate, IC_InTrns_ToDate;
        public static DateTime? IC_OutTrns_FromDate, IC_OutTrns_ToDate;
        public static DateTime? IC_Damage_FromDate, IC_Damage_ToDate;
        public static DateTime? IC_Transfer_FromDate, IC_Transfer_ToDate;

        public static string att_clWeekEnd;
        public static string att_clFormalVacation;
        public static string att_clEmpVacation;
        public static string att_clEmpAbsence;
        public static string att_clDelay;

        public static DateTime? HR_vacation_FromDate, HR_vacation_ToDate;
        public static DateTime? HR_Absence_FromDate, HR_Absence_ToDate;
        public static DateTime? HR_Delay_FromDate, HR_Delay_ToDate;
        public static DateTime? HR_OverTime_FromDate, HR_OverTime_ToDate;
        public static DateTime? HR_Reward_FromDate, HR_Reward_ToDate;
        public static DateTime? HR_Penality_FromDate, HR_Penality_ToDate;
        public static DateTime? HR_Pay_FromDate, HR_Pay_ToDate;
        public static DateTime? HR_Loan_FromDate, HR_Loan_ToDate;
        public static DateTime? HR_Eval_FromDate, HR_Eval_ToDate;
        public static DateTime? HR_Prom_FromDate, HR_Prom_ToDate;
        public static DateTime? HR_SponsrChng_FromDate, HR_SponsrChng_ToDate;
        public static DateTime? HR_Train_FromDate, HR_Train_ToDate;
        public static DateTime? HR_ShiftReplace_FromDate, HR_ShiftReplace_ToDate;

        public static DateTime? Acc_CashTransfer_FromDate, Acc_CashTransfer_ToDate;
        public static DateTime? Cash_FromDate, Cash_ToDate;
        public static DateTime? Jrnl_FromDate, Jrnl_ToDate;
        public static DateTime? Acc_Rev_FromDate, Acc_Rev_ToDate;
        public static DateTime? Acc_Exp_FromDate, Acc_Exp_ToDate;

        public static DateTime? Acc_RecNote_FromDate, Acc_RecNote_ToDate;
        public static DateTime? Acc_PayNote_FromDate, Acc_PayNote_ToDate;

        public static string Acc_PayNote_Still;
        public static string Acc_PayNote_Paid;
        public static string Acc_PayNote_Rejected;
        public static string Acc_PayNote_Overdue;
        public static string Acc_ReceiveNote_Still;
        public static string Acc_ReceiveNote_Paid;
        public static string Acc_ReceiveNote_Rejected;
        public static string Acc_ReceiveNote_Overdue;

        public static DateTime? JO_Reg_FromDate, JO_Reg_ToDate;
        public static DateTime? JO_Due_FromDate, JO_Due_ToDate;

        public static DateTime? Acc_DebitNote_FromDate, Acc_DebitNote_ToDate;
        public static DateTime? Acc_CreditNote_FromDate, Acc_CreditNote_ToDate;

        public static DateTime? Weight_FromDate, Weight_ToDate;

        public static string ActiveNavBarGroup_Loaded;
        public static string StyleName;
        public static bool ShowMainChart_;
        #endregion

        public static HR_User user;
        public static List<UserIdName> lst_Users;
        public static DateTime minDate;
        public static DateTime maxDate;

        //used for saving reports designs files
        public static string ReportsPath;
        public static string ReceiptPrinterName;
        public static string AttachmentsPath;

        static Shared()
        {
            //minDate = Convert.ToDateTime("1/1/1753");
            //maxDate = Convert.ToDateTime("31/12/9999");
            minDate = DateTime.MinValue.AddYears(1754).Date;
            maxDate = DateTime.MaxValue.AddYears(-1000).Date;
        }

    }
}
