﻿if COL_LENGTH('ST_CompanyInfo','E_invoiceAvailable') is  NULL
BEGIN
alter table [dbo].[ST_CompanyInfo]
Add E_invoiceAvailable [nvarchar](200) null
end
go

IF OBJECT_ID(N'dbo.[ST_InvoiceBook]', N'U') IS NULL 
BEGIN   

SET ANSI_NULLS ON
SET QUOTED_IDENTIFIER ON

CREATE TABLE [dbo].[ST_InvoiceBook](
	[InvoiceBookId] [int] IDENTITY(1,1) NOT NULL,
	[InvoiceBookName] [nvarchar](200) NOT NULL,
	[IsTaxable] [bit] NULL,
	[ProcessId] [int] NOT NULL,
	[PrintFileName] [nvarchar](200) NULL,
	[Notes] [nvarchar](max) NULL,
 CONSTRAINT [PK_dbo.ST_InvoiceBook] PRIMARY KEY CLUSTERED 
(
	[InvoiceBookId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
end
GO

