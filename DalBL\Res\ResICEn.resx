﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="txtFrom" xml:space="preserve">
    <value> From </value>
  </data>
  <data name="txtFromDate" xml:space="preserve">
    <value> From Date </value>
  </data>
  <data name="MsgNameExist" xml:space="preserve">
    <value>This name already exists</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>some data are incorrect</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>You made some changes, do you want to save</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>Sorry, you don't have privilege to edit record</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>Sorry, you don't have privilege to add new record</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>Saved Successfully</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="txtTo" xml:space="preserve">
    <value> To </value>
  </data>
  <data name="txtToDate" xml:space="preserve">
    <value> To Date </value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>Deleted Successfully</value>
  </data>
  <data name="txtOpenBalance" xml:space="preserve">
    <value> Open Balance </value>
  </data>
  <data name="txtDrawer" xml:space="preserve">
    <value> Drawer </value>
  </data>
  <data name="txtCredit" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="txtCustomer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="txtDebit" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="txtVendor" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>delete row ?</value>
  </data>
  <data name="MsgNumExist" xml:space="preserve">
    <value>This number already exists</value>
  </data>
  <data name="MsgAskDel" xml:space="preserve">
    <value> Are you sure you want to delete </value>
  </data>
  <data name="txtDealerNm" xml:space="preserve">
    <value>Dealer Name</value>
  </data>
  <data name="txtItemsBalance" xml:space="preserve">
    <value>Items Balance</value>
  </data>
  <data name="txtItemsBestSell" xml:space="preserve">
    <value>Best-Selling Items</value>
  </data>
  <data name="txtItemsLeastSell" xml:space="preserve">
    <value>Least-Selling Items</value>
  </data>
  <data name="txtItemsReorder" xml:space="preserve">
    <value>Items On Reorder</value>
  </data>
  <data name="MsgItemsPrint" xml:space="preserve">
    <value>Sorry, you can not print more than 500 items </value>
  </data>
  <data name="txtAssembly" xml:space="preserve">
    <value>Assembly</value>
  </data>
  <data name="txtCategory" xml:space="preserve">
    <value>Category: </value>
  </data>
  <data name="txtComapny" xml:space="preserve">
    <value>Comapny: </value>
  </data>
  <data name="txtItem" xml:space="preserve">
    <value>Item: </value>
  </data>
  <data name="txtItemMovement" xml:space="preserve">
    <value>Items Movement</value>
  </data>
  <data name="txtItemTotalPurchases" xml:space="preserve">
    <value>Item Total Purchases</value>
  </data>
  <data name="txtItemTotalPurchasesReturns" xml:space="preserve">
    <value>Item Total Purchases Returns</value>
  </data>
  <data name="txtItemTotalSales" xml:space="preserve">
    <value>Item Total Sales</value>
  </data>
  <data name="txtItemTotalSalesReturn" xml:space="preserve">
    <value>Item Total Sales Returns</value>
  </data>
  <data name="txtRaw" xml:space="preserve">
    <value>Inventory</value>
  </data>
  <data name="txtService" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="txtStore" xml:space="preserve">
    <value>Store: </value>
  </data>
  <data name="txtItemsNotSold" xml:space="preserve">
    <value>Items Not Sold</value>
  </data>
  <data name="MsgCatCode" xml:space="preserve">
    <value>Please, enter category code</value>
  </data>
  <data name="MsgCatName" xml:space="preserve">
    <value>Please, enter category name</value>
  </data>
  <data name="MsgCodeExist" xml:space="preserve">
    <value>This code already exists</value>
  </data>
  <data name="MsgCompCode" xml:space="preserve">
    <value>Please, enter Group code</value>
  </data>
  <data name="MsgCompName" xml:space="preserve">
    <value>Please, enter Group name</value>
  </data>
  <data name="MsgDelCat" xml:space="preserve">
    <value>Are you sure you want to delete this category</value>
  </data>
  <data name="MsgDelCatDenied" xml:space="preserve">
    <value>Sorry, you can't delete this category, there is some items related to it</value>
  </data>
  <data name="MsgDelComp" xml:space="preserve">
    <value>Are you sure you want to delete this Group</value>
  </data>
  <data name="MsgDelCompDenied" xml:space="preserve">
    <value>Sorry, you can't delete this Group , there is some items related to it</value>
  </data>
  <data name="MsgDelStore" xml:space="preserve">
    <value>Are you sure you want to delete this store</value>
  </data>
  <data name="MsgFNameExist" xml:space="preserve">
    <value>Foreign name already exists </value>
  </data>
  <data name="MsgMainStoreDel" xml:space="preserve">
    <value>Sorry, you can't delete the main branch</value>
  </data>
  <data name="MsgStoreCode" xml:space="preserve">
    <value>Please, enter code</value>
  </data>
  <data name="MsgStoreItems" xml:space="preserve">
    <value>sorry, you can't delete this store, it contains items inside</value>
  </data>
  <data name="MsgStoreJournals" xml:space="preserve">
    <value>You have to delete this branch transactions first</value>
  </data>
  <data name="MsgStoreName" xml:space="preserve">
    <value>Please, enter name</value>
  </data>
  <data name="MsgZeroCode" xml:space="preserve">
    <value>Code can't be zero</value>
  </data>
  <data name="MsgDelBarcode" xml:space="preserve">
    <value>Are you sure you want to delete this international barcode</value>
  </data>
  <data name="MsgDelBarcode2" xml:space="preserve">
    <value>Are you sure you want to delete the last international barcode</value>
  </data>
  <data name="MsgDelExpenseAsk" xml:space="preserve">
    <value>Delete expenses?</value>
  </data>
  <data name="MsgDelItem" xml:space="preserve">
    <value>Are you sure you want to delete this item</value>
  </data>
  <data name="MsgDelItemAsk" xml:space="preserve">
    <value>Delete item?</value>
  </data>
  <data name="MsgDelItemDenied1" xml:space="preserve">
    <value>Sorry, you can't delete this item, it exists in Bill Of Material of other item</value>
  </data>
  <data name="MsgDelItemDenied2" xml:space="preserve">
    <value>Sorry, you can't delete this item, there exist some quantity of it in store</value>
  </data>
  <data name="MsgInterCode" xml:space="preserve">
    <value>Please, enter international code correctly</value>
  </data>
  <data name="MsgInterCodeExist" xml:space="preserve">
    <value>This International code is used before</value>
  </data>
  <data name="ValExpenses" xml:space="preserve">
    <value>Please enter expenses</value>
  </data>
  <data name="ValExpensesZero" xml:space="preserve">
    <value>Expenses must be larger than zero</value>
  </data>
  <data name="ValTxtItemBOMDel" xml:space="preserve">
    <value>Sorry, you can't delete this item, it's already in use by bills of materials</value>
  </data>
  <data name="ValTxtItemCode1" xml:space="preserve">
    <value>Please, enter item code1</value>
  </data>
  <data name="ValTxtItemCode1Exist" xml:space="preserve">
    <value>Code1 is used by another item</value>
  </data>
  <data name="ValTxtItemCode2Exist" xml:space="preserve">
    <value>Codes are used by another item</value>
  </data>
  <data name="ValTxtItemExist" xml:space="preserve">
    <value>Item already exits</value>
  </data>
  <data name="ValTxtItemName" xml:space="preserve">
    <value>Please, enter item name</value>
  </data>
  <data name="ValTxtItemPurchasePrice" xml:space="preserve">
    <value>Please, enter item purchase price</value>
  </data>
  <data name="ValTxtItemSalePrice" xml:space="preserve">
    <value>Purchase price can't be larger than sales price of retail unit</value>
  </data>
  <data name="ValTxtItemSellPrice" xml:space="preserve">
    <value>Please, enter retail unit sales price</value>
  </data>
  <data name="ValTxtItemUOM1" xml:space="preserve">
    <value>You can't enter wholesale unit2 without entering wholesale unit1 before</value>
  </data>
  <data name="ValTxtSelectItem" xml:space="preserve">
    <value>Please select item</value>
  </data>
  <data name="ValTxtSelectUOM" xml:space="preserve">
    <value>Please select UOM</value>
  </data>
  <data name="VatTxtZeroQty" xml:space="preserve">
    <value>Quantity can't be zero</value>
  </data>
  <data name="txtEditItemQty" xml:space="preserve">
    <value>New quantity should be larger than or equal Zero</value>
  </data>
  <data name="txtEditItemQty2" xml:space="preserve">
    <value>Enter new quantity for at least one item</value>
  </data>
  <data name="MsgDeleteInv" xml:space="preserve">
    <value>Are you sure you want to delete Bill and all its details </value>
  </data>
  <data name="MsgNoEnoughQty" xml:space="preserve">
    <value>There is no enough Qty of some items</value>
  </data>
  <data name="MsgNoEnoughQty_continue" xml:space="preserve">
    <value>There is no enough Qty of some items, Continue ?</value>
  </data>
  <data name="txtExceedsReorder" xml:space="preserve">
    <value>Exceeds Reorder level , Continue ?</value>
  </data>
  <data name="txtIC_damage" xml:space="preserve">
    <value>Damage Bill</value>
  </data>
  <data name="txtIC_intrns" xml:space="preserve">
    <value>Receive Bill</value>
  </data>
  <data name="txtIC_openbalance" xml:space="preserve">
    <value>Open Balance</value>
  </data>
  <data name="txtIC_outtrns" xml:space="preserve">
    <value>Outgoing Bill</value>
  </data>
  <data name="txtIC_stocktaking" xml:space="preserve">
    <value>Stocktaking </value>
  </data>
  <data name="txtIC_storemove" xml:space="preserve">
    <value>Transfer Bill</value>
  </data>
  <data name="txtValidateDiscount" xml:space="preserve">
    <value>Please select discount</value>
  </data>
  <data name="txtValidateInvNumber" xml:space="preserve">
    <value>Please record bill number</value>
  </data>
  <data name="txtValidateItem" xml:space="preserve">
    <value>Please select Item</value>
  </data>
  <data name="txtValidateMaxDiscount" xml:space="preserve">
    <value>Discount ratio must be less than 100</value>
  </data>
  <data name="txtValidateMinQty" xml:space="preserve">
    <value>Current qty after decrease outgoing qty is less than item minimum qty </value>
  </data>
  <data name="txtValidateNoRows" xml:space="preserve">
    <value>Please enter at least one item </value>
  </data>
  <data name="txtValidateQty" xml:space="preserve">
    <value>Qty must be larger than 0</value>
  </data>
  <data name="txtValidateReorderQty" xml:space="preserve">
    <value>Current qty after decrease outgoing qty is less than item reorder qty</value>
  </data>
  <data name="txtValidateSPrice" xml:space="preserve">
    <value>Sell price must be larger than 0</value>
  </data>
  <data name="txtValidateSpriceLargerPprice" xml:space="preserve">
    <value>Sell Price must be larger than purchase price</value>
  </data>
  <data name="txtValidateStoreQty" xml:space="preserve">
    <value>Sold qty cant be larger than store qty</value>
  </data>
  <data name="txtValidateUom" xml:space="preserve">
    <value>Please select unit of measure</value>
  </data>
  <data name="txt_Number" xml:space="preserve">
    <value>Number </value>
  </data>
  <data name="txtInvDate" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="txtNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="txtSerial" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="txtValidateItemMaxLimit" xml:space="preserve">
    <value>Purchased qty and store qty more than max limit of the item</value>
  </data>
  <data name="txtValidatePPrice" xml:space="preserve">
    <value>Purchase price must be larger than 0</value>
  </data>
  <data name="MsgItemdoesntExist" xml:space="preserve">
    <value>Item doesn't exist</value>
  </data>
  <data name="MsgstocktakingCommited" xml:space="preserve">
    <value>Stocktalking Commited Successfuly </value>
  </data>
  <data name="Msgstocktaking_cant_upadte" xml:space="preserve">
    <value>Sory , you cant edit or delete a commited stocktaking</value>
  </data>
  <data name="Msgstocktaking_commit" xml:space="preserve">
    <value>Commit stocktaking cant be edited or backward after commit , Are you Sure ?</value>
  </data>
  <data name="Msgstocktaking_lose" xml:space="preserve">
    <value>You have a deficit in the inventory by Value :</value>
  </data>
  <data name="Msgstocktaking_save" xml:space="preserve">
    <value>Save stocktaking doesn't mean commit it in the store , to commit the stocktaking please press Commit</value>
  </data>
  <data name="Msgstocktaking_win" xml:space="preserve">
    <value>You have an increase in inventory by Value :</value>
  </data>
  <data name="MsgValidateDateandStore" xml:space="preserve">
    <value>Please Select Store and Date</value>
  </data>
  <data name="Msgvalidateoutqty" xml:space="preserve">
    <value>Outgoing qty is larger than current store qty</value>
  </data>
  <data name="Msgvalidateoutqty_forbid" xml:space="preserve">
    <value>Outgoing qty can't be larger than the current store qty</value>
  </data>
  <data name="txtItemExist" xml:space="preserve">
    <value>Item Already exist</value>
  </data>
  <data name="txtJornalOpenBalance" xml:space="preserve">
    <value>Open balance Journal</value>
  </data>
  <data name="MsgValidateStore" xml:space="preserve">
    <value>Please Select Source Store and Destination Store</value>
  </data>
  <data name="MsgValidateStore1" xml:space="preserve">
    <value>Source Store cant be the same as Destination Store</value>
  </data>
  <data name="CategoryCode" xml:space="preserve">
    <value>Category Code</value>
  </data>
  <data name="categoryFname" xml:space="preserve">
    <value>Category F Name</value>
  </data>
  <data name="categoryName" xml:space="preserve">
    <value>Category Name</value>
  </data>
  <data name="CompanyCode" xml:space="preserve">
    <value>Group Code</value>
  </data>
  <data name="companyFname" xml:space="preserve">
    <value>Group F Name</value>
  </data>
  <data name="companyName" xml:space="preserve">
    <value>Group Name</value>
  </data>
  <data name="variesPerItem" xml:space="preserve">
    <value>varies per item</value>
  </data>
  <data name="FixedRatio" xml:space="preserve">
    <value>Fixed Ratio%</value>
  </data>
  <data name="MsgDelPriceList" xml:space="preserve">
    <value>Are you sure you want to delete this Price List</value>
  </data>
  <data name="MsgDelPriceListDenied" xml:space="preserve">
    <value>Sorry, you can't delete this Price List, there is some customers use it</value>
  </data>
  <data name="MsgPLNAme" xml:space="preserve">
    <value>Please, enter Price List name</value>
  </data>
  <data name="MsgPLRatio" xml:space="preserve">
    <value>Please, enter Ratio correctly</value>
  </data>
  <data name="PerItem" xml:space="preserve">
    <value>Per Item</value>
  </data>
  <data name="MsgDelItemDenied3" xml:space="preserve">
    <value>Sorry, you can't delete this item, it exists in a price level list</value>
  </data>
  <data name="MsgDelItemDenied4" xml:space="preserve">
    <value>Sorry, you can't delete this item, it exists in some invoices</value>
  </data>
  <data name="msgUserDefaultStore" xml:space="preserve">
    <value>This store is assigned as default store for some users</value>
  </data>
  <data name="MsgSalePerQty" xml:space="preserve">
    <value>Please review item sales prices per quantity</value>
  </data>
  <data name="MsgData" xml:space="preserve">
    <value>Please complete data correctly</value>
  </data>
  <data name="MsgMtCode" xml:space="preserve">
    <value>Please enter matrix code</value>
  </data>
  <data name="MsgMtName" xml:space="preserve">
    <value>Please enter matrix name</value>
  </data>
  <data name="MsgMtRows" xml:space="preserve">
    <value>Please enter matrix attributes</value>
  </data>
  <data name="MsgMtrxValid" xml:space="preserve">
    <value>Please check data entered correctly</value>
  </data>
  <data name="MsgDelMtxItems" xml:space="preserve">
    <value>You have to delete child matrix items first</value>
  </data>
  <data name="MsgMtrxDelDenied" xml:space="preserve">
    <value>Sorry, you can't delete item, some matrix items are already used</value>
  </data>
  <data name="MsgSaveItemFirst" xml:space="preserve">
    <value>Please save item first</value>
  </data>
  <data name="txtMatrix" xml:space="preserve">
    <value>Matrix</value>
  </data>
  <data name="VendorExist" xml:space="preserve">
    <value>Vendor Exists Before</value>
  </data>
  <data name="MsgDelPriceListDenied1" xml:space="preserve">
    <value>Sorry, you can't delete this Price List, there is some vendors use it</value>
  </data>
  <data name="txtSubTotal" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="MsgMerchendaisingAcc" xml:space="preserve">
    <value>Please select Merchandising accounts in settings screen</value>
  </data>
  <data name="MsgBranch" xml:space="preserve">
    <value>Sorry, you can't add  more than two levels for branches and stores.</value>
  </data>
  <data name="MsgBranchItems" xml:space="preserve">
    <value>Sorry, you can't make a sub-store to that branch, because it used also as a store</value>
  </data>
  <data name="MsgSubStore" xml:space="preserve">
    <value>Sorry, you can't make a sub-store to that branch, try to contact system administrator</value>
  </data>
  <data name="MsgDelMtrx" xml:space="preserve">
    <value>Sorry, you can't delete this matrix, it's already in use</value>
  </data>
  <data name="MsgDelInvoiceBook" xml:space="preserve">
    <value>Are you sure you want to delete invoice book</value>
  </data>
  <data name="delBranchDenied" xml:space="preserve">
    <value>Sorry you can't delete this branch, you've to delete it's stores first</value>
  </data>
  <data name="storeLevel" xml:space="preserve">
    <value>Sorry, you can't change Branch or Store Level</value>
  </data>
  <data name="DelItemOnMr" xml:space="preserve">
    <value>Sorry, you can't delete this item, it's in use by marketting plans</value>
  </data>
  <data name="MsgDimension" xml:space="preserve">
    <value>Item Dimensions can't be less than or equal to zero</value>
  </data>
  <data name="MsgMtrxData" xml:space="preserve">
    <value>Please review dimensions data for matrix items</value>
  </data>
  <data name="StockAccs" xml:space="preserve">
    <value>Please select all Branch/Stock Accounts</value>
  </data>
  <data name="txtIC_intrnsList" xml:space="preserve">
    <value>Receive Bills</value>
  </data>
  <data name="txtIC_openbalanceList" xml:space="preserve">
    <value>Open Balances</value>
  </data>
  <data name="frmInTrnsList" xml:space="preserve">
    <value>Receive Bills</value>
  </data>
  <data name="txt_Code1" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="txt_Code2" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="txt_CurrentQty" xml:space="preserve">
    <value>Current Q</value>
  </data>
  <data name="txt_Qty" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="subCat" xml:space="preserve">
    <value>Sorry, you can't add subcategory, there's items already registered</value>
  </data>
  <data name="IcSubCat" xml:space="preserve">
    <value>Please select subcategory</value>
  </data>
  <data name="ItemCodesDuplicate" xml:space="preserve">
    <value>Codes are duplicated</value>
  </data>
  <data name="ValItemCodeLength" xml:space="preserve">
    <value>Item code length cann't exceed item length in barcode template</value>
  </data>
  <data name="SelectVendor" xml:space="preserve">
    <value>Please select vendor</value>
  </data>
  <data name="MsgOutBefore" xml:space="preserve">
    <value>disbursed before</value>
  </data>
  <data name="rpt_IC_ItemsZeroQty" xml:space="preserve">
    <value>Store With zero Qty</value>
  </data>
  <data name="ReceiveGood" xml:space="preserve">
    <value>Receive Goods</value>
  </data>
  <data name="ReceiveGoodList" xml:space="preserve">
    <value>Receive Goods List</value>
  </data>
  <data name="txtIC_replacement" xml:space="preserve">
    <value>Replacement</value>
  </data>
</root>