﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppName" xml:space="preserve">
    <value>LinkIT ERP System</value>
  </data>
  <data name="mi_CloseAll" xml:space="preserve">
    <value>Close All</value>
  </data>
  <data name="mi_HideCharts" xml:space="preserve">
    <value>Hide Charts</value>
  </data>
  <data name="mi_NewExpenses" xml:space="preserve">
    <value>New Expenses</value>
  </data>
  <data name="mi_NewPayNote" xml:space="preserve">
    <value>Cash Payment Note</value>
  </data>
  <data name="mi_NewReceiveNote" xml:space="preserve">
    <value>Cash Receive Note</value>
  </data>
  <data name="mi_NewRevenue" xml:space="preserve">
    <value>New Revenue</value>
  </data>
  <data name="mi_ShowAll" xml:space="preserve">
    <value>Show All</value>
  </data>
  <data name="mi_ShowCharts" xml:space="preserve">
    <value>Show Charts</value>
  </data>
  <data name="MsgFileNotExist" xml:space="preserve">
    <value>File doesn't exist</value>
  </data>
  <data name="Fri" xml:space="preserve">
    <value>Friday</value>
  </data>
  <data name="Mon" xml:space="preserve">
    <value>Monday</value>
  </data>
  <data name="Sat" xml:space="preserve">
    <value>Saturday</value>
  </data>
  <data name="Sun" xml:space="preserve">
    <value>Sunday</value>
  </data>
  <data name="Thu" xml:space="preserve">
    <value>Thursday</value>
  </data>
  <data name="Tus" xml:space="preserve">
    <value>Tusday</value>
  </data>
  <data name="Wed" xml:space="preserve">
    <value>Wednesday</value>
  </data>
  <data name="Fortnightly" xml:space="preserve">
    <value>Fortnightly</value>
  </data>
  <data name="Hourly" xml:space="preserve">
    <value>Hourly</value>
  </data>
  <data name="Monthly" xml:space="preserve">
    <value>Monthly</value>
  </data>
  <data name="Pertask" xml:space="preserve">
    <value>Per-Task</value>
  </data>
  <data name="Weekly" xml:space="preserve">
    <value>Weekly</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>You made some changes, do you want to save</value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>Deleted Successfully</value>
  </data>
  <data name="MsgDelExpenses" xml:space="preserve">
    <value>Are you sure you want to delete expenses ?</value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>delete row ?</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>some data are incorrect</value>
  </data>
  <data name="Msgmanfend" xml:space="preserve">
    <value>Job Order ended successfully</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>Sorry, you don't have privilege to edit record</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>Sorry, you don't have privilege to add new record</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>Saved Successfully</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="MsgValidatemanfnumber" xml:space="preserve">
    <value>Please Enter Job order Number</value>
  </data>
  <data name="MsgValidatemanfstartdate" xml:space="preserve">
    <value>Please Enter Job Order Start Date</value>
  </data>
  <data name="MsgValidatemanfstore" xml:space="preserve">
    <value>Please Enter the Produced Item Store </value>
  </data>
  <data name="MsgValidatePitem" xml:space="preserve">
    <value>Please Select Product Item</value>
  </data>
  <data name="MsgWdeletemanf" xml:space="preserve">
    <value>Job order and all its details and jornals will be deleted .. Continue ?</value>
  </data>
  <data name="MsgWsavemanf" xml:space="preserve">
    <value>Job Order will be ended and produced item will be stored .. Continue ?</value>
  </data>
  <data name="txtmanfExpense" xml:space="preserve">
    <value>Expenses of Job Order No.</value>
  </data>
  <data name="txtValidateDate" xml:space="preserve">
    <value>Enter Date</value>
  </data>
  <data name="txtValidateDrawer" xml:space="preserve">
    <value>Please Select drawer</value>
  </data>
  <data name="txtValidateEmp" xml:space="preserve">
    <value>Select Empolyee</value>
  </data>
  <data name="txtValidateExpensesText" xml:space="preserve">
    <value>Enter expenses notes</value>
  </data>
  <data name="txtValidateExpensesValue" xml:space="preserve">
    <value>Enter expenses value</value>
  </data>
  <data name="txtValidateItem" xml:space="preserve">
    <value>Select Item</value>
  </data>
  <data name="txtValidateQty" xml:space="preserve">
    <value>Qty must be larger than 0</value>
  </data>
  <data name="txtValidateStore" xml:space="preserve">
    <value>Selext Store</value>
  </data>
  <data name="txtValidateWorkedhours" xml:space="preserve">
    <value>Enter Worked Hours</value>
  </data>
  <data name="custOpen" xml:space="preserve">
    <value>Customer Open Balance</value>
  </data>
  <data name="VendOpen" xml:space="preserve">
    <value>Vendor Open Balance</value>
  </data>
  <data name="accountCreationBy" xml:space="preserve">
    <value>Account created by the system</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="CloseInventory" xml:space="preserve">
    <value>Close Inventory</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="OpenInventory" xml:space="preserve">
    <value>Open Inventory</value>
  </data>
  <data name="Purchases" xml:space="preserve">
    <value>Purchases</value>
  </data>
  <data name="PurchasesReturn" xml:space="preserve">
    <value>Purchases Return</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="SalesReturn" xml:space="preserve">
    <value>Sales Return</value>
  </data>
  <data name="Vendor" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="txt_Date" xml:space="preserve">
    <value>Job Date</value>
  </data>
  <data name="txt_prod" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="txt_Qty" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="MsgValidatemanfprodUOM" xml:space="preserve">
    <value>Please Select product UOM</value>
  </data>
  <data name="txt_V_Manf_EndDate" xml:space="preserve">
    <value>Please Enter Job Order End Date</value>
  </data>
  <data name="txt_V_manf_EndDate_Larger_StartDate" xml:space="preserve">
    <value>Job Order End Date must be After Job Start Date</value>
  </data>
  <data name="MsgPostedFailed" xml:space="preserve">
    <value>Error , Sales Invoices not posted</value>
  </data>
  <data name="MsgPostedSuccessfully" xml:space="preserve">
    <value>Sales Invoices Posted Successfully</value>
  </data>
  <data name="txtSLPosting" xml:space="preserve">
    <value>Post Sales Invoices Date :</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>User Name</value>
  </data>
  <data name="MsgAskPost" xml:space="preserve">
    <value>Are you sure you want to post sales invoices ?</value>
  </data>
  <data name="MsgCapitalAcc" xml:space="preserve">
    <value>Please select Capital account in settings screen</value>
  </data>
  <data name="MsgCustomersAcc" xml:space="preserve">
    <value>Please select Customers main account in settings screen</value>
  </data>
  <data name="MsgDiscountAcc" xml:space="preserve">
    <value>Please select Discount account in settings screen</value>
  </data>
  <data name="MsgManufacturingExpAcc" xml:space="preserve">
    <value>Please select Manufacturing Expenses account in settings screen</value>
  </data>
  <data name="MsgSalesTaxAcc" xml:space="preserve">
    <value>Please select Sales Tax account in settings screen</value>
  </data>
  <data name="MsgVendorsAcc" xml:space="preserve">
    <value>Please select Vendors main account in settings screen</value>
  </data>
  <data name="MsgSelectCustomer" xml:space="preserve">
    <value>Please select customer</value>
  </data>
  <data name="MsgSelectVendor" xml:space="preserve">
    <value>Please select vendor</value>
  </data>
  <data name="MsgPostedBill" xml:space="preserve">
    <value>Sorry , you can't edit or delete posted bills</value>
  </data>
  <data name="MsgNameRequired" xml:space="preserve">
    <value>Please enter name</value>
  </data>
  <data name="MsgAskPostJournals" xml:space="preserve">
    <value>Are you sure you want to post these financial Journals </value>
  </data>
  <data name="MsgAskUnPostJournals" xml:space="preserve">
    <value>Are you sure you want to unpost these financial Journals </value>
  </data>
  <data name="MsgEnterCode" xml:space="preserve">
    <value>Please, enter code</value>
  </data>
  <data name="MsgJournalPostedSuccessfully" xml:space="preserve">
    <value>Jounals Posted Successfully</value>
  </data>
  <data name="DeselectAll" xml:space="preserve">
    <value>Deselect All</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="MsgFaAccRequired" xml:space="preserve">
    <value>Please select Fixed Asset Expeneses Account</value>
  </data>
  <data name="MsgFaDeprAccRequired" xml:space="preserve">
    <value>Please enter Depreciation Account</value>
  </data>
  <data name="MsgFixedAsetsAcc" xml:space="preserve">
    <value>Please select Fixed Assets main account in settings screen</value>
  </data>
  <data name="MsgAskDelFA" xml:space="preserve">
    <value>Are you sure you want to delete this Fixed Asset</value>
  </data>
  <data name="DelEntryDenied" xml:space="preserve">
    <value>Sorry, you can't delete this entry, it's already in use.</value>
  </data>
  <data name="NoChange" xml:space="preserve">
    <value>No Change</value>
  </data>
  <data name="DelJO" xml:space="preserve">
    <value>Sorry, you can't delete this Job Order, it's already in use by sales invoices.</value>
  </data>
  <data name="EditInClosedPeriodDenie" xml:space="preserve">
    <value>Sorry, you can't edit data in a closed period</value>
  </data>
  <data name="EditInClosedPeriodWarning" xml:space="preserve">
    <value>you are saving data in a closed period, do you want to continue</value>
  </data>
  <data name="Expenses" xml:space="preserve">
    <value>Expenses</value>
  </data>
  <data name="Revenue" xml:space="preserve">
    <value>Revenue</value>
  </data>
  <data name="InvBookUsedDel" xml:space="preserve">
    <value>Sorry, you can't delete this Book, it's already in use by invoices</value>
  </data>
  <data name="MsgAskDelInvBook" xml:space="preserve">
    <value>Are you sure you want to delete this Invoice book</value>
  </data>
  <data name="linkAccountParent" xml:space="preserve">
    <value>you can't link to a parent account</value>
  </data>
  <data name="DetailIdBrcodPrntd" xml:space="preserve">
    <value>Sorry you can't edit this invoice, after you printed barcode stickers</value>
  </data>
  <data name="DetailIdBrcodPrntdDel" xml:space="preserve">
    <value>Deleting this invoice will ruin barcode stickers printed, are you sure you wan't to continue</value>
  </data>
  <data name="MsgWarnningSelectManfActualRaws" xml:space="preserve">
    <value>There is no actual raws for this job order , do you want to continue ?</value>
  </data>
  <data name="AppPOSName" xml:space="preserve">
    <value>LinkIT POS System</value>
  </data>
  <data name="ValPass" xml:space="preserve">
    <value>Passwords doesn't match</value>
  </data>
  <data name="valUserName" xml:space="preserve">
    <value>User Name Required</value>
  </data>
  <data name="MsgSelectComp" xml:space="preserve">
    <value>Please select Company</value>
  </data>
  <data name="MsgSelectCustGrp" xml:space="preserve">
    <value>Please select Customer Group</value>
  </data>
  <data name="txtValidateNoRows" xml:space="preserve">
    <value>Please enter at least one item</value>
  </data>
  <data name="txtValidateNoteNum" xml:space="preserve">
    <value>Please enter Note Number</value>
  </data>
  <data name="DelIndirectComp" xml:space="preserve">
    <value>Sorry, you can't delete this company, you've to delete it's indirect sales notes first</value>
  </data>
  <data name="DelMrItem" xml:space="preserve">
    <value>Sorry, you can't remove this item, it's already in use by a customer group</value>
  </data>
  <data name="DelMrItemWarning" xml:space="preserve">
    <value>Remove Item?</value>
  </data>
  <data name="LastRate" xml:space="preserve">
    <value>Evaluete All Currencies by Last Rate</value>
  </data>
  <data name="TrnsRate" xml:space="preserve">
    <value>Evaluete All Currencies by Transaction Rate</value>
  </data>
  <data name="CrncyDel" xml:space="preserve">
    <value>Sorry, you can't delete this entry, it's already in use.</value>
  </data>
  <data name="NetSales" xml:space="preserve">
    <value>Net Sales</value>
  </data>
  <data name="PurchaseDiscount" xml:space="preserve">
    <value>Purchase Discount</value>
  </data>
  <data name="SalesCost" xml:space="preserve">
    <value>Sales Cost</value>
  </data>
  <data name="SalesDiscount" xml:space="preserve">
    <value>Sales Discount</value>
  </data>
  <data name="MsgDelManfOrder" xml:space="preserve">
    <value>Are you sure you want to delete this production order?</value>
  </data>
  <data name="ValMsgManfOrderNo" xml:space="preserve">
    <value>Please enter Production order Number</value>
  </data>
  <data name="MsgJournalUnPostedSuccessfully" xml:space="preserve">
    <value>Journals post cacelled successfully</value>
  </data>
  <data name="ManfNo" xml:space="preserve">
    <value>Manf No : </value>
  </data>
  <data name="txt_QtyText" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="txt_ValueText" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="txt_ICInTransNo" xml:space="preserve">
    <value>Receive Bill Number </value>
  </data>
  <data name="txt_ICOutTransNo" xml:space="preserve">
    <value>Outgoing Bill Number </value>
  </data>
  <data name="txt_ICStoreMoveNo" xml:space="preserve">
    <value>Move From Store Bill Number</value>
  </data>
  <data name="txt_ManfQCNo" xml:space="preserve">
    <value>Quality Control Bill Number </value>
  </data>
  <data name="MsgAskToSave" xml:space="preserve">
    <value>You've to save this form first</value>
  </data>
  <data name="DelLC" xml:space="preserve">
    <value>Are you sure you want to delete this Letter of Credit</value>
  </data>
  <data name="LCSettingAcc" xml:space="preserve">
    <value>Please select Letter Of Credit account in settings screen</value>
  </data>
  <data name="DelLCDenied" xml:space="preserve">
    <value>Sorry, you can't delete this letter of credit it's already in use by purchase invoice no. </value>
  </data>
  <data name="DelLCDenied2" xml:space="preserve">
    <value>Sorry, you can't delete this letter of credit, you've to delete it's jpurnals first</value>
  </data>
  <data name="openBlncDate" xml:space="preserve">
    <value>Please select open balance date</value>
  </data>
  <data name="MsgAskDelRows" xml:space="preserve">
    <value>Are you sure you want to delete selected rows ? </value>
  </data>
  <data name="DelBomDenied" xml:space="preserve">
    <value>Sorry, you can't delete this BOM, it's already in use by Job Orders.</value>
  </data>
  <data name="invDeleted" xml:space="preserve">
    <value>Invoice doesn't exist, a new one will be created</value>
  </data>
  <data name="MsgContinue" xml:space="preserve">
    <value>Do you want to continue</value>
  </data>
  <data name="SelectrevExpAcc" xml:space="preserve">
    <value>Please select revenue and expenses accounts</value>
  </data>
  <data name="AccessDenied" xml:space="preserve">
    <value>Sorry you are not allowd to access the system, please contact system administrator</value>
  </data>
  <data name="ValidUser" xml:space="preserve">
    <value>Please enter user name and password correctly</value>
  </data>
  <data name="DelBillDenied" xml:space="preserve">
    <value>Sorry you can't delete this bill, these invoices are linked</value>
  </data>
  <data name="DelInvoiceDenied" xml:space="preserve">
    <value>Sorry you can't delete this invoice, these bills are linked</value>
  </data>
  <data name="DelOrderDeniedIC" xml:space="preserve">
    <value>Sorry you can't delete this order, these bills are linked</value>
  </data>
  <data name="DelOrderDeniedInv" xml:space="preserve">
    <value>Sorry you can't delete this order, these invoices are linked</value>
  </data>
  <data name="SelectBill" xml:space="preserve">
    <value>Please Select Receive Bill first</value>
  </data>
  <data name="SelectInv" xml:space="preserve">
    <value>Please Select Receive Invoice first</value>
  </data>
  <data name="ItemPosting" xml:space="preserve">
    <value>Please set gl posting accounts for items categories</value>
  </data>
  <data name="AppClose" xml:space="preserve">
    <value>Do you want to exit the system?</value>
  </data>
  <data name="MsgBlockedCustomer" xml:space="preserve">
    <value>This Customer Is Blocked</value>
  </data>
  <data name="MsgCusTaxAcc" xml:space="preserve">
    <value>Please select Custom Tax account in settings screen</value>
  </data>
  <data name="txtmanfIndirectExpense" xml:space="preserve">
    <value>Indirect expense of Job Order No. </value>
  </data>
  <data name="LastEvaluationDateError" xml:space="preserve">
    <value>Sorry, you can't save after last evalutaion date</value>
  </data>
  <data name="Daily" xml:space="preserve">
    <value>Daily</value>
  </data>
  <data name="InermediateAccount" xml:space="preserve">
    <value>Please select the intermediate sotck account from settings</value>
  </data>
  <data name="ValidAdvancedPayment" xml:space="preserve">
    <value>Enter Advanced Payment</value>
  </data>
  <data name="ValidAreaSize" xml:space="preserve">
    <value>Enter Area Size</value>
  </data>
  <data name="ValidCostMeter" xml:space="preserve">
    <value>Enter Cost Meter</value>
  </data>
  <data name="ValidCustomer" xml:space="preserve">
    <value>Enter Customer</value>
  </data>
  <data name="ValidFloor" xml:space="preserve">
    <value>Enter Floor</value>
  </data>
  <data name="ValidDueName" xml:space="preserve">
    <value>Cant repeat name</value>
  </data>
  <data name="validCode" xml:space="preserve">
    <value>Enter Code</value>
  </data>
  <data name="validDate" xml:space="preserve">
    <value>Date From Is Biger Than Date To...</value>
  </data>
  <data name="validDateFrom" xml:space="preserve">
    <value>Enter Date From</value>
  </data>
  <data name="validDateTo" xml:space="preserve">
    <value>Enter Date To</value>
  </data>
  <data name="ValidRowInMontlyDue" xml:space="preserve">
    <value>Must insert one row at least</value>
  </data>
  <data name="validEndDateContract" xml:space="preserve">
    <value>Enter End Date....</value>
  </data>
  <data name="validContractDate" xml:space="preserve">
    <value>Enter Contract Date</value>
  </data>
  <data name="validMonthlyDue" xml:space="preserve">
    <value>Enter Monthly Due</value>
  </data>
  <data name="ValidAreaSizeGreaterThanMallArea" xml:space="preserve">
    <value>Area Size is greater than Mall Area</value>
  </data>
  <data name="ValidTotalValueMonthlyDue" xml:space="preserve">
    <value>Value Must Be More Than 0</value>
  </data>
  <data name="validmonthlyDues" xml:space="preserve">
    <value>There Are Monthly Dues In this Duration ,Please Insert anthor Duration..</value>
  </data>
  <data name="validDateContract" xml:space="preserve">
    <value>End Date Must Be After Start Date</value>
  </data>
  <data name="ValidUom" xml:space="preserve">
    <value>Can`t Stop Unit...</value>
  </data>
  <data name="validAreaNumber" xml:space="preserve">
    <value>Area Number Is Required</value>
  </data>
</root>