﻿using EInvoice.Models;
using EInvoice.MyHelper;
using Models_1.ViewModels.InvoiceVM;
using Models_1.ViewModels.ResponseVM.GetDocumentVM;
using Models_1.ViewModels.ValidationVM;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static EInvoice.MyHelper.Utilities;

namespace EInvoice.BL
{
    public static class InvoiceBL
    {
        public static void SaveDocuments(List<InvoiceData> documents, ERPEinvoiceContext DB)
        {
            foreach (var document in documents)
            {

                //if (string.IsNullOrEmpty(document.invoiceCode))
                //{
                    if (document.DocumentType.ToUpper() == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
                    {
                        CreateInvoice(document, DB);                   
                    }
                    if (document.DocumentType.ToUpper() == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.C))
                    {
                        CreateCreditNote(document, DB);
                    }
                    if (document.DocumentType.ToUpper() == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.D))
                    {
                        CreateDebitNote(document, DB);
                    }
               // }
                //else
                //{
                //    if (document.DocumentType.ToUpper() == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
                //    {
                //        EditDocument(document,DB);
                //    }
                    
                //}
            }
        }

        private static void CreateInvoice(InvoiceData invoice , ERPEinvoiceContext DB)
        {

            using (var transaction = DB.Database.BeginTransaction())
            {
                try { 
                var invoiceData = new SlInvoice();
                invoiceData.InvoiceCode = invoice.invoiceCode;
                invoiceData.CustomerId = invoice.CustomerData.CustomerId;
                invoiceData.InvoiceDate = invoice.InvoiceDate;
                invoiceData.CrncId = invoice.InvoiceDetailData.FirstOrDefault().CurrencyData.CrncId;
                invoiceData.StoreId = invoice.StoreData.StoreId;    /////
                invoiceData.UserId = 0;                             /////
                invoiceData.Paid = 0;                               /////
                invoiceData.AddTaxValue =invoice.TotalTax;
                invoiceData.DiscountValue = invoice.TotalDiscount;
                invoiceData.Net = invoice.Net;
                invoiceData.AddTaxRatio = 0;
                DB.SlInvoice.Add(invoiceData);
                DB.SaveChanges();
                var id = invoiceData.SlInvoiceId;
                      
                    var invoiceDetails = new List<SlInvoiceDetail>();
                    foreach (var detail in invoice.InvoiceDetailData)
                    {
                        var invoiceDetail = new SlInvoiceDetail();
                        invoiceDetail.SlInvoiceId = invoiceData.SlInvoiceId; ;
                        invoiceDetail.ItemId = detail.ItemData.ItemId;
                        invoiceDetail.Qty = detail.Quantity;
                        invoiceDetail.Uomid = detail.UomData.Uomid;
                        invoiceDetail.SellPrice = detail.Amount;
                        invoiceDetail.CostPrice = 0;
                        if(detail.Discounts!=null)
                        {
                            
                            invoiceDetail.DiscountValue = detail.Discounts.Sum(c=>c.amount);
                            invoiceDetail.DiscountRatio = detail.Discounts.FirstOrDefault() != null ? detail.Discounts.FirstOrDefault().rate : 0;
                            invoiceDetail.DiscountRatio2 = detail.Discounts.Count()>=2 ? detail.Discounts.Take(2).LastOrDefault().rate : 0;
                            invoiceDetail.DiscountRatio3 = detail.Discounts.Count() == 3 ? detail.Discounts.Take(2).LastOrDefault().rate : 0;



                        }
                        // itemDiscounts
                        invoiceDetail.SalesTax = 0;
                        invoiceDetail.SalesTaxRatio = 0;
                        invoiceDetail.TotalSellPrice = detail.Amount * detail.Quantity;
                        invoiceDetail.Uomindex = 0;
                        invoiceDetail.CustomTax = 0;
                        invoiceDetail.CustomTaxRatio = 0;
                      //  invoiceDetail.DiscountRatio3 = 0;
                      //  invoiceDetail.DiscountRatio2 = 0;
                        invoiceDetail.BonusDiscount = detail.discountAfterTax;
                        //invoiceDetail.SlInvoiceDetailSubTaxValue = taxDetails;
                        //invoiceDetails.Add(invoiceDetail);
                        DB.SlInvoiceDetail.Add(invoiceDetail);
                        DB.SaveChanges();
                        if(detail.Taxes!=null)
                        {
                            var taxDetails = detail.Taxes.Select(x => FillInvoiceTaxModel(x)).ToList();

                            foreach (var taxDetail in taxDetails)
                            {
                                var SlInvoiceDetailSubTaxValue = new SlInvoiceDetailSubTaxValue();
                                SlInvoiceDetailSubTaxValue = taxDetail;
                                SlInvoiceDetailSubTaxValue.InvoiceDetailId = invoiceDetail.SlInvoiceDetailId;
                                DB.SlInvoiceDetailSubTaxValue.Add(SlInvoiceDetailSubTaxValue);
                                DB.SaveChanges();
                            }
                        }
                     
                    }

                    transaction.Commit();

                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                }
            }
        }

        private static SlInvoiceDetailSubTaxValue FillInvoiceTaxModel(Models_1.ViewModels.InvoiceVM.Tax tax)
        {
            var model = new SlInvoiceDetailSubTaxValue() {
                EsubTypeId=tax.taxId,
                Value = tax.amount,
                TaxRatio = tax.rate
            };

            return model;
        }

        private static void CreateCreditNote(InvoiceData note, ERPEinvoiceContext DB)
        {
            using (var transaction = DB.Database.BeginTransaction())
            {
                try
                {
                    var invoiceData = new SlReturn();
                    invoiceData.ReturnCode = note.invoiceCode;
                    invoiceData.CustomerId = note.CustomerData.CustomerId;
                    invoiceData.ReturnDate = note.InvoiceDate;
                    invoiceData.CrncId = note.InvoiceDetailData.FirstOrDefault().CurrencyData.CrncId;
                    invoiceData.StoreId = note.StoreData.StoreId;    /////
                    invoiceData.UserId = 0;                             /////
                    invoiceData.Paid = 0;                               /////
                    invoiceData.AddTaxValue = 0;
                    invoiceData.AddTaxValue = note.TotalTax;
                    invoiceData.DiscountValue = note.TotalDiscount;
                    invoiceData.Net = note.Net;
                    DB.SlReturn.Add(invoiceData);
                    DB.SaveChanges();

                    var invoiceDetails = new List<SlInvoiceDetail>();
                    foreach (var detail in note.InvoiceDetailData)
                    {
                        var invoiceDetail = new SlReturnDetail();
                        invoiceDetail.SlReturnId = invoiceData.SlReturnId;
                        invoiceDetail.ItemId = detail.ItemData.ItemId;
                        invoiceDetail.Qty = detail.Quantity;
                        invoiceDetail.Uomid = detail.UomData.Uomid;
                        invoiceDetail.SellPrice = detail.Amount;
                        if (detail.Discounts != null)
                        {

                            invoiceDetail.DiscountValue = detail.Discounts.Sum(c => c.amount);
                            invoiceDetail.DiscountRatio = detail.Discounts.FirstOrDefault() != null ? detail.Discounts.FirstOrDefault().rate : 0;
                            invoiceDetail.DiscountRatio2 = detail.Discounts.Count() >= 2 ? detail.Discounts.Take(2).LastOrDefault().rate : 0;
                            invoiceDetail.DiscountRatio3 = detail.Discounts.Count() == 3 ? detail.Discounts.Take(2).LastOrDefault().rate : 0;



                        }
                        invoiceDetail.SalesTax = 0;
                        invoiceDetail.SalesTaxRatio = 0;
                        invoiceDetail.TotalSellPrice = detail.Amount * detail.Quantity;
                        invoiceDetail.Uomindex = 0;
                        invoiceDetail.CustomTax = 0;
                        invoiceDetail.CustomTaxRatio = 0;
                       // invoiceDetail.DiscountRatio3 = 0;
                        //invoiceDetail.DiscountRatio2 = 0;
                        invoiceDetail.BonusDiscount = detail.discountAfterTax;
                        //invoiceDetail.SlInvoiceDetailSubTaxValue = taxDetails;
                        //invoiceDetails.Add(invoiceDetail);
                        DB.SlReturnDetail.Add(invoiceDetail);
                        DB.SaveChanges();
                        if(detail.Taxes!=null)
                        {
                            var taxDetails = detail.Taxes.Select(x => FillCreditNoteTaxModel(x)).ToList();

                            foreach (var taxDetail in taxDetails)
                            {
                                var SlInvoiceDetailSubTaxValue = new SlReturnInvoiceDetailSubTaxValue();
                                SlInvoiceDetailSubTaxValue = taxDetail;
                                SlInvoiceDetailSubTaxValue.ReturnInvoiceDetailId = invoiceDetail.SlReturnDetailId;
                                DB.SlReturnInvoiceDetailSubTaxValue.Add(SlInvoiceDetailSubTaxValue);
                                DB.SaveChanges();
                            }
                        }
                        
                    }
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                }
            }
        }

        private static SlReturnInvoiceDetailSubTaxValue FillCreditNoteTaxModel(Tax tax)
        {
            var model = new SlReturnInvoiceDetailSubTaxValue()
            {
                EsubTypeId = tax.taxId,
                Value = tax.amount,
                TaxRatio = tax.rate
            };

            return model;
        }
        private static SlAddDetailSubTaxValue FillDepitNoteTaxModel(Tax tax)
        {
            var model = new SlAddDetailSubTaxValue()
            {
                EsubTypeId = tax.taxId,
                Value = tax.amount,
                TaxRatio = tax.rate
            };

            return model;
        }
        private static void CreateDebitNote(InvoiceData note, ERPEinvoiceContext DB)
        {
            using (var transaction = DB.Database.BeginTransaction())
            {
                try
                {
                    var invoiceData = new SlAdd();
                    invoiceData.ReturnCode = note.invoiceCode;
                    invoiceData.CustomerId = note.CustomerData.CustomerId;
                    invoiceData.ReturnDate = note.InvoiceDate;
                    invoiceData.CrncId = note.InvoiceDetailData.FirstOrDefault().CurrencyData.CrncId;
                    invoiceData.StoreId = note.StoreData.StoreId;    /////
                    invoiceData.UserId = 0;                             /////
                    invoiceData.Paid = 0;                               /////
                    invoiceData.AddTaxValue = note.TotalTax;
                    invoiceData.DiscountValue = note.TotalDiscount;
                    invoiceData.Net = note.Net;
                    invoiceData.AddTaxRatio = 0;
                    DB.SlAdd.Add(invoiceData);
                    DB.SaveChanges();

                    var invoiceDetails = new List<SlAddDetail>();
                    foreach (var detail in note.InvoiceDetailData)
                    {
                        var invoiceDetail = new SlAddDetail();
                        invoiceDetail.SlAddId = invoiceData.SlAddId; ;
                        invoiceDetail.ItemId = detail.ItemData.ItemId;
                        invoiceDetail.Qty = detail.Quantity;
                        invoiceDetail.Uomid = detail.UomData.Uomid;
                        invoiceDetail.SellPrice = detail.Amount;
                        if (detail.Discounts != null)
                        {

                            invoiceDetail.DiscountValue = detail.Discounts.Sum(c => c.amount);
                            invoiceDetail.DiscountRatio = detail.Discounts.FirstOrDefault() != null ? detail.Discounts.FirstOrDefault().rate : 0;
                            invoiceDetail.DiscountRatio2 = detail.Discounts.Count() >= 2 ? detail.Discounts.Take(2).LastOrDefault().rate : 0;
                            invoiceDetail.DiscountRatio3 = detail.Discounts.Count() == 3 ? detail.Discounts.Take(2).LastOrDefault().rate : 0;



                        }
                        invoiceDetail.SalesTax = 0;
                        invoiceDetail.SalesTaxRatio = 0;
                        invoiceDetail.TotalSellPrice = detail.Amount * detail.Quantity;
                        invoiceDetail.Uomindex = 0;
                        invoiceDetail.CustomTax = 0;
                        invoiceDetail.CustomTaxRatio = 0;
                       // invoiceDetail.DiscountRatio3 = 0;
                       // invoiceDetail.DiscountRatio2 = 0;
                        invoiceDetail.BonusDiscount = detail.discountAfterTax;
                       // invoiceDetail.ta = taxDetails;
                        invoiceDetails.Add(invoiceDetail);
                        DB.SlAddDetail.Add(invoiceDetail);
                        DB.SaveChanges();
                        if(detail.Taxes!=null)
                        {
                            var taxDetails = detail.Taxes.Select(x => FillDepitNoteTaxModel(x)).ToList();
                            foreach (var taxDetail in taxDetails)
                            {
                                var SlInvoiceDetailSubTaxValue = new SlAddDetailSubTaxValue();
                                SlInvoiceDetailSubTaxValue = taxDetail;
                                SlInvoiceDetailSubTaxValue.SlAddDetailId = invoiceDetail.SlAddDetailId;
                                DB.SlAddDetailSubTaxValue.Add(SlInvoiceDetailSubTaxValue);
                                DB.SaveChanges();
                            }
                        }
                       
                    }
                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                }
            }
        }

        //private static SlReturnInvoiceDetailSubTaxValue FillDebitNoteTaxModel(Tax tax)
        //{
        //    var model = new SlReturnInvoiceDetailSubTaxValue()
        //    {
        //        EsubTypeId = tax.taxId,
        //        Value = tax.amount,
        //        TaxRatio = tax.rate
        //    };
        //    return model;
        //}

        private static void EditDocument(InvoiceData document , ERPEinvoiceContext DB)
        {
            if (document.DocumentType.ToUpper() == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
            {
                var invoice = DB.SlInvoice.FirstOrDefault(a => a.InvoiceCode == document.invoiceCode.TrimEnd().TrimStart() && a.InvoiceDate == document.InvoiceDate);
                if (invoice!=null)
                {
                    if(string.IsNullOrEmpty(invoice.Uuid) && string.IsNullOrEmpty(invoice.Estatus))
                    {
                        DeleteInvoice(document, DB);
                    }
                        CreateInvoice(document, DB);
                }
            }
        }

        private static void DeleteInvoice(InvoiceData document, ERPEinvoiceContext DB)
        {
            using (var transaction = DB.Database.BeginTransaction())
            {
                try { 
                var invoice = DB.SlInvoice.FirstOrDefault(a => a.InvoiceCode == document.invoiceCode.TrimEnd().TrimStart() && a.InvoiceDate == document.InvoiceDate);
                var invoiceDetails = DB.SlInvoiceDetail.Where(x => x.SlInvoiceId == invoice.SlInvoiceId).ToList();
                var invoiceDetailTaxes = DB.SlInvoiceDetailSubTaxValue.Where(x => invoiceDetails.Select(z => z.SlInvoiceId).Contains(x.InvoiceDetailId)).ToList();

                    DB.SlInvoiceDetailSubTaxValue.RemoveRange(invoiceDetailTaxes);
                    DB.SlInvoiceDetail.RemoveRange(invoiceDetails);
                    DB.SlInvoice.Remove(invoice);

                    transaction.Commit();
                }
                catch (Exception ex)
                {
                    MyHelper.Utilities.UpdateST_UserLog(DB, document.invoiceCode, $"Failed To Delete Invoice : {document.invoiceCode} - Error {ex.InnerException + " - " + ex.Message}", 1, 1);
                    transaction.Rollback();
                }
            }
        }

        public static List<Invoice> GetInvoiceModel(List<Note4Post> invoicesList, ERPEinvoiceContext DB)
        {
            List<Invoice> invoices = new List<Invoice>();
            invoices = invoicesList.Select(x => (x.documentType == (int)Models_1.ViewModels.DocumentType.I ?
            GetInvoice(x.invoiceId, DB) :(x.documentType == (int)Models_1.ViewModels.DocumentType.C?
            GetReturnInvoice(x.invoiceId, DB): GetAddInvoice(x.invoiceId, DB)))).ToList();
            //if (InvoiceType==Convert.ToInt32(EInvoice.MyHelper.Utilities.InvoiceType.SalesInvoice))
            // invoices = invoiceIds.Select(x => GetInvoice(x, DB)).ToList();
            //if (InvoiceType == Convert.ToInt32(EInvoice.MyHelper.Utilities.InvoiceType.SalesReturn))
            //    invoices = invoiceIds.Select(x => GetReturnInvoice(x, DB)).ToList();
            return invoices;
        }


        private static Invoice GetInvoice(int invoiceId, ERPEinvoiceContext DB)
        {
            var invoice = new Invoice();
            //var invoiceDetail = new InvoiceDetail();

            var invoiceData = DB.SlInvoice.FirstOrDefault(x => x.SlInvoiceId == invoiceId);
            var invoiceDetails = DB.SlInvoiceDetail.Where(x => x.SlInvoiceId == invoiceId).ToList();
            var invoiceDetailData = invoiceDetails.Select(v => GetInvoiceDetail(v, invoiceData.CrncId, invoiceData.CrncRate, DB)).ToList();
            invoice.invoiceDetails = invoiceDetailData;
            invoice.invoiceId = invoiceId;
            invoice.customerId = invoiceData.CustomerId;

            invoice.companyId = DB.StCompanyInfo.FirstOrDefault().CompanyId;
            invoice.storeId = invoiceData.StoreId;
            invoice.date = invoiceData.InvoiceDate;
            invoice.documentType = "I";
            invoice.TotalDiscount = invoiceData.DiscountValue;
            invoice.invoiceCode = invoiceData.InvoiceCode;
            invoice.PurchaseOrderNumber = invoiceData.PurchaseOrderNo;
            invoice.DeliveryDate = invoiceData.DeliverDate.HasValue ? invoiceData.DeliverDate.Value.ToString("yyyy-MM-dd") : null;
            return invoice;
        }

        private static Invoice GetReturnInvoice(int ReturninvoiceId, ERPEinvoiceContext DB)
        {
            var invoice = new Invoice();
         

            var invoiceData = DB.SlReturn.FirstOrDefault(x => x.SlReturnId == ReturninvoiceId);
            var invoiceDetails = DB.SlReturnDetail.Where(x => x.SlReturnId == ReturninvoiceId).ToList();
            var invoiceDetailData = invoiceDetails.Select(v => GetInvoiceReturnDetail(v, invoiceData.CrncId, invoiceData.CrncRate, DB)).ToList();
            invoice.invoiceDetails = invoiceDetailData;
            invoice.invoiceId = ReturninvoiceId;
            invoice.customerId = invoiceData.CustomerId;
            invoice.companyId = DB.StCompanyInfo.FirstOrDefault().CompanyId;
            invoice.storeId = invoiceData.StoreId;
            invoice.date = invoiceData.ReturnDate;
            invoice.documentType = "C";
            invoice.TotalDiscount = invoiceData.DiscountValue;
            invoice.invoiceCode = invoiceData.ReturnCode;

            return invoice;
        }

        private static Invoice GetAddInvoice(int AddInvoiceId, ERPEinvoiceContext DB)
        {
            var invoice = new Invoice();


            var invoiceData = DB.SlAdd.FirstOrDefault(x => x.SlAddId == AddInvoiceId);
            var invoiceDetails = DB.SlAddDetail.Where(x => x.SlAddId == AddInvoiceId).ToList();
            var invoiceDetailData = invoiceDetails.Select(v => GetInvoiceAddDetail(v, invoiceData.CrncId, invoiceData.CrncRate, DB)).ToList();
            invoice.invoiceDetails = invoiceDetailData;
            invoice.invoiceId = AddInvoiceId;
            invoice.customerId = invoiceData.CustomerId;
            invoice.companyId = DB.StCompanyInfo.FirstOrDefault().CompanyId;
            invoice.storeId = invoiceData.StoreId;
            invoice.date = invoiceData.ReturnDate;
            invoice.documentType = "D";
            invoice.TotalDiscount = invoiceData.DiscountValue;
            invoice.invoiceCode = invoiceData.ReturnCode;
           
            return invoice;
        }

        private static InvoiceDetail GetInvoiceDetail(SlInvoiceDetail detail , int currencyId , decimal exchangeRate, ERPEinvoiceContext DB)
        {
            var invoiceDetail = new InvoiceDetail();           
            
                invoiceDetail.itemId = detail.ItemId;
                invoiceDetail.quantity = detail.Qty;
                invoiceDetail.currencyId = currencyId;
                invoiceDetail.Description = detail.ItemDescription;
                invoiceDetail.exchangeRate = exchangeRate;
                invoiceDetail.amount = detail.SellPrice;
                invoiceDetail.totalAmount = detail.TotalSellPrice;
                invoiceDetail.uomId = detail.Uomid;               
                invoiceDetail.discountAfterTax = detail.BonusDiscount??0;
                invoiceDetail.discount = new DetailDiscount() {type = 1 , rate = detail.DiscountValue };
                invoiceDetail.taxes = DB.SlInvoiceDetailSubTaxValue.Where(x => x.InvoiceDetailId == detail.SlInvoiceDetailId).Select(z => new Tax
                {
                    subTaxId = z.EsubTypeId,
                    amount = z.Value,
                    rate = z.TaxRatio??0
                }).ToList();
            
            return invoiceDetail;
        }

        // return invoice
        private static InvoiceDetail GetInvoiceReturnDetail(SlReturnDetail detail, int currencyId, decimal exchangeRate, ERPEinvoiceContext DB)
        {
            var invoiceDetail = new InvoiceDetail();

            invoiceDetail.itemId = detail.ItemId;
            invoiceDetail.quantity = detail.Qty;
            invoiceDetail.currencyId = currencyId;
            invoiceDetail.exchangeRate = exchangeRate;
            invoiceDetail.amount = detail.SellPrice;
            invoiceDetail.totalAmount = detail.TotalSellPrice;
            invoiceDetail.uomId = detail.Uomid;
            invoiceDetail.discountAfterTax = detail.BonusDiscount ?? 0;
            invoiceDetail.Description = detail.ItemDescription;
            invoiceDetail.discount = new DetailDiscount() { type = 1, rate = detail.DiscountValue };
            invoiceDetail.taxes = DB.SlReturnInvoiceDetailSubTaxValue.Where(x => x.ReturnInvoiceDetailId == detail.SlReturnDetailId).Select(z => new Tax
            {
                subTaxId = z.EsubTypeId,
                amount = z.Value,
                rate = z.TaxRatio ?? 0
            }).ToList();

            return invoiceDetail;
        }

        // add invoice
        private static InvoiceDetail GetInvoiceAddDetail(SlAddDetail detail, int currencyId, decimal exchangeRate, ERPEinvoiceContext DB)
        {
            var invoiceDetail = new InvoiceDetail();

            invoiceDetail.itemId = detail.ItemId;
            invoiceDetail.quantity = detail.Qty;
            invoiceDetail.currencyId = currencyId;
            invoiceDetail.exchangeRate = exchangeRate;
            invoiceDetail.amount = detail.SellPrice;
            invoiceDetail.totalAmount = detail.TotalSellPrice;
            invoiceDetail.uomId = detail.Uomid;
            invoiceDetail.Description = detail.ItemDescription;
            invoiceDetail.discountAfterTax = 0;
            invoiceDetail.discount = new DetailDiscount() { type = 1, rate = detail.DiscountValue };
            invoiceDetail.taxes = DB.SlAddDetailSubTaxValue.Where(x => x.SlAddDetailId == detail.SlAddDetailId).Select(z => new Tax
            {
                subTaxId = z.EsubTypeId,
                amount = z.Value,
                rate = z.TaxRatio ?? 0
            }).ToList();

            return invoiceDetail;
        }

        public static void UpdateInvoicesAfterSync(GetDocumentResponse documentResponse,int documentId, ERPEinvoiceContext DB,int invoiceType=1)
        {
            try { 
            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Enter UpdateInvoicesAfterSync Method" , 1, 1);
          
            var status = documentResponse?.validationResults?.status;
            var errors = documentResponse?.validationResults?.validationSteps.Select(x=>x.error).ToList();
            var errornote = new StringBuilder();
            errornote.Append(status);
            if (errors != null) { 
            foreach (var error in errors)
            {
                if(error!=null)
                errornote.Append($" : {error?.errorCode??""}- {error?.propertyName ?? ""} - {error?.error??""}");
            }
            }
            //var Notes = errornote.ToString();
            if (invoiceType==Convert.ToInt32(Models_1.ViewModels.DocumentType.I))
            {
                var document = DB.SlInvoice.FirstOrDefault(x => x.SlInvoiceId == documentId);
                if (document != null)
                {
                    if (string.IsNullOrEmpty(document.Uuid)) document.SyncDate = DateTime.UtcNow;
                    document.LastSyncDate = DateTime.UtcNow;
                    document.LongId = documentResponse.longId;
                    document.Uuid = documentResponse.uuid;

                    document.Estatus = documentResponse.status.ToString();
                    document.EstatusCode = (documentResponse.status == Enum.GetName(typeof(DocumentStatus), DocumentStatus.Valid) || documentResponse.status == Enum.GetName(typeof(DocumentStatus), DocumentStatus.Cancelled)) ? (byte?)1 : 0;
                    var logNote = $"Document with Code {document.InvoiceCode} Sent Successfully with UUID {document.Uuid}";
                    document.Notes =string.IsNullOrEmpty(errornote.ToString())? logNote : errornote.ToString();
                    UpdateST_UserLog(DB, documentResponse.internalId, logNote, (int)FormAction.Add, 1);
                    DB.SaveChanges();
                }

            }

           else if (invoiceType == Convert.ToInt32(Models_1.ViewModels.DocumentType.C))
            {
                var document = DB.SlReturn.FirstOrDefault(x => x.SlReturnId == documentId);
                if (document != null)
                {

                    if (string.IsNullOrEmpty(document.Uuid)) document.SyncDate = DateTime.UtcNow;
                    document.LastSyncDate = DateTime.UtcNow;
                    document.LongId = documentResponse.longId;
                    document.Uuid = documentResponse.uuid;

                    document.Estatus = documentResponse.status.ToString();
                    document.EstatusCode = (documentResponse.status == Enum.GetName(typeof(DocumentStatus), DocumentStatus.Valid) || documentResponse.status == Enum.GetName(typeof(DocumentStatus), DocumentStatus.Cancelled)) ? (byte?)1 : 0;
                    document.Notes = errornote.ToString();
                    var logNote = $" Return Invoice with Code {document.ReturnCode} Sent Successfully with UUID {document.Uuid}";
                    UpdateST_UserLog(DB, documentResponse.internalId, logNote, (int)FormAction.Add, 1);

                    DB.SaveChanges();
                }

            }
                else if (invoiceType == Convert.ToInt32(Models_1.ViewModels.DocumentType.D))
                {
                    var document = DB.SlAdd.FirstOrDefault(x => x.SlAddId == documentId);
                    if (document != null)
                    {


                        if (string.IsNullOrEmpty(document.Uuid)) document.SyncDate = DateTime.UtcNow;
                        document.LastSyncDate = DateTime.UtcNow;
                        document.LongId = documentResponse.longId;
                        document.Uuid = documentResponse.uuid;

                        document.Estatus = documentResponse.status.ToString();
                        document.EstatusCode = (documentResponse.status == Enum.GetName(typeof(DocumentStatus), DocumentStatus.Valid) || documentResponse.status == Enum.GetName(typeof(DocumentStatus), DocumentStatus.Cancelled)) ? (byte?)1 : 0;
                        document.Notes = errornote.ToString();
                        var logNote = $"Debit Note with Code {document.ReturnCode} Sent Successfully with UUID {document.Uuid}";
                        UpdateST_UserLog(DB, documentResponse.internalId, logNote, (int)FormAction.Add, 1);

                        DB.SaveChanges();
                    }

                }
            }
            catch(Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From UpdateInvoicesAfterSync Method - "+" - " + ex.Message + " - " + ex.InnerException, 1, 1);
            }

        }


        public static ValidationMessage LogRejectedDocument(Models_1.ViewModels.ResponseVM.SubmitDocumentVM.DocumentRejected documentRejected, ERPEinvoiceContext DB, List<int> invoiceIds, int documentType)
        {

            var logNote = new StringBuilder();
            var invoiceCode = documentRejected.internalId;

            var error = documentRejected.error;
            var documentNote = new StringBuilder();

            if (!String.IsNullOrEmpty(error.code)) documentNote.Append(error.code).Append(" - ");
            if (!String.IsNullOrEmpty(error.message)) documentNote.Append(error.message).Append(" - ");
            if (!String.IsNullOrEmpty(error.target)) documentNote.Append(error.target).Append(" - ");
            if (error.details != null)
            {
                var errorDetails = error.details.FirstOrDefault();
                if (!String.IsNullOrEmpty(errorDetails.code)) documentNote.Append(errorDetails.code).Append(" - ");
                if (!String.IsNullOrEmpty(errorDetails.target)) documentNote.Append(errorDetails.target).Append(" - ");
                if (!String.IsNullOrEmpty(errorDetails.message)) documentNote.Append(errorDetails.message);
            };

            var validationMessage = new ValidationMessage() {invoiceCode = invoiceCode ,
                Message = new List<string>() {
                    documentNote.ToString()
                } };
            
            

            logNote.Append($"Document with Code {invoiceCode} Faild to submitted with Errors");

            var errornote = JsonConvert.SerializeObject(documentRejected);
            if(documentType == Convert.ToInt32(Models_1.ViewModels.DocumentType.I))
            {
                var document = DB.SlInvoice.FirstOrDefault(x => x.InvoiceCode == invoiceCode && invoiceIds.Contains(x.SlInvoiceId));
                if (document != null)
                {
                    if (string.IsNullOrEmpty(document.Uuid)) document.SyncDate = DateTime.UtcNow;
                    document.LastSyncDate = DateTime.UtcNow;
                    document.Notes = documentNote.ToString();
                    document.EstatusCode = 0;
                    document.Estatus = DocumentStatus.Invalid.ToString();
                    DB.SaveChanges();
                }
            }
          else if (documentType == Convert.ToInt32(Models_1.ViewModels.DocumentType.C))
            {
                var document = DB.SlReturn.FirstOrDefault(x => x.ReturnCode == invoiceCode && invoiceIds.Contains(x.SlReturnId));
                if (document != null)
                {
                    if (string.IsNullOrEmpty(document.Uuid)) document.SyncDate = DateTime.UtcNow;
                    document.LastSyncDate = DateTime.UtcNow;
                    document.Notes = logNote.ToString();
                    document.EstatusCode = 0;
                    document.Estatus = DocumentStatus.Invalid.ToString();
                    DB.SaveChanges();
                }
            }

            else if (documentType == Convert.ToInt32(Models_1.ViewModels.DocumentType.D))
            {
                var document = DB.SlAdd.FirstOrDefault(x => x.ReturnCode == invoiceCode && invoiceIds.Contains(x.SlAddId));
                if (document != null)
                {
                    if (string.IsNullOrEmpty(document.Uuid)) document.SyncDate = DateTime.UtcNow;
                    document.LastSyncDate = DateTime.UtcNow;
                    document.Notes = logNote.ToString();
                    document.EstatusCode = 0;
                    document.Estatus = DocumentStatus.Invalid.ToString();
                    DB.SaveChanges();
                }
            }

            logNote.Append(" - ").Append(errornote);
            UpdateST_UserLog(DB, documentRejected.internalId, logNote.ToString(), (int)FormAction.Add, 1);
            return validationMessage;
        }

        public static void UpdateInvoicesUUIDsAfterSync(Models_1.ViewModels.ResponseVM.SubmitDocumentVM.DocumentAccepted documentAccepted, ERPEinvoiceContext DB,List<int> invoiceIds, int invoiceType = 1)
        {
            try
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Enter UpdateInvoicesUUIDsAfterSync Method", 1, 1);
                var invoiceCode = documentAccepted.internalId;
                if (invoiceType == (int)Models_1.ViewModels.DocumentType.I)
                {
                    var document = DB.SlInvoice.FirstOrDefault(x => x.InvoiceCode == invoiceCode && invoiceIds.Contains(x.SlInvoiceId));
                    if (document != null)
                    {
                        if (string.IsNullOrEmpty(document.Uuid)) document.SyncDate = DateTime.UtcNow;
                        document.LastSyncDate = DateTime.UtcNow;
                        document.LongId = documentAccepted.longId;
                        document.Uuid = documentAccepted.uuid;
                        var logNote = $"Document with Code {invoiceCode} Sent Successfully with UUID {document.Uuid}";
                        document.Notes =logNote;
                        UpdateST_UserLog(DB, documentAccepted.internalId, logNote, (int)FormAction.Add, 1);
                        DB.SaveChanges();
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"UpdateInvoicesUUIDsAfterSync Method - Update Document", 1, 1);
                    }
                }
                else if (invoiceType == (int)Models_1.ViewModels.DocumentType.C)
                {
                    var document = DB.SlReturn.FirstOrDefault(x => x.ReturnCode == invoiceCode && invoiceIds.Contains(x.SlReturnId));
                    if (document != null)
                    {

                        if (string.IsNullOrEmpty(document.Uuid)) document.SyncDate = DateTime.UtcNow;
                        document.LastSyncDate = DateTime.UtcNow;
                        document.LongId = documentAccepted.longId;
                        document.Uuid = documentAccepted.uuid;
                        var logNote = $"Document with Code {invoiceCode} Sent Successfully with UUID {document.Uuid}";
                        document.Notes = logNote;
                        UpdateST_UserLog(DB, documentAccepted.internalId, logNote, (int)FormAction.Add, 1);

                        DB.SaveChanges();
                    }
                }

                else if (invoiceType == (int)Models_1.ViewModels.DocumentType.D)
                {
                    var document = DB.SlAdd.FirstOrDefault(x => x.ReturnCode == invoiceCode && invoiceIds.Contains(x.SlAddId));
                    if (document != null)
                    {

                        if (string.IsNullOrEmpty(document.Uuid)) document.SyncDate = DateTime.UtcNow;
                        document.LastSyncDate = DateTime.UtcNow;
                        document.LongId = documentAccepted.longId;
                        document.Uuid = documentAccepted.uuid;
                        var logNote = $"Document with Code {invoiceCode} Sent Successfully with UUID {document.Uuid}";
                        document.Notes = logNote;
                        UpdateST_UserLog(DB, documentAccepted.internalId, logNote, (int)FormAction.Add, 1);

                        DB.SaveChanges();
                    }
                }

            }
            catch (Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From UpdateInvoicesUUIDsAfterSync Method - " + " - " + ex.Message + " - " + ex.InnerException, 1, 1);
            }
        }

        public static void UpdateDocumentAfterChangeStatus(string uuid , int documentType , int status, ERPEinvoiceContext DB)
        {
            try { 
            var statusString = (status == (int)DocumentStatus.Cancelled ? DocumentStatus.Cancelled.ToString() : DocumentStatus.Rejected.ToString());
            if (documentType == (int)Models_1.ViewModels.DocumentType.I)
            {
                var document = DB.SlInvoice.FirstOrDefault(x => x.Uuid == uuid);
                if (document != null)
                {
                    document.EstatusCode = 1;
                    document.Estatus = statusString;
                    document.Notes = $"Document With Code {document.InvoiceCode} {statusString} Successfully";
                }
            }

            else if (documentType == (int)Models_1.ViewModels.DocumentType.C)
            {
                var document = DB.SlReturn.FirstOrDefault(x => x.Uuid == uuid);
                if (document != null)
                {
                    document.EstatusCode = 1;
                    document.Estatus = statusString;
                    document.Notes = $"Document With Code {document.ReturnCode} {statusString} Successfully";
                }
            }

                else if (documentType == (int)Models_1.ViewModels.DocumentType.D)
                {
                    var document = DB.SlAdd.FirstOrDefault(x => x.Uuid == uuid);
                    if (document != null)
                    {
                        document.EstatusCode = 1;
                        document.Estatus = statusString;
                        document.Notes = $"Document With Code {document.ReturnCode} {statusString} Successfully";
                    }
                }

                DB.SaveChanges();
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"UpdateDocumentAfterChangeStatus Method - Document Status Updated Successfully", 1, 1);
            }
            catch (Exception ex) {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From UpdateDocumentAfterChangeStatus Method - " + " - " + ex.Message + " - " + ex.InnerException, 1, 1);
            }
        }

        public static void LogRejectedChangeStatusDocuments(Models_1.ViewModels.ResponseVM.SubmitDocumentVM.Error error, string uuid , int documentType, int status, ERPEinvoiceContext DB)
        {
            try
            {
                var errors = GetErrors(error);
                var statusString = (status == (int)DocumentStatus.Cancelled ? DocumentStatus.Cancelled.ToString() : DocumentStatus.Rejected.ToString());
                if (documentType == (int)Models_1.ViewModels.DocumentType.I)
                {
                    var document = DB.SlInvoice.FirstOrDefault(x => x.Uuid == uuid);
                    if (document != null)
                    {
                      document.Notes = $"Document With Code {document.InvoiceCode} Faild to Change Status with errors : {errors}";
                    }
                }

                else if (documentType == (int)Models_1.ViewModels.DocumentType.C)
                {
                    var document = DB.SlReturn.FirstOrDefault(x => x.Uuid == uuid);
                    if (document != null)
                    {
                      document.Notes = $"Document With Code {document.ReturnCode} Faild to Change Status with errors : {errors}";
                    }
                }

                else if (documentType == (int)Models_1.ViewModels.DocumentType.D)
                {
                    var document = DB.SlAdd.FirstOrDefault(x => x.Uuid == uuid);
                    if (document != null)
                    {
                        document.Notes = $"Document With Code {document.ReturnCode} Faild to Change Status with errors : {errors}";
                    }
                }
                DB.SaveChanges();
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"LogRejectedChangeStatusDocuments Method - Document Status Updated Successfully", 1, 1);
            }
            catch (Exception ex)
            {
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Exception From LogRejectedChangeStatusDocuments Method - " + " - " + ex.Message + " - " + ex.InnerException, 1, 1);
            }
        }

        public static string GetErrors(Models_1.ViewModels.ResponseVM.SubmitDocumentVM.Error error)
        {
            var documentNote = new StringBuilder();

            if (!String.IsNullOrEmpty(error.code)) documentNote.Append(error.code).Append(" - ");
            if (!String.IsNullOrEmpty(error.message)) documentNote.Append(error.message).Append(" - ");
            if (!String.IsNullOrEmpty(error.target)) documentNote.Append(error.target).Append(" - ");
            if (error.details != null)
            {
                var errorDetails = error.details.FirstOrDefault();
                if (!String.IsNullOrEmpty(errorDetails.code)) documentNote.Append(errorDetails.code).Append(" - ");
                if (!String.IsNullOrEmpty(errorDetails.target)) documentNote.Append(errorDetails.target).Append(" - ");
                if (!String.IsNullOrEmpty(errorDetails.message)) documentNote.Append(errorDetails.message);
            };

            return documentNote.ToString();
        }

    }

}
