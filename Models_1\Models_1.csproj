﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{4670BD8C-095D-40DE-A45A-4119482C5B15}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Models_1</RootNamespace>
    <AssemblyName>Models_1</AssemblyName>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="netstandard, Version=2.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51" />
    <Reference Include="Newtonsoft.Json, Version=11.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\..\..\..\Program Files\dotnet\sdk\NuGetFallbackFolder\newtonsoft.json\11.0.2\lib\netstandard2.0\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ViewModels\Address.cs" />
    <Compile Include="ViewModels\ChangeDocumentStatusVM\UpdateDocumentStatus.cs" />
    <Compile Include="ViewModels\ChangeDocumentStatusVM\ChangeStatus.cs" />
    <Compile Include="ViewModels\DatabaseVM\HrCountryVM.cs" />
    <Compile Include="ViewModels\DatabaseVM\IcItemVM.cs" />
    <Compile Include="ViewModels\DatabaseVM\IcStoreVM.cs" />
    <Compile Include="ViewModels\DatabaseVM\IcUomVM.cs" />
    <Compile Include="ViewModels\DatabaseVM\SlCustomerVM.cs" />
    <Compile Include="ViewModels\DatabaseVM\StCompanyInfoVM.cs" />
    <Compile Include="ViewModels\DatabaseVM\StCurrencyVM.cs" />
    <Compile Include="ViewModels\DebitCreditDocument.cs" />
    <Compile Include="ViewModels\DebitCreditDocuments.cs" />
    <Compile Include="ViewModels\Delivery.cs" />
    <Compile Include="ViewModels\Discount.cs" />
    <Compile Include="ViewModels\Document.cs" />
    <Compile Include="ViewModels\documentpackage.cs" />
    <Compile Include="ViewModels\Documents.cs" />
    <Compile Include="ViewModels\Enums.cs" />
    <Compile Include="ViewModels\InvoiceLine.cs" />
    <Compile Include="ViewModels\InvoiceVM\DebitNote4Post.cs" />
    <Compile Include="ViewModels\InvoiceVM\Invoice.cs" />
    <Compile Include="ViewModels\InvoiceVM\InvoiceDetail.cs" />
    <Compile Include="ViewModels\Issuer.cs" />
    <Compile Include="ViewModels\ItemCodes.cs" />
    <Compile Include="ViewModels\Login.cs" />
    <Compile Include="ViewModels\Payment.cs" />
    <Compile Include="ViewModels\QueryParameters.cs" />
    <Compile Include="ViewModels\Receipt\ReceiptDocument.cs" />
    <Compile Include="ViewModels\Receiver.cs" />
    <Compile Include="ViewModels\ResponseVM\ChangeStatusErrorVM\ErrorResult.cs" />
    <Compile Include="ViewModels\ResponseVM\GetDocumentVM\DocumentValidationResults.cs" />
    <Compile Include="ViewModels\ResponseVM\GetDocumentVM\GetDocumentResponse.cs" />
    <Compile Include="ViewModels\ResponseVM\GetDocumentVM\InvoiceLineItemCode.cs" />
    <Compile Include="ViewModels\ResponseVM\GetDocumentVM\ValidationStepResult.cs" />
    <Compile Include="ViewModels\ResponseVM\GetRecentDocumentsVM\GetRecentDocumentsRespones.cs" />
    <Compile Include="ViewModels\ResponseVM\GetRecentDocumentsVM\Metadata.cs" />
    <Compile Include="ViewModels\ResponseVM\GetRecentDocumentsVM\Result.cs" />
    <Compile Include="ViewModels\ResponseVM\SubmitDocumentVM\DocumentAccepted.cs" />
    <Compile Include="ViewModels\ResponseVM\SubmitDocumentVM\DocumentRejected.cs" />
    <Compile Include="ViewModels\ResponseVM\SubmitDocumentVM\Error.cs" />
    <Compile Include="ViewModels\ResponseVM\SubmitDocumentVM\SubmissionError.cs" />
    <Compile Include="ViewModels\ResponseVM\SubmitDocumentVM\SubmitDocumentResponse.cs" />
    <Compile Include="ViewModels\Signature.cs" />
    <Compile Include="ViewModels\SignuatureVM\DocumentsToSignVM.cs" />
    <Compile Include="ViewModels\SignuatureVM\SignuatureVM.cs" />
    <Compile Include="ViewModels\TaxableItem.cs" />
    <Compile Include="ViewModels\TaxCalculatorVM\TaxCalculatorVM.cs" />
    <Compile Include="ViewModels\TaxTotal.cs" />
    <Compile Include="ViewModels\TokenVM\TokenVM.cs" />
    <Compile Include="ViewModels\UnitValue.cs" />
    <Compile Include="ViewModels\ValidationVM\ReceiptValidator.cs" />
    <Compile Include="ViewModels\ValidationVM\ValidateModel.cs" />
    <Compile Include="ViewModels\ValidationVM\ValidationMessage.cs" />
    <Compile Include="ViewModels\ValidationVM\ValidationMessages.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>