﻿IF OBJECT_ID(N'dbo.[SlReturnInvoiceDetailSubTaxValue]', N'U') IS NULL BEGIN   

SET ANSI_NULLS ON

SET QUOTED_IDENTIFIER ON

CREATE TABLE [dbo].[SlReturnInvoiceDetailSubTaxValue](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[ReturnInvoiceDetailId]   [int] NOT NULL,
	[esubTypeId] [int] NOT NULL,
	[value] [decimal](20, 6) NOT NULL,
	[TaxRatio] [decimal] (20,6) null
 CONSTRAINT [PK_SlReturnInvoiceDetailSubTaxValue] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

End