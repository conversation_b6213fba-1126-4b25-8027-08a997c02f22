# Postman Testing Guide for SaveDocument API

## Quick Reference for Testing

### Mandatory Fields Checklist ⚠️
Before testing, ensure these fields are always included:
- **Invoice**: `companyId`, `storeId`, `customerId`, `date`, `documentType`, `invoiceCode`, `invoiceDetails`
- **Invoice Details**: `itemId`, `quantity`, `currencyId`, `exchangeRate`, `amount`, `uomId`

### Test Data Requirements
- Valid `companyId`, `storeId`, `customerId` that exist in database
- Valid `itemId` that exists in IcItem table
- Valid `currencyId` and `uomId` that exist in respective tables
- Unique `invoiceCode` for each test

---

## Setup Instructions

### 1. Create New Collection
1. Open Postman
2. Click "New" → "Collection"
3. Name it "EInvoice API Tests"
4. Add description: "Testing collection for EInvoice SaveDocument API"

### 2. Environment Setup
Create a new environment with the following variables:
- `base_url`: `http://localhost:5000` (adjust port as needed)
- `api_path`: `/api/EInvoice`

### 3. Collection Variables
Set the following collection variables:
- `invoice_code`: `INV-{{$timestamp}}`
- `company_id`: `1`
- `store_id`: `1`
- `customer_id`: `1`

## Test Cases

### Test Case 1: Valid Invoice Submission

**Request Details:**
- **Method**: POST
- **URL**: `{{base_url}}{{api_path}}/SaveInvoice`
- **Headers**:
  ```
  Content-Type: application/json
  Accept: application/json
  ```

**Request Body:**
```json
[
  {
    "companyId": {{company_id}},
    "storeId": {{store_id}},
    "storeName": "Main Store",
    "customerId": {{customer_id}},
    "customerName": "Test Customer",
    "date": "{{$isoTimestamp}}",
    "invoiceId": null,
    "documentType": "I",
    "TotalDiscount": 50.00,
    "TotalTax": 70.00,
    "Net": 500.00,
    "invoiceCode": "{{invoice_code}}",
    "PurchaseOrderNumber": "PO-2024-001",
    "ProformaNumber": "",
    "DeliveryDate": "2024-12-25",
    "Uuid": [],
    "invoiceDetails": [
      {
        "Id": 0,
        "itemId": 1,
        "itemName": "Test Product",
        "quantity": 2,
        "currencyId": 1,
        "currencyName": "EGP",
        "exchangeRate": 1.0,
        "amount": 250.00,
        "uomId": 1,
        "uomName": "Piece",
        "Description": "Test product description",
        "discountAfterTax": 0.00,
        "totalAmount": 500.00,
        "discount": {
          "type": 1,
          "amount": 25.00,
          "rate": 5.0
        },
        "discounts": [
          {
            "type": 1,
            "amount": 25.00,
            "rate": 5.0
          }
        ],
        "taxes": [
          {
            "taxId": 1,
            "taxName": "T1",
            "subTaxId": 1,
            "subTaxName": "VAT",
            "type": 1,
            "rate": 14.0,
            "amount": 70.00
          }
        ]
      }
    ]
  }
]
```

**Expected Response:**
- **Status Code**: 200 OK
- **Response Body**: `"Document Saved"`

**Postman Tests Script:**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response indicates success", function () {
    pm.expect(pm.response.text()).to.include("Document Saved");
});

pm.test("Response time is less than 5000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(5000);
});
```

### Test Case 2: Invalid Invoice - Missing Required Fields

**Request Body:**
```json
[
  {
    "companyId": {{company_id}},
    "storeId": {{store_id}},
    "customerId": {{customer_id}},
    "date": "{{$isoTimestamp}}",
    "documentType": "I",
    "invoiceCode": "",
    "invoiceDetails": []
  }
]
```

**Expected Response:**
- **Status Code**: 400 Bad Request
- **Response Body**: Validation error messages

**Postman Tests Script:**
```javascript
pm.test("Status code is 400", function () {
    pm.response.to.have.status(400);
});

pm.test("Response contains validation errors", function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson).to.have.property("Messages");
});
```

### Test Case 2.1: Minimal Valid Invoice (Mandatory Fields Only)

**Request Body:**
```json
[
  {
    "companyId": {{company_id}},
    "storeId": {{store_id}},
    "customerId": {{customer_id}},
    "date": "{{$isoTimestamp}}",
    "documentType": "I",
    "invoiceCode": "MIN-{{$timestamp}}",
    "invoiceDetails": [
      {
        "itemId": 1,
        "quantity": 1,
        "currencyId": 1,
        "exchangeRate": 1.0,
        "amount": 100.00,
        "uomId": 1
      }
    ]
  }
]
```

**Expected Response:**
- **Status Code**: 200 OK
- **Response Body**: `"Document Saved"`

**Postman Tests Script:**
```javascript
pm.test("Minimal valid invoice saves successfully", function () {
    pm.response.to.have.status(200);
    pm.expect(pm.response.text()).to.include("Document Saved");
});
```

### Test Case 2.2: Missing Mandatory Field Tests

#### Missing companyId
```json
[
  {
    "storeId": {{store_id}},
    "customerId": {{customer_id}},
    "date": "{{$isoTimestamp}}",
    "documentType": "I",
    "invoiceCode": "TEST-{{$timestamp}}",
    "invoiceDetails": [{"itemId": 1, "quantity": 1, "currencyId": 1, "exchangeRate": 1.0, "amount": 100.00, "uomId": 1}]
  }
]
```

#### Missing invoiceDetails
```json
[
  {
    "companyId": {{company_id}},
    "storeId": {{store_id}},
    "customerId": {{customer_id}},
    "date": "{{$isoTimestamp}}",
    "documentType": "I",
    "invoiceCode": "TEST-{{$timestamp}}",
    "invoiceDetails": []
  }
]
```

#### Missing itemId in details
```json
[
  {
    "companyId": {{company_id}},
    "storeId": {{store_id}},
    "customerId": {{customer_id}},
    "date": "{{$isoTimestamp}}",
    "documentType": "I",
    "invoiceCode": "TEST-{{$timestamp}}",
    "invoiceDetails": [
      {
        "quantity": 1,
        "currencyId": 1,
        "exchangeRate": 1.0,
        "amount": 100.00,
        "uomId": 1
      }
    ]
  }
]
```

### Test Case 3: Duplicate Invoice Code

**Request Body:** (Same as Test Case 1 but with same invoice code)

**Expected Response:**
- **Status Code**: 400 Bad Request
- **Response Body**: Error about duplicate invoice code

### Test Case 4: Invalid Item ID

**Request Body:** (Same as Test Case 1 but with non-existent itemId)

**Expected Response:**
- **Status Code**: 400 Bad Request
- **Response Body**: Error about invalid item

### Test Case 5: Credit Note Submission

**Request Body:**
```json
[
  {
    "companyId": {{company_id}},
    "storeId": {{store_id}},
    "storeName": "Main Store",
    "customerId": {{customer_id}},
    "customerName": "Test Customer",
    "date": "{{$isoTimestamp}}",
    "invoiceId": null,
    "documentType": "C",
    "TotalDiscount": 0.00,
    "TotalTax": 35.00,
    "Net": 250.00,
    "invoiceCode": "CN-{{$timestamp}}",
    "PurchaseOrderNumber": "",
    "ProformaNumber": "",
    "DeliveryDate": "",
    "Uuid": ["original-invoice-uuid"],
    "invoiceDetails": [
      {
        "Id": 0,
        "itemId": 1,
        "itemName": "Test Product",
        "quantity": 1,
        "currencyId": 1,
        "currencyName": "EGP",
        "exchangeRate": 1.0,
        "amount": 250.00,
        "uomId": 1,
        "uomName": "Piece",
        "Description": "Credit note for returned item",
        "discountAfterTax": 0.00,
        "totalAmount": 250.00,
        "discount": {
          "type": 0,
          "amount": 0.00,
          "rate": 0.0
        },
        "discounts": [],
        "taxes": [
          {
            "taxId": 1,
            "taxName": "T1",
            "subTaxId": 1,
            "subTaxName": "VAT",
            "type": 1,
            "rate": 14.0,
            "amount": 35.00
          }
        ]
      }
    ]
  }
]
```

## Pre-request Scripts

### Generate Dynamic Invoice Code
```javascript
// Set dynamic invoice code with timestamp
pm.collectionVariables.set("invoice_code", "INV-" + Date.now());
```

### Set Current Date
```javascript
// Set current date in ISO format
pm.collectionVariables.set("current_date", new Date().toISOString());
```

## Common Test Scenarios

### 1. Boundary Testing
- Test with maximum allowed decimal places
- Test with very large amounts
- Test with zero quantities
- Test with negative amounts (should fail)

### 2. Currency Testing
- Test with different currencies
- Test with various exchange rates
- Test currency conversion calculations

### 3. Tax Calculation Testing
- Test different tax types (T1, T2, T3, T4)
- Test tax rate calculations
- Test multiple taxes on single item

### 4. Discount Testing
- Test percentage discounts
- Test fixed amount discounts
- Test multiple discounts
- Test discount calculations

## Error Response Examples

### Validation Error Response
```json
{
  "Messages": [
    {
      "invoiceCode": "INV-123",
      "Message": [
        "كود الفاتورة مطلوب",
        "تاريخ الفاتورة مطلوب",
        "يجب ان يكون هناك صنف واحد علي الاقل"
      ],
      "Islocal": true
    }
  ]
}
```

## Performance Testing

### Load Testing Script
```javascript
// Run this in Postman Runner with multiple iterations
pm.test("Response time acceptable", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
});

pm.test("No server errors", function () {
    pm.response.to.not.have.status(500);
});
```

## Troubleshooting

### Common Issues:
1. **Connection Refused**: Check if the API server is running
2. **404 Not Found**: Verify the URL and route configuration
3. **500 Internal Server Error**: Check server logs for database connection issues
4. **Validation Errors**: Ensure all required master data exists (company, store, customer, items)

### Debug Steps:
1. Check server logs for detailed error messages
2. Verify database connectivity
3. Ensure all referenced IDs exist in master tables
4. Check data types and formats match expected values

## Collection Export

To share this collection:
1. Click the three dots next to collection name
2. Select "Export"
3. Choose "Collection v2.1"
4. Save the JSON file for sharing
