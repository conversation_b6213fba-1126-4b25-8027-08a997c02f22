{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=.;Initial Catalog=Nice_Food_myToken;Integrated Security=True;Pooling=False;Connect Timeout=0"
    //"DefaultConnection": "Data Source=sql;Initial Catalog=ERPMultiCare;Integrated Security=True;Pooling=False;Connect Timeout=0"


  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning"
    }
  },
  "BaseUrl": "https://api.invoicing.eta.gov.eg",
  "LoginUrl": "https://id.eta.gov.eg/connect/token",
  //"SignuaturenUrl": "http://localhost:85/GeneratorSign/api/Generator/GetSigBase24",
  "SignuaturenUrl": "http://localhost:57978/api/Generator/GetSigBase64",
  "publicKey": "0DBDB4754DB609045EF54FC8DDA4F3B62745C50C",
  "tokenSerial": "2A7D65A6800F0004",
  "libraryPath": "D:\\linkit\\sign\\eps2003csp1164.dll",
  "tokenName": "Egypt Trust",
  "AllowedHosts": "*"
}
