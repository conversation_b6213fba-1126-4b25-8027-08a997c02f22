﻿//using System;
//using System.Collections.Generic;
//using System.IO;
//using System.Linq;
//using System.Threading.Tasks;

//using Chilkat;

//namespace EInvoice.BL
//{
//    public class CertificateChilcate
//    {
//        // This example requires the Chilkat API to have been previously unlocked.
//        // See Global Unlock Sample for sample code.

//        // Note: Chilkat's PKCS11 implementation runs on Windows, Linux, Mac OS X, and other supported operating systems.
//        public void GenerateSigniture()
//        {


//            Pkcs11 pkcs11 = new Pkcs11();

//            pkcs11.SharedLibPath = "aetpkss1.dll";
//            bool success = pkcs11.Initialize();
//            if (success == false)
//            {
//                writeToFile("pkcs11 intialize: " + pkcs11.LastErrorText, ""); 
//                return;
//            }

//            // We need to call Discover to get the slot ID.
//            // (A slot is a smart card reader or USB token.)
//            bool onlyTokensPresent = true;
//            Chilkat.JsonObject json = new Chilkat.JsonObject();
//            success = pkcs11.Discover(onlyTokensPresent, json);
//            if (success == false)
//            {
//                writeToFile("onlyTokensPresent: " + pkcs11.LastErrorText, "");               
//                return;
//            }

//            // Make sure we have at least one slot.
//            if (json.SizeOfArray("slot") <= 0)
//            {
//                writeToFile("No occuplied slots","");
                
//                return;
//            }

//            // Get the ID of the 1st slot
//            int slotID = json.IntOf("slot[0].id");

//            // Open a session.
//            bool readWrite = true;
//            success = pkcs11.OpenSession(slotID, readWrite);
//            if (success == false)
//            {
//                writeToFile("OpenSession" + pkcs11.LastErrorText, "");
//                return;
//            }

//            // Make it an authenticated session by calling Login.
//            // 
//            // If we don't authenticate, then we won't be able to see the private keys, and thus
//            // we won't know which certificates have an associated private key stored on the smart card.

//            // The smart card PIN is passed to the Login method.
//            // userType 1 indicates a "Normal User".
//            int userType = 1;
//            string pin = "82678469";
//            success = pkcs11.Login(userType, pin);
//            if (success == false)
//            {
//                writeToFile("Login" + pkcs11.LastErrorText, "");

//                success = pkcs11.CloseSession();
//                return;
//            }

//            // --------------------------------------------------------------------------
//            // The FindCert method can find a particular certificate in a number of different ways.
//            // I'll demonstrate some common ways..

//            // 1) In many cases you'll be working with a smart card that contains one certificate that is to be used for 
//            // signing, and it is the certificate that is associated with the private key also stored on the smart card (or USB token).
//            // There may be other certificates on the card, but these are the issuer certificates in the chain of authentication.
//            // You're just interested in getting the certificate with the private key.
//            // You can do it like this:
//            Chilkat.Cert cert = new Chilkat.Cert();
//            // Pass the keyword "privateKey" in the 1st argument, and an empty string in the 2nd arg.
//            // This returns the 1st certificate that has a private key.
//            success = pkcs11.FindCert("privateKey", "", cert);
//            if (success == true)
//            {
//                writeToFile("Cert with private key" + cert.SubjectCN, "");

                
//            }
//            else
//            {

//                writeToFile("No certificates having a private key were found.","");

                
//            }

//            // 2) Find a certificate by the Subject Common Name
//            success = pkcs11.FindCert("subjectCN", "Chil application account (8c9be8e0-5544-4cd0-9062-290fbff353a7)", cert);
//            if (success == true)
//            {
//                writeToFile("Found", cert.SubjectCN);

                
//            }
//            else
//            {
//                writeToFile("Not Found", cert.SubjectCN);

               
//            }

//            // 3) Find a certificate by hex serial number
//            success = pkcs11.FindCert("serial", "5087bf1feda006af54a02f23a851104948acc26f", cert);
//            if (success == true)
//            {
//                writeToFile("Not Found", cert.SubjectCN);

//                Debug.WriteLine("Found: " + cert.SerialNumber);
//            }
//            else
//            {
//                writeToFile("Not Found", cert.SubjectCN);

//                Debug.WriteLine("Not found: " + cert.SerialNumber);
//            }

//            // 4) Find a certificate by hex serial number and Issuer Common Name
//            success = pkcs11.FindCert("serial:issuerCN", "5087bf1feda006af54a02f23a851104948acc26f:Ibanity Production Third Party Application CA", cert);
//            if (success == true)
//            {
//                writeToFile("Not Found", cert.SubjectCN);

//                Debug.WriteLine("Found: " + cert.SubjectCN);
//            }
//            else
//            {
//                writeToFile("Not Found", cert.SubjectCN);

//                Debug.WriteLine("Not found: " + cert.SubjectCN);
//            }

//            // --------------------------------------------------------------------------

//            // Revert to an unauthenticated session by calling Logout.
//            success = pkcs11.Logout();
//            if (success == false)
//            {
//                writeToFile("Logout", pkcs11.LastErrorText);
//                success = pkcs11.CloseSession();
//                return;
//            }

//            // When finished, close the session.
//            // It is important to close the session (memory leaks will occur if the session is not properly closed).
//            success = pkcs11.CloseSession();
//            if (success == false)
//            {
//                writeToFile("CloseSession", pkcs11.LastErrorText);

              
//                return;
//            }

//            writeToFile("Success","");

           

//        }

//        public static void writeToFile(string msg, string action)
//        {
//            string path = "c:\\log\\log.txt";
//            //if (File.Exists(path))
//            //    File.WriteAllText(path, "scmd.DeleteContext" + msg);
//            //else
//            File.AppendAllText(path, action + " : " + Environment.NewLine + msg);
//        }
//    }

   
//}
