﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="txtFrom" xml:space="preserve">
    <value> من </value>
  </data>
  <data name="txtFromDate" xml:space="preserve">
    <value> من تاريخ </value>
  </data>
  <data name="Msg01" xml:space="preserve">
    <value>عفواً، لا يمكن حذف هذا الحساب</value>
  </data>
  <data name="Msg02" xml:space="preserve">
    <value>يجب حذف القيود الخاصه بهذا الحساب اولا</value>
  </data>
  <data name="Msg03" xml:space="preserve">
    <value>يجب حذف الحسابات الفرعية اولا</value>
  </data>
  <data name="Msg04" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا الحساب</value>
  </data>
  <data name="Msg05" xml:space="preserve">
    <value>عفوا، لا يمكن انشاء حساب فرعي</value>
  </data>
  <data name="Msg06" xml:space="preserve">
    <value>يرجى إدخال اسم الحساب</value>
  </data>
  <data name="MsgNameExist" xml:space="preserve">
    <value>هذا الاسم مسجل من قبل</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>تأكد من صحة البيانات</value>
  </data>
  <data name="Msg09" xml:space="preserve">
    <value>لا يمكن تعديل هذا الحساب</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>معلومة</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية تعديل هذا البيان</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية اضافة بيان جديد</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>سؤال</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>تم الحفظ بنجاح</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="NoLossNoProfit" xml:space="preserve">
    <value>لايوجد ربح أو خسارة</value>
  </data>
  <data name="txtTo" xml:space="preserve">
    <value> الى </value>
  </data>
  <data name="txtToDate" xml:space="preserve">
    <value> الى تاريخ </value>
  </data>
  <data name="TotalLoss" xml:space="preserve">
    <value>مجمل الخساره</value>
  </data>
  <data name="TotalProfit" xml:space="preserve">
    <value>مجمل الربح</value>
  </data>
  <data name="Msg10" xml:space="preserve">
    <value>عفوا, لايمكن حذف الخزينه الرئيسيه</value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>تم الحذف بنجاح</value>
  </data>
  <data name="ValTxtName" xml:space="preserve">
    <value>يجب تسجيل الاسم</value>
  </data>
  <data name="txt1" xml:space="preserve">
    <value>يجب تسجيل رقم الحساب</value>
  </data>
  <data name="txtBank" xml:space="preserve">
    <value> بنك </value>
  </data>
  <data name="txtOpenBalance" xml:space="preserve">
    <value> رصيد افتتاحي </value>
  </data>
  <data name="Msg11" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذه الخزنه</value>
  </data>
  <data name="txtDrawer" xml:space="preserve">
    <value> خزينه </value>
  </data>
  <data name="Msg12" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف سند الدفع النقدي</value>
  </data>
  <data name="Msg13" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف سند الاستلام النقدي</value>
  </data>
  <data name="txtCashPay" xml:space="preserve">
    <value>سند دفع نقدي</value>
  </data>
  <data name="txtCashReceive" xml:space="preserve">
    <value>سند قبض نقدي</value>
  </data>
  <data name="txtCredit" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="txtCustomer" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="txtDebit" xml:space="preserve">
    <value>مدين</value>
  </data>
  <data name="txtPurDisc" xml:space="preserve">
    <value>خصم مكتسب</value>
  </data>
  <data name="txtSelDisc" xml:space="preserve">
    <value>خصم مسموح به</value>
  </data>
  <data name="txtVendor" xml:space="preserve">
    <value>المورد</value>
  </data>
  <data name="ValtxtAcc" xml:space="preserve">
    <value>يجب اختيار الحساب</value>
  </data>
  <data name="ValtxtAmount" xml:space="preserve">
    <value>يجب تسجيل المبلغ</value>
  </data>
  <data name="ValtxtDrwr" xml:space="preserve">
    <value>يجب اختيار الخزنه</value>
  </data>
  <data name="ValtxtMinus" xml:space="preserve">
    <value>لايمكن ادخال قيم سالبه</value>
  </data>
  <data name="Msg14" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف سند التحويل النقدي</value>
  </data>
  <data name="txtBankAccount" xml:space="preserve">
    <value>حساب بنك</value>
  </data>
  <data name="txtCashPayList" xml:space="preserve">
    <value>قائمة سندات دفع نقدي</value>
  </data>
  <data name="txtCashReceiveList" xml:space="preserve">
    <value>قائمة سندات قبض نقدي</value>
  </data>
  <data name="txtCashTrnsfr" xml:space="preserve">
    <value>سند تحويل نقدي</value>
  </data>
  <data name="Msg15" xml:space="preserve">
    <value>عفواً، لا يمكن حذف هذا القيد من هنا, يجب ان يحذف من مصدره</value>
  </data>
  <data name="Msg16" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا القيد</value>
  </data>
  <data name="Msg17" xml:space="preserve">
    <value>يرجى إدخال الرقم الدفتري</value>
  </data>
  <data name="Msg18" xml:space="preserve">
    <value>عفواً، لا يمكن تعديل هذا القيد من هنا, يجب ان يعدل من مصدره</value>
  </data>
  <data name="Msg19" xml:space="preserve">
    <value>يجب تسجيل القيود</value>
  </data>
  <data name="Msg20" xml:space="preserve">
    <value>يجب ان يتساوي الطرف الدائن مع الطرف المدين</value>
  </data>
  <data name="Msg21" xml:space="preserve">
    <value>لايمكن استخدام حسابات نهاية المدة في قيود, فهي تحسب بشكل تلقائي</value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>حذف صف ؟</value>
  </data>
  <data name="MsgNumExist" xml:space="preserve">
    <value>هذا الرقم مسجل من قبل</value>
  </data>
  <data name="txt2" xml:space="preserve">
    <value>حساب المتاجره - مجمل الخساره</value>
  </data>
  <data name="txt3" xml:space="preserve">
    <value>حساب المتاجره - مجمل الربح</value>
  </data>
  <data name="txt4" xml:space="preserve">
    <value>تسوية الحسابات</value>
  </data>
  <data name="ValtxtJrnl1" xml:space="preserve">
    <value>يجب ادخال قيمه في حقل مدين أو دائن</value>
  </data>
  <data name="ValtxtJrnl2" xml:space="preserve">
    <value> أحد الحقلين لابد ان يكون بصفر</value>
  </data>
  <data name="Msg22" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف ورق الدفع</value>
  </data>
  <data name="Msg23" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف ورق القبض</value>
  </data>
  <data name="txtBill" xml:space="preserve">
    <value> كمبيالة </value>
  </data>
  <data name="txtBillNum" xml:space="preserve">
    <value>رقم الكمبيالة</value>
  </data>
  <data name="txtCheck" xml:space="preserve">
    <value> شيك </value>
  </data>
  <data name="txtCheckNum" xml:space="preserve">
    <value>رقم الشيك</value>
  </data>
  <data name="txtPayNote" xml:space="preserve">
    <value> سند دفع</value>
  </data>
  <data name="txtReceiveNote" xml:space="preserve">
    <value> سند قبض</value>
  </data>
  <data name="Valtxt1" xml:space="preserve">
    <value>يجب تسجيل ورقة واحدة علي الأقل</value>
  </data>
  <data name="VAltxt2" xml:space="preserve">
    <value>يجب ان يتساوي المبلغ الاجمالي مع مجموع الأوراق</value>
  </data>
  <data name="valtxtbank" xml:space="preserve">
    <value>يجب اختيار البنك</value>
  </data>
  <data name="ValtxtDueDate" xml:space="preserve">
    <value>يجب ادخال تاريخ الاستحقاق</value>
  </data>
  <data name="ValtxtTotlAmnt" xml:space="preserve">
    <value>يجب تسجيل المبلغ الاجمالي</value>
  </data>
  <data name="ValtxtNoteNum" xml:space="preserve">
    <value>يجب ادخال رقم الورقة</value>
  </data>
  <data name="txt5" xml:space="preserve">
    <value>حساب المتاجره مدين ب</value>
  </data>
  <data name="txt6" xml:space="preserve">
    <value>حساب المتاجره دائن ب</value>
  </data>
  <data name="txt7" xml:space="preserve">
    <value>هذا الحساب مدين ب</value>
  </data>
  <data name="txt8" xml:space="preserve">
    <value>هذا الحساب دائن ب</value>
  </data>
  <data name="txt9" xml:space="preserve">
    <value>متضمن الحسابات الفرعية</value>
  </data>
  <data name="MsgAskDel" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد حذف</value>
  </data>
  <data name="txtBounceDate" xml:space="preserve">
    <value>تاريخ الرد</value>
  </data>
  <data name="txtPayDate" xml:space="preserve">
    <value>تاريخ السداد</value>
  </data>
  <data name="ValtxtBounce" xml:space="preserve">
    <value>يجب تحديد تاريخ الرد</value>
  </data>
  <data name="ValTxtMaturity" xml:space="preserve">
    <value>يجب تسجيل تاريخ استحقاق الورقة</value>
  </data>
  <data name="ValTxtPayDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ سداد الورقة</value>
  </data>
  <data name="txtEndorsed" xml:space="preserve">
    <value>مظهر لشخص اخر</value>
  </data>
  <data name="txtEndorseDate" xml:space="preserve">
    <value>تاريخ التظهير</value>
  </data>
  <data name="txtNoteBounce" xml:space="preserve">
    <value>رد</value>
  </data>
  <data name="txtNoteEndorse" xml:space="preserve">
    <value> تظهير </value>
  </data>
  <data name="txtNoteOutstanding" xml:space="preserve">
    <value>مستحق</value>
  </data>
  <data name="txtNotePay" xml:space="preserve">
    <value> سداد </value>
  </data>
  <data name="txtPayedBank" xml:space="preserve">
    <value>مسدد لحساب بنك</value>
  </data>
  <data name="txtPayedDrwr" xml:space="preserve">
    <value>مسدد للخزينة</value>
  </data>
  <data name="ValTxtBillDrwr" xml:space="preserve">
    <value>يجب اختيار الخزينة المسدد لها الكمبيالة</value>
  </data>
  <data name="ValTxtCheckDrwr" xml:space="preserve">
    <value>يجب اختيار حساب السداد للورقة</value>
  </data>
  <data name="ValTxtEndorse" xml:space="preserve">
    <value>يجب اختيار الطرف المظهر له الورقة</value>
  </data>
  <data name="txtDealerNm" xml:space="preserve">
    <value> اسم المتعامل</value>
  </data>
  <data name="Msg24" xml:space="preserve">
    <value>سيتم سداد جميع أوراق الدفع المختارة، هل تريد المتابعة</value>
  </data>
  <data name="Msg25" xml:space="preserve">
    <value>تم سداد أوراق الدفع بنجاح</value>
  </data>
  <data name="Msg26" xml:space="preserve">
    <value>برجاء اختيار خزينة سداد الكمبيالات و الضغط على متابعة</value>
  </data>
  <data name="Msg27" xml:space="preserve">
    <value>سيتم تحديث بيانات أوراق القبض المختارة، هل تريد المتابعة</value>
  </data>
  <data name="Msg28" xml:space="preserve">
    <value>برجاء اختيار خزينة السداد و الضغط على متابعة</value>
  </data>
  <data name="Msg29" xml:space="preserve">
    <value>تم تحديث بيانات أوراق القبض بنجاح</value>
  </data>
  <data name="Msg30" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا الايراد</value>
  </data>
  <data name="Msg31" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا المصروف</value>
  </data>
  <data name="txt10" xml:space="preserve">
    <value>تم عمل الحساب من شاشة الايرادات</value>
  </data>
  <data name="txt11" xml:space="preserve">
    <value>تم عمل الحساب من شاشة المصروفات</value>
  </data>
  <data name="txtExp" xml:space="preserve">
    <value>تسجيل مصروفات</value>
  </data>
  <data name="txtExpList" xml:space="preserve">
    <value>قائمة المصروفات</value>
  </data>
  <data name="txtExpNum" xml:space="preserve">
    <value>مصروف رقم  </value>
  </data>
  <data name="txtExpType" xml:space="preserve">
    <value>نوع المصروف</value>
  </data>
  <data name="txtExpTypeAdd" xml:space="preserve">
    <value>اضافة نوع مصروف</value>
  </data>
  <data name="txtRev" xml:space="preserve">
    <value>تسجيل ايرادات</value>
  </data>
  <data name="txtRevList" xml:space="preserve">
    <value>قائمة الايرادات</value>
  </data>
  <data name="txtRevNum" xml:space="preserve">
    <value>ايراد رقم </value>
  </data>
  <data name="txtRevType" xml:space="preserve">
    <value>نوع الايراد</value>
  </data>
  <data name="txtRevTypeAdd" xml:space="preserve">
    <value>اضافة نوع ايراد</value>
  </data>
  <data name="ValTxt3" xml:space="preserve">
    <value>يجب تسجيل اسم الايراد بشكل صحيح</value>
  </data>
  <data name="ValTxt4" xml:space="preserve">
    <value>يجب تسجيل اسم المصروف بشكل صحيح</value>
  </data>
  <data name="st_CompName" xml:space="preserve">
    <value>اسم المنشأه</value>
  </data>
  <data name="st_CompTel" xml:space="preserve">
    <value>تليفون المنشأه</value>
  </data>
  <data name="st_ItemCode" xml:space="preserve">
    <value>كود الصنف</value>
  </data>
  <data name="st_ItemName" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="st_ItemPrice" xml:space="preserve">
    <value>سعر الصنف</value>
  </data>
  <data name="st_vendorCode" xml:space="preserve">
    <value>كود المورد</value>
  </data>
  <data name="st_SetDaysCount" xml:space="preserve">
    <value>يجب تسجيل عدد الأيام</value>
  </data>
  <data name="NetLoss" xml:space="preserve">
    <value>صافي الخسارة</value>
  </data>
  <data name="NetProfit" xml:space="preserve">
    <value>صافي الربح</value>
  </data>
  <data name="MsgCCCode" xml:space="preserve">
    <value>يرجي ادخال كود مركز التكلفة</value>
  </data>
  <data name="MsgCCName" xml:space="preserve">
    <value>يرجي ادخال اسم مركز التكلفة</value>
  </data>
  <data name="MsgDelCC" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف مركز التكلفة هذا</value>
  </data>
  <data name="MsgDelCCDenied" xml:space="preserve">
    <value>يجب حذف القيود الخاصه بمركز التكلفة هذا اولا</value>
  </data>
  <data name="MsgZeroCode" xml:space="preserve">
    <value>الكود لايمكن أن يساوي صفر</value>
  </data>
  <data name="ValTxtCstCntr" xml:space="preserve">
    <value>يجب اختيار مركز التكلفة</value>
  </data>
  <data name="valCodeExist" xml:space="preserve">
    <value>هذا الكود مسجل من قبل</value>
  </data>
  <data name="valtxtCode" xml:space="preserve">
    <value>يجب تسجيل الكود</value>
  </data>
  <data name="Msg32" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف قائمة الحساب المخصصة</value>
  </data>
  <data name="Msg33" xml:space="preserve">
    <value>يجب تسجيل الحسابات</value>
  </data>
  <data name="Msg34" xml:space="preserve">
    <value>يجب حذف هذا الحساب من قوائم الحسابات المخصصة اولا</value>
  </data>
  <data name="ValTxtAccExist" xml:space="preserve">
    <value>الحساب موجود مسبقا</value>
  </data>
  <data name="ValTxtCstmLst" xml:space="preserve">
    <value>يجب اختيار قائمة حسابات مخصصة</value>
  </data>
  <data name="Msg35" xml:space="preserve">
    <value>يجب تسجيل اسم قائمة الحسابات</value>
  </data>
  <data name="txt_Account" xml:space="preserve">
    <value>الحســاب</value>
  </data>
  <data name="txt_JournalNumber" xml:space="preserve">
    <value>قيد يومية رقم </value>
  </data>
  <data name="txt_incoming" xml:space="preserve">
    <value>وارد</value>
  </data>
  <data name="txt_outgoing" xml:space="preserve">
    <value>صادر</value>
  </data>
  <data name="txt_Number" xml:space="preserve">
    <value>رقــم</value>
  </data>
  <data name="valtxtChildAcc" xml:space="preserve">
    <value>يجب اختيار حساب فرعي</value>
  </data>
  <data name="txt_C" xml:space="preserve">
    <value>د</value>
  </data>
  <data name="txt_D" xml:space="preserve">
    <value>م</value>
  </data>
  <data name="msgUserDefault" xml:space="preserve">
    <value>هذه الخزينة مخصصة كخزينة افتراضية لبعض المستخدمين</value>
  </data>
  <data name="emp" xml:space="preserve">
    <value>موظف</value>
  </data>
  <data name="MsgHRAccount" xml:space="preserve">
    <value>عفوا لايمكن حذف هذا الحساب، الحساب مستخدم من قبل النظام، يمكنكم تغيير ذلك من الاعدادات</value>
  </data>
  <data name="txtExpense" xml:space="preserve">
    <value>سند مصروف</value>
  </data>
  <data name="txtRevenue" xml:space="preserve">
    <value>سند إيراد</value>
  </data>
  <data name="txtAcc" xml:space="preserve">
    <value>حساب</value>
  </data>
  <data name="txtCostCenter" xml:space="preserve">
    <value>مركز تكلفة</value>
  </data>
  <data name="txtCustomList" xml:space="preserve">
    <value>قائمة مخصصة</value>
  </data>
  <data name="txtDiscount" xml:space="preserve">
    <value>خصم</value>
  </data>
  <data name="st_PurchaseInvoice" xml:space="preserve">
    <value>رقم فاتورة المشتريات</value>
  </data>
  <data name="MsgBankAcc" xml:space="preserve">
    <value>برجاء تحديد حساب البنوك وأوراق القبض والدفع من شاشة الإعدادات</value>
  </data>
  <data name="MsgDrawerAcc" xml:space="preserve">
    <value>برجاء تحديد حساب الخزانات الرئيسي من شاشة الإعدادات</value>
  </data>
  <data name="MsgNotesPayableAcc" xml:space="preserve">
    <value>برجاء تحديد حساب أوراق الدفع من شاشة الإعدادات</value>
  </data>
  <data name="MsgNotesReceivableAcc" xml:space="preserve">
    <value>برجاء تحديد حساب أوراق القبض من شاشة الإعدادات</value>
  </data>
  <data name="MsgMerchendaisingAcc" xml:space="preserve">
    <value>برجاء تحديد حساب المتاجرة من شاشة الإعدادات</value>
  </data>
  <data name="MsgAccSettng" xml:space="preserve">
    <value>هذا الحساب مستخدم في إعدادات الحسابات، لايمكن حذفه</value>
  </data>
  <data name="MsgDrwrBank" xml:space="preserve">
    <value>يجب تسجيل حساب خزينة او بنك أولا</value>
  </data>
  <data name="excess" xml:space="preserve">
    <value>فائض</value>
  </data>
  <data name="shortage" xml:space="preserve">
    <value>عجز</value>
  </data>
  <data name="MsgAccLevel" xml:space="preserve">
    <value>لقد تم تجاوز عدد الحسابات المسموح بها في هذا المستوى من شجرة الحسابات</value>
  </data>
  <data name="ValTxtCollectBankAcc" xml:space="preserve">
    <value>يجب اختيار حافظة ايداع البنك</value>
  </data>
  <data name="MsgNotesReceivableUnderCollect" xml:space="preserve">
    <value>برجاء تحديد حساب أوراق تحت التحصيل من شاشة الإعدادات</value>
  </data>
  <data name="ValTxtCollectDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ الايداع بالبنك</value>
  </data>
  <data name="ValParentAcc" xml:space="preserve">
    <value>عفوا، لايمكن عمل حساب فرعي لحساب مستخدم بالفعل</value>
  </data>
  <data name="delCCChild" xml:space="preserve">
    <value>عفوا، لايمكنك حذف مركز التكلفه، يجب حذف مراكز التكلفه الفرعيه اولا</value>
  </data>
  <data name="MsgCantAddCCC" xml:space="preserve">
    <value>عفوا، لا يمكن انشاء مركز تكلفة فرعي</value>
  </data>
  <data name="txtAccName" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="txtCashPayNotes" xml:space="preserve">
    <value>سندات الدفع النقدي</value>
  </data>
  <data name="txtCashReceiveNotes" xml:space="preserve">
    <value>سندات القبض النقدي</value>
  </data>
  <data name="ExpireDate" xml:space="preserve">
    <value>تاريخ الصلاحية</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>اشعار دائن</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>اشعار مدين</value>
  </data>
  <data name="CreditNoteList" xml:space="preserve">
    <value>قائمة الاشعارات الدائنة</value>
  </data>
  <data name="DebitNoteList" xml:space="preserve">
    <value>قائمة الاشعارات المدينة</value>
  </data>
  <data name="MsgDelCrdtNote" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف سند اشعار دائن</value>
  </data>
  <data name="MsgDelDbtNote" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف سند اشعار مدين</value>
  </data>
  <data name="ValSelectDbtCrdtAcc" xml:space="preserve">
    <value>يجب اختيار حساب الاشعار</value>
  </data>
  <data name="diff" xml:space="preserve">
    <value>الفرق</value>
  </data>
  <data name="valprExp" xml:space="preserve">
    <value>لايمكن حذف المصروف فهو مستخدم بفاتورة مشتريات رقم</value>
  </data>
  <data name="valprExp1" xml:space="preserve">
    <value>لايمكن إضافة المصروف فهو مستخدم بفاتورة مشتريات رقم</value>
  </data>
  <data name="MsgIncomAccs" xml:space="preserve">
    <value>برجاء إختيار حسابات قائمة الدخل في شاشة الإعدادات</value>
  </data>
  <data name="DelAccContinualStore" xml:space="preserve">
    <value>عفوا لايمكن حذف الحساب فهو مستخددم لأحد المخازن</value>
  </data>
  <data name="DelFaGrp" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف فئة الأصول الثابتة هذه</value>
  </data>
  <data name="DelFaGrp2" xml:space="preserve">
    <value>عفوا، لايمكن حذف هذه الفئة، يوجد أصول مرتبطة بها</value>
  </data>
  <data name="FaAccs" xml:space="preserve">
    <value>يجب اختيار حساب الأصول الثابتة وحساب مجمع الاهلاك من شاشة الإعدادات</value>
  </data>
  <data name="DelFaDenied" xml:space="preserve">
    <value>عفوا، لايمكن حذف الأصل، يوجد قيود مرتبطة به</value>
  </data>
  <data name="DelAccBom" xml:space="preserve">
    <value>عفوا لايمكن حذف الحساب فهو مستخدم بنظام الانتاج</value>
  </data>
  <data name="st_ItemCode2" xml:space="preserve">
    <value>كود 2</value>
  </data>
  <data name="MsgccDelDenie" xml:space="preserve">
    <value>عفوا لايمكن حذف مركز التكلفة</value>
  </data>
  <data name="SelectPeriod" xml:space="preserve">
    <value>برجاء تحديد الفترة بشكل سليم</value>
  </data>
  <data name="valTxtUsedAccount" xml:space="preserve">
    <value>لايمكن اضافة الحساب تحت حساب مستخدم</value>
  </data>
  <data name="st_Batch" xml:space="preserve">
    <value>رقم تشغيلة</value>
  </data>
  <data name="st_Qty" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="ValCrncRate" xml:space="preserve">
    <value>المعامل يجب ان يكون اكبر من صفر</value>
  </data>
  <data name="ValDelAccDenied" xml:space="preserve">
    <value>لايمكن حذف هذا الحساب من شاشة دليل الحسابات</value>
  </data>
  <data name="acTypeCredit" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="acTypeDebit" xml:space="preserve">
    <value>مدين</value>
  </data>
  <data name="acTypeWithout" xml:space="preserve">
    <value>غير محدد</value>
  </data>
  <data name="ccTypeMandatory" xml:space="preserve">
    <value>إجباري</value>
  </data>
  <data name="ccTypeOptional" xml:space="preserve">
    <value>إختياري</value>
  </data>
  <data name="ccTypeWithout" xml:space="preserve">
    <value>بدون</value>
  </data>
  <data name="valArchive" xml:space="preserve">
    <value>عفوا,لا يمكن عرض الارشيف في اكثر من سنة</value>
  </data>
  <data name="MsgFiscalYearEndDate" xml:space="preserve">
    <value>يجب اختيار تاريخ انتهاء السنة المالية من شاشة بيانات المنشأة</value>
  </data>
  <data name="MsgVisaAcc" xml:space="preserve">
    <value>برجاء تحديد حساب الفيزات وأوراق القبض والدفع من شاشة الإعدادات</value>
  </data>
  <data name="delManfExpWarn" xml:space="preserve">
    <value>انت على وشك حذف هذا العنصر !! هل انت متأكد؟</value>
  </data>
  <data name="valManfExp" xml:space="preserve">
    <value>عفواً، هذا المصروف مستخدم في تشغيلة اخرى.</value>
  </data>
  <data name="ValPayableNotes" xml:space="preserve">
    <value>أوراق الدفع</value>
  </data>
  <data name="txtPartialPayment" xml:space="preserve">
    <value>سداد جزئي</value>
  </data>
  <data name="txtCashNote" xml:space="preserve">
    <value>ورقة القبض</value>
  </data>
  <data name="valstoreExp1" xml:space="preserve">
    <value>لايمكن إضافة المصروف فهو مستخدم بنقل مخزن رقم</value>
  </data>
  <data name="valSourceNoteError" xml:space="preserve">
    <value>يجب اختيار ورقة القبض</value>
  </data>
  <data name="valIntrmdtStorActId" xml:space="preserve">
    <value>يرجى التأكد من اختيار حساب المخزون البيعي من الإعدادات</value>
  </data>
  <data name="capitalDisError" xml:space="preserve">
    <value>يجب أن يتساوى مجموع نسب راس المال مع إجمالى رأس المال</value>
  </data>
  <data name="SalesAfterCost" xml:space="preserve">
    <value>مجمل الربح البيع النقدى</value>
  </data>
  <data name="SalesNet" xml:space="preserve">
    <value>صافي المبيعات</value>
  </data>
</root>