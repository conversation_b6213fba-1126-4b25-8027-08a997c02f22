﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models_1.ViewModels.DatabaseVM
{
    public class StCompanyInfoVM
    {
        public int CompanyId { get; set; }
        public string CmpNameAr { get; set; }
        public string CmpNameEn { get; set; }
        public string CmpAddress { get; set; }
        public string CmpCity { get; set; }
        public string CmpCountry { get; set; }
        public string CmpTel { get; set; }
        public string CmpMobile { get; set; }
        public byte[] Logo { get; set; }
        public string CommercialBook { get; set; }
        public string TaxCard { get; set; }
        public string MngrName { get; set; }
        public string MngrAdress { get; set; }
        public string MngrTel { get; set; }
        public string MngrMobile { get; set; }
        public bool DoPeriodicBackup { get; set; }
        public string BackupPath { get; set; }
        public int NumOfDays { get; set; }
        public double? Code { get; set; }
        public DateTime? StartDate { get; set; }
        public string ServerName { get; set; }
        public string Ipaddresses { get; set; }
        public string Manufacturing { get; set; }
        public string Accounting { get; set; }
        public string Inventory { get; set; }
        public string Hr { get; set; }
        public string Pos { get; set; }
        public string Processes { get; set; }
        public string PriceList { get; set; }
        public string Tax { get; set; }
        public string ItemMatrix { get; set; }
        public string Contract { get; set; }
        public string EgyptPharmacyTax { get; set; }
        public string WoodTrade { get; set; }
        public string OfflinePostToGl { get; set; }
        public string InvoicePostToStore { get; set; }
        public string JobOrder { get; set; }
        public string SalesOrder { get; set; }
        public DateTime? FiscalYearStartDate { get; set; }
        public DateTime? FiscalYearEndDate { get; set; }
        public string ModelGlobalPos { get; set; }
        public string Marketting { get; set; }
        public string Currencies { get; set; }
        public string Checks { get; set; }
        public bool StockIsPeriodic { get; set; }
        public string LetterOfCredit { get; set; }
        public string Bascol { get; set; }
        public string SalesMan { get; set; }
        public string RealState { get; set; }
        public bool FpDependOnInOut { get; set; }
        public string Payslip { get; set; }
        public string ItemsPosting { get; set; }
        public string PrInvoicePostToStore { get; set; }
        public string Cars { get; set; }
        public string IsLaundry { get; set; }
        public string Iban { get; set; }
        public string Libra { get; set; }
        public string SalesOnly { get; set; }
        public string Mall { get; set; }
        public string ImpExp { get; set; }
        public string Shareholder { get; set; }
        public string Fa { get; set; }
        public string PayRec { get; set; }
        public string ActivityType { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string DonglePin { get; set; }
        public string ServiceProviderSignature { get; set; }


    }


}
