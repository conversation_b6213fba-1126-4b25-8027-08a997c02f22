﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class HrUser
    {
        public int UserId { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public byte? AccessType { get; set; }
        public bool IsDeleted { get; set; }
        public string ActiveNavBarGroup { get; set; }
        public DateTime? PrIFromDate { get; set; }
        public DateTime? PrIToDate { get; set; }
        public DateTime? PrRFromDate { get; set; }
        public DateTime? PrRToDate { get; set; }
        public DateTime? SlIFromDate { get; set; }
        public DateTime? SlIToDate { get; set; }
        public DateTime? SlRFromDate { get; set; }
        public DateTime? SlRToDate { get; set; }
        public DateTime? ManFromDate { get; set; }
        public DateTime? ManToDate { get; set; }
        public DateTime? CashFromDate { get; set; }
        public DateTime? CashToDate { get; set; }
        public DateTime? JrnlFromDate { get; set; }
        public DateTime? JrnlToDate { get; set; }
        public bool? ShowMainChart { get; set; }
        public string StyleName { get; set; }
        public DateTime? SlQFromDate { get; set; }
        public DateTime? SlQToDate { get; set; }
        public DateTime? IcInTrnsFromDate { get; set; }
        public DateTime? IcInTrnsToDate { get; set; }
        public DateTime? IcOutTrnsFromDate { get; set; }
        public DateTime? IcOutTrnsToDate { get; set; }
        public DateTime? IcDamageFromDate { get; set; }
        public DateTime? IcDamageToDate { get; set; }
        public DateTime? IcTransferFromDate { get; set; }
        public DateTime? IcTransferToDate { get; set; }
        public string AttClWeekEnd { get; set; }
        public string AttClFormalVacation { get; set; }
        public string AttClEmpVacation { get; set; }
        public string AttClEmpAbsence { get; set; }
        public string AttClDelay { get; set; }
        public DateTime? HrVacationFromDate { get; set; }
        public DateTime? HrVacationToDate { get; set; }
        public DateTime? HrAbsenceFromDate { get; set; }
        public DateTime? HrAbsenceToDate { get; set; }
        public DateTime? HrDelayFromDate { get; set; }
        public DateTime? HrDelayToDate { get; set; }
        public DateTime? HrOverTimeFromDate { get; set; }
        public DateTime? HrOverTimeToDate { get; set; }
        public DateTime? HrRewardFromDate { get; set; }
        public DateTime? HrRewardToDate { get; set; }
        public DateTime? HrPenalityFromDate { get; set; }
        public DateTime? HrPenalityToDate { get; set; }
        public DateTime? HrPayFromDate { get; set; }
        public DateTime? HrPayToDate { get; set; }
        public DateTime? AccCashTransferFromDate { get; set; }
        public DateTime? AccCashTransferToDate { get; set; }
        public string AccPayNoteStill { get; set; }
        public string AccPayNotePaid { get; set; }
        public string AccPayNoteRejected { get; set; }
        public string AccPayNoteOverdue { get; set; }
        public string AccReceiveNoteStill { get; set; }
        public string AccReceiveNotePaid { get; set; }
        public string AccReceiveNoteRejected { get; set; }
        public string AccReceiveNoteOverdue { get; set; }
        public DateTime? AccRevFromDate { get; set; }
        public DateTime? AccRevToDate { get; set; }
        public DateTime? AccExpFromDate { get; set; }
        public DateTime? AccExpToDate { get; set; }
        public DateTime? AccRecNoteFromDate { get; set; }
        public DateTime? AccRecNoteToDate { get; set; }
        public DateTime? AccPayNoteFromDate { get; set; }
        public DateTime? AccPayNoteToDate { get; set; }
        public DateTime? JoRegFromDate { get; set; }
        public DateTime? JoRegToDate { get; set; }
        public DateTime? JoDueFromDate { get; set; }
        public DateTime? JoDueToDate { get; set; }
        public DateTime? AccDebitNoteFromDate { get; set; }
        public DateTime? AccDebitNoteToDate { get; set; }
        public DateTime? AccCreditNoteFromDate { get; set; }
        public DateTime? AccCreditNoteToDate { get; set; }
        public DateTime? ManQcFromDate { get; set; }
        public DateTime? ManQcToDate { get; set; }
        public DateTime? MrInDrctSlFromDate { get; set; }
        public DateTime? MrInDrctSlToDate { get; set; }
        public DateTime? LoanFromDate { get; set; }
        public DateTime? LoanToDate { get; set; }
        public DateTime? WeightFromDate { get; set; }
        public DateTime? WeightToDate { get; set; }
        public DateTime? HrEvalFromDate { get; set; }
        public DateTime? HrEvalToDate { get; set; }
        public DateTime? HrPromFromDate { get; set; }
        public DateTime? HrPromToDate { get; set; }
        public DateTime? HrSponsrChngFromDate { get; set; }
        public DateTime? HrSponsrChngToDate { get; set; }
        public DateTime? HrTrainFromDate { get; set; }
        public DateTime? HrTrainToDate { get; set; }
        public bool FocusGridInInvoices { get; set; }
        public bool UseBarcodeScanner { get; set; }
        public bool UseContainsToSearchItems { get; set; }
        public bool InvoicesUseSearchItems { get; set; }
        public string InvoicesNotes { get; set; }
        public bool UserChangeStore { get; set; }
        public bool UserChangeDrawer { get; set; }
        public int DefaultStore { get; set; }
        public int? DefaultDrawer { get; set; }
        public int? DefaultRawItemsStore { get; set; }
        public int DefaultCustGrp { get; set; }
        public bool UserChangeCostCenterInInv { get; set; }
        public bool AccessOtherUserTrns { get; set; }
        public bool? StoresShowSellP { get; set; }
        public bool StoresShowPurchaseP { get; set; }
        public bool ShowPrintPreview { get; set; }
        public string SubtotalBackcolor { get; set; }
        public bool UseAccountsTreeInRevExp { get; set; }
        public bool SellPriceUponQtyAvailable { get; set; }
        public bool ColumnChooserAvailable { get; set; }
        public bool SaveGridFilters { get; set; }
        public bool SellShowCrntQty { get; set; }
        public bool SellReorder { get; set; }
        public bool? SellUnderMinQty { get; set; }
        public bool? SellWithNoBalance { get; set; }
        public bool? SellLessThanBuyPrice { get; set; }
        public bool? SellCustomerOverCredit { get; set; }
        public bool? SlInvoicePayMethod { get; set; }
        public bool? SlReturnPayMethod { get; set; }
        public int? DefaultSalesRep { get; set; }
        public int? DefaultSlinvInvBookId { get; set; }
        public int? DefaultSlretInvBookId { get; set; }
        public bool HideDeliverDate { get; set; }
        public bool HideAgedReceivables { get; set; }
        public bool HideShipTo { get; set; }
        public bool HidePo { get; set; }
        public bool HideSalesEmp { get; set; }
        public bool HidePurchasePrice { get; set; }
        public bool HideItemDiscount { get; set; }
        public bool HideBatchColumn4User { get; set; }
        public bool UserEditSalePrice { get; set; }
        public bool UserCanWriteDiscount { get; set; }
        public bool UserMakeOnCreditInv { get; set; }
        public bool? BuyOverMaxQty { get; set; }
        public bool? BuyVendorOverCredit { get; set; }
        public bool? PrInvoicePayMethod { get; set; }
        public bool? PrReturnPayMethod { get; set; }
        public int? DefaultPrinvInvBookId { get; set; }
        public int? DefaultPrretInvBookId { get; set; }
        public bool UserEditTransactionDate { get; set; }
        public bool UserPostToGl { get; set; }
        public bool UserEditPostedBills { get; set; }
        public bool EditInClosedPeriod { get; set; }
        public int AccSecurityLevel { get; set; }
        public int? NotesReceivableAlertDays { get; set; }
        public int? NotesPayableAlertDays { get; set; }
        public bool UserEditNotesReceivableRecipientEmp { get; set; }
        public bool UserRegisterKnownScale { get; set; }
        public bool ExportReport { get; set; }
        public bool AutoShowPrPriceChange { get; set; }
        public int AlarmWarrantyItemsDays { get; set; }
        public int AlarmExpiredItemsDays { get; set; }
        public DateTime? ShiftReplaceFromDate { get; set; }
        public DateTime? ShiftReplaceToDate { get; set; }
        public bool UserCanEditQty { get; set; }
        public int AlarmItemsReorder { get; set; }
        public int AlarmItemsMinLimit { get; set; }
        public bool? ShowLastSellPrices { get; set; }
        public bool? ShowLastPurchasePrices { get; set; }
        public bool? CanApproveSalesOrders { get; set; }
        public bool CanSaveSlWithOldDate { get; set; }
        public bool PostJournalToGlbyDefault { get; set; }
        public int? DefaultNotesReceiver { get; set; }
        public bool CanSaveSlWithUpcomingDate { get; set; }
        public bool? CanApproveSlInvoices { get; set; }
        public bool? UserShowBankOrDrawerBalanceInPayReceive { get; set; }
        public int? DefaultVisa { get; set; }
        public int? MaxUserId { get; set; }
        public decimal? MaxSalesValue { get; set; }
        public bool? PrHidePurchasePrice { get; set; }
        public int? DueSlInvoicesAlertDays { get; set; }
        public bool? CanApproveInternalRequest { get; set; }
        public bool? CanApprovePrQuote { get; set; }
        public bool? CanApprovePrOrder { get; set; }
        public bool? JoAlert { get; set; }
        public int? JoAlertDays { get; set; }
        public bool? HrEmployeeSalary { get; set; }
        public bool? UserCanAccessToJournalFromSameForm { get; set; }
        public bool? ShowManufactureExpenses { get; set; }
        public int? CrncId { get; set; }
    }
}
