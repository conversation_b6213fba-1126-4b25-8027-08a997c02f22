﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models_1.ViewModels.DatabaseVM
{
    public class IcStoreVM
    {
        public int StoreId { get; set; }
        public string StoreCode { get; set; }
        public string StoreNameAr { get; set; }
        public string StoreNameEn { get; set; }
        public string Address { get; set; }
        public string Tel { get; set; }
        public int PurchaseAccount { get; set; }
        public int PurchaseReturnAccount { get; set; }
        public int SellAccount { get; set; }
        public int SellReturnAccount { get; set; }
        public int OpenInventoryAccount { get; set; }
        public int CloseInventoryAccount { get; set; }
        public byte CostMethod { get; set; }
        public string ManagerName { get; set; }
        public int? ParentId { get; set; }
        public int? CostOfSoldGoodsAcc { get; set; }
        public int? SalesDiscountAcc { get; set; }
        public int? PurchaseDiscountAcc { get; set; }
        public int? CostCenter { get; set; }
        public int? PricelistId { get; set; }
        public string Mobile { get; set; }
        public bool? IsStopped { get; set; }
        public string Ecode { get; set; }
        public string Governate { get; set; }
        public string RegionCity { get; set; }
        public string Street { get; set; }
        public string BuildingNumber { get; set; }
        public int CountryId { get; set; }

        public string CountryCode { get; set; }

    }
}
