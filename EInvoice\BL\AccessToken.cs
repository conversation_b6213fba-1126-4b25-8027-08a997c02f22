﻿using EInvoice.Models;
using EInvoice.ViewModels;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Utilities;

namespace EInvoice.BL
{
    public static class AccessToken
    {
        private static TokenVM _accessToken;

        public static string GetAccessToken(ERPEinvoiceContext DB)
        {
            if ((_accessToken?.ExpiresDate??DateTime.UtcNow.AddMinutes(-1)) < DateTime.UtcNow)
            {
                var companyData = DB.StCompanyInfo.FirstOrDefault();
                _accessToken = FetchToken(companyData.ClientId, companyData.ClientSecret);
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Token Expired", 1, 1);
                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"New Token Generated - Generated Token : " + _accessToken.access_token, 1, 1);
                return _accessToken.access_token;
            }
            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Token Not Expired", 1, 1);
            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Generated Token : " + _accessToken.access_token, 1, 1);
            return _accessToken.access_token;
        }

        public static TokenVM FetchToken(string clientIdDec , string clientSecretDec)
        {
            var currentDateTime = DateTime.UtcNow;
            var clientId = Crypto.DecryptStringAES(clientIdDec, Crypto.Key);
            var clientSecret = Crypto.DecryptStringAES(clientSecretDec, Crypto.Key);
            HttpClient client = new HttpClient();
            var RequestBody = new Dictionary<string, string>();
            RequestBody.Add("grant_type", "client_credentials");
            RequestBody.Add("client_id", clientId);
            RequestBody.Add("client_secret", clientSecret);
            RequestBody.Add("scope", "InvoicingAPI");

            var content = new FormUrlEncodedContent(RequestBody);

            var Configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json").Build();
            var LoginUrl = Configuration.GetSection("LoginUrl").Value;
            var tokenResponse = client.PostAsync(LoginUrl, content).Result;

            var contents = tokenResponse.Content.ReadAsStringAsync().Result;
            var token = JsonConvert.DeserializeObject<TokenVM>(contents);
            token.ExpiresDate = currentDateTime.AddSeconds(token.expires_in);
            return token;
        }
    }
}
