﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class SlAdd
    {
        public SlAdd()
        {
            SlAddDetail = new HashSet<SlAddDetail>();
        }

        public int SlAddId { get; set; }
        public string ReturnCode { get; set; }
        public int StoreId { get; set; }
        public int CustomerId { get; set; }
        public DateTime ReturnDate { get; set; }
        public string Notes { get; set; }
        public decimal Expenses { get; set; }
        public decimal DiscountRatio { get; set; }
        public decimal DiscountValue { get; set; }
        public decimal Net { get; set; }
        public decimal Paid { get; set; }
        public decimal Remains { get; set; }
        public bool? PayMethod { get; set; }
        public int UserId { get; set; }
        public int JornalId { get; set; }
        public int? DrawerAccountId { get; set; }
        public decimal TaxValue { get; set; }
        public int? PayAccountId2 { get; set; }
        public decimal? PayAcc2Paid { get; set; }
        public bool? IsInTrans { get; set; }
        public decimal DeductTaxRatio { get; set; }
        public decimal DeductTaxValue { get; set; }
        public int? InvoiceBookId { get; set; }
        public decimal AddTaxRatio { get; set; }
        public decimal AddTaxValue { get; set; }
        public int? SalesEmpId { get; set; }
        public int CrncId { get; set; }
        public decimal CrncRate { get; set; }
        public int? CostCenterId { get; set; }
        public string DriverName { get; set; }
        public string VehicleNumber { get; set; }
        public string Destination { get; set; }
        public int? ProcessId { get; set; }
        public int? SourceId { get; set; }
        public int? LastUpdateUserId { get; set; }
        public DateTime? LastUpdateDate { get; set; }
        public string ScaleWeightSerial { get; set; }
        public decimal CustomTaxValue { get; set; }
        public decimal? HandingValue { get; set; }
        public string Uuid { get; set; }
        public string IssuerId { get; set; }
        public DateTime? LastSyncDate { get; set; }
        public DateTime? SyncDate { get; set; }
        public string Estatus { get; set; }
        public byte? EstatusCode { get; set; }
        public string LongId { get; set; }

        public virtual ICollection<SlAddDetail> SlAddDetail { get; set; }
    }
}
