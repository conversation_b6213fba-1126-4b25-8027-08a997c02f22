﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="txtFrom" xml:space="preserve">
    <value> من </value>
  </data>
  <data name="txtFromDate" xml:space="preserve">
    <value> من تاريخ </value>
  </data>
  <data name="MsgNameExist" xml:space="preserve">
    <value>هذا الاسم مسجل من قبل</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>تأكد من صحة البيانات</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>معلومة</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية تعديل هذا البيان</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية اضافة بيان جديد</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>سؤال</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>تم الحفظ بنجاح</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="txtTo" xml:space="preserve">
    <value> الى </value>
  </data>
  <data name="txtToDate" xml:space="preserve">
    <value> الى تاريخ </value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>تم الحذف بنجاح</value>
  </data>
  <data name="txtOpenBalance" xml:space="preserve">
    <value> رصيد افتتاحي </value>
  </data>
  <data name="txtDrawer" xml:space="preserve">
    <value> خزينه </value>
  </data>
  <data name="txtCredit" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="txtCustomer" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="txtDebit" xml:space="preserve">
    <value>مدين</value>
  </data>
  <data name="txtVendor" xml:space="preserve">
    <value> المورد: </value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>حذف صف ؟</value>
  </data>
  <data name="MsgNumExist" xml:space="preserve">
    <value>هذا الرقم مسجل من قبل</value>
  </data>
  <data name="MsgAskDel" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد حذف</value>
  </data>
  <data name="txtDealerNm" xml:space="preserve">
    <value> اسم المتعامل</value>
  </data>
  <data name="txtItemsBalance" xml:space="preserve">
    <value>أرصدة الأصناف</value>
  </data>
  <data name="txtItemsBestSell" xml:space="preserve">
    <value>الاصناف الاكثر مبيعا</value>
  </data>
  <data name="txtItemsLeastSell" xml:space="preserve">
    <value>الاصناف الاقل مبيعا</value>
  </data>
  <data name="txtItemsReorder" xml:space="preserve">
    <value>أصناف وصلت لحد الطلب</value>
  </data>
  <data name="txtAssembly" xml:space="preserve">
    <value>تام</value>
  </data>
  <data name="txtCategory" xml:space="preserve">
    <value> الفئه: </value>
  </data>
  <data name="txtComapny" xml:space="preserve">
    <value>الشركة: </value>
  </data>
  <data name="txtItem" xml:space="preserve">
    <value>الصنف:</value>
  </data>
  <data name="txtItemMovement" xml:space="preserve">
    <value>حركة الاصناف</value>
  </data>
  <data name="txtItemTotalPurchases" xml:space="preserve">
    <value>اجمالي مشتريات صنف/أصناف</value>
  </data>
  <data name="txtItemTotalPurchasesReturns" xml:space="preserve">
    <value>اجمالي مردود مشتريات صنف/أصناف</value>
  </data>
  <data name="txtItemTotalSales" xml:space="preserve">
    <value>اجمالي مبيعات صنف/أصناف</value>
  </data>
  <data name="txtItemTotalSalesReturn" xml:space="preserve">
    <value>اجمالي مردود مبيعات صنف/أصناف</value>
  </data>
  <data name="txtStore" xml:space="preserve">
    <value>المخزن:</value>
  </data>
  <data name="MsgNoAccount" xml:space="preserve">
    <value>لايوجد حساب لهذا المورد</value>
  </data>
  <data name="MsgChkCodeDuplication" xml:space="preserve">
    <value>كود 1 موجود مسبقا</value>
  </data>
  <data name="MsgAskToSaveInv" xml:space="preserve">
    <value>برجاء حفظ الفاتورة اولا</value>
  </data>
  <data name="MsgDeleteInv" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد حذف المستند</value>
  </data>
  <data name="txtContinue" xml:space="preserve">
    <value>متابعة ?</value>
  </data>
  <data name="txtDiscRatio" xml:space="preserve">
    <value>خ ن :</value>
  </data>
  <data name="txtDiscValue" xml:space="preserve">
    <value>خ ق :</value>
  </data>
  <data name="txtExpenses" xml:space="preserve">
    <value>مصاريف :</value>
  </data>
  <data name="txtInvDate" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="txtInvNumber" xml:space="preserve">
    <value>رقم الفاتورة</value>
  </data>
  <data name="txtInvPayMethod" xml:space="preserve">
    <value>نوع السداد</value>
  </data>
  <data name="txtLastPPrices" xml:space="preserve">
    <value>اخر اسعار شراء</value>
  </data>
  <data name="txtLastVendorPPrices" xml:space="preserve">
    <value>اخر اسعار شراء من مورد</value>
  </data>
  <data name="txtNet" xml:space="preserve">
    <value>صافي :</value>
  </data>
  <data name="txtNotes" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="txtPaid" xml:space="preserve">
    <value>مدفوع :</value>
  </data>
  <data name="txtPRInvoiceNumber" xml:space="preserve">
    <value>فاتورة مشتريات رقم</value>
  </data>
  <data name="txtRemains" xml:space="preserve">
    <value>متبقي :</value>
  </data>
  <data name="txtSerial" xml:space="preserve">
    <value>تسلسل</value>
  </data>
  <data name="txtTotal" xml:space="preserve">
    <value>الاجمــالي :</value>
  </data>
  <data name="txtValidateDiscount" xml:space="preserve">
    <value>يجب تحديد الخصم</value>
  </data>
  <data name="txtValidateInvNumber" xml:space="preserve">
    <value>يجب تسجيل رقم الفاتوره</value>
  </data>
  <data name="txtValidateItem" xml:space="preserve">
    <value>يجب اختيار الصنف</value>
  </data>
  <data name="txtValidateItemMaxLimit" xml:space="preserve">
    <value>الكمية المشتراه و الكمية الموجودة حاليا في المخزن أكبر من الحد الأقصى للكمية</value>
  </data>
  <data name="txtValidateMaxDiscount" xml:space="preserve">
    <value>نسبة الخصم لايمكن ان تتجاوز المائة</value>
  </data>
  <data name="txtValidateNoRows" xml:space="preserve">
    <value>يجب تسجيل صنف علي الاقل في الفاتوره</value>
  </data>
  <data name="txtValidatePPrice" xml:space="preserve">
    <value>سعر الشراء يجب أن يكون أكبر من الصفر</value>
  </data>
  <data name="txtValidateQty" xml:space="preserve">
    <value>الكمية يجب ان تكون اكبر من الصفر</value>
  </data>
  <data name="txtValidateUom" xml:space="preserve">
    <value>يجب اختيار وحدة القياس</value>
  </data>
  <data name="txtValidateVendorMaxCredit" xml:space="preserve">
    <value>المورد تخطى حد الائتمان كدائن</value>
  </data>
  <data name="MsgNoEnoughQty_continue" xml:space="preserve">
    <value>لايوجد كميه كافيه من أحد الاصناف, هل تريد الاستمرار  ؟</value>
  </data>
  <data name="txtPRReturnNumber" xml:space="preserve">
    <value>فاتورة مردود مشتريات رقم</value>
  </data>
  <data name="MsgAskConfirmDeleteVen" xml:space="preserve">
    <value>هل انت متأكد انك تريد حذف هذ المورد ?</value>
  </data>
  <data name="MsgDeleteJornalsFirst" xml:space="preserve">
    <value>يجب حذف القيود الخاصه بهذا المورد أولا</value>
  </data>
  <data name="MsgEnterVendorCode" xml:space="preserve">
    <value>يرجى إدخال كود المورد</value>
  </data>
  <data name="MsgEnterVendorName" xml:space="preserve">
    <value>يرجى إدخال اسم المورد</value>
  </data>
  <data name="MsgValidateVendorCode" xml:space="preserve">
    <value>كود المورد لايمكن أن يساوي صفر</value>
  </data>
  <data name="txtCantDeleteVen" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف المورد العام</value>
  </data>
  <data name="txtCantEditVen" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد تعديل المورد العام</value>
  </data>
  <data name="txt_Discount" xml:space="preserve">
    <value>خصم</value>
  </data>
  <data name="txt_Paid" xml:space="preserve">
    <value>سداد</value>
  </data>
  <data name="txt_Tax" xml:space="preserve">
    <value>ضريبة</value>
  </data>
  <data name="txt_PurchaseExpenses" xml:space="preserve">
    <value>مصاريف مشتريات</value>
  </data>
  <data name="accDetail" xml:space="preserve">
    <value>كشف حساب تفصيلي</value>
  </data>
  <data name="txtTaxRatio" xml:space="preserve">
    <value> ض ع ن:</value>
  </data>
  <data name="txtTaxValue" xml:space="preserve">
    <value>ض ع ق:</value>
  </data>
  <data name="txtValidatePostDate" xml:space="preserve">
    <value>من فضلك ادخل تاريخ تسليم المخزن</value>
  </data>
  <data name="MsgAskChangePList" xml:space="preserve">
    <value>هل تريد تحديث قوائم أسعار العملاء أيضا بنفس نسبة زيادة الأســعار ؟</value>
  </data>
  <data name="msgLinkAcc" xml:space="preserve">
    <value>عفوا، لايمكنك تغيير هذا الحساب، عليك حذف القيود الخاصة به أولا</value>
  </data>
  <data name="msgDelLinked" xml:space="preserve">
    <value>عفوا، لايمكنك حذف هذا الحساب، يوجد حسابات اخرى مرتبطة به</value>
  </data>
  <data name="msgLinkAccOpen" xml:space="preserve">
    <value>عفوا، لايمكن انشاء رصيد افتتاحي لهذا الحساب</value>
  </data>
  <data name="MsgAskPrintBarCode" xml:space="preserve">
    <value>هل تريد طباعة ملصقات باركود لفاتورة المشتريات</value>
  </data>
  <data name="MsgAskCreateInTrns" xml:space="preserve">
    <value>هل تريد انشاء اذن اضافة للمخزن الان؟</value>
  </data>
  <data name="MsgAskCreateOutTrns" xml:space="preserve">
    <value>هل تريد انشاء اذن صرف من المخزن الان ؟</value>
  </data>
  <data name="defaultCustGroupAcc" xml:space="preserve">
    <value>الوضع الإفتراضي لحسابات موردين هذه الفئة</value>
  </data>
  <data name="MsgDelCustGroup" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف فئة الموردين هذه</value>
  </data>
  <data name="MsgDelCustGroup2" xml:space="preserve">
    <value>عفوا، لايمكن حذف فئة الموردين، يوجد موردين مرتبطين بها</value>
  </data>
  <data name="ValVendorCodeLength" xml:space="preserve">
    <value>كود المورد لايمكن أن يكون اكبر من المحدد في نموذج طباعة الباركود</value>
  </data>
  <data name="ValBranch" xml:space="preserve">
    <value>يجب اختيار الفرع</value>
  </data>
  <data name="msgMustApprovePrOrder" xml:space="preserve">
    <value>لم يتم اعتماد أمر الشراء بعد</value>
  </data>
  <data name="msgMustApprovePrQuote" xml:space="preserve">
    <value>لم يتم اعتماد عرض سعر الشراء بعد</value>
  </data>
  <data name="FineNumber" xml:space="preserve">
    <value>غرامة رقم</value>
  </data>
  <data name="preformaType" xml:space="preserve">
    <value>غير مسموح بتغير نوع البروفورمة</value>
  </data>
  <data name="PreInvoicesAdvanced" xml:space="preserve">
    <value />
  </data>
</root>