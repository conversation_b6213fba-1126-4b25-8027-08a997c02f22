﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class HrEmployee
    {
        public int EmpId { get; set; }
        public string EmpName { get; set; }
        public DateTime? EmpBirthDate { get; set; }
        public string EmpAddress { get; set; }
        public bool EmpGender { get; set; }
        public string NationalId { get; set; }
        public byte? MaritalStatus { get; set; }
        public byte? MilitaryStatus { get; set; }
        public string Education { get; set; }
        public string PassportNo { get; set; }
        public string Email { get; set; }
        public string Tel { get; set; }
        public byte[] Photo { get; set; }
        public byte PayPeriod { get; set; }
        public decimal SalaryBasic { get; set; }
        public decimal SalaryVariable { get; set; }
        public decimal Hour { get; set; }
        public decimal Day { get; set; }
        public decimal AbsensePenaltyDay { get; set; }
        public decimal AbsensePenaltyValue { get; set; }
        public int JobId { get; set; }
        public int DeptId { get; set; }
        public int? GroupId { get; set; }
        public DateTime? AppointmentDate { get; set; }
        public bool EmpState { get; set; }
        public bool? ContractType { get; set; }
        public string ContractPeriod { get; set; }
        public int? AccountId { get; set; }
        public decimal CashInvCommission { get; set; }
        public string EmpFname { get; set; }
        public byte CommissionType { get; set; }
        public int? AccruedAccount { get; set; }
        public bool DeliveryRep { get; set; }
        public bool SalesRep { get; set; }
        public string EmpCode { get; set; }
        public decimal OnCreditInvCommision { get; set; }
        public decimal DiscountInvCommision { get; set; }
        public int? ExpensesAccount { get; set; }
        public int EnrollNumber { get; set; }
        public int BirthCountryId { get; set; }
        public int NathionalityId { get; set; }
        public int ReligionId { get; set; }
        public int QualificationId { get; set; }
        public DateTime? IdCardEndDate { get; set; }
        public string ResidenceNo { get; set; }
        public DateTime? ResidenceStartDate { get; set; }
        public DateTime? ResidenceEndDate { get; set; }
        public string ResidenceAccompany { get; set; }
        public int? PassportCountryId { get; set; }
        public DateTime? PassportStartDate { get; set; }
        public DateTime? PassportEndDate { get; set; }
        public int? SponsorId { get; set; }
        public string InsuranceNo { get; set; }
        public string DrivingLicense { get; set; }
        public int? Shift1Id { get; set; }
        public int? Shift2Id { get; set; }
        public int? DelayRuleId { get; set; }
        public int? OverTimeRuleId { get; set; }
        public string BankName { get; set; }
        public string BankAccountNum { get; set; }
        public decimal AbsenseNoPermPenaltyDay { get; set; }
        public decimal AbsenseNoPermPenaltyValue { get; set; }
        public bool CalcIncomeTax { get; set; }
        public DateTime? InsuranceDate { get; set; }
        public DateTime? ContractEndDate { get; set; }
        public DateTime? DrivingEndDate { get; set; }
        public decimal OfferInvCommision { get; set; }
        public decimal Target { get; set; }
        public decimal SalesR { get; set; }
        public decimal CollectionR { get; set; }
        public decimal SalesMinimumR { get; set; }
        public decimal SalesMinimumV { get; set; }
        public decimal CollectionMinimumR { get; set; }
        public decimal CollectionMinimumV { get; set; }
        public int? InOutRuleId { get; set; }
        public int? Shift3Id { get; set; }
        public string Tel2 { get; set; }
        public string WorkTel { get; set; }
        public string LandLine { get; set; }
        public string RelName { get; set; }
        public string Relation { get; set; }
        public string RelTel { get; set; }
        public string Children { get; set; }
        public string HomeAddress { get; set; }
        public int? WorkEntityId { get; set; }
        public DateTime? QualYear { get; set; }
        public int? BranchId { get; set; }
        public bool? IsInsurance { get; set; }
        public bool? IsNetTax { get; set; }
        public bool? Absencebenefit { get; set; }
        public int? InsuranceAcc { get; set; }
        public int? HrSubtractAcc { get; set; }
        public bool? HasIncentive { get; set; }
        public int? CostCenterId { get; set; }
        public int? BankId { get; set; }
    }
}
