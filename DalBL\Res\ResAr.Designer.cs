﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResAr {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResAr() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResAr", typeof(ResAr).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا, غير مصرح لك فتح البرنامج, يرجي مراجعة مدير النظام.
        /// </summary>
        public static string AccessDenied {
            get {
                return ResourceManager.GetString("AccessDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم انشاء الحساب بواسطة النظام.
        /// </summary>
        public static string accountCreationBy {
            get {
                return ResourceManager.GetString("accountCreationBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد الخروج من النظام ؟.
        /// </summary>
        public static string AppClose {
            get {
                return ResourceManager.GetString("AppClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لينكيت لتخطيط موارد الشركات.
        /// </summary>
        public static string AppName {
            get {
                return ResourceManager.GetString("AppName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لينكيت لنقاط البيع.
        /// </summary>
        public static string AppPOSName {
            get {
                return ResourceManager.GetString("AppPOSName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نهاية المده.
        /// </summary>
        public static string CloseInventory {
            get {
                return ResourceManager.GetString("CloseInventory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عميل.
        /// </summary>
        public static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رصيد افتتاحي عميل.
        /// </summary>
        public static string custOpen {
            get {
                return ResourceManager.GetString("custOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to باليوم.
        /// </summary>
        public static string Daily {
            get {
                return ResourceManager.GetString("Daily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف الإذن، هذه الفواتير مرتبطة به.
        /// </summary>
        public static string DelBillDenied {
            get {
                return ResourceManager.GetString("DelBillDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف قائمة الخامات، فهي مستخدمة بالفعل في التشغيلات.
        /// </summary>
        public static string DelBomDenied {
            get {
                return ResourceManager.GetString("DelBomDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف هذا البند فهو مستخدم بالفعل.
        /// </summary>
        public static string DelEntryDenied {
            get {
                return ResourceManager.GetString("DelEntryDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف الشركة، يوجد استمارات بيع غير مباشر لهذه الشركة.
        /// </summary>
        public static string DelIndirectComp {
            get {
                return ResourceManager.GetString("DelIndirectComp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف الفاتورة، هذه الأذون مرتبطة بها.
        /// </summary>
        public static string DelInvoiceDenied {
            get {
                return ResourceManager.GetString("DelInvoiceDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف أمر العمل، يوجد فواتير بيع مرتبطة به.
        /// </summary>
        public static string DelJO {
            get {
                return ResourceManager.GetString("DelJO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد انك تريد حذف هذا الإعتماد المستندي.
        /// </summary>
        public static string DelLC {
            get {
                return ResourceManager.GetString("DelLC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف الاعتماد المستندي فهو مستخدم بفاتورة مشتريات رقم .
        /// </summary>
        public static string DelLCDenied {
            get {
                return ResourceManager.GetString("DelLCDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف الاعتماد المستندي يوجد قيود على الحساب الخاص به.
        /// </summary>
        public static string DelLCDenied2 {
            get {
                return ResourceManager.GetString("DelLCDenied2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن ازالة الصنف، فهو مازال مخصص لأحد فئات العملاء.
        /// </summary>
        public static string DelMrItem {
            get {
                return ResourceManager.GetString("DelMrItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إزالة صنف؟.
        /// </summary>
        public static string DelMrItemWarning {
            get {
                return ResourceManager.GetString("DelMrItemWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف امر التوريد، هذه الأذون مرتبطة به.
        /// </summary>
        public static string DelOrderDeniedIC {
            get {
                return ResourceManager.GetString("DelOrderDeniedIC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف امر التوريد، هذه الفواتير مرتبطة به.
        /// </summary>
        public static string DelOrderDeniedInv {
            get {
                return ResourceManager.GetString("DelOrderDeniedInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الغاء اختيار الكل.
        /// </summary>
        public static string DeselectAll {
            get {
                return ResourceManager.GetString("DeselectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن تعديل الفاتورة بعد طباعة ملصقات الباركود.
        /// </summary>
        public static string DetailIdBrcodPrntd {
            get {
                return ResourceManager.GetString("DetailIdBrcodPrntd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف الفاتورة سيؤدي لتلف ملصقات الباركود المطبوعة لها، هل تريد الإستمرار.
        /// </summary>
        public static string DetailIdBrcodPrntdDel {
            get {
                return ResourceManager.GetString("DetailIdBrcodPrntdDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكنك تعديل بيانات في فترة مالية مغلقة.
        /// </summary>
        public static string EditInClosedPeriodDenie {
            get {
                return ResourceManager.GetString("EditInClosedPeriodDenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to انت تقوم بتعديلات في فترة مالية مغلقة، هل تريد الاستمرار.
        /// </summary>
        public static string EditInClosedPeriodWarning {
            get {
                return ResourceManager.GetString("EditInClosedPeriodWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to موظف.
        /// </summary>
        public static string Employee {
            get {
                return ResourceManager.GetString("Employee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مصروفات.
        /// </summary>
        public static string Expenses {
            get {
                return ResourceManager.GetString("Expenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كل اسبوعين.
        /// </summary>
        public static string Fortnightly {
            get {
                return ResourceManager.GetString("Fortnightly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الجمعه.
        /// </summary>
        public static string Fri {
            get {
                return ResourceManager.GetString("Fri", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بالساعه.
        /// </summary>
        public static string Hourly {
            get {
                return ResourceManager.GetString("Hourly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى تحديد حساب المخزون الوسيط من الإعدادات.
        /// </summary>
        public static string InermediateAccount {
            get {
                return ResourceManager.GetString("InermediateAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف الدفتر، فقد تم استخدامه بالفعل.
        /// </summary>
        public static string InvBookUsedDel {
            get {
                return ResourceManager.GetString("InvBookUsedDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الفاتورة غير موجودة، سيتم إنشاء فاتورة جديدة.
        /// </summary>
        public static string invDeleted {
            get {
                return ResourceManager.GetString("invDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل حسابات الترحيل لفئات الأصناف بشكل كامل.
        /// </summary>
        public static string ItemPosting {
            get {
                return ResourceManager.GetString("ItemPosting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواَ,  تاريخ الحفظ يتخطى أقصى موعد يمكن ترحيله.
        /// </summary>
        public static string LastEvaluationDateError {
            get {
                return ResourceManager.GetString("LastEvaluationDateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقييم كل العملات بأخر معامل.
        /// </summary>
        public static string LastRate {
            get {
                return ResourceManager.GetString("LastRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تحديد الحساب الرئيسي للإعتمادات المستندية من شاشة الإعدادات.
        /// </summary>
        public static string LCSettingAcc {
            get {
                return ResourceManager.GetString("LCSettingAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن الربط بحساب رئيسي.
        /// </summary>
        public static string linkAccountParent {
            get {
                return ResourceManager.GetString("linkAccountParent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تشغيلة رقم : .
        /// </summary>
        public static string ManfNo {
            get {
                return ResourceManager.GetString("ManfNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to غلق الكل.
        /// </summary>
        public static string mi_CloseAll {
            get {
                return ResourceManager.GetString("mi_CloseAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اخفاء الاحصائيات.
        /// </summary>
        public static string mi_HideCharts {
            get {
                return ResourceManager.GetString("mi_HideCharts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تسجيل مصروفات.
        /// </summary>
        public static string mi_NewExpenses {
            get {
                return ResourceManager.GetString("mi_NewExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند دفع نقدي.
        /// </summary>
        public static string mi_NewPayNote {
            get {
                return ResourceManager.GetString("mi_NewPayNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند قبض نقدي.
        /// </summary>
        public static string mi_NewReceiveNote {
            get {
                return ResourceManager.GetString("mi_NewReceiveNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تسجيل ايرادات.
        /// </summary>
        public static string mi_NewRevenue {
            get {
                return ResourceManager.GetString("mi_NewRevenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عرض الكل.
        /// </summary>
        public static string mi_ShowAll {
            get {
                return ResourceManager.GetString("mi_ShowAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عرض الاحصائيات.
        /// </summary>
        public static string mi_ShowCharts {
            get {
                return ResourceManager.GetString("mi_ShowCharts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاتنين.
        /// </summary>
        public static string Mon {
            get {
                return ResourceManager.GetString("Mon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بالشهر.
        /// </summary>
        public static string Monthly {
            get {
                return ResourceManager.GetString("Monthly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد انك تريد حذف هذا الأصل الثابت.
        /// </summary>
        public static string MsgAskDelFA {
            get {
                return ResourceManager.GetString("MsgAskDelFA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد انك تريد حذف هذا الأصل الدفتر.
        /// </summary>
        public static string MsgAskDelInvBook {
            get {
                return ResourceManager.GetString("MsgAskDelInvBook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد انك تريد حذف البيانات المختاره.
        /// </summary>
        public static string MsgAskDelRows {
            get {
                return ResourceManager.GetString("MsgAskDelRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد من أنك تريد ترحيل فواتير البيع و اقفال اليومية ؟.
        /// </summary>
        public static string MsgAskPost {
            get {
                return ResourceManager.GetString("MsgAskPost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد من أنك تريد ترحيل هذه القيود المالية ؟.
        /// </summary>
        public static string MsgAskPostJournals {
            get {
                return ResourceManager.GetString("MsgAskPostJournals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حفظ المستند أولا.
        /// </summary>
        public static string MsgAskToSave {
            get {
                return ResourceManager.GetString("MsgAskToSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد من أنك تريد إالغاء ترحيل هذه القيود المالية ؟.
        /// </summary>
        public static string MsgAskUnPostJournals {
            get {
                return ResourceManager.GetString("MsgAskUnPostJournals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا العميل محظور.
        /// </summary>
        public static string MsgBlockedCustomer {
            get {
                return ResourceManager.GetString("MsgBlockedCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب رأس المال من شاشة الإعدادات.
        /// </summary>
        public static string MsgCapitalAcc {
            get {
                return ResourceManager.GetString("MsgCapitalAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد الإستمرار ؟.
        /// </summary>
        public static string MsgContinue {
            get {
                return ResourceManager.GetString("MsgContinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب ضريبة الجدول من شاشة الإعدادات.
        /// </summary>
        public static string MsgCusTaxAcc {
            get {
                return ResourceManager.GetString("MsgCusTaxAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب العملاء الرئيسي من شاشة الإعدادات.
        /// </summary>
        public static string MsgCustomersAcc {
            get {
                return ResourceManager.GetString("MsgCustomersAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحذف بنجاح.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد حذف المصاريف.
        /// </summary>
        public static string MsgDelExpenses {
            get {
                return ResourceManager.GetString("MsgDelExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف أمر الإنتاج؟.
        /// </summary>
        public static string MsgDelManfOrder {
            get {
                return ResourceManager.GetString("MsgDelManfOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف صف ؟.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب الخصم من شاشة الإعدادات.
        /// </summary>
        public static string MsgDiscountAcc {
            get {
                return ResourceManager.GetString("MsgDiscountAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب إدخال الكود.
        /// </summary>
        public static string MsgEnterCode {
            get {
                return ResourceManager.GetString("MsgEnterCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب إختيار حساب مصروفات الإهلاك.
        /// </summary>
        public static string MsgFaAccRequired {
            get {
                return ResourceManager.GetString("MsgFaAccRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب إدخال حساب مجمع الإهلاك.
        /// </summary>
        public static string MsgFaDeprAccRequired {
            get {
                return ResourceManager.GetString("MsgFaDeprAccRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الملف غير موجود.
        /// </summary>
        public static string MsgFileNotExist {
            get {
                return ResourceManager.GetString("MsgFileNotExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب الأصول الثابتة من شاشة الإعدادات.
        /// </summary>
        public static string MsgFixedAsetsAcc {
            get {
                return ResourceManager.GetString("MsgFixedAsetsAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من صحة البيانات.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم ترحيل القيود بنجاح.
        /// </summary>
        public static string MsgJournalPostedSuccessfully {
            get {
                return ResourceManager.GetString("MsgJournalPostedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الغاء ترحيل القيود بنجاح.
        /// </summary>
        public static string MsgJournalUnPostedSuccessfully {
            get {
                return ResourceManager.GetString("MsgJournalUnPostedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم انهاء التشغيل بنجاح.
        /// </summary>
        public static string Msgmanfend {
            get {
                return ResourceManager.GetString("Msgmanfend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب مصروفات التشغيل من شاشة الإعدادات.
        /// </summary>
        public static string MsgManufacturingExpAcc {
            get {
                return ResourceManager.GetString("MsgManufacturingExpAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء إدخال الإسم.
        /// </summary>
        public static string MsgNameRequired {
            get {
                return ResourceManager.GetString("MsgNameRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا ، لا يمكنك تعديل او حذف بيان تم ترحيله.
        /// </summary>
        public static string MsgPostedBill {
            get {
                return ResourceManager.GetString("MsgPostedBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ لم يتم ترحيل الفواتير .
        /// </summary>
        public static string MsgPostedFailed {
            get {
                return ResourceManager.GetString("MsgPostedFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم ترحيل فواتير البيع بنجاح.
        /// </summary>
        public static string MsgPostedSuccessfully {
            get {
                return ResourceManager.GetString("MsgPostedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية تعديل هذا البيان.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية اضافة بيان جديد.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب ضريبة المبيعات من شاشة الإعدادات.
        /// </summary>
        public static string MsgSalesTaxAcc {
            get {
                return ResourceManager.GetString("MsgSalesTaxAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحفظ بنجاح.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الشركة.
        /// </summary>
        public static string MsgSelectComp {
            get {
                return ResourceManager.GetString("MsgSelectComp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار فئة العميل.
        /// </summary>
        public static string MsgSelectCustGrp {
            get {
                return ResourceManager.GetString("MsgSelectCustGrp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء إختيار عميل.
        /// </summary>
        public static string MsgSelectCustomer {
            get {
                return ResourceManager.GetString("MsgSelectCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء إختيار مورد.
        /// </summary>
        public static string MsgSelectVendor {
            get {
                return ResourceManager.GetString("MsgSelectVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to معلومة.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سؤال.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تحذير.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من ادخال رقم التشغيلة.
        /// </summary>
        public static string MsgValidatemanfnumber {
            get {
                return ResourceManager.GetString("MsgValidatemanfnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من اختيار وحدة قياس المنتج.
        /// </summary>
        public static string MsgValidatemanfprodUOM {
            get {
                return ResourceManager.GetString("MsgValidatemanfprodUOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من ادخال تاريخ بدء التشغيل.
        /// </summary>
        public static string MsgValidatemanfstartdate {
            get {
                return ResourceManager.GetString("MsgValidatemanfstartdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من ادخال مخزن التشغيل.
        /// </summary>
        public static string MsgValidatemanfstore {
            get {
                return ResourceManager.GetString("MsgValidatemanfstore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من اختيار المنتج المشغل.
        /// </summary>
        public static string MsgValidatePitem {
            get {
                return ResourceManager.GetString("MsgValidatePitem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب الموردون الرئيسي من شاشة الإعدادات.
        /// </summary>
        public static string MsgVendorsAcc {
            get {
                return ResourceManager.GetString("MsgVendorsAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يوجد استهلاكات فعليه للتشغيلة، هل تريد الاستمرار ؟.
        /// </summary>
        public static string MsgWarnningSelectManfActualRaws {
            get {
                return ResourceManager.GetString("MsgWarnningSelectManfActualRaws", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سيتم حذف التشغيلة و كل القيود و محتوياتها في المخازن؟.
        /// </summary>
        public static string MsgWdeletemanf {
            get {
                return ResourceManager.GetString("MsgWdeletemanf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سيتم تسجيل انهاء التشغيل و حفظ المنتج المشغل في المخزن متابعة؟.
        /// </summary>
        public static string MsgWsavemanf {
            get {
                return ResourceManager.GetString("MsgWsavemanf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to صافي المبيعات.
        /// </summary>
        public static string NetSales {
            get {
                return ResourceManager.GetString("NetSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا تغيير.
        /// </summary>
        public static string NoChange {
            get {
                return ResourceManager.GetString("NoChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد تاريخ الرصيد الإفتتاحي.
        /// </summary>
        public static string openBlncDate {
            get {
                return ResourceManager.GetString("openBlncDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اول المده.
        /// </summary>
        public static string OpenInventory {
            get {
                return ResourceManager.GetString("OpenInventory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بالقطعه.
        /// </summary>
        public static string Pertask {
            get {
                return ResourceManager.GetString("Pertask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خصم مكتسب.
        /// </summary>
        public static string PurchaseDiscount {
            get {
                return ResourceManager.GetString("PurchaseDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مشتريات.
        /// </summary>
        public static string Purchases {
            get {
                return ResourceManager.GetString("Purchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مردود مشتريات.
        /// </summary>
        public static string PurchasesReturn {
            get {
                return ResourceManager.GetString("PurchasesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ايرادات.
        /// </summary>
        public static string Revenue {
            get {
                return ResourceManager.GetString("Revenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبيعات.
        /// </summary>
        public static string Sales {
            get {
                return ResourceManager.GetString("Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تكلفة البضاعة المباعة.
        /// </summary>
        public static string SalesCost {
            get {
                return ResourceManager.GetString("SalesCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خصم مسموح به.
        /// </summary>
        public static string SalesDiscount {
            get {
                return ResourceManager.GetString("SalesDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مردود مبيعات.
        /// </summary>
        public static string SalesReturn {
            get {
                return ResourceManager.GetString("SalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to السبت.
        /// </summary>
        public static string Sat {
            get {
                return ResourceManager.GetString("Sat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اختيار الكل.
        /// </summary>
        public static string SelectAll {
            get {
                return ResourceManager.GetString("SelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تحميل اذن الاستلام أولا.
        /// </summary>
        public static string SelectBill {
            get {
                return ResourceManager.GetString("SelectBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تحميل الفاتورة أولا.
        /// </summary>
        public static string SelectInv {
            get {
                return ResourceManager.GetString("SelectInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار حسابات الايرادات والمصروفات.
        /// </summary>
        public static string SelectrevExpAcc {
            get {
                return ResourceManager.GetString("SelectrevExpAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاحد.
        /// </summary>
        public static string Sun {
            get {
                return ResourceManager.GetString("Sun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الخميس.
        /// </summary>
        public static string Thu {
            get {
                return ResourceManager.GetString("Thu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تقييم كل العملات بمعامل الحركة.
        /// </summary>
        public static string TrnsRate {
            get {
                return ResourceManager.GetString("TrnsRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الثلاثاء.
        /// </summary>
        public static string Tus {
            get {
                return ResourceManager.GetString("Tus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ التشغيلة.
        /// </summary>
        public static string txt_Date {
            get {
                return ResourceManager.GetString("txt_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اذن اضافة رقم .
        /// </summary>
        public static string txt_ICInTransNo {
            get {
                return ResourceManager.GetString("txt_ICInTransNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اذن صرف رقم .
        /// </summary>
        public static string txt_ICOutTransNo {
            get {
                return ResourceManager.GetString("txt_ICOutTransNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند نقل من مخزن رقم .
        /// </summary>
        public static string txt_ICStoreMoveNo {
            get {
                return ResourceManager.GetString("txt_ICStoreMoveNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند مراقبة جودة رقم.
        /// </summary>
        public static string txt_ManfQCNo {
            get {
                return ResourceManager.GetString("txt_ManfQCNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المنتج.
        /// </summary>
        public static string txt_prod {
            get {
                return ResourceManager.GetString("txt_prod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية.
        /// </summary>
        public static string txt_Qty {
            get {
                return ResourceManager.GetString("txt_Qty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كمية.
        /// </summary>
        public static string txt_QtyText {
            get {
                return ResourceManager.GetString("txt_QtyText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من ادخال تاريخ انتهاء التشغيل.
        /// </summary>
        public static string txt_V_Manf_EndDate {
            get {
                return ResourceManager.GetString("txt_V_Manf_EndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ الانتهاء يجب ان يكون بعد تاريخ بدء التشغيل.
        /// </summary>
        public static string txt_V_manf_EndDate_Larger_StartDate {
            get {
                return ResourceManager.GetString("txt_V_manf_EndDate_Larger_StartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قيمة.
        /// </summary>
        public static string txt_ValueText {
            get {
                return ResourceManager.GetString("txt_ValueText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مصاريف التشغيلة رقم.
        /// </summary>
        public static string txtmanfExpense {
            get {
                return ResourceManager.GetString("txtmanfExpense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مصاريف غير مباشرة للتشغيلة رقم .
        /// </summary>
        public static string txtmanfIndirectExpense {
            get {
                return ResourceManager.GetString("txtmanfIndirectExpense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ترحيل فواتير بيع  تاريخ.
        /// </summary>
        public static string txtSLPosting {
            get {
                return ResourceManager.GetString("txtSLPosting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار التاريخ.
        /// </summary>
        public static string txtValidateDate {
            get {
                return ResourceManager.GetString("txtValidateDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الخزينة.
        /// </summary>
        public static string txtValidateDrawer {
            get {
                return ResourceManager.GetString("txtValidateDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الموظف.
        /// </summary>
        public static string txtValidateEmp {
            get {
                return ResourceManager.GetString("txtValidateEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب كتابة بيانات المصروفات.
        /// </summary>
        public static string txtValidateExpensesText {
            get {
                return ResourceManager.GetString("txtValidateExpensesText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب كتابة قيمة المصروفات.
        /// </summary>
        public static string txtValidateExpensesValue {
            get {
                return ResourceManager.GetString("txtValidateExpensesValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الصنف.
        /// </summary>
        public static string txtValidateItem {
            get {
                return ResourceManager.GetString("txtValidateItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل صنف على الأقل.
        /// </summary>
        public static string txtValidateNoRows {
            get {
                return ResourceManager.GetString("txtValidateNoRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل رقم الاستمارة.
        /// </summary>
        public static string txtValidateNoteNum {
            get {
                return ResourceManager.GetString("txtValidateNoteNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية يجب أن تكون أكبر من الصفر.
        /// </summary>
        public static string txtValidateQty {
            get {
                return ResourceManager.GetString("txtValidateQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار المخزن.
        /// </summary>
        public static string txtValidateStore {
            get {
                return ResourceManager.GetString("txtValidateStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب كتابة عدد ساعات العمل.
        /// </summary>
        public static string txtValidateWorkedhours {
            get {
                return ResourceManager.GetString("txtValidateWorkedhours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم المستخدم .
        /// </summary>
        public static string UserName {
            get {
                return ResourceManager.GetString("UserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال الدفعة المقدمة.
        /// </summary>
        public static string ValidAdvancedPayment {
            get {
                return ResourceManager.GetString("ValidAdvancedPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رقم المحل مطلوب.
        /// </summary>
        public static string validAreaNumber {
            get {
                return ResourceManager.GetString("validAreaNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال مساحة الدور.
        /// </summary>
        public static string ValidAreaSize {
            get {
                return ResourceManager.GetString("ValidAreaSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مساحة المحل اكبر من مساحة المول.
        /// </summary>
        public static string ValidAreaSizeGreaterThanMallArea {
            get {
                return ResourceManager.GetString("ValidAreaSizeGreaterThanMallArea", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ادخل الكود.
        /// </summary>
        public static string validCode {
            get {
                return ResourceManager.GetString("validCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال تاريخ التعاقد.
        /// </summary>
        public static string validContractDate {
            get {
                return ResourceManager.GetString("validContractDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  يجب ادخال تكلفة المتر.
        /// </summary>
        public static string ValidCostMeter {
            get {
                return ResourceManager.GetString("ValidCostMeter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال العميل.
        /// </summary>
        public static string ValidCustomer {
            get {
                return ResourceManager.GetString("ValidCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا: بداية الفترة بعد نهايتها.
        /// </summary>
        public static string validDate {
            get {
                return ResourceManager.GetString("validDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ انتهاء التعاقد يجب ان يكون بعد نهايته.
        /// </summary>
        public static string validDateContract {
            get {
                return ResourceManager.GetString("validDateContract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ادخل بداية الفترة.
        /// </summary>
        public static string validDateFrom {
            get {
                return ResourceManager.GetString("validDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ادخل   نهاية الفترة.
        /// </summary>
        public static string validDateTo {
            get {
                return ResourceManager.GetString("validDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يمكن تكرار الاسم.
        /// </summary>
        public static string ValidDueName {
            get {
                return ResourceManager.GetString("ValidDueName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال تاريخ الانتهاء.
        /// </summary>
        public static string validEndDateContract {
            get {
                return ResourceManager.GetString("validEndDateContract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال الدور.
        /// </summary>
        public static string ValidFloor {
            get {
                return ResourceManager.GetString("ValidFloor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل الاسنجقاق الشهري.
        /// </summary>
        public static string validMonthlyDue {
            get {
                return ResourceManager.GetString("validMonthlyDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد استحقاقات شهرية في هذه الفتره ..ادخل فتره اخري.
        /// </summary>
        public static string validmonthlyDues {
            get {
                return ResourceManager.GetString("validmonthlyDues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجل صف واحد على الاقل في الاستحقاق.
        /// </summary>
        public static string ValidRowInMontlyDue {
            get {
                return ResourceManager.GetString("ValidRowInMontlyDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to القيمة الاجمالية المستحقه يجب ان تكون اكبر من صفر.
        /// </summary>
        public static string ValidTotalValueMonthlyDue {
            get {
                return ResourceManager.GetString("ValidTotalValueMonthlyDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يمكن ايقاف الوحدة.
        /// </summary>
        public static string ValidUom {
            get {
                return ResourceManager.GetString("ValidUom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا, الاسم أو الرقم السري غير صحيح.
        /// </summary>
        public static string ValidUser {
            get {
                return ResourceManager.GetString("ValidUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من إدخال رقم امر الإنتاج.
        /// </summary>
        public static string ValMsgManfOrderNo {
            get {
                return ResourceManager.GetString("ValMsgManfOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الرقم السري غير متطابق.
        /// </summary>
        public static string ValPass {
            get {
                return ResourceManager.GetString("ValPass", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال اسم المستخدم.
        /// </summary>
        public static string valUserName {
            get {
                return ResourceManager.GetString("valUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رصيد افتتاحي مورد.
        /// </summary>
        public static string VendOpen {
            get {
                return ResourceManager.GetString("VendOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مورد.
        /// </summary>
        public static string Vendor {
            get {
                return ResourceManager.GetString("Vendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاربعاء.
        /// </summary>
        public static string Wed {
            get {
                return ResourceManager.GetString("Wed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بالاسبوع.
        /// </summary>
        public static string Weekly {
            get {
                return ResourceManager.GetString("Weekly", resourceCulture);
            }
        }
    }
}
