﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResICAr {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResICAr() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResICAr", typeof(ResICAr).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود الفئه.
        /// </summary>
        public static string CategoryCode {
            get {
                return ResourceManager.GetString("CategoryCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم الفئة ج.
        /// </summary>
        public static string categoryFname {
            get {
                return ResourceManager.GetString("categoryFname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم الفئة.
        /// </summary>
        public static string categoryName {
            get {
                return ResourceManager.GetString("categoryName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود المجموعة.
        /// </summary>
        public static string CompanyCode {
            get {
                return ResourceManager.GetString("CompanyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم المجموعة ج.
        /// </summary>
        public static string companyFname {
            get {
                return ResourceManager.GetString("companyFname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم المجموعة.
        /// </summary>
        public static string companyName {
            get {
                return ResourceManager.GetString("companyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف الفرع، يجب حذف المخازن التابعه له اولا.
        /// </summary>
        public static string delBranchDenied {
            get {
                return ResourceManager.GetString("delBranchDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف الصنف، فهو مستخدمة من خلال خطط التسويق.
        /// </summary>
        public static string DelItemOnMr {
            get {
                return ResourceManager.GetString("DelItemOnMr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to %نسبة ثابتة.
        /// </summary>
        public static string FixedRatio {
            get {
                return ResourceManager.GetString("FixedRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اذونات الاضــافة.
        /// </summary>
        public static string frmInTrnsList {
            get {
                return ResourceManager.GetString("frmInTrnsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار فئة فرعية.
        /// </summary>
        public static string IcSubCat {
            get {
                return ResourceManager.GetString("IcSubCat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد أكواد مكررة.
        /// </summary>
        public static string ItemCodesDuplicate {
            get {
                return ResourceManager.GetString("ItemCodesDuplicate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف.
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، عدد المستويات المسموح بها في الأفرع والمخازن مستويان فقط.
        /// </summary>
        public static string MsgBranch {
            get {
                return ResourceManager.GetString("MsgBranch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن انشاء مخزن فرعي لفرع به أرصدة مخزنية.
        /// </summary>
        public static string MsgBranchItems {
            get {
                return ResourceManager.GetString("MsgBranchItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال كود الفئة.
        /// </summary>
        public static string MsgCatCode {
            get {
                return ResourceManager.GetString("MsgCatCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال اسم الفئة.
        /// </summary>
        public static string MsgCatName {
            get {
                return ResourceManager.GetString("MsgCatName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الكود مسجل من قبل.
        /// </summary>
        public static string MsgCodeExist {
            get {
                return ResourceManager.GetString("MsgCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال الكود.
        /// </summary>
        public static string MsgCompCode {
            get {
                return ResourceManager.GetString("MsgCompCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال الاسم.
        /// </summary>
        public static string MsgCompName {
            get {
                return ResourceManager.GetString("MsgCompName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تسجيل البيانات بشكل صحيح.
        /// </summary>
        public static string MsgData {
            get {
                return ResourceManager.GetString("MsgData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحذف بنجاح.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد بالغعل حذف هذا الباركود الدولي.
        /// </summary>
        public static string MsgDelBarcode {
            get {
                return ResourceManager.GetString("MsgDelBarcode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد بالغعل حذف آخر باركود دولي.
        /// </summary>
        public static string MsgDelBarcode2 {
            get {
                return ResourceManager.GetString("MsgDelBarcode2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه الفئه.
        /// </summary>
        public static string MsgDelCat {
            get {
                return ResourceManager.GetString("MsgDelCat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، يوجد أصناف مرتبطة بهذه الفئة، لا يمكن حذف الفئة.
        /// </summary>
        public static string MsgDelCatDenied {
            get {
                return ResourceManager.GetString("MsgDelCatDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد انك تريد حذف هذه المجموعة.
        /// </summary>
        public static string MsgDelComp {
            get {
                return ResourceManager.GetString("MsgDelComp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، يوجد أصناف مرتبطة بهذه المجموعة، لا يمكن حذف المجموعة.
        /// </summary>
        public static string MsgDelCompDenied {
            get {
                return ResourceManager.GetString("MsgDelCompDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف السند و جميع بياناته.
        /// </summary>
        public static string MsgDeleteInv {
            get {
                return ResourceManager.GetString("MsgDeleteInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف مصروفات؟.
        /// </summary>
        public static string MsgDelExpenseAsk {
            get {
                return ResourceManager.GetString("MsgDelExpenseAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف دفتر الفواتير.
        /// </summary>
        public static string MsgDelInvoiceBook {
            get {
                return ResourceManager.GetString("MsgDelInvoiceBook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا الصنف.
        /// </summary>
        public static string MsgDelItem {
            get {
                return ResourceManager.GetString("MsgDelItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف صنف ؟.
        /// </summary>
        public static string MsgDelItemAsk {
            get {
                return ResourceManager.GetString("MsgDelItemAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف الصنف لأنه موجود في قائمة مواد خام أصناف اخري.
        /// </summary>
        public static string MsgDelItemDenied1 {
            get {
                return ResourceManager.GetString("MsgDelItemDenied1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف الصنف, يوجد كميات منه بالمخازن.
        /// </summary>
        public static string MsgDelItemDenied2 {
            get {
                return ResourceManager.GetString("MsgDelItemDenied2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف الصنف لأنه موجود في قائمة أسعار.
        /// </summary>
        public static string MsgDelItemDenied3 {
            get {
                return ResourceManager.GetString("MsgDelItemDenied3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة.
        /// </summary>
        public static string MsgDelItemDenied4 {
            get {
                return ResourceManager.GetString("MsgDelItemDenied4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف المصفوفه فهي مستخدمة بالفعل.
        /// </summary>
        public static string MsgDelMtrx {
            get {
                return ResourceManager.GetString("MsgDelMtrx", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف أصناف المصفوفه أولا.
        /// </summary>
        public static string MsgDelMtxItems {
            get {
                return ResourceManager.GetString("MsgDelMtxItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف قائمة الأسعار هذه.
        /// </summary>
        public static string MsgDelPriceList {
            get {
                return ResourceManager.GetString("MsgDelPriceList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، يوجد عملاء مرتبطين بهذه القائمة، لا يمكن حذف قائمة الأسعار.
        /// </summary>
        public static string MsgDelPriceListDenied {
            get {
                return ResourceManager.GetString("MsgDelPriceListDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، يوجد موردين مرتبطين بهذه القائمة، لا يمكن حذف قائمة الأسعار.
        /// </summary>
        public static string MsgDelPriceListDenied1 {
            get {
                return ResourceManager.GetString("MsgDelPriceListDenied1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف صف ؟.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد انك تريد حذف هذا المخزن.
        /// </summary>
        public static string MsgDelStore {
            get {
                return ResourceManager.GetString("MsgDelStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أبعاد الصنف لايمكن أن تكون أقل من أو مساوية للصفر.
        /// </summary>
        public static string MsgDimension {
            get {
                return ResourceManager.GetString("MsgDimension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاسم الاجنبي مسجل من قبل.
        /// </summary>
        public static string MsgFNameExist {
            get {
                return ResourceManager.GetString("MsgFNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من صحة البيانات.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال الكود الدولي بشكل صحيح.
        /// </summary>
        public static string MsgInterCode {
            get {
                return ResourceManager.GetString("MsgInterCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الكود الدولي تم استخدامه مسبقا.
        /// </summary>
        public static string MsgInterCodeExist {
            get {
                return ResourceManager.GetString("MsgInterCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الصنف غير موجود.
        /// </summary>
        public static string MsgItemdoesntExist {
            get {
                return ResourceManager.GetString("MsgItemdoesntExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، لايمكن طباعة اكثر من 500 صنف.
        /// </summary>
        public static string MsgItemsPrint {
            get {
                return ResourceManager.GetString("MsgItemsPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف الفرع الرئيسي.
        /// </summary>
        public static string MsgMainStoreDel {
            get {
                return ResourceManager.GetString("MsgMainStoreDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حسابات المتاجرة من شاشة الإعدادات.
        /// </summary>
        public static string MsgMerchendaisingAcc {
            get {
                return ResourceManager.GetString("MsgMerchendaisingAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال كود المصفوفه.
        /// </summary>
        public static string MsgMtCode {
            get {
                return ResourceManager.GetString("MsgMtCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال اسم المصفوفه.
        /// </summary>
        public static string MsgMtName {
            get {
                return ResourceManager.GetString("MsgMtName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال بنود المصفوفه.
        /// </summary>
        public static string MsgMtRows {
            get {
                return ResourceManager.GetString("MsgMtRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء مراجعة بيانات الأبعاد في مصفوفة الأصناف.
        /// </summary>
        public static string MsgMtrxData {
            get {
                return ResourceManager.GetString("MsgMtrxData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف الصنف، يوجد أصناف من المصفوفه مستخدم من قبل.
        /// </summary>
        public static string MsgMtrxDelDenied {
            get {
                return ResourceManager.GetString("MsgMtrxDelDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء التأكد من إدخال البيانات بشكل صحيح.
        /// </summary>
        public static string MsgMtrxValid {
            get {
                return ResourceManager.GetString("MsgMtrxValid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الاسم مسجل من قبل.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد كميه كافيه من أحد الاصناف.
        /// </summary>
        public static string MsgNoEnoughQty {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد كميه كافيه من أحد الاصناف, هل تريد الاستمرار  ؟.
        /// </summary>
        public static string MsgNoEnoughQty_continue {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty_continue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الرقم مسجل من قبل.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لقد تم الصرف من قبل.
        /// </summary>
        public static string MsgOutBefore {
            get {
                return ResourceManager.GetString("MsgOutBefore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء ادخال اسم قائمة الأسعار.
        /// </summary>
        public static string MsgPLNAme {
            get {
                return ResourceManager.GetString("MsgPLNAme", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء ادخال النسبة بشكل صحيح.
        /// </summary>
        public static string MsgPLRatio {
            get {
                return ResourceManager.GetString("MsgPLRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية تعديل هذا البيان.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية اضافة بيان جديد.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء مراجعة بيانات اسعار بيع الصنف حسب الكمية.
        /// </summary>
        public static string MsgSalePerQty {
            get {
                return ResourceManager.GetString("MsgSalePerQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحفظ بنجاح.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء حفظ الصنف اولا.
        /// </summary>
        public static string MsgSaveItemFirst {
            get {
                return ResourceManager.GetString("MsgSaveItemFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يمكن تعديل أو حذف جرد سبق اعتماده.
        /// </summary>
        public static string Msgstocktaking_cant_upadte {
            get {
                return ResourceManager.GetString("Msgstocktaking_cant_upadte", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اعتماد الجرد لا يمكن التراجع عنه أو تعديله بعد ذلك, هل أنت متأكد من عملية الجرد.
        /// </summary>
        public static string Msgstocktaking_commit {
            get {
                return ResourceManager.GetString("Msgstocktaking_commit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لديك خسارة في الجرد بقيمة.
        /// </summary>
        public static string Msgstocktaking_lose {
            get {
                return ResourceManager.GetString("Msgstocktaking_lose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حفظ مستند الجرد لايعني اعتماده في المخزن, لاعتماد الجرد يجب الضغط علي زر اعتماد.
        /// </summary>
        public static string Msgstocktaking_save {
            get {
                return ResourceManager.GetString("Msgstocktaking_save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لديك ربح في الجرد بقيمة.
        /// </summary>
        public static string Msgstocktaking_win {
            get {
                return ResourceManager.GetString("Msgstocktaking_win", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم اعتماد الجرد بنجاح.
        /// </summary>
        public static string MsgstocktakingCommited {
            get {
                return ResourceManager.GetString("MsgstocktakingCommited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال الكود.
        /// </summary>
        public static string MsgStoreCode {
            get {
                return ResourceManager.GetString("MsgStoreCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، يوجد أصناف داخل هذا المخزن, لا يمكن حذف المخزن.
        /// </summary>
        public static string MsgStoreItems {
            get {
                return ResourceManager.GetString("MsgStoreItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف العمليات الخاصه بهذا الفرع اولا.
        /// </summary>
        public static string MsgStoreJournals {
            get {
                return ResourceManager.GetString("MsgStoreJournals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال الاسم.
        /// </summary>
        public static string MsgStoreName {
            get {
                return ResourceManager.GetString("MsgStoreName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكنك إنشاء مخازن فرعية، برجاء مراجعة مدير النظام.
        /// </summary>
        public static string MsgSubStore {
            get {
                return ResourceManager.GetString("MsgSubStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to معلومة.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سؤال.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تحذير.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا المخزن مخصص كمخزن افتراضي لبعض المستخدمين.
        /// </summary>
        public static string msgUserDefaultStore {
            get {
                return ResourceManager.GetString("msgUserDefaultStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال التاريخ و اختيار المخزن.
        /// </summary>
        public static string MsgValidateDateandStore {
            get {
                return ResourceManager.GetString("MsgValidateDateandStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية المصروفة اكبر من الرصيد الحالي.
        /// </summary>
        public static string Msgvalidateoutqty {
            get {
                return ResourceManager.GetString("Msgvalidateoutqty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية المصروفة لا يمكن ان تكون اكبر من الرصيدالحالي.
        /// </summary>
        public static string Msgvalidateoutqty_forbid {
            get {
                return ResourceManager.GetString("Msgvalidateoutqty_forbid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار المخزن المحول منه و المخزن المحول اليه.
        /// </summary>
        public static string MsgValidateStore {
            get {
                return ResourceManager.GetString("MsgValidateStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المخزن المحول منه لا يمكن ان يكون نفس المخزن المحول اليه.
        /// </summary>
        public static string MsgValidateStore1 {
            get {
                return ResourceManager.GetString("MsgValidateStore1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكود لايمكن أن يساوي صفر.
        /// </summary>
        public static string MsgZeroCode {
            get {
                return ResourceManager.GetString("MsgZeroCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حسب كل صنف.
        /// </summary>
        public static string PerItem {
            get {
                return ResourceManager.GetString("PerItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إذن استلام.
        /// </summary>
        public static string ReceiveGood {
            get {
                return ResourceManager.GetString("ReceiveGood", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أذونات الاستلام.
        /// </summary>
        public static string ReceiveGoodList {
            get {
                return ResourceManager.GetString("ReceiveGoodList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الأصناف رصيدها صفر.
        /// </summary>
        public static string rpt_IC_ItemsZeroQty {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsZeroQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب إختيار المورد.
        /// </summary>
        public static string SelectVendor {
            get {
                return ResourceManager.GetString("SelectVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب إختيار حسابات المخزن/الفرع.
        /// </summary>
        public static string StockAccs {
            get {
                return ResourceManager.GetString("StockAccs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن تغيير مستوى الفرع أو المخزن.
        /// </summary>
        public static string storeLevel {
            get {
                return ResourceManager.GetString("storeLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن انشاء فئة فرعية، يوجد اصناف لهذه الفئة.
        /// </summary>
        public static string subCat {
            get {
                return ResourceManager.GetString("subCat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود 1.
        /// </summary>
        public static string txt_Code1 {
            get {
                return ResourceManager.GetString("txt_Code1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود 2.
        /// </summary>
        public static string txt_Code2 {
            get {
                return ResourceManager.GetString("txt_Code2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ك حالية.
        /// </summary>
        public static string txt_CurrentQty {
            get {
                return ResourceManager.GetString("txt_CurrentQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رقم.
        /// </summary>
        public static string txt_Number {
            get {
                return ResourceManager.GetString("txt_Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كمية.
        /// </summary>
        public static string txt_Qty {
            get {
                return ResourceManager.GetString("txt_Qty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تام.
        /// </summary>
        public static string txtAssembly {
            get {
                return ResourceManager.GetString("txtAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الفئه: .
        /// </summary>
        public static string txtCategory {
            get {
                return ResourceManager.GetString("txtCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المجموعة: .
        /// </summary>
        public static string txtComapny {
            get {
                return ResourceManager.GetString("txtComapny", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دائن.
        /// </summary>
        public static string txtCredit {
            get {
                return ResourceManager.GetString("txtCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to العميل.
        /// </summary>
        public static string txtCustomer {
            get {
                return ResourceManager.GetString("txtCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  اسم المتعامل.
        /// </summary>
        public static string txtDealerNm {
            get {
                return ResourceManager.GetString("txtDealerNm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مدين.
        /// </summary>
        public static string txtDebit {
            get {
                return ResourceManager.GetString("txtDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  خزينه .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية الجديدة يجب أن تكون أكبر من أو تساوي صفر.
        /// </summary>
        public static string txtEditItemQty {
            get {
                return ResourceManager.GetString("txtEditItemQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل الكمية الجديدة لصنف واحد علي الأقل.
        /// </summary>
        public static string txtEditItemQty2 {
            get {
                return ResourceManager.GetString("txtEditItemQty2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تجاوز حد الطلب هل تريد الاستمرار ؟.
        /// </summary>
        public static string txtExceedsReorder {
            get {
                return ResourceManager.GetString("txtExceedsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من تاريخ .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند هالك/ تالف .
        /// </summary>
        public static string txtIC_damage {
            get {
                return ResourceManager.GetString("txtIC_damage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند اضافة.
        /// </summary>
        public static string txtIC_intrns {
            get {
                return ResourceManager.GetString("txtIC_intrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اذونات الاضافة.
        /// </summary>
        public static string txtIC_intrnsList {
            get {
                return ResourceManager.GetString("txtIC_intrnsList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رصيد افتتاحي.
        /// </summary>
        public static string txtIC_openbalance {
            get {
                return ResourceManager.GetString("txtIC_openbalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ارصدة افتتاحية.
        /// </summary>
        public static string txtIC_openbalanceList {
            get {
                return ResourceManager.GetString("txtIC_openbalanceList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند خصم .
        /// </summary>
        public static string txtIC_outtrns {
            get {
                return ResourceManager.GetString("txtIC_outtrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to استمارة تبديل.
        /// </summary>
        public static string txtIC_replacement {
            get {
                return ResourceManager.GetString("txtIC_replacement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to استمارة جرد.
        /// </summary>
        public static string txtIC_stocktaking {
            get {
                return ResourceManager.GetString("txtIC_stocktaking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند نقل من مخزن.
        /// </summary>
        public static string txtIC_storemove {
            get {
                return ResourceManager.GetString("txtIC_storemove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to التاريخ.
        /// </summary>
        public static string txtInvDate {
            get {
                return ResourceManager.GetString("txtInvDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الصنف:.
        /// </summary>
        public static string txtItem {
            get {
                return ResourceManager.GetString("txtItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الصنف موجود مسبقا.
        /// </summary>
        public static string txtItemExist {
            get {
                return ResourceManager.GetString("txtItemExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حركة الاصناف.
        /// </summary>
        public static string txtItemMovement {
            get {
                return ResourceManager.GetString("txtItemMovement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أرصدة الأصناف.
        /// </summary>
        public static string txtItemsBalance {
            get {
                return ResourceManager.GetString("txtItemsBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاصناف الاكثر مبيعا.
        /// </summary>
        public static string txtItemsBestSell {
            get {
                return ResourceManager.GetString("txtItemsBestSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاصناف الاقل مبيعا.
        /// </summary>
        public static string txtItemsLeastSell {
            get {
                return ResourceManager.GetString("txtItemsLeastSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف لم تباع مطلقا.
        /// </summary>
        public static string txtItemsNotSold {
            get {
                return ResourceManager.GetString("txtItemsNotSold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف وصلت لحد الطلب.
        /// </summary>
        public static string txtItemsReorder {
            get {
                return ResourceManager.GetString("txtItemsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مشتريات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalPurchases {
            get {
                return ResourceManager.GetString("txtItemTotalPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مردود مشتريات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalPurchasesReturns {
            get {
                return ResourceManager.GetString("txtItemTotalPurchasesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مبيعات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalSales {
            get {
                return ResourceManager.GetString("txtItemTotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مردود مبيعات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalSalesReturn {
            get {
                return ResourceManager.GetString("txtItemTotalSalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قيد بضاعة اول المدة.
        /// </summary>
        public static string txtJornalOpenBalance {
            get {
                return ResourceManager.GetString("txtJornalOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مصفوفة.
        /// </summary>
        public static string txtMatrix {
            get {
                return ResourceManager.GetString("txtMatrix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ملاحظات.
        /// </summary>
        public static string txtNotes {
            get {
                return ResourceManager.GetString("txtNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  رصيد افتتاحي .
        /// </summary>
        public static string txtOpenBalance {
            get {
                return ResourceManager.GetString("txtOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مخزني.
        /// </summary>
        public static string txtRaw {
            get {
                return ResourceManager.GetString("txtRaw", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تسلسل.
        /// </summary>
        public static string txtSerial {
            get {
                return ResourceManager.GetString("txtSerial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خدمه.
        /// </summary>
        public static string txtService {
            get {
                return ResourceManager.GetString("txtService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المخزن:.
        /// </summary>
        public static string txtStore {
            get {
                return ResourceManager.GetString("txtStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مجموع فرعي.
        /// </summary>
        public static string txtSubTotal {
            get {
                return ResourceManager.GetString("txtSubTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى تاريخ .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تحديد الخصم.
        /// </summary>
        public static string txtValidateDiscount {
            get {
                return ResourceManager.GetString("txtValidateDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل رقم السند.
        /// </summary>
        public static string txtValidateInvNumber {
            get {
                return ResourceManager.GetString("txtValidateInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الصنف.
        /// </summary>
        public static string txtValidateItem {
            get {
                return ResourceManager.GetString("txtValidateItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية المشتراه و الكمية الموجودة حاليا في المخزن أكبر من الحد الأقصى للكمية.
        /// </summary>
        public static string txtValidateItemMaxLimit {
            get {
                return ResourceManager.GetString("txtValidateItemMaxLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نسبة الخصم لايمكن ان تتجاوز المائة.
        /// </summary>
        public static string txtValidateMaxDiscount {
            get {
                return ResourceManager.GetString("txtValidateMaxDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية الموجودة حاليا في المخزن بعد خصم الكمية المصروفة أقل من الحد الأدنى للكمية.
        /// </summary>
        public static string txtValidateMinQty {
            get {
                return ResourceManager.GetString("txtValidateMinQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل صنف واحد علي الاقل .
        /// </summary>
        public static string txtValidateNoRows {
            get {
                return ResourceManager.GetString("txtValidateNoRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سعر الشراء يجب أن يكون أكبر من الصفر.
        /// </summary>
        public static string txtValidatePPrice {
            get {
                return ResourceManager.GetString("txtValidatePPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية يجب ان تكون اكبر من الصفر.
        /// </summary>
        public static string txtValidateQty {
            get {
                return ResourceManager.GetString("txtValidateQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية الموجودة حاليا في المخزن بعد خصم الكمية المصروفة أقل من حد الطلب.
        /// </summary>
        public static string txtValidateReorderQty {
            get {
                return ResourceManager.GetString("txtValidateReorderQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سعر البيع يجب أن يكون أكبر من الصفر.
        /// </summary>
        public static string txtValidateSPrice {
            get {
                return ResourceManager.GetString("txtValidateSPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سعر البيع يجب أن يكون أكبر من سعر الشراء.
        /// </summary>
        public static string txtValidateSpriceLargerPprice {
            get {
                return ResourceManager.GetString("txtValidateSpriceLargerPprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية لا يمكن ان تكون اكبر من الرصيدالحالي.
        /// </summary>
        public static string txtValidateStoreQty {
            get {
                return ResourceManager.GetString("txtValidateStoreQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار وحدة القياس.
        /// </summary>
        public static string txtValidateUom {
            get {
                return ResourceManager.GetString("txtValidateUom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المورد.
        /// </summary>
        public static string txtVendor {
            get {
                return ResourceManager.GetString("txtVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب كتابة المصاريف.
        /// </summary>
        public static string ValExpenses {
            get {
                return ResourceManager.GetString("ValExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المصروفات يجب ان تكون اكبر من الصفر.
        /// </summary>
        public static string ValExpensesZero {
            get {
                return ResourceManager.GetString("ValExpensesZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود الصنف لايمكن أن يكون اكبر من المحدد في نموذج طباعة الباركود.
        /// </summary>
        public static string ValItemCodeLength {
            get {
                return ResourceManager.GetString("ValItemCodeLength", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف الصنف فهو مستخدم بقوائم خامات الانتاج.
        /// </summary>
        public static string ValTxtItemBOMDel {
            get {
                return ResourceManager.GetString("ValTxtItemBOMDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل كود1 للصنف.
        /// </summary>
        public static string ValTxtItemCode1 {
            get {
                return ResourceManager.GetString("ValTxtItemCode1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد صنف لديه نفس الكود 1.
        /// </summary>
        public static string ValTxtItemCode1Exist {
            get {
                return ResourceManager.GetString("ValTxtItemCode1Exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد صنف لديه نفس الأكواد.
        /// </summary>
        public static string ValTxtItemCode2Exist {
            get {
                return ResourceManager.GetString("ValTxtItemCode2Exist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الصنف موجود مسبقا.
        /// </summary>
        public static string ValTxtItemExist {
            get {
                return ResourceManager.GetString("ValTxtItemExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل اسم الصنف.
        /// </summary>
        public static string ValTxtItemName {
            get {
                return ResourceManager.GetString("ValTxtItemName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل سعر الشراء.
        /// </summary>
        public static string ValTxtItemPurchasePrice {
            get {
                return ResourceManager.GetString("ValTxtItemPurchasePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سعر الشراء لايمكن ان يكون اكبر من سعر بيع وحدة التجزئه.
        /// </summary>
        public static string ValTxtItemSalePrice {
            get {
                return ResourceManager.GetString("ValTxtItemSalePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل سعر بيع وحدة التجزئه.
        /// </summary>
        public static string ValTxtItemSellPrice {
            get {
                return ResourceManager.GetString("ValTxtItemSellPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن تسجيل وحدة فرعية 2 بدون تسجيل وحدة فرعية 1.
        /// </summary>
        public static string ValTxtItemUOM1 {
            get {
                return ResourceManager.GetString("ValTxtItemUOM1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الصنف.
        /// </summary>
        public static string ValTxtSelectItem {
            get {
                return ResourceManager.GetString("ValTxtSelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار وحدة القياس.
        /// </summary>
        public static string ValTxtSelectUOM {
            get {
                return ResourceManager.GetString("ValTxtSelectUOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حسب كل صنف.
        /// </summary>
        public static string variesPerItem {
            get {
                return ResourceManager.GetString("variesPerItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية يجب ان تكون اكبر من الصفر.
        /// </summary>
        public static string VatTxtZeroQty {
            get {
                return ResourceManager.GetString("VatTxtZeroQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المرود مسجل بالفعل.
        /// </summary>
        public static string VendorExist {
            get {
                return ResourceManager.GetString("VendorExist", resourceCulture);
            }
        }
    }
}
