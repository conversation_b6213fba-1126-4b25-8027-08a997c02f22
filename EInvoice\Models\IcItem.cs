﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class IcItem
    {
        public int ItemId { get; set; }
        public int ItemCode1 { get; set; }
        public string ItemCode2 { get; set; }
        public string ItemNameAr { get; set; }
        public string ItemNameEn { get; set; }
        public string Description { get; set; }
        public int Category { get; set; }
        public int Company { get; set; }
        public decimal PurchasePrice { get; set; }
        public byte SmallUom { get; set; }
        public decimal SmallUomprice { get; set; }
        public byte? MediumUom { get; set; }
        public string MediumUomfactor { get; set; }
        public decimal? MediumUomprice { get; set; }
        public byte? LargeUom { get; set; }
        public string LargeUomfactor { get; set; }
        public decimal? LargeUomprice { get; set; }
        public int ReorderLevel { get; set; }
        public int MaxQty { get; set; }
        public int MinQty { get; set; }
        public byte ChangePriceMethod { get; set; }
        public bool ChangeSellPrice { get; set; }
        public bool IsExpire { get; set; }
        public bool IsDeleted { get; set; }
        public string Pic<PERSON>ath { get; set; }
        public int ItemType { get; set; }
        public int? MtrxId1 { get; set; }
        public int? MtrxId2 { get; set; }
        public int? MtrxId3 { get; set; }
        public string MtrxCode0 { get; set; }
        public string MtrxSprtr0 { get; set; }
        public string MtrxSprtr1 { get; set; }
        public string MtrxSprtr2 { get; set; }
        public string MtrxSprtr3 { get; set; }
        public int? MtrxParentItem { get; set; }
        public int? MtrxAttribute1 { get; set; }
        public int? MtrxAttribute2 { get; set; }
        public int? MtrxAttribute3 { get; set; }
        public decimal PurchaseDiscRatio { get; set; }
        public decimal PurchaseTaxValue { get; set; }
        public string DescriptionEn { get; set; }
        public decimal SalesDiscRatio { get; set; }
        public decimal SalesTaxRatio { get; set; }
        public decimal SalesTaxValue { get; set; }
        public decimal PurchaseTaxRatio { get; set; }
        public bool UsedInMarketing { get; set; }
        public decimal Height { get; set; }
        public decimal Width { get; set; }
        public decimal Length { get; set; }
        public byte DfltSellUomIndx { get; set; }
        public byte DfltPrchsUomIndx { get; set; }
        public decimal? MediumUomfactorDecimal { get; set; }
        public decimal? LargeUomfactorDecimal { get; set; }
        public int WarrantyMonths { get; set; }
        public string MediumUomcode { get; set; }
        public string LargeUomcode { get; set; }
        public bool CalcTaxBeforeDisc { get; set; }
        public bool IsPos { get; set; }
        public decimal CustomSalesTaxRatio { get; set; }
        public decimal CustomPurchasesTaxRatio { get; set; }
        public decimal? AudiencePrice { get; set; }
        public int? Floor { get; set; }
        public int? Room { get; set; }
        public long? Elec { get; set; }
        public long? Water { get; set; }
        public string Unitno { get; set; }
        public bool? IsLibra { get; set; }
        public bool? PricingWithSmall { get; set; }
        public bool? VariableWeight { get; set; }
        public bool? IsOffer { get; set; }
        public int? Expiry { get; set; }
        public int? DfltWeightUnit { get; set; }
        public bool? SmallUomisStopped { get; set; }
        public bool? MediumUomisStopped { get; set; }
        public bool? LargeUomisStopped { get; set; }
        public bool? SmallIsStopped { get; set; }
        public bool? MediumIsStopped { get; set; }
        public bool? LargeIsStopped { get; set; }
        public string ItemEcode { get; set; }
        public string ItemEtype { get; set; }
    }
}
