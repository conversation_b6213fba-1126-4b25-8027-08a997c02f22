﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResRptEn {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResRptEn() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResRptEn", typeof(ResRptEn).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Batch: .
        /// </summary>
        public static string Batch {
            get {
                return ResourceManager.GetString("Batch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit.
        /// </summary>
        public static string Credit {
            get {
                return ResourceManager.GetString("Credit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer visits report.
        /// </summary>
        public static string CustomerVisits {
            get {
                return ResourceManager.GetString("CustomerVisits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        public static string Debit {
            get {
                return ResourceManager.GetString("Debit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due.
        /// </summary>
        public static string Due {
            get {
                return ResourceManager.GetString("Due", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Earlier .
        /// </summary>
        public static string DueEarlierPeriod {
            get {
                return ResourceManager.GetString("DueEarlierPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Later.
        /// </summary>
        public static string DueLaterPeriod {
            get {
                return ResourceManager.GetString("DueLaterPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account: .
        /// </summary>
        public static string Faccount {
            get {
                return ResourceManager.GetString("Faccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category: .
        /// </summary>
        public static string Fcat {
            get {
                return ResourceManager.GetString("Fcat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category:All .
        /// </summary>
        public static string FcatAll {
            get {
                return ResourceManager.GetString("FcatAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group: .
        /// </summary>
        public static string Fcomp {
            get {
                return ResourceManager.GetString("Fcomp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group: All .
        /// </summary>
        public static string FcompAll {
            get {
                return ResourceManager.GetString("FcompAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Center: .
        /// </summary>
        public static string FcostCenter {
            get {
                return ResourceManager.GetString("FcostCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Group: .
        /// </summary>
        public static string FCustGroup {
            get {
                return ResourceManager.GetString("FCustGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Group : All .
        /// </summary>
        public static string FcustGroupAll {
            get {
                return ResourceManager.GetString("FcustGroupAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer: .
        /// </summary>
        public static string Fcustomer {
            get {
                return ResourceManager.GetString("Fcustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer:All .
        /// </summary>
        public static string FcustomerAll {
            get {
                return ResourceManager.GetString("FcustomerAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom Accounts List: .
        /// </summary>
        public static string FcustomList {
            get {
                return ResourceManager.GetString("FcustomList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date: .
        /// </summary>
        public static string FDate {
            get {
                return ResourceManager.GetString("FDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Before Date: .
        /// </summary>
        public static string FdateBefore {
            get {
                return ResourceManager.GetString("FdateBefore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Employee Group: .
        /// </summary>
        public static string FempGroup {
            get {
                return ResourceManager.GetString("FempGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Employee Group: All .
        /// </summary>
        public static string FempGroupAll {
            get {
                return ResourceManager.GetString("FempGroupAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  from .
        /// </summary>
        public static string FFrom {
            get {
                return ResourceManager.GetString("FFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to from date: .
        /// </summary>
        public static string FfromDate {
            get {
                return ResourceManager.GetString("FfromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Book:.
        /// </summary>
        public static string FInvBook {
            get {
                return ResourceManager.GetString("FInvBook", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Book: All.
        /// </summary>
        public static string FInvBookAll {
            get {
                return ResourceManager.GetString("FInvBookAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Name: .
        /// </summary>
        public static string FitemName {
            get {
                return ResourceManager.GetString("FitemName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Name: All .
        /// </summary>
        public static string FitemsAll {
            get {
                return ResourceManager.GetString("FitemsAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Type: .
        /// </summary>
        public static string FitemType {
            get {
                return ResourceManager.GetString("FitemType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Type : All .
        /// </summary>
        public static string FitemTypeAll {
            get {
                return ResourceManager.GetString("FitemTypeAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Department: .
        /// </summary>
        public static string FjoDept {
            get {
                return ResourceManager.GetString("FjoDept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Department : All .
        /// </summary>
        public static string FjoDeptAll {
            get {
                return ResourceManager.GetString("FjoDeptAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Priority: .
        /// </summary>
        public static string FjoPriority {
            get {
                return ResourceManager.GetString("FjoPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Priority : All .
        /// </summary>
        public static string FjoPriorityAll {
            get {
                return ResourceManager.GetString("FjoPriorityAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status: .
        /// </summary>
        public static string FjoStatus {
            get {
                return ResourceManager.GetString("FjoStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status : All .
        /// </summary>
        public static string FjoStatusAll {
            get {
                return ResourceManager.GetString("FjoStatusAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Detailed Statment of Account.
        /// </summary>
        public static string frm_Acc_AccountDetails {
            get {
                return ResourceManager.GetString("frm_Acc_AccountDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor Detailed Statment of Account.
        /// </summary>
        public static string frm_Acc_PR_AccountDetails {
            get {
                return ResourceManager.GetString("frm_Acc_PR_AccountDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customers Debit.
        /// </summary>
        public static string frm_Customers_Debit {
            get {
                return ResourceManager.GetString("frm_Customers_Debit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expected Salaries.
        /// </summary>
        public static string frm_HR_AllExpectedPays {
            get {
                return ResourceManager.GetString("frm_HR_AllExpectedPays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payslips.
        /// </summary>
        public static string frm_HR_AllPays {
            get {
                return ResourceManager.GetString("frm_HR_AllPays", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attendence Report.
        /// </summary>
        public static string frm_HR_Att {
            get {
                return ResourceManager.GetString("frm_HR_Att", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employees Insurance.
        /// </summary>
        public static string frm_HR_Insurance {
            get {
                return ResourceManager.GetString("frm_HR_Insurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vacations Balances.
        /// </summary>
        public static string frm_HR_VacationBal {
            get {
                return ResourceManager.GetString("frm_HR_VacationBal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vacations Report.
        /// </summary>
        public static string frm_HR_Vacations {
            get {
                return ResourceManager.GetString("frm_HR_Vacations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Balances Horizontal.
        /// </summary>
        public static string frm_IC_ItemsQtyH {
            get {
                return ResourceManager.GetString("frm_IC_ItemsQtyH", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Quantity Report Hor. Without Cost.
        /// </summary>
        public static string frm_IC_ItemsQtyHNoCost {
            get {
                return ResourceManager.GetString("frm_IC_ItemsQtyHNoCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Turnover.
        /// </summary>
        public static string frm_IC_ItemsTurnOver {
            get {
                return ResourceManager.GetString("frm_IC_ItemsTurnOver", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sold and Purchased Items.
        /// </summary>
        public static string frm_ItemsPr_and_Sl {
            get {
                return ResourceManager.GetString("frm_ItemsPr_and_Sl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Orders Products &amp; Raws.
        /// </summary>
        public static string frm_ManfItems {
            get {
                return ResourceManager.GetString("frm_ManfItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Marketing upon Customer Group.
        /// </summary>
        public static string frm_MrAllSales {
            get {
                return ResourceManager.GetString("frm_MrAllSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contractor Extract.
        /// </summary>
        public static string frm_PR_ContractorExtract {
            get {
                return ResourceManager.GetString("frm_PR_ContractorExtract", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders And Achievements.
        /// </summary>
        public static string frm_PurchaseOrderAndAchievement {
            get {
                return ResourceManager.GetString("frm_PurchaseOrderAndAchievement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Orders And Achievements.
        /// </summary>
        public static string frm_SalesOrderAndAchievement {
            get {
                return ResourceManager.GetString("frm_SalesOrderAndAchievement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Rep Day Summary.
        /// </summary>
        public static string frm_SalesRep_DaySummary {
            get {
                return ResourceManager.GetString("frm_SalesRep_DaySummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cars Weights.
        /// </summary>
        public static string frm_SL_Car_Weights {
            get {
                return ResourceManager.GetString("frm_SL_Car_Weights", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delegates Sales per Item Category.
        /// </summary>
        public static string frm_SL_DelegatesSales_ItemCategory {
            get {
                return ResourceManager.GetString("frm_SL_DelegatesSales_ItemCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net Purchase and Purchase Return Report.
        /// </summary>
        public static string frm_SL_ItemsNetPurchaseDetails {
            get {
                return ResourceManager.GetString("frm_SL_ItemsNetPurchaseDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net Detailed Sale Report.
        /// </summary>
        public static string frm_SL_ItemsNetSalesDetails {
            get {
                return ResourceManager.GetString("frm_SL_ItemsNetSalesDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Total Sales &amp; Returns.
        /// </summary>
        public static string frm_SL_ItemsSalesDetails {
            get {
                return ResourceManager.GetString("frm_SL_ItemsSalesDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Total Sales.
        /// </summary>
        public static string frm_SL_ItemsSalesTotals {
            get {
                return ResourceManager.GetString("frm_SL_ItemsSalesTotals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Orders Sales.
        /// </summary>
        public static string frm_SL_JobOrderInv {
            get {
                return ResourceManager.GetString("frm_SL_JobOrderInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Orders Items.
        /// </summary>
        public static string frm_SL_SalesOrderItems {
            get {
                return ResourceManager.GetString("frm_SL_SalesOrderItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Items and Current Balances.
        /// </summary>
        public static string frm_SL_SalesOrderItemsAndBalance {
            get {
                return ResourceManager.GetString("frm_SL_SalesOrderItemsAndBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Warranty.
        /// </summary>
        public static string frm_SL_Warranty {
            get {
                return ResourceManager.GetString("frm_SL_Warranty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Sales Employee: .
        /// </summary>
        public static string FsalesEmp {
            get {
                return ResourceManager.GetString("FsalesEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Sales Employee: All .
        /// </summary>
        public static string FsalesEmpAll {
            get {
                return ResourceManager.GetString("FsalesEmpAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Store: .
        /// </summary>
        public static string Fstore {
            get {
                return ResourceManager.GetString("Fstore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Store Name:All .
        /// </summary>
        public static string FstoreAll {
            get {
                return ResourceManager.GetString("FstoreAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  to .
        /// </summary>
        public static string FTo {
            get {
                return ResourceManager.GetString("FTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to to date: .
        /// </summary>
        public static string FtoDate {
            get {
                return ResourceManager.GetString("FtoDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  User: .
        /// </summary>
        public static string Fuser {
            get {
                return ResourceManager.GetString("Fuser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  User: All .
        /// </summary>
        public static string FuserAll {
            get {
                return ResourceManager.GetString("FuserAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor: .
        /// </summary>
        public static string Fvendor {
            get {
                return ResourceManager.GetString("Fvendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor:All .
        /// </summary>
        public static string FvendorAll {
            get {
                return ResourceManager.GetString("FvendorAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor Group:.
        /// </summary>
        public static string FvenGroup {
            get {
                return ResourceManager.GetString("FvenGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor Group : All.
        /// </summary>
        public static string FvenGroupAll {
            get {
                return ResourceManager.GetString("FvenGroupAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loss.
        /// </summary>
        public static string Loss {
            get {
                return ResourceManager.GetString("Loss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you have no privilege to open this report.
        /// </summary>
        public static string MsgPrv {
            get {
                return ResourceManager.GetString("MsgPrv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Matrix1.
        /// </summary>
        public static string mtrx1 {
            get {
                return ResourceManager.GetString("mtrx1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Matrix2.
        /// </summary>
        public static string mtrx2 {
            get {
                return ResourceManager.GetString("mtrx2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Matrix3.
        /// </summary>
        public static string mtrx3 {
            get {
                return ResourceManager.GetString("mtrx3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please set a right path to reports designs from Settings Screen..
        /// </summary>
        public static string path {
            get {
                return ResourceManager.GetString("path", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to After finishing report design, you must save it without changing report name, to folder.
        /// </summary>
        public static string path2 {
            get {
                return ResourceManager.GetString("path2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profit.
        /// </summary>
        public static string Profit {
            get {
                return ResourceManager.GetString("Profit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account&apos;s Cost Centers Balances.
        /// </summary>
        public static string rpt_Acc_Account_CostCenters {
            get {
                return ResourceManager.GetString("rpt_Acc_Account_CostCenters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Balance Sheet.
        /// </summary>
        public static string rpt_Acc_Balance {
            get {
                return ResourceManager.GetString("rpt_Acc_Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Centers Balances.
        /// </summary>
        public static string rpt_Acc_CostCenter_AccDetails {
            get {
                return ResourceManager.GetString("rpt_Acc_CostCenter_AccDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Centers Total Balances.
        /// </summary>
        public static string rpt_Acc_CostCenterTotalBalances {
            get {
                return ResourceManager.GetString("rpt_Acc_CostCenterTotalBalances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom Account Lists Total Balances.
        /// </summary>
        public static string rpt_Acc_CustomAccListBalances {
            get {
                return ResourceManager.GetString("rpt_Acc_CustomAccListBalances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cutom Accounts List Balances.
        /// </summary>
        public static string rpt_Acc_CustomAccListDetails {
            get {
                return ResourceManager.GetString("rpt_Acc_CustomAccListDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Income.
        /// </summary>
        public static string rpt_Acc_DailyIncome {
            get {
                return ResourceManager.GetString("rpt_Acc_DailyIncome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Payments.
        /// </summary>
        public static string rpt_Acc_DailyPayments {
            get {
                return ResourceManager.GetString("rpt_Acc_DailyPayments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DailyPayments And IncomePayments.
        /// </summary>
        public static string rpt_Acc_DailyPaymentsAndIncomePayments {
            get {
                return ResourceManager.GetString("rpt_Acc_DailyPaymentsAndIncomePayments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Income Statement.
        /// </summary>
        public static string rpt_Acc_Income {
            get {
                return ResourceManager.GetString("rpt_Acc_Income", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payments.
        /// </summary>
        public static string rpt_Acc_Payments {
            get {
                return ResourceManager.GetString("rpt_Acc_Payments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detailed Statement of Account.
        /// </summary>
        public static string rpt_Acc_PR_AccountDetails {
            get {
                return ResourceManager.GetString("rpt_Acc_PR_AccountDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendors Balances.
        /// </summary>
        public static string rpt_Acc_PR_AccountsBalances {
            get {
                return ResourceManager.GetString("rpt_Acc_PR_AccountsBalances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendors Balances with Commercial papers.
        /// </summary>
        public static string rpt_Acc_PR_AccountsBalancesWithNotes {
            get {
                return ResourceManager.GetString("rpt_Acc_PR_AccountsBalancesWithNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detailed Statement of Account.
        /// </summary>
        public static string rpt_Acc_SL_AccountDetails {
            get {
                return ResourceManager.GetString("rpt_Acc_SL_AccountDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customers Balances.
        /// </summary>
        public static string rpt_Acc_SL_AccountsBalances {
            get {
                return ResourceManager.GetString("rpt_Acc_SL_AccountsBalances", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customers Balances with Commercial papers.
        /// </summary>
        public static string rpt_Acc_SL_AccountsBalancesWithNotes {
            get {
                return ResourceManager.GetString("rpt_Acc_SL_AccountsBalancesWithNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sub Ledger.
        /// </summary>
        public static string rpt_ACC_SubLedger {
            get {
                return ResourceManager.GetString("rpt_ACC_SubLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DelegatesSalesReport.
        /// </summary>
        public static string rpt_DelegatesSales {
            get {
                return ResourceManager.GetString("rpt_DelegatesSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Commission for Outgoing bill items.
        /// </summary>
        public static string rpt_HR_DeliveryCommision {
            get {
                return ResourceManager.GetString("rpt_HR_DeliveryCommision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Production.
        /// </summary>
        public static string rpt_HR_ManfCommision {
            get {
                return ResourceManager.GetString("rpt_HR_ManfCommision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Invoices Direct Commisions.
        /// </summary>
        public static string rpt_HR_SalesCommision {
            get {
                return ResourceManager.GetString("rpt_HR_SalesCommision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Sales Commission per Target and Payments.
        /// </summary>
        public static string rpt_HR_SalesEmpCommission {
            get {
                return ResourceManager.GetString("rpt_HR_SalesEmpCommission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Commission for Sales Invoices items.
        /// </summary>
        public static string rpt_HR_SalesInvItemsCommision {
            get {
                return ResourceManager.GetString("rpt_HR_SalesInvItemsCommision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total In &amp; Out Item Operations.
        /// </summary>
        public static string rpt_IC_Item_In_Out_Balance {
            get {
                return ResourceManager.GetString("rpt_IC_Item_In_Out_Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items IN and Out Transactions.
        /// </summary>
        public static string rpt_IC_ItemOpenInOutClose {
            get {
                return ResourceManager.GetString("rpt_IC_ItemOpenInOutClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Total Transactions in Stores.
        /// </summary>
        public static string rpt_IC_ItemOpenInOutCloseQty {
            get {
                return ResourceManager.GetString("rpt_IC_ItemOpenInOutCloseQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Expires.
        /// </summary>
        public static string rpt_IC_ItemsExpired {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsExpired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Most Sold Items.
        /// </summary>
        public static string rpt_IC_ItemsMaxSell {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsMaxSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items On Min Level.
        /// </summary>
        public static string rpt_IC_ItemsMinLevel {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsMinLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Least Sold Items.
        /// </summary>
        public static string rpt_IC_ItemsMinSell {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsMinSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Not Sold.
        /// </summary>
        public static string rpt_IC_ItemsNotSold {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsNotSold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Balances.
        /// </summary>
        public static string rpt_IC_ItemsOpenBalance {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Balances.
        /// </summary>
        public static string rpt_IC_ItemsQty {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Balances in Details.
        /// </summary>
        public static string rpt_IC_ItemsQtyDetails {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsQtyDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory Valuation.
        /// </summary>
        public static string rpt_IC_ItemsQtyWithPrices {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsQtyWithPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory evaluation using sales price.
        /// </summary>
        public static string rpt_IC_ItemsQtyWithSalesPrice {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsQtyWithSalesPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items On Reorder.
        /// </summary>
        public static string rpt_IC_ItemsReorder {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Total Transactions.
        /// </summary>
        public static string rpt_IC_ItemsTotals {
            get {
                return ResourceManager.GetString("rpt_IC_ItemsTotals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full Item Transactions Details .
        /// </summary>
        public static string rpt_IC_ItemTransactions {
            get {
                return ResourceManager.GetString("rpt_IC_ItemTransactions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Stocktaking.
        /// </summary>
        public static string rpt_IC_ItemTransactionsDetails {
            get {
                return ResourceManager.GetString("rpt_IC_ItemTransactionsDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Transactions Details without cost.
        /// </summary>
        public static string rpt_IC_ItemTransactionsNoCost {
            get {
                return ResourceManager.GetString("rpt_IC_ItemTransactionsNoCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sold Items Cost.
        /// </summary>
        public static string rpt_IC_SoldItemsCost {
            get {
                return ResourceManager.GetString("rpt_IC_SoldItemsCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Details.
        /// </summary>
        public static string rpt_InvoiceDetails {
            get {
                return ResourceManager.GetString("rpt_InvoiceDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Changes of Items Prices .
        /// </summary>
        public static string rpt_ItemPriceChangings {
            get {
                return ResourceManager.GetString("rpt_ItemPriceChangings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Percentage Of Capital In Deferred Invoices.
        /// </summary>
        public static string rpt_PercentageOfCapitalInDeferredInvoices {
            get {
                return ResourceManager.GetString("rpt_PercentageOfCapitalInDeferredInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sample 41 taxes.
        /// </summary>
        public static string rpt_PR_InvoicesDiscountTaxHeaders {
            get {
                return ResourceManager.GetString("rpt_PR_InvoicesDiscountTaxHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Purchase Invoices.
        /// </summary>
        public static string rpt_PR_InvoicesHeaders {
            get {
                return ResourceManager.GetString("rpt_PR_InvoicesHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Purchases Qty and Balances.
        /// </summary>
        public static string rpt_PR_ItemsPurchases {
            get {
                return ResourceManager.GetString("rpt_PR_ItemsPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Purchases Return Qty and Balances.
        /// </summary>
        public static string rpt_PR_ItemsReturns {
            get {
                return ResourceManager.GetString("rpt_PR_ItemsReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Purchase Return Invoices.
        /// </summary>
        public static string rpt_PR_ReturnHeaders {
            get {
                return ResourceManager.GetString("rpt_PR_ReturnHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Purchased Items From Vendor.
        /// </summary>
        public static string rpt_PR_VendorItemsPurchases {
            get {
                return ResourceManager.GetString("rpt_PR_VendorItemsPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchases &amp; Receiving Bills.
        /// </summary>
        public static string rpt_PR_VendorItemsPurchases_InTrns {
            get {
                return ResourceManager.GetString("rpt_PR_VendorItemsPurchases_InTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Purchases Returns Items To Vendor.
        /// </summary>
        public static string rpt_PR_VendorItemsPurchasesReturns {
            get {
                return ResourceManager.GetString("rpt_PR_VendorItemsPurchasesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Sold To Customers.
        /// </summary>
        public static string rpt_SL_CustomerItemsSales {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerItemsSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales &amp; Outgoing Bills.
        /// </summary>
        public static string rpt_SL_CustomerItemsSales_OutTrns {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerItemsSales_OutTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Returned from Customers.
        /// </summary>
        public static string rpt_SL_CustomerItemsSalesReturn {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerItemsSalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales and returns items detailed.
        /// </summary>
        public static string rpt_SL_CustomerItemsSalesReturns {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerItemsSalesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Transactions.
        /// </summary>
        public static string rpt_SL_CustomerTrans {
            get {
                return ResourceManager.GetString("rpt_SL_CustomerTrans", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Officials Sales.
        /// </summary>
        public static string rpt_SL_DeliveryOfficialsSales {
            get {
                return ResourceManager.GetString("rpt_SL_DeliveryOfficialsSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date of Sales Invoices.
        /// </summary>
        public static string rpt_SL_Invoices_Due {
            get {
                return ResourceManager.GetString("rpt_SL_Invoices_Due", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Sell Invoices.
        /// </summary>
        public static string rpt_SL_InvoicesHeaders {
            get {
                return ResourceManager.GetString("rpt_SL_InvoicesHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Sales Return Qty and Balances.
        /// </summary>
        public static string rpt_SL_ItemsReturn {
            get {
                return ResourceManager.GetString("rpt_SL_ItemsReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Items Sales Qty and Balances.
        /// </summary>
        public static string rpt_SL_ItemsSales {
            get {
                return ResourceManager.GetString("rpt_SL_ItemsSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Profit/Loss.
        /// </summary>
        public static string rpt_SL_ItemTrade {
            get {
                return ResourceManager.GetString("rpt_SL_ItemTrade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SellInvoiceProfit_Loss.
        /// </summary>
        public static string rpt_SL_Profit_Loss {
            get {
                return ResourceManager.GetString("rpt_SL_Profit_Loss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Sell Return Invoices.
        /// </summary>
        public static string rpt_SL_ReturnHeaders {
            get {
                return ResourceManager.GetString("rpt_SL_ReturnHeaders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string String1 {
            get {
                return ResourceManager.GetString("String1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select account.
        /// </summary>
        public static string ValAccount {
            get {
                return ResourceManager.GetString("ValAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select cost center.
        /// </summary>
        public static string ValCostCenter {
            get {
                return ResourceManager.GetString("ValCostCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select customer.
        /// </summary>
        public static string ValCustomer {
            get {
                return ResourceManager.GetString("ValCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select custom accounts list.
        /// </summary>
        public static string ValCustomList {
            get {
                return ResourceManager.GetString("ValCustomList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select start and end dates.
        /// </summary>
        public static string ValDateFromTo {
            get {
                return ResourceManager.GetString("ValDateFromTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Item.
        /// </summary>
        public static string ValItem {
            get {
                return ResourceManager.GetString("ValItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select employee.
        /// </summary>
        public static string valSalesEmp {
            get {
                return ResourceManager.GetString("valSalesEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Store.
        /// </summary>
        public static string ValStore {
            get {
                return ResourceManager.GetString("ValStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select vendor.
        /// </summary>
        public static string ValVendor {
            get {
                return ResourceManager.GetString("ValVendor", resourceCulture);
            }
        }
    }
}
