﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="txtFrom" xml:space="preserve">
    <value> من </value>
  </data>
  <data name="txtFromDate" xml:space="preserve">
    <value> من تاريخ </value>
  </data>
  <data name="MsgNameExist" xml:space="preserve">
    <value>هذا الاسم مسجل من قبل</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>تأكد من صحة البيانات</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>معلومة</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية تعديل هذا البيان</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية اضافة بيان جديد</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>سؤال</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>تم الحفظ بنجاح</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="txtTo" xml:space="preserve">
    <value> الى </value>
  </data>
  <data name="txtToDate" xml:space="preserve">
    <value> الى تاريخ </value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>تم الحذف بنجاح</value>
  </data>
  <data name="txtOpenBalance" xml:space="preserve">
    <value> رصيد افتتاحي </value>
  </data>
  <data name="txtDrawer" xml:space="preserve">
    <value> خزينه </value>
  </data>
  <data name="txtCredit" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="txtCustomer" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="txtDebit" xml:space="preserve">
    <value>مدين</value>
  </data>
  <data name="txtVendor" xml:space="preserve">
    <value>المورد</value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>حذف صف ؟</value>
  </data>
  <data name="MsgNumExist" xml:space="preserve">
    <value>هذا الرقم مسجل من قبل</value>
  </data>
  <data name="MsgAskDel" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد حذف</value>
  </data>
  <data name="txtDealerNm" xml:space="preserve">
    <value> اسم المتعامل</value>
  </data>
  <data name="txtItemsBalance" xml:space="preserve">
    <value>أرصدة الأصناف</value>
  </data>
  <data name="txtItemsBestSell" xml:space="preserve">
    <value>الاصناف الاكثر مبيعا</value>
  </data>
  <data name="txtItemsLeastSell" xml:space="preserve">
    <value>الاصناف الاقل مبيعا</value>
  </data>
  <data name="txtItemsReorder" xml:space="preserve">
    <value>أصناف وصلت لحد الطلب</value>
  </data>
  <data name="MsgItemsPrint" xml:space="preserve">
    <value>عفواً، لايمكن طباعة اكثر من 500 صنف</value>
  </data>
  <data name="txtAssembly" xml:space="preserve">
    <value>تام</value>
  </data>
  <data name="txtCategory" xml:space="preserve">
    <value> الفئه: </value>
  </data>
  <data name="txtComapny" xml:space="preserve">
    <value>المجموعة: </value>
  </data>
  <data name="txtItem" xml:space="preserve">
    <value>الصنف:</value>
  </data>
  <data name="txtItemMovement" xml:space="preserve">
    <value>حركة الاصناف</value>
  </data>
  <data name="txtItemTotalPurchases" xml:space="preserve">
    <value>اجمالي مشتريات صنف/أصناف</value>
  </data>
  <data name="txtItemTotalPurchasesReturns" xml:space="preserve">
    <value>اجمالي مردود مشتريات صنف/أصناف</value>
  </data>
  <data name="txtItemTotalSales" xml:space="preserve">
    <value>اجمالي مبيعات صنف/أصناف</value>
  </data>
  <data name="txtItemTotalSalesReturn" xml:space="preserve">
    <value>اجمالي مردود مبيعات صنف/أصناف</value>
  </data>
  <data name="txtRaw" xml:space="preserve">
    <value>مخزني</value>
  </data>
  <data name="txtService" xml:space="preserve">
    <value>خدمه</value>
  </data>
  <data name="txtStore" xml:space="preserve">
    <value>المخزن:</value>
  </data>
  <data name="txtItemsNotSold" xml:space="preserve">
    <value>أصناف لم تباع مطلقا</value>
  </data>
  <data name="MsgCatCode" xml:space="preserve">
    <value>يرجى إدخال كود الفئة</value>
  </data>
  <data name="MsgCatName" xml:space="preserve">
    <value>يرجى إدخال اسم الفئة</value>
  </data>
  <data name="MsgCodeExist" xml:space="preserve">
    <value>هذا الكود مسجل من قبل</value>
  </data>
  <data name="MsgCompCode" xml:space="preserve">
    <value>يرجى إدخال الكود</value>
  </data>
  <data name="MsgCompName" xml:space="preserve">
    <value>يرجى إدخال الاسم</value>
  </data>
  <data name="MsgDelCat" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذه الفئه</value>
  </data>
  <data name="MsgDelCatDenied" xml:space="preserve">
    <value>عفواً، يوجد أصناف مرتبطة بهذه الفئة، لا يمكن حذف الفئة</value>
  </data>
  <data name="MsgDelComp" xml:space="preserve">
    <value>هل انت متأكد انك تريد حذف هذه المجموعة</value>
  </data>
  <data name="MsgDelCompDenied" xml:space="preserve">
    <value>عفواً، يوجد أصناف مرتبطة بهذه المجموعة، لا يمكن حذف المجموعة</value>
  </data>
  <data name="MsgDelStore" xml:space="preserve">
    <value>هل أنت متأكد انك تريد حذف هذا المخزن</value>
  </data>
  <data name="MsgFNameExist" xml:space="preserve">
    <value>الاسم الاجنبي مسجل من قبل</value>
  </data>
  <data name="MsgMainStoreDel" xml:space="preserve">
    <value>عفوا، لايمكن حذف الفرع الرئيسي</value>
  </data>
  <data name="MsgStoreCode" xml:space="preserve">
    <value>يرجى إدخال الكود</value>
  </data>
  <data name="MsgStoreItems" xml:space="preserve">
    <value>عفواً، يوجد أصناف داخل هذا المخزن, لا يمكن حذف المخزن</value>
  </data>
  <data name="MsgStoreJournals" xml:space="preserve">
    <value>يجب حذف العمليات الخاصه بهذا الفرع اولا</value>
  </data>
  <data name="MsgStoreName" xml:space="preserve">
    <value>يرجى إدخال الاسم</value>
  </data>
  <data name="MsgZeroCode" xml:space="preserve">
    <value>الكود لايمكن أن يساوي صفر</value>
  </data>
  <data name="MsgDelBarcode" xml:space="preserve">
    <value>هل تريد بالغعل حذف هذا الباركود الدولي</value>
  </data>
  <data name="MsgDelBarcode2" xml:space="preserve">
    <value>هل تريد بالغعل حذف آخر باركود دولي</value>
  </data>
  <data name="MsgDelExpenseAsk" xml:space="preserve">
    <value>حذف مصروفات؟</value>
  </data>
  <data name="MsgDelItem" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا الصنف</value>
  </data>
  <data name="MsgDelItemAsk" xml:space="preserve">
    <value>حذف صنف ؟</value>
  </data>
  <data name="MsgDelItemDenied1" xml:space="preserve">
    <value>لايمكن حذف الصنف لأنه موجود في قائمة مواد خام أصناف اخري</value>
  </data>
  <data name="MsgDelItemDenied2" xml:space="preserve">
    <value>لايمكن حذف الصنف, يوجد كميات منه بالمخازن</value>
  </data>
  <data name="MsgInterCode" xml:space="preserve">
    <value>يرجى إدخال الكود الدولي بشكل صحيح</value>
  </data>
  <data name="MsgInterCodeExist" xml:space="preserve">
    <value>هذا الكود الدولي تم استخدامه مسبقا</value>
  </data>
  <data name="ValExpenses" xml:space="preserve">
    <value>يجب كتابة المصاريف</value>
  </data>
  <data name="ValExpensesZero" xml:space="preserve">
    <value>المصروفات يجب ان تكون اكبر من الصفر</value>
  </data>
  <data name="ValTxtItemBOMDel" xml:space="preserve">
    <value>عفوا، لايمكن حذف الصنف فهو مستخدم بقوائم خامات الانتاج</value>
  </data>
  <data name="ValTxtItemCode1" xml:space="preserve">
    <value>يجب تسجيل كود1 للصنف</value>
  </data>
  <data name="ValTxtItemCode1Exist" xml:space="preserve">
    <value>يوجد صنف لديه نفس الكود 1</value>
  </data>
  <data name="ValTxtItemCode2Exist" xml:space="preserve">
    <value>يوجد صنف لديه نفس الأكواد</value>
  </data>
  <data name="ValTxtItemExist" xml:space="preserve">
    <value>الصنف موجود مسبقا</value>
  </data>
  <data name="ValTxtItemName" xml:space="preserve">
    <value>يجب تسجيل اسم الصنف</value>
  </data>
  <data name="ValTxtItemPurchasePrice" xml:space="preserve">
    <value>يجب تسجيل سعر الشراء</value>
  </data>
  <data name="ValTxtItemSalePrice" xml:space="preserve">
    <value>سعر الشراء لايمكن ان يكون اكبر من سعر بيع وحدة التجزئه</value>
  </data>
  <data name="ValTxtItemSellPrice" xml:space="preserve">
    <value>يجب تسجيل سعر بيع وحدة التجزئه</value>
  </data>
  <data name="ValTxtItemUOM1" xml:space="preserve">
    <value>لايمكن تسجيل وحدة فرعية 2 بدون تسجيل وحدة فرعية 1</value>
  </data>
  <data name="ValTxtSelectItem" xml:space="preserve">
    <value>يجب اختيار الصنف</value>
  </data>
  <data name="ValTxtSelectUOM" xml:space="preserve">
    <value>يجب اختيار وحدة القياس</value>
  </data>
  <data name="VatTxtZeroQty" xml:space="preserve">
    <value>الكمية يجب ان تكون اكبر من الصفر</value>
  </data>
  <data name="txtEditItemQty" xml:space="preserve">
    <value>الكمية الجديدة يجب أن تكون أكبر من أو تساوي صفر</value>
  </data>
  <data name="txtEditItemQty2" xml:space="preserve">
    <value>يجب تسجيل الكمية الجديدة لصنف واحد علي الأقل</value>
  </data>
  <data name="MsgDeleteInv" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد حذف السند و جميع بياناته</value>
  </data>
  <data name="MsgNoEnoughQty" xml:space="preserve">
    <value>لايوجد كميه كافيه من أحد الاصناف</value>
  </data>
  <data name="MsgNoEnoughQty_continue" xml:space="preserve">
    <value>لايوجد كميه كافيه من أحد الاصناف, هل تريد الاستمرار  ؟</value>
  </data>
  <data name="txtExceedsReorder" xml:space="preserve">
    <value>تجاوز حد الطلب هل تريد الاستمرار ؟</value>
  </data>
  <data name="txtIC_damage" xml:space="preserve">
    <value>سند هالك/ تالف </value>
  </data>
  <data name="txtIC_intrns" xml:space="preserve">
    <value>سند اضافة</value>
  </data>
  <data name="txtIC_openbalance" xml:space="preserve">
    <value>رصيد افتتاحي</value>
  </data>
  <data name="txtIC_outtrns" xml:space="preserve">
    <value>سند خصم </value>
  </data>
  <data name="txtIC_stocktaking" xml:space="preserve">
    <value>استمارة جرد</value>
  </data>
  <data name="txtIC_storemove" xml:space="preserve">
    <value>سند نقل من مخزن</value>
  </data>
  <data name="txtValidateDiscount" xml:space="preserve">
    <value>يجب تحديد الخصم</value>
  </data>
  <data name="txtValidateInvNumber" xml:space="preserve">
    <value>يجب تسجيل رقم السند</value>
  </data>
  <data name="txtValidateItem" xml:space="preserve">
    <value>يجب اختيار الصنف</value>
  </data>
  <data name="txtValidateMaxDiscount" xml:space="preserve">
    <value>نسبة الخصم لايمكن ان تتجاوز المائة</value>
  </data>
  <data name="txtValidateMinQty" xml:space="preserve">
    <value>الكمية الموجودة حاليا في المخزن بعد خصم الكمية المصروفة أقل من الحد الأدنى للكمية</value>
  </data>
  <data name="txtValidateNoRows" xml:space="preserve">
    <value>يجب تسجيل صنف واحد علي الاقل </value>
  </data>
  <data name="txtValidateQty" xml:space="preserve">
    <value>الكمية يجب ان تكون اكبر من الصفر</value>
  </data>
  <data name="txtValidateReorderQty" xml:space="preserve">
    <value>الكمية الموجودة حاليا في المخزن بعد خصم الكمية المصروفة أقل من حد الطلب</value>
  </data>
  <data name="txtValidateSPrice" xml:space="preserve">
    <value>سعر البيع يجب أن يكون أكبر من الصفر</value>
  </data>
  <data name="txtValidateSpriceLargerPprice" xml:space="preserve">
    <value>سعر البيع يجب أن يكون أكبر من سعر الشراء</value>
  </data>
  <data name="txtValidateStoreQty" xml:space="preserve">
    <value>الكمية لا يمكن ان تكون اكبر من الرصيدالحالي</value>
  </data>
  <data name="txtValidateUom" xml:space="preserve">
    <value>يجب اختيار وحدة القياس</value>
  </data>
  <data name="txt_Number" xml:space="preserve">
    <value>رقم</value>
  </data>
  <data name="txtInvDate" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="txtNotes" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="txtSerial" xml:space="preserve">
    <value>تسلسل</value>
  </data>
  <data name="txtValidateItemMaxLimit" xml:space="preserve">
    <value>الكمية المشتراه و الكمية الموجودة حاليا في المخزن أكبر من الحد الأقصى للكمية</value>
  </data>
  <data name="txtValidatePPrice" xml:space="preserve">
    <value>سعر الشراء يجب أن يكون أكبر من الصفر</value>
  </data>
  <data name="MsgItemdoesntExist" xml:space="preserve">
    <value>هذا الصنف غير موجود</value>
  </data>
  <data name="MsgstocktakingCommited" xml:space="preserve">
    <value>تم اعتماد الجرد بنجاح</value>
  </data>
  <data name="Msgstocktaking_cant_upadte" xml:space="preserve">
    <value>لا يمكن تعديل أو حذف جرد سبق اعتماده</value>
  </data>
  <data name="Msgstocktaking_commit" xml:space="preserve">
    <value>اعتماد الجرد لا يمكن التراجع عنه أو تعديله بعد ذلك, هل أنت متأكد من عملية الجرد</value>
  </data>
  <data name="Msgstocktaking_lose" xml:space="preserve">
    <value>لديك خسارة في الجرد بقيمة</value>
  </data>
  <data name="Msgstocktaking_save" xml:space="preserve">
    <value>حفظ مستند الجرد لايعني اعتماده في المخزن, لاعتماد الجرد يجب الضغط علي زر اعتماد</value>
  </data>
  <data name="Msgstocktaking_win" xml:space="preserve">
    <value>لديك ربح في الجرد بقيمة</value>
  </data>
  <data name="MsgValidateDateandStore" xml:space="preserve">
    <value>يجب ادخال التاريخ و اختيار المخزن</value>
  </data>
  <data name="Msgvalidateoutqty" xml:space="preserve">
    <value>الكمية المصروفة اكبر من الرصيد الحالي</value>
  </data>
  <data name="Msgvalidateoutqty_forbid" xml:space="preserve">
    <value>الكمية المصروفة لا يمكن ان تكون اكبر من الرصيدالحالي</value>
  </data>
  <data name="txtItemExist" xml:space="preserve">
    <value>الصنف موجود مسبقا</value>
  </data>
  <data name="txtJornalOpenBalance" xml:space="preserve">
    <value>قيد بضاعة اول المدة</value>
  </data>
  <data name="MsgValidateStore" xml:space="preserve">
    <value>يجب اختيار المخزن المحول منه و المخزن المحول اليه</value>
  </data>
  <data name="MsgValidateStore1" xml:space="preserve">
    <value>المخزن المحول منه لا يمكن ان يكون نفس المخزن المحول اليه</value>
  </data>
  <data name="CategoryCode" xml:space="preserve">
    <value>كود الفئه</value>
  </data>
  <data name="categoryFname" xml:space="preserve">
    <value>اسم الفئة ج</value>
  </data>
  <data name="categoryName" xml:space="preserve">
    <value>اسم الفئة</value>
  </data>
  <data name="CompanyCode" xml:space="preserve">
    <value>كود المجموعة</value>
  </data>
  <data name="companyFname" xml:space="preserve">
    <value>اسم المجموعة ج</value>
  </data>
  <data name="companyName" xml:space="preserve">
    <value>اسم المجموعة</value>
  </data>
  <data name="variesPerItem" xml:space="preserve">
    <value>حسب كل صنف</value>
  </data>
  <data name="FixedRatio" xml:space="preserve">
    <value>%نسبة ثابتة</value>
  </data>
  <data name="MsgDelPriceList" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف قائمة الأسعار هذه</value>
  </data>
  <data name="MsgDelPriceListDenied" xml:space="preserve">
    <value>عفواً، يوجد عملاء مرتبطين بهذه القائمة، لا يمكن حذف قائمة الأسعار</value>
  </data>
  <data name="MsgPLNAme" xml:space="preserve">
    <value>برجاء ادخال اسم قائمة الأسعار</value>
  </data>
  <data name="MsgPLRatio" xml:space="preserve">
    <value>برجاء ادخال النسبة بشكل صحيح</value>
  </data>
  <data name="PerItem" xml:space="preserve">
    <value>حسب كل صنف</value>
  </data>
  <data name="MsgDelItemDenied3" xml:space="preserve">
    <value>لايمكن حذف الصنف لأنه موجود في قائمة أسعار</value>
  </data>
  <data name="MsgDelItemDenied4" xml:space="preserve">
    <value>لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة</value>
  </data>
  <data name="msgUserDefaultStore" xml:space="preserve">
    <value>هذا المخزن مخصص كمخزن افتراضي لبعض المستخدمين</value>
  </data>
  <data name="MsgSalePerQty" xml:space="preserve">
    <value>برجاء مراجعة بيانات اسعار بيع الصنف حسب الكمية</value>
  </data>
  <data name="MsgData" xml:space="preserve">
    <value>برجاء تسجيل البيانات بشكل صحيح</value>
  </data>
  <data name="MsgMtCode" xml:space="preserve">
    <value>يجب ادخال كود المصفوفه</value>
  </data>
  <data name="MsgMtName" xml:space="preserve">
    <value>يجب ادخال اسم المصفوفه</value>
  </data>
  <data name="MsgMtRows" xml:space="preserve">
    <value>يجب ادخال بنود المصفوفه</value>
  </data>
  <data name="MsgMtrxValid" xml:space="preserve">
    <value>برجاء التأكد من إدخال البيانات بشكل صحيح</value>
  </data>
  <data name="MsgDelMtxItems" xml:space="preserve">
    <value>يجب حذف أصناف المصفوفه أولا</value>
  </data>
  <data name="MsgMtrxDelDenied" xml:space="preserve">
    <value>عفوا لايمكن حذف الصنف، يوجد أصناف من المصفوفه مستخدم من قبل</value>
  </data>
  <data name="MsgSaveItemFirst" xml:space="preserve">
    <value>برجاء حفظ الصنف اولا</value>
  </data>
  <data name="txtMatrix" xml:space="preserve">
    <value>مصفوفة</value>
  </data>
  <data name="VendorExist" xml:space="preserve">
    <value>المرود مسجل بالفعل</value>
  </data>
  <data name="MsgDelPriceListDenied1" xml:space="preserve">
    <value>عفواً، يوجد موردين مرتبطين بهذه القائمة، لا يمكن حذف قائمة الأسعار</value>
  </data>
  <data name="txtSubTotal" xml:space="preserve">
    <value>مجموع فرعي</value>
  </data>
  <data name="MsgMerchendaisingAcc" xml:space="preserve">
    <value>برجاء تحديد حسابات المتاجرة من شاشة الإعدادات</value>
  </data>
  <data name="MsgBranch" xml:space="preserve">
    <value>عفوا، عدد المستويات المسموح بها في الأفرع والمخازن مستويان فقط</value>
  </data>
  <data name="MsgBranchItems" xml:space="preserve">
    <value>عفوا، لايمكن انشاء مخزن فرعي لفرع به أرصدة مخزنية</value>
  </data>
  <data name="MsgSubStore" xml:space="preserve">
    <value>عفوا، لايمكنك إنشاء مخازن فرعية، برجاء مراجعة مدير النظام</value>
  </data>
  <data name="MsgDelMtrx" xml:space="preserve">
    <value>عفوا، لايمكن حذف المصفوفه فهي مستخدمة بالفعل</value>
  </data>
  <data name="MsgDelInvoiceBook" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف دفتر الفواتير</value>
  </data>
  <data name="delBranchDenied" xml:space="preserve">
    <value>عفوا، لايمكن حذف الفرع، يجب حذف المخازن التابعه له اولا</value>
  </data>
  <data name="storeLevel" xml:space="preserve">
    <value>عفوا، لايمكن تغيير مستوى الفرع أو المخزن</value>
  </data>
  <data name="DelItemOnMr" xml:space="preserve">
    <value>عفوا لايمكن حذف الصنف، فهو مستخدمة من خلال خطط التسويق</value>
  </data>
  <data name="MsgDimension" xml:space="preserve">
    <value>أبعاد الصنف لايمكن أن تكون أقل من أو مساوية للصفر</value>
  </data>
  <data name="MsgMtrxData" xml:space="preserve">
    <value>برجاء مراجعة بيانات الأبعاد في مصفوفة الأصناف</value>
  </data>
  <data name="StockAccs" xml:space="preserve">
    <value>يجب إختيار حسابات المخزن/الفرع</value>
  </data>
  <data name="txtIC_intrnsList" xml:space="preserve">
    <value>اذونات الاضافة</value>
  </data>
  <data name="txtIC_openbalanceList" xml:space="preserve">
    <value>ارصدة افتتاحية</value>
  </data>
  <data name="frmInTrnsList" xml:space="preserve">
    <value>اذونات الاضــافة</value>
  </data>
  <data name="txt_Code1" xml:space="preserve">
    <value>كود 1</value>
  </data>
  <data name="txt_Code2" xml:space="preserve">
    <value>كود 2</value>
  </data>
  <data name="txt_CurrentQty" xml:space="preserve">
    <value>ك حالية</value>
  </data>
  <data name="txt_Qty" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="subCat" xml:space="preserve">
    <value>عفوا لايمكن انشاء فئة فرعية، يوجد اصناف لهذه الفئة</value>
  </data>
  <data name="IcSubCat" xml:space="preserve">
    <value>يجب اختيار فئة فرعية</value>
  </data>
  <data name="ItemCodesDuplicate" xml:space="preserve">
    <value>يوجد أكواد مكررة</value>
  </data>
  <data name="ValItemCodeLength" xml:space="preserve">
    <value>كود الصنف لايمكن أن يكون اكبر من المحدد في نموذج طباعة الباركود</value>
  </data>
  <data name="SelectVendor" xml:space="preserve">
    <value>يجب إختيار المورد</value>
  </data>
  <data name="MsgOutBefore" xml:space="preserve">
    <value>لقد تم الصرف من قبل</value>
  </data>
  <data name="rpt_IC_ItemsZeroQty" xml:space="preserve">
    <value>الأصناف رصيدها صفر</value>
  </data>
  <data name="ReceiveGood" xml:space="preserve">
    <value>إذن استلام</value>
  </data>
  <data name="ReceiveGoodList" xml:space="preserve">
    <value>أذونات الاستلام</value>
  </data>
  <data name="txtIC_replacement" xml:space="preserve">
    <value>استمارة تبديل</value>
  </data>
</root>