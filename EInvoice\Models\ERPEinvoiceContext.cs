﻿using System;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Configuration;

namespace EInvoice.Models
{
    public partial class ERPEinvoiceContext : DbContext
    {
        public ERPEinvoiceContext()
        {
        }

        public ERPEinvoiceContext(DbContextOptions<ERPEinvoiceContext> options)
            : base(options)
        {
        }

        public virtual DbSet<AccAccount> AccAccount { get; set; }
        public virtual DbSet<ETaxableType> ETaxableType { get; set; }
        public virtual DbSet<HrCountry> HrCountry { get; set; }
        public virtual DbSet<HrEmployee> HrEmployee { get; set; }
        public virtual DbSet<HrUser> HrUser { get; set; }
        public virtual DbSet<IcCategory> IcCategory { get; set; }
        public virtual DbSet<IcItem> IcItem { get; set; }
        public virtual DbSet<IcStore> IcStore { get; set; }
        public virtual DbSet<IcUom> IcUom { get; set; }
        public virtual DbSet<LkpProcess> LkpProcess { get; set; }
        public virtual DbSet<SlAdd> SlAdd { get; set; }
        public virtual DbSet<SlAddDetail> SlAddDetail { get; set; }
        public virtual DbSet<SlAddDetailSubTaxValue> SlAddDetailSubTaxValue { get; set; }
        public virtual DbSet<SlCustomer> SlCustomer { get; set; }
        public virtual DbSet<SlCustomerGroup> SlCustomerGroup { get; set; }
        public virtual DbSet<SlInvoice> SlInvoice { get; set; }
        public virtual DbSet<SlInvoiceDetail> SlInvoiceDetail { get; set; }
        public virtual DbSet<SlInvoiceDetailSubTaxValue> SlInvoiceDetailSubTaxValue { get; set; }
        public virtual DbSet<SlReturn> SlReturn { get; set; }
        public virtual DbSet<SlReturnDetail> SlReturnDetail { get; set; }
        public virtual DbSet<SlReturnInvoiceDetailSubTaxValue> SlReturnInvoiceDetailSubTaxValue { get; set; }
        public virtual DbSet<StCompanyInfo> StCompanyInfo { get; set; }
        public virtual DbSet<StCurrency> StCurrency { get; set; }
        public virtual DbSet<StInvoiceBook> StInvoiceBook { get; set; }
        public virtual DbSet<StStore> StStore { get; set; }
        public virtual DbSet<StUserLog> StUserLog { get; set; }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {

            if (!optionsBuilder.IsConfigured)
            {
                //#warning To protect potentially sensitive information in your connection string, you should move it out of source code. See http://go.microsoft.com/fwlink/?LinkId=723263 for guidance on storing connection strings.
                var Configuration = new ConfigurationBuilder()
               .SetBasePath(Directory.GetCurrentDirectory())
               .AddJsonFile("appsettings.json");
                optionsBuilder.UseSqlServer(Configuration.Build().GetConnectionString("DefaultConnection"));
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasAnnotation("ProductVersion", "2.2.6-servicing-10079");

            modelBuilder.Entity<AccAccount>(entity =>
            {
                entity.HasKey(e => e.AccountId)
                    .HasName("PK_dbo.ACC_Account");

                entity.ToTable("ACC_Account");

                entity.Property(e => e.AcNumber)
                    .IsRequired()
                    .HasMaxLength(55)
                    .IsUnicode(false);

                entity.Property(e => e.Budget).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Notes).HasMaxLength(200);

                entity.Property(e => e.OldName).HasMaxLength(50);
            });

            modelBuilder.Entity<ETaxableType>(entity =>
            {
                entity.ToTable("E_TaxableType");

                entity.Property(e => e.ETaxableTypeId).HasColumnName("E_TaxableTypeId");

                entity.Property(e => e.Code).IsRequired();

                entity.Property(e => e.DescriptionAr).IsRequired();
            });

            modelBuilder.Entity<HrCountry>(entity =>
            {
                entity.HasKey(e => e.CountryId)
                    .HasName("PK_dbo.HR_Country");

                entity.ToTable("HR_Country");

                entity.Property(e => e.CountryName).HasMaxLength(200);

                entity.Property(e => e.CountryNameEn).HasMaxLength(200);

                entity.Property(e => e.Ecode)
                    .HasColumnName("ECode")
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<HrEmployee>(entity =>
            {
                entity.HasKey(e => e.EmpId)
                    .HasName("PK_dbo.HR_Employee");

                entity.ToTable("HR_Employee");

                entity.Property(e => e.Absencebenefit).HasColumnName("absencebenefit");

                entity.Property(e => e.AbsenseNoPermPenaltyDay).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.AbsenseNoPermPenaltyValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.AbsensePenaltyDay).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.AbsensePenaltyValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.AppointmentDate).HasColumnType("date");

                entity.Property(e => e.BankAccountNum).HasMaxLength(200);

                entity.Property(e => e.BankName).HasMaxLength(200);

                entity.Property(e => e.BranchId).HasColumnName("branchId");

                entity.Property(e => e.CashInvCommission).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Children)
                    .HasColumnName("children")
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CollectionMinimumR)
                    .HasColumnName("Collection_Minimum_R")
                    .HasColumnType("decimal(18, 0)");

                entity.Property(e => e.CollectionMinimumV)
                    .HasColumnName("Collection_Minimum_V")
                    .HasColumnType("decimal(18, 0)");

                entity.Property(e => e.CollectionR)
                    .HasColumnName("Collection_R")
                    .HasColumnType("decimal(18, 0)");

                entity.Property(e => e.ContractEndDate).HasColumnType("datetime");

                entity.Property(e => e.ContractPeriod).HasMaxLength(50);

                entity.Property(e => e.Day).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountInvCommision).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DrivingEndDate).HasColumnType("datetime");

                entity.Property(e => e.DrivingLicense).HasMaxLength(200);

                entity.Property(e => e.Education).HasMaxLength(100);

                entity.Property(e => e.Email)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.EmpAddress).HasMaxLength(100);

                entity.Property(e => e.EmpBirthDate).HasColumnType("date");

                entity.Property(e => e.EmpCode)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.EmpFname)
                    .HasColumnName("EmpFName")
                    .HasMaxLength(50);

                entity.Property(e => e.EmpName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.HasIncentive).HasColumnName("hasIncentive");

                entity.Property(e => e.HomeAddress).HasMaxLength(200);

                entity.Property(e => e.Hour).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.HrSubtractAcc).HasColumnName("HR_SubtractAcc");

                entity.Property(e => e.IdCardEndDate).HasColumnType("datetime");

                entity.Property(e => e.InsuranceDate).HasColumnType("datetime");

                entity.Property(e => e.InsuranceNo).HasMaxLength(200);

                entity.Property(e => e.LandLine)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.NationalId)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.OfferInvCommision).HasColumnType("decimal(18, 3)");

                entity.Property(e => e.OnCreditInvCommision).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PassportEndDate).HasColumnType("datetime");

                entity.Property(e => e.PassportNo)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.PassportStartDate).HasColumnType("datetime");

                entity.Property(e => e.Photo).HasColumnType("image");

                entity.Property(e => e.QualYear).HasColumnType("datetime");

                entity.Property(e => e.RelName).HasMaxLength(50);

                entity.Property(e => e.RelTel)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Relation).HasMaxLength(50);

                entity.Property(e => e.ResidenceEndDate).HasColumnType("datetime");

                entity.Property(e => e.ResidenceNo).HasMaxLength(200);

                entity.Property(e => e.ResidenceStartDate).HasColumnType("datetime");

                entity.Property(e => e.SalaryBasic).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalaryVariable).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesMinimumR)
                    .HasColumnName("Sales_Minimum_R")
                    .HasColumnType("decimal(18, 0)");

                entity.Property(e => e.SalesMinimumV)
                    .HasColumnName("Sales_Minimum_V")
                    .HasColumnType("decimal(18, 0)");

                entity.Property(e => e.SalesR)
                    .HasColumnName("Sales_R")
                    .HasColumnType("decimal(18, 0)");

                entity.Property(e => e.Target).HasColumnType("decimal(18, 0)");

                entity.Property(e => e.Tel)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Tel2)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WorkTel)
                    .HasMaxLength(50)
                    .IsUnicode(false);
            });

            modelBuilder.Entity<HrUser>(entity =>
            {
                entity.HasKey(e => e.UserId)
                    .HasName("PK_dbo.HR_User");

                entity.ToTable("HR_User");

                entity.Property(e => e.AccCashTransferFromDate)
                    .HasColumnName("Acc_CashTransfer_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccCashTransferToDate)
                    .HasColumnName("Acc_CashTransfer_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccCreditNoteFromDate)
                    .HasColumnName("Acc_CreditNote_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccCreditNoteToDate)
                    .HasColumnName("Acc_CreditNote_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccDebitNoteFromDate)
                    .HasColumnName("Acc_DebitNote_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccDebitNoteToDate)
                    .HasColumnName("Acc_DebitNote_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccExpFromDate)
                    .HasColumnName("Acc_Exp_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccExpToDate)
                    .HasColumnName("Acc_Exp_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccPayNoteFromDate)
                    .HasColumnName("Acc_PayNote_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccPayNoteOverdue)
                    .HasColumnName("Acc_PayNote_Overdue")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AccPayNotePaid)
                    .HasColumnName("Acc_PayNote_Paid")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AccPayNoteRejected)
                    .HasColumnName("Acc_PayNote_Rejected")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AccPayNoteStill)
                    .HasColumnName("Acc_PayNote_Still")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AccPayNoteToDate)
                    .HasColumnName("Acc_PayNote_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccRecNoteFromDate)
                    .HasColumnName("Acc_RecNote_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccRecNoteToDate)
                    .HasColumnName("Acc_RecNote_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccReceiveNoteOverdue)
                    .HasColumnName("Acc_ReceiveNote_Overdue")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AccReceiveNotePaid)
                    .HasColumnName("Acc_ReceiveNote_Paid")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AccReceiveNoteRejected)
                    .HasColumnName("Acc_ReceiveNote_Rejected")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AccReceiveNoteStill)
                    .HasColumnName("Acc_ReceiveNote_Still")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AccRevFromDate)
                    .HasColumnName("Acc_Rev_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.AccRevToDate)
                    .HasColumnName("Acc_Rev_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.ActiveNavBarGroup)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.AttClDelay)
                    .HasColumnName("att_clDelay")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AttClEmpAbsence)
                    .HasColumnName("att_clEmpAbsence")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AttClEmpVacation)
                    .HasColumnName("att_clEmpVacation")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AttClFormalVacation)
                    .HasColumnName("att_clFormalVacation")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.AttClWeekEnd)
                    .HasColumnName("att_clWeekEnd")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.CanApproveInternalRequest).HasColumnName("Can_Approve_InternalRequest");

                entity.Property(e => e.CanApprovePrOrder).HasColumnName("CanApprove_PR_Order");

                entity.Property(e => e.CanApprovePrQuote).HasColumnName("CanApprove_PR_Quote");

                entity.Property(e => e.CanApproveSlInvoices).HasColumnName("CanApproveSl_Invoices");

                entity.Property(e => e.CanSaveSlWithOldDate).HasColumnName("CanSave_SL_WithOldDate");

                entity.Property(e => e.CanSaveSlWithUpcomingDate).HasColumnName("CanSave_SL_WithUpcomingDate");

                entity.Property(e => e.CashFromDate)
                    .HasColumnName("Cash_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.CashToDate)
                    .HasColumnName("Cash_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.DefaultPrinvInvBookId).HasColumnName("DefaultPRInv_InvBookId");

                entity.Property(e => e.DefaultPrretInvBookId).HasColumnName("DefaultPRRet_InvBookId");

                entity.Property(e => e.DefaultSlinvInvBookId).HasColumnName("DefaultSLInv_InvBookId");

                entity.Property(e => e.DefaultSlretInvBookId).HasColumnName("DefaultSLRet_InvBookId");

                entity.Property(e => e.DefaultVisa).HasColumnName("defaultVisa");

                entity.Property(e => e.DueSlInvoicesAlertDays).HasColumnName("DueSL_InvoicesAlertDays");

                entity.Property(e => e.HidePo).HasColumnName("HidePO");

                entity.Property(e => e.HrAbsenceFromDate)
                    .HasColumnName("HR_Absence_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrAbsenceToDate)
                    .HasColumnName("HR_Absence_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrDelayFromDate)
                    .HasColumnName("HR_Delay_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrDelayToDate)
                    .HasColumnName("HR_Delay_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrEmployeeSalary)
                    .HasColumnName("HR_EmployeeSalary")
                    .HasDefaultValueSql("((1))");

                entity.Property(e => e.HrEvalFromDate)
                    .HasColumnName("HR_Eval_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrEvalToDate)
                    .HasColumnName("HR_Eval_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrOverTimeFromDate)
                    .HasColumnName("HR_OverTime_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrOverTimeToDate)
                    .HasColumnName("HR_OverTime_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrPayFromDate)
                    .HasColumnName("HR_Pay_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrPayToDate)
                    .HasColumnName("HR_Pay_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrPenalityFromDate)
                    .HasColumnName("HR_Penality_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrPenalityToDate)
                    .HasColumnName("HR_Penality_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrPromFromDate)
                    .HasColumnName("HR_Prom_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrPromToDate)
                    .HasColumnName("HR_Prom_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrRewardFromDate)
                    .HasColumnName("HR_Reward_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrRewardToDate)
                    .HasColumnName("HR_Reward_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrSponsrChngFromDate)
                    .HasColumnName("HR_SponsrChng_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrSponsrChngToDate)
                    .HasColumnName("HR_SponsrChng_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrTrainFromDate)
                    .HasColumnName("HR_Train_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrTrainToDate)
                    .HasColumnName("HR_Train_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrVacationFromDate)
                    .HasColumnName("HR_vacation_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.HrVacationToDate)
                    .HasColumnName("HR_vacation_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.IcDamageFromDate)
                    .HasColumnName("IC_Damage_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.IcDamageToDate)
                    .HasColumnName("IC_Damage_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.IcInTrnsFromDate)
                    .HasColumnName("IC_InTrns_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.IcInTrnsToDate)
                    .HasColumnName("IC_InTrns_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.IcOutTrnsFromDate)
                    .HasColumnName("IC_OutTrns_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.IcOutTrnsToDate)
                    .HasColumnName("IC_OutTrns_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.IcTransferFromDate)
                    .HasColumnName("IC_Transfer_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.IcTransferToDate)
                    .HasColumnName("IC_Transfer_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.InvoicesNotes).IsRequired();

                entity.Property(e => e.JoAlert)
                    .HasColumnName("JO_Alert")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.JoAlertDays)
                    .HasColumnName("JO_AlertDays")
                    .HasDefaultValueSql("((0))");

                entity.Property(e => e.JoDueFromDate)
                    .HasColumnName("JO_Due_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.JoDueToDate)
                    .HasColumnName("JO_Due_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.JoRegFromDate)
                    .HasColumnName("JO_Reg_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.JoRegToDate)
                    .HasColumnName("JO_Reg_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.JrnlFromDate)
                    .HasColumnName("Jrnl_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.JrnlToDate)
                    .HasColumnName("Jrnl_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.LoanFromDate)
                    .HasColumnName("Loan_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.LoanToDate)
                    .HasColumnName("Loan_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.ManFromDate)
                    .HasColumnName("Man_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.ManQcFromDate)
                    .HasColumnName("Man_QC_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.ManQcToDate)
                    .HasColumnName("Man_QC_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.ManToDate)
                    .HasColumnName("Man_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.MaxSalesValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.MrInDrctSlFromDate)
                    .HasColumnName("Mr_InDrctSl_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.MrInDrctSlToDate)
                    .HasColumnName("Mr_InDrctSl_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.PostJournalToGlbyDefault).HasColumnName("PostJournalToGLbyDefault");

                entity.Property(e => e.PrHidePurchasePrice).HasColumnName("PR_HidePurchasePrice");

                entity.Property(e => e.PrIFromDate)
                    .HasColumnName("PR_I_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.PrIToDate)
                    .HasColumnName("PR_I_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.PrInvoicePayMethod).HasColumnName("PR_Invoice_PayMethod");

                entity.Property(e => e.PrRFromDate)
                    .HasColumnName("PR_R_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.PrRToDate)
                    .HasColumnName("PR_R_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.PrReturnPayMethod).HasColumnName("PR_Return_PayMethod");

                entity.Property(e => e.SellShowCrntQty).HasColumnName("Sell_ShowCrntQty");

                entity.Property(e => e.ShiftReplaceFromDate)
                    .HasColumnName("ShiftReplace_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.ShiftReplaceToDate)
                    .HasColumnName("ShiftReplace_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.ShowLastPurchasePrices).HasColumnName("ShowLast_Purchase_Prices");

                entity.Property(e => e.ShowLastSellPrices).HasColumnName("ShowLast_Sell_Prices");

                entity.Property(e => e.ShowManufactureExpenses).HasDefaultValueSql("((1))");

                entity.Property(e => e.SlIFromDate)
                    .HasColumnName("SL_I_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.SlIToDate)
                    .HasColumnName("SL_I_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.SlInvoicePayMethod).HasColumnName("SL_Invoice_PayMethod");

                entity.Property(e => e.SlQFromDate)
                    .HasColumnName("SL_Q_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.SlQToDate)
                    .HasColumnName("SL_Q_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.SlRFromDate)
                    .HasColumnName("SL_R_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.SlRToDate)
                    .HasColumnName("SL_R_ToDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.SlReturnPayMethod).HasColumnName("SL_Return_PayMethod");

                entity.Property(e => e.StyleName)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.SubtotalBackcolor).HasMaxLength(20);

                entity.Property(e => e.UserCanAccessToJournalFromSameForm).HasDefaultValueSql("((0))");

                entity.Property(e => e.UserEditNotesReceivableRecipientEmp).HasColumnName("UserEditNotesReceivable_RecipientEmp");

                entity.Property(e => e.UserName).HasMaxLength(50);

                entity.Property(e => e.UserPostToGl).HasColumnName("UserPostToGL");

                entity.Property(e => e.UserShowBankOrDrawerBalanceInPayReceive).HasColumnName("UserShowBank_or_DrawerBalance_in_Pay_Receive");

                entity.Property(e => e.WeightFromDate)
                    .HasColumnName("Weight_FromDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.WeightToDate)
                    .HasColumnName("Weight_ToDate")
                    .HasColumnType("datetime");
            });

            modelBuilder.Entity<IcCategory>(entity =>
            {
                entity.HasKey(e => e.CategoryId)
                    .HasName("PK_dbo.IC_Category");

                entity.ToTable("IC_Category");

                entity.Property(e => e.CatNumber)
                    .IsRequired()
                    .HasMaxLength(55)
                    .IsUnicode(false);

                entity.Property(e => e.CategoryNameAr)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.CategoryNameEn)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.Code2)
                    .HasMaxLength(55)
                    .IsUnicode(false);

                entity.Property(e => e.Cogsacc).HasColumnName("COGSAcc");

                entity.Property(e => e.Region).HasMaxLength(50);

                entity.Property(e => e.StreetName).HasMaxLength(50);

                entity.Property(e => e.Subegion).HasMaxLength(50);
            });

            modelBuilder.Entity<IcItem>(entity =>
            {
                entity.HasKey(e => e.ItemId)
                    .HasName("PK_dbo.IC_Item");

                entity.ToTable("IC_Item");

                entity.Property(e => e.AudiencePrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CalcTaxBeforeDisc).HasColumnName("calcTaxBeforeDisc");

                entity.Property(e => e.CustomPurchasesTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomSalesTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Height).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.IsLibra).HasColumnName("is_libra");

                entity.Property(e => e.ItemCode2)
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ItemEcode)
                    .HasColumnName("ItemECode")
                    .HasMaxLength(50);

                entity.Property(e => e.ItemEtype)
                    .HasColumnName("ItemEType")
                    .HasMaxLength(50);

                entity.Property(e => e.ItemNameAr)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.ItemNameEn)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.LargeUom).HasColumnName("LargeUOM");

                entity.Property(e => e.LargeUomcode)
                    .HasColumnName("LargeUOMCode")
                    .HasMaxLength(50);

                entity.Property(e => e.LargeUomfactor)
                    .HasColumnName("LargeUOMFactor")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.LargeUomfactorDecimal)
                    .HasColumnName("LargeUOMFactorDecimal")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.LargeUomisStopped).HasColumnName("LargeUOMIsStopped");

                entity.Property(e => e.LargeUomprice)
                    .HasColumnName("LargeUOMPrice")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Length).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.MediumUom).HasColumnName("MediumUOM");

                entity.Property(e => e.MediumUomcode)
                    .HasColumnName("MediumUOMCode")
                    .HasMaxLength(50);

                entity.Property(e => e.MediumUomfactor)
                    .HasColumnName("MediumUOMFactor")
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.MediumUomfactorDecimal)
                    .HasColumnName("MediumUOMFactorDecimal")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.MediumUomisStopped).HasColumnName("MediumUOMIsStopped");

                entity.Property(e => e.MediumUomprice)
                    .HasColumnName("MediumUOMPrice")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.MtrxAttribute1).HasColumnName("mtrxAttribute1");

                entity.Property(e => e.MtrxAttribute2).HasColumnName("mtrxAttribute2");

                entity.Property(e => e.MtrxAttribute3).HasColumnName("mtrxAttribute3");

                entity.Property(e => e.MtrxCode0)
                    .HasColumnName("mtrxCode0")
                    .HasMaxLength(5)
                    .IsUnicode(false);

                entity.Property(e => e.MtrxId1).HasColumnName("mtrxId1");

                entity.Property(e => e.MtrxId2).HasColumnName("mtrxId2");

                entity.Property(e => e.MtrxId3).HasColumnName("mtrxId3");

                entity.Property(e => e.MtrxParentItem).HasColumnName("mtrxParentItem");

                entity.Property(e => e.MtrxSprtr0)
                    .HasColumnName("mtrxSprtr0")
                    .HasMaxLength(1)
                    .IsUnicode(false);

                entity.Property(e => e.MtrxSprtr1)
                    .HasColumnName("mtrxSprtr1")
                    .HasMaxLength(1)
                    .IsUnicode(false);

                entity.Property(e => e.MtrxSprtr2)
                    .HasColumnName("mtrxSprtr2")
                    .HasMaxLength(1)
                    .IsUnicode(false);

                entity.Property(e => e.MtrxSprtr3)
                    .HasColumnName("mtrxSprtr3")
                    .HasMaxLength(1)
                    .IsUnicode(false);

                entity.Property(e => e.PurchaseDiscRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PurchasePrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PurchaseTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PurchaseTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesDiscRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SmallUom).HasColumnName("SmallUOM");

                entity.Property(e => e.SmallUomisStopped).HasColumnName("SmallUOMIsStopped");

                entity.Property(e => e.SmallUomprice)
                    .HasColumnName("SmallUOMPrice")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Unitno).HasMaxLength(5);

                entity.Property(e => e.Width).HasColumnType("decimal(20, 6)");
            });

            modelBuilder.Entity<IcStore>(entity =>
            {
                entity.HasKey(e => e.StoreId)
                    .HasName("PK_dbo.IC_Store");

                entity.ToTable("IC_Store");

                entity.Property(e => e.Address)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.BuildingNumber).HasMaxLength(100);

                entity.Property(e => e.CountryId).HasDefaultValueSql("((1))");

                entity.Property(e => e.Ecode)
                    .HasColumnName("ECode")
                    .HasMaxLength(50);

                entity.Property(e => e.Governate).HasMaxLength(100);

                entity.Property(e => e.ManagerName).HasMaxLength(100);

                entity.Property(e => e.Mobile).HasMaxLength(50);

                entity.Property(e => e.PricelistId).HasColumnName("pricelistId");

                entity.Property(e => e.RegionCity).HasMaxLength(100);

                entity.Property(e => e.StoreCode)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.StoreNameAr)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.StoreNameEn)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.Street).HasMaxLength(100);

                entity.Property(e => e.Tel)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.IcStore)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_IC_Store_HR_Country");
            });

            modelBuilder.Entity<IcUom>(entity =>
            {
                entity.HasKey(e => e.Uomid)
                    .HasName("PK_dbo.IC_UOM");

                entity.ToTable("IC_UOM");

                entity.Property(e => e.Uomid).HasColumnName("UOMId");

                entity.Property(e => e.Ecode)
                    .HasColumnName("ECode")
                    .HasMaxLength(50);

                entity.Property(e => e.Uom)
                    .IsRequired()
                    .HasColumnName("UOM")
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<LkpProcess>(entity =>
            {
                entity.HasKey(e => e.ProcessId)
                    .HasName("PK_dbo.LKP_Process");

                entity.ToTable("LKP_Process");

                entity.Property(e => e.ProcessId).ValueGeneratedNever();

                entity.Property(e => e.ProcessEnglishName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.ProcessName)
                    .IsRequired()
                    .HasMaxLength(50);
            });

            modelBuilder.Entity<SlAdd>(entity =>
            {
                entity.ToTable("SL_Add");

                entity.Property(e => e.SlAddId).HasColumnName("SL_AddId");

                entity.Property(e => e.AddTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.AddTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CrncRate).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DeductTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DeductTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Destination).HasMaxLength(100);

                entity.Property(e => e.DiscountRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DriverName).HasMaxLength(100);

                entity.Property(e => e.Estatus).HasMaxLength(200);

                entity.Property(e => e.Expenses).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.HandingValue).HasColumnType("decimal(18, 0)");

                entity.Property(e => e.IsInTrans).HasColumnName("Is_InTrans");

                entity.Property(e => e.IssuerId).HasColumnName("issuerId");

                entity.Property(e => e.LastSyncDate)
                    .HasColumnName("lastSyncDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.LastUpdateDate).HasColumnType("datetime");

                entity.Property(e => e.LongId).HasColumnName("longId");

                entity.Property(e => e.Net).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Paid).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PayAcc2Paid)
                    .HasColumnName("PayAcc2_Paid")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Remains).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.ReturnCode)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.ReturnDate).HasColumnType("datetime");

                entity.Property(e => e.ScaleWeightSerial).HasMaxLength(50);

                entity.Property(e => e.SyncDate)
                    .HasColumnName("syncDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.TaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Uuid).HasColumnName("uuid");

                entity.Property(e => e.VehicleNumber).HasMaxLength(100);
            });

            modelBuilder.Entity<SlAddDetail>(entity =>
            {
                entity.ToTable("SL_Add_Detail");

                entity.Property(e => e.SlAddDetailId).HasColumnName("SL_Add_DetailId");

                entity.Property(e => e.BonusDiscount)
                    .HasColumnName("bonusDiscount")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTax).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountRatio2).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountRatio3).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Expire).HasColumnType("date");

                entity.Property(e => e.Height).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.KgWeightLibra)
                    .HasColumnName("kg_Weight_libra")
                    .HasColumnType("decimal(18, 8)");

                entity.Property(e => e.Length).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.LibraQty).HasColumnType("decimal(18, 8)");

                entity.Property(e => e.ManufactureDate).HasColumnType("datetime");

                entity.Property(e => e.PiecesCount).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PurchasePrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Qty).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesTax).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SellPrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SlAddId).HasColumnName("SL_AddId");

                entity.Property(e => e.TotalSellPrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Uomid).HasColumnName("UOMId");

                entity.Property(e => e.Uomindex).HasColumnName("UOMIndex");

                entity.Property(e => e.Width).HasColumnType("decimal(20, 6)");

                entity.HasOne(d => d.SlAdd)
                    .WithMany(p => p.SlAddDetail)
                    .HasForeignKey(d => d.SlAddId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("SL_Add_SL_Add_Detail");
            });

            modelBuilder.Entity<SlAddDetailSubTaxValue>(entity =>
            {
                entity.ToTable("Sl_Add_DetailSubTaxValue");

                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.EsubTypeId).HasColumnName("esubTypeId");

                entity.Property(e => e.SlAddDetailId).HasColumnName("Sl_AddDetailId");

                entity.Property(e => e.TaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Value)
                    .HasColumnName("value")
                    .HasColumnType("decimal(20, 6)");
            });

            modelBuilder.Entity<SlCustomer>(entity =>
            {
                entity.HasKey(e => e.CustomerId)
                    .HasName("PK_dbo.SL_Customer");

                entity.ToTable("SL_Customer");

                entity.Property(e => e.Address).HasMaxLength(200);

                entity.Property(e => e.BankAccNum).HasMaxLength(200);

                entity.Property(e => e.BankName).HasMaxLength(200);

                entity.Property(e => e.BuildingNumber).HasMaxLength(100);

                entity.Property(e => e.City).HasMaxLength(50);

                entity.Property(e => e.CountryId).HasDefaultValueSql("((1))");

                entity.Property(e => e.CsType).HasColumnName("csType");

                entity.Property(e => e.DiscountRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Email).HasMaxLength(50);

                entity.Property(e => e.Fax).HasMaxLength(50);

                entity.Property(e => e.Governate).HasMaxLength(100);

                entity.Property(e => e.IdNumber).HasMaxLength(50);

                entity.Property(e => e.IsActive).HasColumnName("Is_Active");

                entity.Property(e => e.IsBlocked).HasColumnName("Is_Blocked");

                entity.Property(e => e.Manager).HasMaxLength(50);

                entity.Property(e => e.MaxCredit).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Mobile).HasMaxLength(50);

                entity.Property(e => e.RepFjob)
                    .HasColumnName("RepFJob")
                    .HasMaxLength(200);

                entity.Property(e => e.RepFname)
                    .HasColumnName("RepFNAme")
                    .HasMaxLength(200);

                entity.Property(e => e.RepId)
                    .HasColumnName("Rep_ID")
                    .HasMaxLength(200);

                entity.Property(e => e.RepMobile)
                    .HasColumnName("Rep_Mobile")
                    .HasMaxLength(200);

                entity.Property(e => e.Representative).HasMaxLength(200);

                entity.Property(e => e.RepresentativeJob)
                    .HasColumnName("Representative_Job")
                    .HasMaxLength(200);

                entity.Property(e => e.Shipping).HasMaxLength(200);

                entity.Property(e => e.TaxCardNumber).HasMaxLength(50);

                entity.Property(e => e.TaxDepartment).HasMaxLength(50);

                entity.Property(e => e.TaxFileNumber).HasMaxLength(50);

                entity.Property(e => e.Tel).HasMaxLength(50);

                entity.Property(e => e.TradeRegistry).HasMaxLength(50);

                entity.Property(e => e.Zip).HasMaxLength(50);

                entity.HasOne(d => d.Country)
                    .WithMany(p => p.SlCustomer)
                    .HasForeignKey(d => d.CountryId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SL_Customer_HR_Country");
            });

            modelBuilder.Entity<SlCustomerGroup>(entity =>
            {
                entity.HasKey(e => e.CustomerGroupId)
                    .HasName("PK_dbo.SL_CustomerGroup");

                entity.ToTable("SL_CustomerGroup");

                entity.Property(e => e.CgnameAr)
                    .IsRequired()
                    .HasColumnName("CGNameAr")
                    .HasMaxLength(100);

                entity.Property(e => e.CgnameEn)
                    .IsRequired()
                    .HasColumnName("CGNameEn")
                    .HasMaxLength(100);

                entity.Property(e => e.CustomerGroupCode)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.Desc)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.MaxCredit).HasColumnType("decimal(20, 6)");
            });

            modelBuilder.Entity<SlInvoice>(entity =>
            {
                entity.ToTable("SL_Invoice");

                entity.Property(e => e.SlInvoiceId).HasColumnName("SL_InvoiceId");

                entity.Property(e => e.AddTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.AddTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.AdvancePaymentRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.AdvancePaymentValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.ApprovedInvoice).HasColumnName("Approved_Invoice");

                entity.Property(e => e.ApprovedUserId).HasColumnName("Approved_UserId");

                entity.Property(e => e.AttnMr).HasMaxLength(200);

                entity.Property(e => e.CrncRate).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DeductTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DeductTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DeliverDate).HasColumnType("datetime");

                entity.Property(e => e.Destination).HasMaxLength(100);

                entity.Property(e => e.DiscountRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DriverName).HasMaxLength(100);

                entity.Property(e => e.DueDate).HasColumnType("datetime");

                entity.Property(e => e.Estatus).HasMaxLength(200);

                entity.Property(e => e.Expenses).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.ExpensesRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.HandingValue).HasColumnType("decimal(18, 8)");

                entity.Property(e => e.InvoiceCode)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.InvoiceDate).HasColumnType("datetime");

                entity.Property(e => e.IsOutTrans).HasColumnName("Is_OutTrans");

                entity.Property(e => e.IsPosted).HasColumnName("Is_Posted");

                entity.Property(e => e.IsStoreForEachRow).HasColumnName("Is_StoreForEachRow");

                entity.Property(e => e.IssuerId).HasColumnName("issuerId");

                entity.Property(e => e.LastSyncDate)
                    .HasColumnName("lastSyncDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.LastUpdateDate).HasColumnType("datetime");

                entity.Property(e => e.LongId).HasColumnName("longId");

                entity.Property(e => e.Net).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Paid).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PayAcc2Paid)
                    .HasColumnName("PayAcc2_Paid")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PostDate).HasColumnType("datetime");

                entity.Property(e => e.Remains).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.RetentionRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.RetentionValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.ScaleWeightSerial).HasMaxLength(50);

                entity.Property(e => e.ShiftAdd).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SyncDate)
                    .HasColumnName("syncDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.TaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.TotalCostPrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.TransportationValue).HasColumnType("decimal(18, 8)");

                entity.Property(e => e.Uuid).HasColumnName("uuid");

                entity.Property(e => e.VehicleNumber).HasMaxLength(100);
            });

            modelBuilder.Entity<SlInvoiceDetail>(entity =>
            {
                entity.ToTable("SL_InvoiceDetail");

                entity.Property(e => e.SlInvoiceDetailId).HasColumnName("SL_InvoiceDetailId");

                entity.Property(e => e.BonusDiscount)
                    .HasColumnName("bonusDiscount")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CostPrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTax).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountRatio2).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountRatio3).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Expire).HasColumnType("date");

                entity.Property(e => e.Height).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.KgWeightLibra)
                    .HasColumnName("kg_Weight_libra")
                    .HasColumnType("decimal(18, 8)");

                entity.Property(e => e.Length).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.LibraQty).HasColumnType("decimal(18, 3)");

                entity.Property(e => e.ManufactureDate).HasColumnType("datetime");

                entity.Property(e => e.PiecesCount).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Qc)
                    .HasColumnName("QC")
                    .HasMaxLength(50);

                entity.Property(e => e.Qty).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesTax).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SellPrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SlInvoiceId).HasColumnName("SL_InvoiceId");

                entity.Property(e => e.TotalSellPrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Uomid).HasColumnName("UOMId");

                entity.Property(e => e.Uomindex).HasColumnName("UOMIndex");

                entity.Property(e => e.Width).HasColumnType("decimal(20, 6)");

                entity.HasOne(d => d.SlInvoice)
                    .WithMany(p => p.SlInvoiceDetail)
                    .HasForeignKey(d => d.SlInvoiceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("SL_Invoice_SL_InvoiceDetail");
            });

            modelBuilder.Entity<SlInvoiceDetailSubTaxValue>(entity =>
            {
                entity.HasKey(e => e.InvoicedetailSubTaxId)
                    .HasName("PK__SL_Invoi__4F37B8E54694B9B4");

                entity.ToTable("SL_InvoiceDetailSubTaxValue");

                entity.Property(e => e.InvoicedetailSubTaxId).HasColumnName("invoicedetailSubTaxId");

                entity.Property(e => e.EsubTypeId).HasColumnName("esubTypeId");

                entity.Property(e => e.TaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Value)
                    .HasColumnName("value")
                    .HasColumnType("decimal(20, 6)");
            });

            modelBuilder.Entity<SlReturn>(entity =>
            {
                entity.ToTable("SL_Return");

                entity.Property(e => e.SlReturnId).HasColumnName("SL_ReturnId");

                entity.Property(e => e.AddTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.AddTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CrncRate).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DeductTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DeductTaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Destination).HasMaxLength(100);

                entity.Property(e => e.DiscountRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DriverName).HasMaxLength(100);

                entity.Property(e => e.Estatus).HasMaxLength(200);

                entity.Property(e => e.Expenses).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.HandingValue).HasColumnType("decimal(18, 0)");

                entity.Property(e => e.IsInTrans).HasColumnName("Is_InTrans");

                entity.Property(e => e.IssuerId).HasColumnName("issuerId");

                entity.Property(e => e.LastSyncDate)
                    .HasColumnName("lastSyncDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.LastUpdateDate).HasColumnType("datetime");

                entity.Property(e => e.LongId).HasColumnName("longId");

                entity.Property(e => e.Net).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Paid).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PayAcc2Paid)
                    .HasColumnName("PayAcc2_Paid")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Remains).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.ReturnCode)
                    .IsRequired()
                    .HasMaxLength(20)
                    .IsUnicode(false);

                entity.Property(e => e.ReturnDate).HasColumnType("datetime");

                entity.Property(e => e.ScaleWeightSerial).HasMaxLength(50);

                entity.Property(e => e.SyncDate)
                    .HasColumnName("syncDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.TaxValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Uuid).HasColumnName("uuid");

                entity.Property(e => e.VehicleNumber).HasMaxLength(100);
            });

            modelBuilder.Entity<SlReturnDetail>(entity =>
            {
                entity.ToTable("SL_ReturnDetail");

                entity.Property(e => e.SlReturnDetailId).HasColumnName("SL_ReturnDetailId");

                entity.Property(e => e.BonusDiscount)
                    .HasColumnName("bonusDiscount")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTax).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CustomTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountRatio2).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountRatio3).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.DiscountValue).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Expire).HasColumnType("date");

                entity.Property(e => e.Height).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.KgWeightLibra)
                    .HasColumnName("kg_Weight_libra")
                    .HasColumnType("decimal(18, 8)");

                entity.Property(e => e.Length).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.LibraQty).HasColumnType("decimal(18, 8)");

                entity.Property(e => e.ManufactureDate).HasColumnType("datetime");

                entity.Property(e => e.PiecesCount).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.PurchasePrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Qty).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesTax).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SalesTaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SellPrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.SlReturnId).HasColumnName("SL_ReturnId");

                entity.Property(e => e.TotalSellPrice).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Uomid).HasColumnName("UOMId");

                entity.Property(e => e.Uomindex).HasColumnName("UOMIndex");

                entity.Property(e => e.Width).HasColumnType("decimal(20, 6)");

                entity.HasOne(d => d.SlReturn)
                    .WithMany(p => p.SlReturnDetail)
                    .HasForeignKey(d => d.SlReturnId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("SL_Return_SL_ReturnDetail");
            });

            modelBuilder.Entity<SlReturnInvoiceDetailSubTaxValue>(entity =>
            {
                entity.Property(e => e.Id).HasColumnName("id");

                entity.Property(e => e.EsubTypeId).HasColumnName("esubTypeId");

                entity.Property(e => e.TaxRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.Value)
                    .HasColumnName("value")
                    .HasColumnType("decimal(20, 6)");
            });

            modelBuilder.Entity<StCompanyInfo>(entity =>
            {
                entity.HasKey(e => e.CompanyId)
                    .HasName("PK_dbo.ST_CompanyInfo");

                entity.ToTable("ST_CompanyInfo");

                entity.Property(e => e.CompanyId).HasColumnName("Company_Id");

                entity.Property(e => e.Accounting).IsUnicode(false);

                entity.Property(e => e.ActivityType).HasMaxLength(200);

                entity.Property(e => e.BackupPath).IsRequired();

                entity.Property(e => e.CertificateCompanyType).HasDefaultValueSql("((1))");

                entity.Property(e => e.Checks).IsUnicode(false);

                entity.Property(e => e.CmpAddress)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.CmpCity)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CmpCountry)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CmpMobile)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CmpNameAr)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CmpNameEn)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.CmpTel)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.CommercialBook)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.Contract).IsUnicode(false);

                entity.Property(e => e.Currencies).IsUnicode(false);

                entity.Property(e => e.DonglePin).HasColumnName("DonglePIN");

               // entity.Property(e => e.EInvoiceAvailable).HasColumnName("E_invoiceAvailable");

                entity.Property(e => e.EgyptPharmacyTax).IsUnicode(false);

                entity.Property(e => e.Fa)
                    .HasColumnName("FA")
                    .HasMaxLength(200);

                entity.Property(e => e.FiscalYearEndDate).HasColumnType("datetime");

                entity.Property(e => e.FiscalYearStartDate).HasColumnType("datetime");

                entity.Property(e => e.Hr)
                    .HasColumnName("HR")
                    .IsUnicode(false);

                entity.Property(e => e.Iban).HasMaxLength(200);

                entity.Property(e => e.ImpExp)
                    .HasColumnName("Imp_exp")
                    .HasMaxLength(200);

                entity.Property(e => e.Inventory).IsUnicode(false);

                entity.Property(e => e.InvoiceDateValidationDays).HasColumnName("InvoiceDate_ValidationDays");

                entity.Property(e => e.InvoicePostToStore).IsUnicode(false);

                entity.Property(e => e.Ipaddresses)
                    .HasColumnName("IPAddresses")
                    .IsUnicode(false);

                entity.Property(e => e.IsLaundry).HasColumnName("Is_Laundry");

                entity.Property(e => e.ItemMatrix).IsUnicode(false);

                entity.Property(e => e.ItemsPosting).IsUnicode(false);

                entity.Property(e => e.JobOrder).IsUnicode(false);

                entity.Property(e => e.LetterOfCredit).IsUnicode(false);

                entity.Property(e => e.Logo).HasColumnType("image");

                entity.Property(e => e.Mall).HasMaxLength(200);

                entity.Property(e => e.Manufacturing).IsUnicode(false);

                entity.Property(e => e.Marketting).IsUnicode(false);

                entity.Property(e => e.MngrAdress)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.MngrMobile)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.MngrName)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.MngrTel)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.ModelGlobalPos)
                    .IsRequired()
                    .HasColumnName("ModelGlobalPOS");

                entity.Property(e => e.OfflinePostToGl)
                    .HasColumnName("OfflinePostToGL")
                    .IsUnicode(false);

                entity.Property(e => e.PayRec)
                    .HasColumnName("Pay_Rec")
                    .HasMaxLength(200);

                entity.Property(e => e.Payslip).IsUnicode(false);

                entity.Property(e => e.Pin).HasColumnName("PIn");

                entity.Property(e => e.Pos)
                    .HasColumnName("POS")
                    .IsUnicode(false);

                entity.Property(e => e.PrInvoicePostToStore).IsUnicode(false);

                entity.Property(e => e.PriceList).IsUnicode(false);

                entity.Property(e => e.Processes).IsUnicode(false);

                entity.Property(e => e.RealState).IsUnicode(false);

                entity.Property(e => e.SalesOrder).IsUnicode(false);

                entity.Property(e => e.ServerName)
                    .HasMaxLength(15)
                    .IsUnicode(false);

                entity.Property(e => e.Shareholder).HasMaxLength(200);

                entity.Property(e => e.StartDate)
                    .HasColumnName("startDate")
                    .HasColumnType("datetime");

                entity.Property(e => e.Tax).IsUnicode(false);

                entity.Property(e => e.TaxCard)
                    .IsRequired()
                    .HasMaxLength(50)
                    .IsUnicode(false);

                entity.Property(e => e.WoodTrade).IsUnicode(false);
               
            });

            modelBuilder.Entity<StCurrency>(entity =>
            {
                entity.HasKey(e => e.CrncId)
                    .HasName("PK_dbo.ST_Currency");

                entity.ToTable("ST_Currency");

                entity.Property(e => e.CrncName)
                    .IsRequired()
                    .HasColumnName("crncName")
                    .HasMaxLength(50);

                entity.Property(e => e.CurrencyPiaster1).HasMaxLength(50);

                entity.Property(e => e.CurrencyPiaster2).HasMaxLength(50);

                entity.Property(e => e.CurrencyPiaster3).HasMaxLength(50);

                entity.Property(e => e.CurrencyPound1).HasMaxLength(50);

                entity.Property(e => e.CurrencyPound2).HasMaxLength(50);

                entity.Property(e => e.CurrencyPound3).HasMaxLength(50);

                entity.Property(e => e.Ecode)
                    .HasColumnName("ECode")
                    .HasMaxLength(50);

                entity.Property(e => e.LastRate).HasColumnType("decimal(20, 6)");
            });

            modelBuilder.Entity<StInvoiceBook>(entity =>
            {
                entity.HasKey(e => e.InvoiceBookId)
                    .HasName("PK_dbo.ST_InvoiceBook");

                entity.ToTable("ST_InvoiceBook");

                entity.Property(e => e.InvoiceBookName)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.PrintFileName).HasMaxLength(200);
            });

            modelBuilder.Entity<StStore>(entity =>
            {
                entity.ToTable("ST_Store");

                entity.Property(e => e.StStoreId).HasColumnName("ST_Store_Id");

                entity.Property(e => e.AccAbsenceAcount).HasColumnName("ACC_AbsenceAcount");

                entity.Property(e => e.AccPenaltyAcount).HasColumnName("ACC_PenaltyAcount");

                entity.Property(e => e.AccVacationAcount).HasColumnName("ACC_VacationAcount");

                entity.Property(e => e.BarcodePrefix)
                    .HasMaxLength(2)
                    .IsUnicode(false);

                entity.Property(e => e.BcIdentifier).HasColumnName("bcIdentifier");

                entity.Property(e => e.BcItemCount).HasColumnName("bcItemCount");

                entity.Property(e => e.BcScaleCount).HasColumnName("bcScaleCount");

                entity.Property(e => e.BcScalePrice).HasColumnName("bcScalePrice");

                entity.Property(e => e.ChAuthorize).HasColumnName("ch_Authorize");

                entity.Property(e => e.ChkCsTypeValidation).HasColumnName("chk_CsTypeValidation");

                entity.Property(e => e.ClosePeriodDate).HasColumnType("date");

                entity.Property(e => e.CompanyShare)
                    .HasColumnName("companyShare")
                    .HasColumnType("decimal(20, 6)");

                entity.Property(e => e.CurrencyPiaster1).HasMaxLength(50);

                entity.Property(e => e.CurrencyPiaster2).HasMaxLength(50);

                entity.Property(e => e.CurrencyPiaster3).HasMaxLength(50);

                entity.Property(e => e.CurrencyPound1).HasMaxLength(50);

                entity.Property(e => e.CurrencyPound2).HasMaxLength(50);

                entity.Property(e => e.CurrencyPound3).HasMaxLength(50);

                entity.Property(e => e.DocumentThreshold).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.EAllowMoreThanTax).HasColumnName("E_AllowMoreThanTax");

                entity.Property(e => e.EmpShare).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.ExcludeLeavesNGl).HasColumnName("ExcludeLeavesN_GL");

                entity.Property(e => e.ExemptionRatio).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.GroupPositems).HasColumnName("GroupPOSItems");

                entity.Property(e => e.GulfHravailable).HasColumnName("GulfHRAvailable");

                entity.Property(e => e.HasChkSum).HasColumnName("hasChkSum");

                entity.Property(e => e.HrSubtractAcc).HasColumnName("HR_SubtractAcc");

                entity.Property(e => e.IntermediateInventoryAcc).HasColumnName("intermediateInventoryAcc");

                entity.Property(e => e.IntermediatePrInventoryAcc).HasColumnName("intermediate_PR_InventoryAcc");

                entity.Property(e => e.IsFutureDueDate).HasColumnName("isFutureDueDate");

                entity.Property(e => e.IsPrice).HasColumnName("isPrice");

                entity.Property(e => e.KgPrFactor)
                    .HasColumnName("KG_PR_Factor")
                    .HasMaxLength(50);

                entity.Property(e => e.KgPrUomId).HasColumnName("KG_PR_UOM_Id");

                entity.Property(e => e.KgSlFactor)
                    .HasColumnName("KG_SL_Factor")
                    .HasMaxLength(50);

                entity.Property(e => e.KgSlUomId).HasColumnName("KG_SL_UOM_Id");

                entity.Property(e => e.LastEvaluationDate).HasColumnType("datetime");

                entity.Property(e => e.LibraUomId).HasColumnName("Libra_UOM_Id");

                entity.Property(e => e.MaxInsureSalary).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.MinInsureSalary).HasColumnType("decimal(20, 6)");

                entity.Property(e => e.MtrxColor).HasColumnName("Mtrx_Color");

                entity.Property(e => e.MtrxGeir).HasColumnName("Mtrx_Geir");

                entity.Property(e => e.MtrxLiter).HasColumnName("Mtrx_Liter");

                entity.Property(e => e.NetSalaryTax).HasColumnType("decimal(18, 6)");

                entity.Property(e => e.PiecesCountNameAr)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.PiecesCountNameEn)
                    .IsRequired()
                    .HasMaxLength(200);

                entity.Property(e => e.ReturnCostAcc).HasColumnName("ReturnCostACC");

                entity.Property(e => e.SalesOrderforClient).HasDefaultValueSql("((0))");

                entity.Property(e => e.Serial2NameAr).IsRequired();

                entity.Property(e => e.Serial2NameEn).IsRequired();

                entity.Property(e => e.SlInvoiceMustBApproved).HasColumnName("SlInvoice_mustB_Approved");

                entity.Property(e => e.TotalMallSize).HasColumnType("decimal(18, 6)");

                entity.Property(e => e.TotalMonthDaysHr).HasColumnName("TotalMonthDays_HR");

                entity.Property(e => e.UseLastCostPrice).HasDefaultValueSql("((0))");

                entity.Property(e => e.UseQc).HasColumnName("UseQC");
            });

            modelBuilder.Entity<StUserLog>(entity =>
            {
                entity.HasKey(e => e.UserLogId)
                    .HasName("PK_dbo.ST_UserLog");

                entity.ToTable("ST_UserLog");

                entity.Property(e => e.ActionDate).HasColumnType("datetime");

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(e => e.NotesAr).IsRequired();
            });
        }
    }
}
