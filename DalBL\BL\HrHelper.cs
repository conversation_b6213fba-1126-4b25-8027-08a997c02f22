﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using DAL.Res;

namespace DAL
{
    public class HrHelper
    {

        public static List<HR_Dept> GetDepts()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            List<HR_Dept> depts = DB.HR_Depts.Select(d => d).ToList();

            List<HR_Dept> lst = new List<HR_Dept>();

            foreach (var d in depts)
            {
                if (depts.Where(c => c.ParentDeptId == d.DeptId).Count() > 0)
                    continue;


                lst.Add(
                    new HR_Dept
                    {
                        DeptId = d.DeptId,
                        DeptNameAr = d.ParentDeptId.HasValue ?
                        depts.Where(c => c.DeptId == d.ParentDeptId).First().DeptNameAr + " - " + d.DeptNameAr
                        : d.DeptNameAr,

                        DeptNameEn = d.DeptNameEn,
                        ManagerName = d.ManagerName,
                        Notes = d.Notes,
                    });
            }

            return lst;
        }

        public static List<HR_WorkEntity> GetWorkEntities()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            List<HR_WorkEntity> depts = DB.HR_WorkEntities.Select(d => d).ToList();

            List<HR_WorkEntity> lst = new List<HR_WorkEntity>();

            foreach (var d in depts)
            {
                if (depts.Where(c => c.ParentEntId == d.Id).Count() > 0)
                    continue;


                lst.Add(
                    new HR_WorkEntity
                    {
                        Id = d.Id,
                        EntNameAr = d.ParentEntId.HasValue ?
                        depts.Where(c => c.Id == d.ParentEntId).First().EntNameAr + " - " + d.EntNameAr
                        : d.EntNameAr,

                        EntNameEn = d.EntNameEn,
                        ManagerName = d.ManagerName,
                        Notes = d.Notes,
                    });
            }

            return lst;
        }

        /// <summary>
        /// Load DataTable with Empolyees
        /// </summary>
        /// <param name="EmpsDataTable">Employees DataTable</param>
        /// <param name="empState">if null, it gets all employees</param>
        /// <returns>return first EmployeeId</returns>
        public static int GetEmployees(DataTable EmpsDataTable, bool? empState)
        {
            if (EmpsDataTable.Columns.Count < 1)
            {
                EmpsDataTable.Columns.Add("EmpId");
                EmpsDataTable.Columns.Add("EmpName");
                EmpsDataTable.Columns.Add("PayPeriod");
                EmpsDataTable.Columns.Add("Age");
                EmpsDataTable.Columns.Add("JobName");
                EmpsDataTable.Columns.Add("DeptName");
                EmpsDataTable.Columns.Add("AccountId");
                EmpsDataTable.Columns.Add("EmpCode");
                EmpsDataTable.Columns.Add("DeptId");
                EmpsDataTable.Columns.Add("JobId");
                EmpsDataTable.Columns.Add("SponsorId");
                EmpsDataTable.Columns.Add("Day");// used in penalty and reward by day
                EmpsDataTable.Columns.Add("DelayRuleId");
                EmpsDataTable.Columns.Add("OverTimeRuleId");
                EmpsDataTable.Columns.Add("EmpFName");
                EmpsDataTable.Columns.Add("GroupId");
            }
            ERPDataContext DB = new ERPDataContext();
            var empsQuery = (from m in DB.HR_Employees
                             join j in DB.HR_Jobs
                             on m.JobId equals j.JobId
                             join d in DB.HR_Depts
                             on m.DeptId equals d.DeptId
                             where empState != null ? m.EmpState == empState : true
                             select new
                             {
                                 m.EmpId,
                                 m.EmpCode,
                                 m.EmpName,
                                 PayPeriod = m.PayPeriod == 0 ?
                                 (Shared.IsEnglish == true ? ResEn.Hourly : ResAr.Hourly)//"بالساعه" 
                                 : m.PayPeriod == 1 ?
                                 (Shared.IsEnglish == true ? ResEn.Weekly : ResAr.Weekly)//"بالاسبوع" 
                                 : m.PayPeriod == 2 ? (Shared.IsEnglish == true ? ResEn.Fortnightly : ResAr.Fortnightly)//"كل اسبوعين" 
                                 : m.PayPeriod == 3 ? (Shared.IsEnglish == true ? ResEn.Monthly : ResAr.Monthly)//"بالشهر" 
                                 : m.PayPeriod == 5 ? (Shared.IsEnglish == true ? ResEn.Daily : ResAr.Daily)//"باليوم" 
                                 : (Shared.IsEnglish == true ? ResEn.Pertask : ResAr.Pertask)//"بالقطعه"
                                 ,
                                 m.EmpBirthDate,
                                 JobName = j.JobNameAr,
                                 DeptName = d.DeptNameAr,
                                 AccountId = m.AccountId,
                                 m.DeptId,
                                 m.JobId,
                                 SponsorId = m.SponsorId,
                                 m.Day,
                                 m.DelayRuleId,
                                 m.OverTimeRuleId,
                                 m.EmpFName,
                                 m.GroupId
                             }).OrderBy(e => e.DeptId).ThenBy(e => e.EmpCode).ToList();

            int count = 0;
            int dfltEmp = 0;
            string age = string.Empty;

            if (empsQuery.Count() > 0)
            {
                foreach (var emp in empsQuery)
                {
                    if (count == 0)
                        dfltEmp = emp.EmpId;

                    age = emp.EmpBirthDate.HasValue ? CalcAgeInDetail(emp.EmpBirthDate.Value.Date) : string.Empty;
                    EmpsDataTable.Rows.Add(
                        emp.EmpId,
                        emp.EmpName,
                        emp.PayPeriod,
                        age,
                        emp.JobName,
                        emp.DeptName,
                        emp.AccountId,
                        emp.EmpCode,
                        emp.DeptId,
                        emp.JobId,
                        emp.SponsorId,
                        emp.Day,
                        emp.DelayRuleId,
                        emp.OverTimeRuleId,
                        emp.EmpFName);

                    count++;
                }
            }
            return dfltEmp;
        }

        public static string CalcAgeInDetail(DateTime birthdate)
        {
            string strAge = "";
            int days = MyHelper.Get_Server_DateTime().Subtract(birthdate).Days;

            if (days < 60)//less than two months
                strAge = days.ToString() + "يوم";
            else if (days > 60 && days < 365)
            {
                decimal months = days / 30;
                strAge = Math.Round(months).ToString() + "شهر";
            }
            else
            {
                TimeSpan t = TimeSpan.FromDays(days);
                DateTime ffd = DateTime.Now.Subtract(t);
                decimal years = days / 365;
                decimal months = (days % 365) / 30;
                strAge = Math.Round(years).ToString() + "," + Math.Round(months).ToString() + "سنه";
            }
            return strAge;
        }

        /// <summary>
        /// return true if the day is a weekend
        /// </summary>
        /// <param name="day"></param>
        /// <param name="Sat"></param>
        /// <param name="Sun"></param>
        /// <param name="Mon"></param>
        /// <param name="Tus"></param>
        /// <param name="Wed"></param>
        /// <param name="Thu"></param>
        /// <param name="Fri"></param>
        /// <returns></returns>
        public static bool IsWeekend(DateTime day, int shiftId, List<EmpShift> lst_EmpShift)
        {
            List<EmpShift> thisShift = lst_EmpShift.Where(x => x.ShiftId == shiftId).ToList();

            DateTime shiftStartDate = thisShift.Select(x => x.StartDateTime).First();

            int diff = day.Subtract(shiftStartDate).Days;

            int dayMatch = diff % thisShift.Count;
            if (thisShift[dayMatch].TimeId == 0)
                return true;
            else
                return false;
        }

        /// <summary>
        /// return weekDay name
        /// </summary>
        /// <param name="day"></param>
        /// <returns></returns>
        public static string Weekday(DateTime day)
        {
            if (day.DayOfWeek == DayOfWeek.Saturday)
                return Shared.IsEnglish == true ? ResEn.Sat : ResAr.Sat;//"السبت"
            if (day.DayOfWeek == DayOfWeek.Sunday)
                return Shared.IsEnglish == true ? ResEn.Sun : ResAr.Sun;//"الاحد"
            if (day.DayOfWeek == DayOfWeek.Monday)
                return Shared.IsEnglish == true ? ResEn.Mon : ResAr.Mon;//"الاتنين"
            if (day.DayOfWeek == DayOfWeek.Tuesday)
                return Shared.IsEnglish == true ? ResEn.Tus : ResAr.Tus;//"الثلاثاء"
            if (day.DayOfWeek == DayOfWeek.Wednesday)
                return Shared.IsEnglish == true ? ResEn.Wed : ResAr.Wed;//"الاربعاء"
            if (day.DayOfWeek == DayOfWeek.Thursday)
                return Shared.IsEnglish == true ? ResEn.Thu : ResAr.Thu;//"الخميس"
            if (day.DayOfWeek == DayOfWeek.Friday)
                return Shared.IsEnglish == true ? ResEn.Fri : ResAr.Fri;//"الجمعه"
            return "";
        }


        public static DateTime WeekStartDay(DateTime today)
        {
            if (today.DayOfWeek == DayOfWeek.Saturday)
                return today;
            else if (today.DayOfWeek == DayOfWeek.Sunday)
                return today.AddDays(-1);
            else if (today.DayOfWeek == DayOfWeek.Monday)
                return today.AddDays(-2);
            else if (today.DayOfWeek == DayOfWeek.Tuesday)
                return today.AddDays(-3);
            else if (today.DayOfWeek == DayOfWeek.Wednesday)
                return today.AddDays(-4);
            else if (today.DayOfWeek == DayOfWeek.Thursday)
                return today.AddDays(-5);
            else return today.AddDays(-6);
        }

        public static DateTime WeekEndDay(DateTime today)
        {
            if (today.DayOfWeek == DayOfWeek.Saturday)
                return today.AddDays(6);
            else if (today.DayOfWeek == DayOfWeek.Sunday)
                return today.AddDays(5);
            else if (today.DayOfWeek == DayOfWeek.Monday)
                return today.AddDays(4);
            else if (today.DayOfWeek == DayOfWeek.Tuesday)
                return today.AddDays(3);
            else if (today.DayOfWeek == DayOfWeek.Wednesday)
                return today.AddDays(2);
            else if (today.DayOfWeek == DayOfWeek.Thursday)
                return today.AddDays(1);
            else return today;
        }


        /// <summary>
        /// method return account_id for employee by empId and create Account if he has no Account
        /// </summary>
        /// <param name="empId"></param>
        /// <returns></returns>
        public static int GetEmpAccountId(int emp_Id, int? HrLoansAccount)
        {
            ERPDataContext hrDB = new ERPDataContext();
            var emp = hrDB.HR_Employees.Where(x => x.EmpId == emp_Id).FirstOrDefault();
            if (emp.AccountId != null)
                return emp.AccountId.Value;

            ERPDataContext erpDB = new ERPDataContext();
            ACC_Account acc = new ACC_Account();
            acc.AcNameAr = emp.EmpName.Trim();
            acc.AcNameEn = "";
            acc.AcType = false;
            acc.AllowChild = false;
            acc.AllowEdit = false;
            acc.Notes = Shared.IsEnglish ? ResEn.accountCreationBy : ResAr.accountCreationBy; //"Account created by the system" : "تم انشاء الحساب بواسطة النظام";
            acc.AccSecurityLevel = 1;   //default

            var parentAcc = erpDB.ACC_Accounts.Where(x => x.AccountId == HrLoansAccount.Value).FirstOrDefault();
            acc.ParentActId = parentAcc.AccountId;
            acc.Level = parentAcc.Level + 1;
            acc.AcNumber = HelperAcc.AccNumGenerated(parentAcc);

            erpDB.ACC_Accounts.InsertOnSubmit(acc);
            erpDB.SubmitChanges();

            emp.AccountId = acc.AccountId;
            hrDB.SubmitChanges();

            return acc.AccountId;
        }


        #region PaySlip Creation

        public static void CreateBenefitsDataTable(ref DataTable dtBenefitAdd, ref DataTable dtBenefitSubtract)
        {
            dtBenefitAdd.Columns.Add("PayDetailId");
            dtBenefitAdd.Columns.Add("PayId");
            dtBenefitAdd.Columns.Add("BenefitName").AllowDBNull = false;
            dtBenefitAdd.Columns.Add("Amount").DataType = typeof(double);
            dtBenefitAdd.Columns["Amount"].AllowDBNull = false;
            dtBenefitAdd.Columns.Add("BenefitType");
            dtBenefitAdd.Columns.Add("BenefitCalculation");
            dtBenefitAdd.Columns.Add("IsLoan").DefaultValue = "0";
            dtBenefitAdd.Columns.Add("BenefitId").DefaultValue = "0";
            dtBenefitAdd.Columns.Add("IsTax").DataType = typeof(bool);
            dtBenefitAdd.Columns["IsTax"].DefaultValue = true;
            dtBenefitAdd.Columns.Add("IsReward").DataType = typeof(bool);
            dtBenefitAdd.Columns["IsReward"].DefaultValue = true;

            dtBenefitAdd.Columns.Add("IsInsurance").DefaultValue = "0";
            dtBenefitAdd.Columns.Add("CompanyInsuranceCalculation");

            dtBenefitAdd.Columns.Add("CompanyInsuranceAmount").DataType = typeof(double);
            dtBenefitAdd.Columns["CompanyInsuranceAmount"].AllowDBNull = false;
            dtBenefitAdd.Columns["CompanyInsuranceAmount"].DefaultValue = 0;
            dtBenefitAdd.Columns.Add("SubtractAcc").DataType = typeof(int);
            dtBenefitAdd.Columns["SubtractAcc"].DefaultValue = 0;


            dtBenefitSubtract.Columns.Add("PayDetailId");
            dtBenefitSubtract.Columns.Add("PayId");
            dtBenefitSubtract.Columns.Add("BenefitName").AllowDBNull = false;
            dtBenefitSubtract.Columns.Add("Amount").DataType = typeof(double); ;
            dtBenefitSubtract.Columns["Amount"].AllowDBNull = false;
            dtBenefitSubtract.Columns.Add("BenefitType");
            dtBenefitSubtract.Columns.Add("BenefitCalculation");
            dtBenefitSubtract.Columns.Add("IsLoan").DefaultValue = "0";
            dtBenefitSubtract.Columns.Add("LoanDetailId");
            dtBenefitSubtract.Columns.Add("BenefitId").DefaultValue = "0";
            dtBenefitSubtract.Columns.Add("IsTax").DataType = typeof(bool);
            dtBenefitSubtract.Columns["IsTax"].DefaultValue = true;

            dtBenefitSubtract.Columns.Add("IsInsurance").DefaultValue = "0";
            dtBenefitSubtract.Columns.Add("CompanyInsuranceCalculation");

            dtBenefitSubtract.Columns.Add("CompanyInsuranceAmount").DataType = typeof(double);
            dtBenefitSubtract.Columns["CompanyInsuranceAmount"].AllowDBNull = false;
            dtBenefitSubtract.Columns["CompanyInsuranceAmount"].DefaultValue = 0;
            dtBenefitSubtract.Columns.Add("SubtractAcc").DataType = typeof(int);
            dtBenefitSubtract.Columns["SubtractAcc"].DefaultValue = 0;
            dtBenefitSubtract.Columns.Add("SubtractType").DataType = typeof(byte);
            dtBenefitSubtract.Columns["SubtractType"].DefaultValue = 0;
        }

        public static void GetEmpNewPeriod(HR_Employee Employee, out DateTime startDate, out DateTime endDate,
            bool isSecondPayslip)
        {
            ERPDataContext DB = new ERPDataContext();
            DateTime today = DateTime.Now.Date;
            startDate = endDate = DateTime.MinValue;

            var lastPayPeriod = (from p in DB.HR_Pays
                                 where p.EmpId == Employee.EmpId
                                 where p.IsSecondPaySlip == isSecondPayslip
                                 select p.ToDay).AsEnumerable().LastOrDefault();

            if (lastPayPeriod == DateTime.MinValue)// no payments before
            {
                if (Employee.PayPeriod == (int)PayPeriod.Month || Employee.PayPeriod == (int)PayPeriod.Hour || Employee.PayPeriod == (int)PayPeriod.Daily)//get Month start and month end
                {
                    startDate = today.AddDays(-today.Day + 1).Date;
                    endDate = startDate.AddMonths(1).AddDays(-1);
                }
                else if (Employee.PayPeriod == (int)PayPeriod.Week ||
                    Employee.PayPeriod == (int)PayPeriod.Hour)//get week start and week end
                {
                    startDate = HrHelper.WeekStartDay(today);
                    endDate = HrHelper.WeekEndDay(today);
                }
                else if (Employee.PayPeriod == (int)PayPeriod.TwoWeeks)//get two week start and end
                {
                    startDate = HrHelper.WeekStartDay(today);
                    endDate = HrHelper.WeekEndDay(today).AddDays(7);
                }
            }
            else
            {
                DateTime start = lastPayPeriod.AddDays(1);

                startDate = start;

                if (Employee.PayPeriod == (int)PayPeriod.Month || Employee.PayPeriod == (int)PayPeriod.Hour || Employee.PayPeriod == (int)PayPeriod.Daily)//get Month start and month end                    
                    endDate = start.AddMonths(1).AddDays(-1);
                else if (Employee.PayPeriod == (int)PayPeriod.Week ||
                    Employee.PayPeriod == (int)PayPeriod.Hour)//get week start and week end
                    endDate = HrHelper.WeekEndDay(start);
                else if (Employee.PayPeriod == (int)PayPeriod.TwoWeeks)//get two week start and end                    
                    endDate = start.AddDays(13);
            }
        }

        public static void GetBenefits(HR_Employee Employee, DateTime startDate, DateTime endDate,
            ref DataTable dtBnftAdd, ref DataTable dtBnftSubtract, bool isSecondPayslip, out double minutes,
                out decimal workDays, bool FpDependOnInOut, bool calcDelayForAttendOnly,
            ST_Store st_store, List<ST_IncomeTaxDiscount> lst_IncomeTaxDiscount, List<ST_IncomeTaxLevel> lst_IncomeTaxLevel
            , out decimal compShare, out decimal empShare)
        {
            minutes = 0;
            workDays = 0;
            compShare = 0;
            empShare = 0;

            decimal totalInsbenfits = 0;
            decimal totalbenf = 0;

            ERPDataContext DB = new ERPDataContext();
            dtBnftAdd.Rows.Clear();
            dtBnftSubtract.Rows.Clear();
            decimal totalSalary = Employee.SalaryBasic + Employee.SalaryVariable;

            GetWorkedDaysHours(DB, FpDependOnInOut, Employee, startDate, endDate, out workDays, out minutes, calcDelayForAttendOnly);
            if (Employee.PayPeriod == (int)PayPeriod.Daily)
            {
                dtBnftAdd.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? "Working Days Value" : "قيمة أيام العمل"
                        , workDays * Employee.Day, true,
                        "", 0, 0, false, false, 0, null, 0);
            }
            #region Action = New : get benefits
            decimal benfDaysRatio = 0;
            /*if (Shared.st_Store.TotalMonthDays_HR.HasValue && Shared.st_Store.TotalMonthDays_HR.Value > 0)
            {
                benfDaysRatio = workDays / Shared.st_Store.TotalMonthDays_HR.Value;
            }*/
            benfDaysRatio = workDays /*/ (System.DateTime.DaysInMonth(startDate.Year, startDate.Month))*/;
            var bnfts = from b in DB.HR_EmployeeBenefits
                        join n in DB.HR_Benefits
                        on b.BenefitId equals n.BenefitId
                        where b.EmpId == Employee.EmpId
                        where n.UsedInSecondPaySlip == isSecondPayslip
                        select new
                        {
                            n.BenefitId,
                            n.BenefitNameAr,
                            n.BenefitType,
                            n.BenefitCalculation,
                            n.IsInsurance,
                            n.CompanyInsuranceRatio,
                            b.Value,
                            b.Notes,
                            n.IsWorkingDays,
                            n.IsTaxable,
                            n.subtractAcc
                        };

            foreach (var b in bnfts)
            {
                decimal amount = 0;

                string calc = string.Empty;
                string CompCalc = string.Empty;

                if (b.BenefitCalculation == (byte)BenefitCalculation.Amount)
                {
                    if (b.IsWorkingDays == true)
                    {
                        amount = b.Value * benfDaysRatio;
                    }
                    else
                    {
                        amount = b.Value;
                    }
                    calc = Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount;//"مبلغ ثابت"
                }
                else if (b.BenefitCalculation == (byte)BenefitCalculation.RatioOfBasic)
                {
                    if (b.IsWorkingDays == true)
                        amount = ((Employee.SalaryBasic + Employee.SalaryVariable) * (b.Value / 100)) * benfDaysRatio;
                    else
                        amount = (Employee.SalaryBasic + Employee.SalaryVariable) * (b.Value / 100);
                    calc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة "
                        + decimal.ToDouble(b.Value).ToString() +
                        (Shared.IsEnglish == true ? ResHREn.OfBasic : ResHRAr.OfBasic);//" % من الاساسي"
                }
                else if (b.BenefitCalculation == (byte)BenefitCalculation.Ratioovariable)
                {
                    if (b.IsWorkingDays == true)
                        amount = (Employee.SalaryVariable * (b.Value / 100)) * benfDaysRatio;
                    else
                        amount = Employee.SalaryVariable * (b.Value / 100);
                    calc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة " 
                        + decimal.ToDouble(b.Value).ToString() +
                        (Shared.IsEnglish == true ? ResHREn.OfVariable : ResHRAr.OfVariable);//"% من المتغير"
                }

                #region company Insurance Ratio
                /*if (b.IsInsurance)
                {
                    if (b.BenefitCalculation == (byte)BenefitCalculation.RatioOfBasic)
                    {
                        CompAmount = Employee.SalaryBasic * b.CompanyInsuranceRatio;
                        CompCalc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة "
                            + decimal.ToDouble(b.CompanyInsuranceRatio * 100).ToString() +
                            (Shared.IsEnglish == true ? ResHREn.OfBasic : ResHRAr.OfBasic);//" % من الاساسي"
                    }
                    else if (b.BenefitCalculation == (byte)BenefitCalculation.Ratioovariable)
                    {
                        CompAmount = Employee.SalaryVariable * b.CompanyInsuranceRatio;
                        CompCalc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة " 
                            + decimal.ToDouble(b.CompanyInsuranceRatio * 100).ToString() +
                            (Shared.IsEnglish == true ? ResHREn.OfVariable : ResHRAr.OfVariable);//"% من المتغير"
                    }
                }*/
                #endregion                

                if (b.BenefitType == true)
                {
                    dtBnftAdd.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, b.BenefitId, b.IsTaxable == null ? false : b.IsTaxable, false, b.IsInsurance, null, 0, b.subtractAcc != null ? b.subtractAcc : 0);
                    totalInsbenfits += b.IsInsurance == false ? amount : 0;
                    totalbenf += amount;
                    //totalSalary += amount;
                }
                else
                {
                    dtBnftSubtract.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, DBNull.Value, b.BenefitId, b.IsTaxable == null ? false : b.IsTaxable, 0, null, 0, b.subtractAcc != null ? b.subtractAcc : 0, (byte) HR_SubtractType.Default);

                }
            }

            #region Income Tax
            if (st_store.IncomeTaxAvailable && Employee.CalcIncomeTax)
            {
                foreach (ST_IncomeTaxDiscount d in lst_IncomeTaxDiscount)
                {
                    totalSalary -= d.TaxDisAnnualValue / 12;
                }

                decimal AnnualTaxableSalary = totalSalary * 12;
                decimal tax = 0;

                foreach (ST_IncomeTaxLevel d in lst_IncomeTaxLevel)
                {
                    if (AnnualTaxableSalary >= (d.To - d.From))
                    {
                        AnnualTaxableSalary -= (d.To - d.From);
                        tax += (d.To - d.From) * d.Ratio;
                        continue;
                    }
                    if (AnnualTaxableSalary < (d.To - d.From))
                    {
                        tax += AnnualTaxableSalary * d.Ratio;
                        break;
                    }
                    if (AnnualTaxableSalary < d.From)
                        break;

                }

                if (tax > 0)
                    dtBnftSubtract.Rows.Add(0, 0, Shared.IsEnglish ? ResHREn.incomeTax : ResHRAr.incomeTax,
                        decimal.ToDouble(tax / 12), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount,
                        0,
                        DBNull.Value,
                        0,
                        1,
                        0, null, 0);


            }
            #endregion

            #endregion

            #region  Action = New : get forms in this period
            decimal vacation_ = 0, overtime_ = 0, delay_ = 0, absence_ = 0, reward_ = 0, penalty_ = 0;
            decimal MinutesInHour = 60;
            //overtime
            if (isSecondPayslip == false)
            {
                var vacations = (from o in DB.HR_Vacations
                                 join v in DB.HR_VacationsExtraTypes
                                 on o.OtherVacId equals v.VacId
                                 where o.EmpId == Employee.EmpId &&
                                 o.StartDate.Date >= startDate &&
                                 o.StartDate.Date <= endDate &&
                                 v.DayDiscRatio > 0
                                 select new
                                 {
                                     o.DeductWholeDay,
                                     o.Duration,
                                     o.StartDate,
                                     o.EndDate,
                                     v.DayDiscRatio,
                                 }).ToList();

                foreach (var v in vacations)
                {
                    if (Employee.absencebenefit == true)
                    {
                        decimal dayCost = CalcAbsenceBenfit(Employee, true, false);
                        vacation_ += dayCost * v.DayDiscRatio * Convert.ToDecimal(v.EndDate.Date.Subtract(v.StartDate.Date).TotalDays + 1) *
                            (v.DeductWholeDay ? (decimal)1 : (decimal)0.5);
                    }
                    else
                    {
                        vacation_ += Employee.Day * v.DayDiscRatio * Convert.ToDecimal(v.EndDate.Date.Subtract(v.StartDate.Date).TotalDays + 1) *
                            (v.DeductWholeDay ? (decimal)1 : (decimal)0.5);
                    }
                }

                if (vacation_ > 0)
                    dtBnftSubtract.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Vacations : ResHRAr.Vacations//اجازات
                        , decimal.ToDouble(vacation_), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, DBNull.Value, 0, 0, 0, null, 0, 0, (byte) HR_SubtractType.Vacation);
                if (DB.HR_Employees.Where(x => x.EmpId == Employee.EmpId).Select(x => x.OverTimeRuleId).FirstOrDefault() == null)
                {
                    var overtime = (from o in DB.HR_OverTimes
                                    where o.EmpId == Employee.EmpId &&
                                    o.OverTimeDay.Date >= startDate &&
                                    o.OverTimeDay.Date <= endDate &&
                                    o.Payed == true
                                    select o.OverTimeMinutes).ToList().DefaultIfEmpty(0).Sum();
                    if (overtime > 0)
                        overtime_ = overtime / MinutesInHour * Employee.Hour;

                }
                else
                {
                    // overtime 
                    overtime_ = CalculateOverPerRole(Employee.EmpId, startDate, endDate);
                }
                if (overtime_ > 0)
                    dtBnftAdd.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Overtime : ResHRAr.Overtime//"اضافي"
                        , decimal.ToDouble(overtime_), true,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, 0, true, true, 0, null, 0);
                //delay
                var delay = (from o in DB.HR_Delays
                             where o.EmpId == Employee.EmpId &&
                             o.DelayDay.Date >= startDate &&
                             o.DelayDay.Date <= endDate &&
                             o.Payed == false
                             select o.DelayMinutes).ToList().DefaultIfEmpty(0).Sum();
                if (delay > 0)
                    delay_ = delay / MinutesInHour * Employee.Hour;

                if (delay_ > 0)
                    dtBnftSubtract.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Delay : ResHRAr.Delay//"تاخير"
                        , decimal.ToDouble(delay_), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, DBNull.Value, 0, 0, 0, null, 0);

                //absence
                var absence = (from o in DB.HR_Absences
                               where o.EmpId == Employee.EmpId &&
                               (o.StartDate.Date >= startDate &&
                               o.StartDate.Date <= endDate
                               ||
                               o.EndDate.Date >= startDate &&
                               o.EndDate.Date <= endDate) &&
                               o.Payed == false
                               select o).ToList();
                foreach (var a in absence)
                {
                    int absDays = 0;

                    if (a.StartDate.Date >= startDate && a.EndDate.Date <= endDate)
                        absDays = a.Duration;
                    else if (a.StartDate.Date >= startDate && a.EndDate.Date > endDate)
                        absDays = Convert.ToInt32(endDate.Subtract(a.StartDate).TotalDays + 1);
                    else if (a.StartDate.Date < startDate && a.EndDate.Date < endDate)
                        absDays = Convert.ToInt32(a.EndDate.Subtract(startDate).TotalDays + 1);

                    if (a.WithPermission)
                    {

                        if (Employee.AbsensePenaltyDay > 0 && absDays > 0)
                        {
                            if (Employee.absencebenefit == true)
                            {
                                decimal dayCost = CalcAbsenceBenfit(Employee, false);
                                absence_ += (absDays * Employee.AbsensePenaltyDay) * dayCost;
                            }
                            else
                            {
                                absence_ += (absDays * Employee.AbsensePenaltyDay) * Employee.Day;
                            }
                        }
                        else if (Employee.AbsensePenaltyValue > 0 && absDays > 0)
                            absence_ += absDays * Employee.AbsensePenaltyValue;
                        else
                            absence_ += 0;
                    }
                    else
                    {
                        //mohammad 14-08-2017                                   
                        if (Employee.AbsenseNoPermPenaltyDay > 0 && absDays > 0)// absence_ > 0)
                        {
                            if (Employee.absencebenefit == true)
                            {
                                decimal dayCost = CalcAbsenceBenfit(Employee, false);
                                absence_ += (absDays * Employee.AbsenseNoPermPenaltyDay) * dayCost;
                            }
                            else
                            {
                                absence_ += (absDays * Employee.AbsenseNoPermPenaltyDay) * Employee.Day;
                            }
                        }
                        else if (Employee.AbsenseNoPermPenaltyValue > 0 && absDays > 0)//absence_ > 0)
                            absence_ += absDays * Employee.AbsenseNoPermPenaltyValue;
                        else//mohammad 14-08-2017
                            absence_ += absDays;
                        //absence_ += 0;
                    }
                }


                if (absence_ > 0)
                    dtBnftSubtract.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Absence : ResHRAr.Absence//"غياب"
                        , decimal.ToDouble(absence_), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, DBNull.Value, 0, 0, 0, null, 0, 0, (byte) HR_SubtractType.Absence);

                //rewards
                var reward = (from o in DB.HR_Rewards
                              where o.EmpId == Employee.EmpId &&
                              o.RewardDay.Date >= startDate &&
                               o.RewardDay.Date <= endDate
                              where o.IsNow != true
                              select o.RewardValue).ToList().DefaultIfEmpty(0).Sum();
                reward_ = reward;

                if (reward_ > 0)
                    dtBnftAdd.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Reward : ResHRAr.Reward//"مكافأة "                    
                        , decimal.ToDouble(reward_), true,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, 0, true, true, 0, null, 0);

                //penalty
                var penalty = (from o in DB.HR_Penalties
                               where o.EmpId == Employee.EmpId &&
                               o.PenaltyDay.Date >= startDate &&
                                o.PenaltyDay.Date <= endDate
                               select o.PenaltyValue).ToList().DefaultIfEmpty(0).Sum();
                penalty_ = penalty;

                if (penalty_ > 0)
                    dtBnftSubtract.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Penalty : ResHRAr.Penalty//"جزاء"
                        , decimal.ToDouble(penalty_), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, DBNull.Value, 0, 0, 0, null, 0, 0, (byte) HR_SubtractType.Penalty);
            }
            #endregion

            #region get Loans or Sales commsion
            if (isSecondPayslip == false /* --> Added by mohammad -->*/ && Employee.AccountId.HasValue)
            {
                decimal loanOrCommision = HelperAcc.Get_account_balance(Employee.AccountId.Value);
                if (loanOrCommision < 0) //debit
                {
                    //get next loan installment
                    var nextLoanPart = (from l in DB.HR_Loans
                                        join d in DB.HR_LoanDetails
                                        on l.LoanId equals d.LoanId
                                        where l.EmpId == Employee.EmpId
                                        where d.Payed == false
                                        //update  7/5/2018 alaa
                                        where d.DuePaymentDate == null

                                        orderby d.LoanDetailId
                                        select new
                                        {
                                            d.Amount,
                                            d.LoanDetailId,
                                            d.LoanId
                                        }).ToList();
                    //update 7/5/2018 alaa

                    var loanWithDueDate = (from l in DB.HR_Loans
                                           join d in DB.HR_LoanDetails
                                           on l.LoanId equals d.LoanId
                                           where l.EmpId == Employee.EmpId
                                           where d.Payed == false
                                           where d.DuePaymentDate.HasValue ? /*d.DuePaymentDate.Value.Date >= startDate.Date && */d.DuePaymentDate.Value.Date <= endDate.Date : false
                                           select new
                                           {
                                               d.Amount,
                                               d.LoanDetailId,
                                               d.LoanId
                                           }).ToList();


                    //Mohammad 27-01-2021
                    var all_LaterLoans_Except_Other_Debits = (from l in DB.HR_Loans
                                                              join d in DB.HR_LoanDetails
                                                              on l.LoanId equals d.LoanId
                                                              where l.EmpId == Employee.EmpId
                                                              where d.Payed == false
                                                              where d.DuePaymentDate > endDate.Date
                                                              select d.Amount).ToList().DefaultIfEmpty().Sum() + loanOrCommision;

                    if (nextLoanPart.Count == 0 && loanWithDueDate.Count == 0 && all_LaterLoans_Except_Other_Debits < 0) // loan not recorded by loan screen
                    {
                        dtBnftSubtract.Rows.Add(0, 0,
                            Shared.IsEnglish == true ? ResHREn.loan : ResHRAr.loan//سلفة
                            , decimal.ToDouble(Math.Abs(loanOrCommision)), false,
                            Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, "1", DBNull.Value, 0, 0, 0, null, 0, (byte)HR_SubtractType.Loan);
                    }
                    else //loan recorded by loan screen
                    {
                        if (nextLoanPart.Count > 0)
                        {
                            dtBnftSubtract.Rows.Add(0, 0,
                                                    Shared.IsEnglish == true ? ResHREn.loan : ResHRAr.loan//سلفة
                                                    , decimal.ToDouble(Math.Abs(nextLoanPart[0].Amount)), false,
                                                    Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, "1",
                                                    nextLoanPart[0].LoanDetailId, 0, false, 0, null, 0, (byte)HR_SubtractType.Loan);
                        }
                        foreach (var item in loanWithDueDate)
                        {
                            dtBnftSubtract.Rows.Add(0, 0,
                                                Shared.IsEnglish == true ? ResHREn.loan : ResHRAr.loan//سلفة
                                                , decimal.ToDouble(Math.Abs(item.Amount)), false,
                                                Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, "1",
                                                item.LoanDetailId, 0, false, 0, null, 0);
                        }

                    }
                }
                else if (loanOrCommision > 0)//credit
                    dtBnftAdd.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.OtherBenefits : ResHRAr.OtherBenefits//مستحقات أخرى
                        , decimal.ToDouble(Math.Abs(loanOrCommision)), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, "1", 0, true, true, 0, null, 0);
            }
            #endregion
            if (Employee.IsInsurance == true)
            {
                decimal incentive = CalcIncentive(Employee);
                CalcInsurance(Shared.st_Store.TotalSalaryType == true ? totalSalary+incentive : (totalSalary + totalbenf+ incentive), totalInsbenfits, out compShare, out empShare);
            }
        }
        public static void GetBenefits(HR_Employee Employee, DateTime startDate, DateTime endDate,
            ref DataTable dtBnftAdd, ref DataTable dtBnftSubtract, bool isSecondPayslip, out double minutes,
                out decimal workDays, bool FpDependOnInOut, bool calcDelayForAttendOnly,
            ST_Store st_store, List<ST_IncomeTaxDiscount> lst_IncomeTaxDiscount, List<ST_IncomeTaxLevel> lst_IncomeTaxLevel)
        {
            minutes = 0;
            workDays = 0;

            ERPDataContext DB = new ERPDataContext();
            dtBnftAdd.Rows.Clear();
            dtBnftSubtract.Rows.Clear();
            decimal totalSalary = Employee.SalaryBasic + Employee.SalaryVariable;

            GetWorkedDaysHours(DB, FpDependOnInOut, Employee, startDate, endDate, out workDays, out minutes, calcDelayForAttendOnly);
            if (Employee.PayPeriod == (int)PayPeriod.Daily)
            {
                dtBnftAdd.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? "Working Days Value" : "قيمة أيام العمل"
                        , workDays * Employee.Day, true,
                        "", 0, 0, false, false, 0, null, 0);
            }
            #region Action = New : get benefits
            decimal benfDaysRatio = 0;
            if (Shared.st_Store.TotalMonthDays_HR.HasValue && Shared.st_Store.TotalMonthDays_HR.Value > 0)
            {
                benfDaysRatio = workDays / Shared.st_Store.TotalMonthDays_HR.Value;
            }
            var bnfts = from b in DB.HR_EmployeeBenefits
                        join n in DB.HR_Benefits
                        on b.BenefitId equals n.BenefitId
                        where b.EmpId == Employee.EmpId
                        where n.UsedInSecondPaySlip == isSecondPayslip
                        select new
                        {
                            n.BenefitId,
                            n.BenefitNameAr,
                            n.BenefitType,
                            n.BenefitCalculation,
                            n.IsInsurance,
                            n.CompanyInsuranceRatio,
                            b.Value,
                            b.Notes,
                            n.IsWorkingDays
                        };

            foreach (var b in bnfts)
            {
                decimal amount = 0;
                decimal CompAmount = 0;

                string calc = string.Empty;
                string CompCalc = string.Empty;

                if (b.BenefitCalculation == (byte)BenefitCalculation.Amount)
                {
                    if (b.IsWorkingDays == true)
                    {
                        amount = b.Value * benfDaysRatio;
                    }
                    else
                    {
                        amount = b.Value;
                    }
                    calc = Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount;//"مبلغ ثابت"
                }
                else if (b.BenefitCalculation == (byte)BenefitCalculation.RatioOfBasic)
                {
                    if (b.IsWorkingDays == true)
                        amount = (Employee.SalaryBasic * (b.Value / 100)) * benfDaysRatio;
                    else
                        amount = Employee.SalaryBasic * (b.Value / 100);
                    calc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة "
                        + decimal.ToDouble(b.Value).ToString() +
                        (Shared.IsEnglish == true ? ResHREn.OfBasic : ResHRAr.OfBasic);//" % من الاساسي"
                }
                else if (b.BenefitCalculation == (byte)BenefitCalculation.Ratioovariable)
                {
                    if (b.IsWorkingDays == true)
                        amount = (Employee.SalaryVariable * (b.Value / 100)) * benfDaysRatio;
                    else
                        amount = Employee.SalaryVariable * (b.Value / 100);
                    calc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة " 
                        + decimal.ToDouble(b.Value).ToString() +
                        (Shared.IsEnglish == true ? ResHREn.OfVariable : ResHRAr.OfVariable);//"% من المتغير"
                }

                #region company Insurance Ratio
                if (b.IsInsurance)
                {
                    if (b.BenefitCalculation == (byte)BenefitCalculation.RatioOfBasic)
                    {
                        CompAmount = Employee.SalaryBasic * b.CompanyInsuranceRatio;
                        CompCalc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة "
                            + decimal.ToDouble(b.CompanyInsuranceRatio * 100).ToString() +
                            (Shared.IsEnglish == true ? ResHREn.OfBasic : ResHRAr.OfBasic);//" % من الاساسي"
                    }
                    else if (b.BenefitCalculation == (byte)BenefitCalculation.Ratioovariable)
                    {
                        CompAmount = Employee.SalaryVariable * b.CompanyInsuranceRatio;
                        CompCalc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة " 
                            + decimal.ToDouble(b.CompanyInsuranceRatio * 100).ToString() +
                            (Shared.IsEnglish == true ? ResHREn.OfVariable : ResHRAr.OfVariable);//"% من المتغير"
                    }
                }
                #endregion                

                if (b.BenefitType == true)
                {
                    if (b.IsInsurance == false)
                        dtBnftAdd.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, b.BenefitId, false, false, 0, null, 0);
                    else
                        dtBnftAdd.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, b.BenefitId, false, false, 1, CompCalc, CompAmount);

                    totalSalary += amount;
                }
                else
                {
                    if (b.IsInsurance == false)
                        dtBnftSubtract.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, DBNull.Value, b.BenefitId, 0, 0, null, 0);
                    else
                        dtBnftSubtract.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, DBNull.Value, b.BenefitId, 0, 1, CompCalc, CompAmount);

                    totalSalary -= amount;
                }
            }

            /*var benfQualData = from bq in DB.HR_Qalification_Benifits
                               join b in DB.HR_Benefits
                               on bq.BenfId equals b.BenefitId
                               join eb in DB.HR_EmployeeBenefits
                               on b.BenefitId equals eb.BenefitId
                               where bq.QalId == Employee.QualificationId
                               select new
                               {
                                   b.BenefitId,
                                   b.BenefitNameAr,
                                   b.BenefitType,
                                   b.BenefitCalculation,
                                   eb.Value,
                                   eb.Notes,
                                   b.IsWorkingDays,
                                   b.IsInsurance,
                                   b.CompanyInsuranceRatio
                               };

            foreach (var b in benfQualData)
            {
                decimal amount = 0;
                decimal CompAmount = 0;

                string calc = string.Empty;
                string CompCalc = string.Empty;

                if (b.BenefitCalculation == (byte)BenefitCalculation.Amount)
                {
                    if (b.IsWorkingDays == true)
                    {
                        amount = b.Value * benfDaysRatio;
                    }
                    else
                    {
                        amount = b.Value;
                    }
                    calc = Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount;//"مبلغ ثابت"
                }
                else if (b.BenefitCalculation == (byte)BenefitCalculation.RatioOfBasic)
                {
                    if (b.IsWorkingDays == true)
                        amount = (Employee.SalaryBasic * (b.Value / 100)) * benfDaysRatio;
                    else
                        amount = Employee.SalaryBasic * (b.Value / 100);
                    calc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة "
                        + decimal.ToDouble(b.Value).ToString() +
                        (Shared.IsEnglish == true ? ResHREn.OfBasic : ResHRAr.OfBasic);//" % من الاساسي"
                }
                else if (b.BenefitCalculation == (byte)BenefitCalculation.Ratioovariable)
                {
                    if (b.IsWorkingDays == true)
                        amount = Employee.SalaryVariable * (b.Value / 100) * benfDaysRatio;
                    else
                        amount = Employee.SalaryVariable * (b.Value / 100);
                    calc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة " 
                        + decimal.ToDouble(b.Value).ToString() +
                        (Shared.IsEnglish == true ? ResHREn.OfVariable : ResHRAr.OfVariable);//"% من المتغير"
                }

                #region company Insurance Ratio
                if (b.IsInsurance)
                {
                    if (b.BenefitCalculation == (byte)BenefitCalculation.RatioOfBasic)
                    {
                        CompAmount = Employee.SalaryBasic * b.CompanyInsuranceRatio;
                        CompCalc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة "
                            + decimal.ToDouble(b.CompanyInsuranceRatio * 100).ToString() +
                            (Shared.IsEnglish == true ? ResHREn.OfBasic : ResHRAr.OfBasic);//" % من الاساسي"
                    }
                    else if (b.BenefitCalculation == (byte)BenefitCalculation.Ratioovariable)
                    {
                        CompAmount = Employee.SalaryVariable * b.CompanyInsuranceRatio;
                        CompCalc = (Shared.IsEnglish == true ? ResHREn.Ratio : ResHRAr.Ratio)//"نسبة " 
                            + decimal.ToDouble(b.CompanyInsuranceRatio * 100).ToString() +
                            (Shared.IsEnglish == true ? ResHREn.OfVariable : ResHRAr.OfVariable);//"% من المتغير"
                    }
                }
                #endregion                

                if (b.BenefitType == true)
                {
                    if (b.IsInsurance == false)
                        dtBnftAdd.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, b.BenefitId, 0, 0, null, 0);
                    else
                        dtBnftAdd.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, b.BenefitId, 0, 1, CompCalc, CompAmount);

                    totalSalary += amount;
                }
                else
                {
                    if (b.IsInsurance == false)
                        dtBnftSubtract.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, DBNull.Value, b.BenefitId, 0, 0, null, 0);
                    else
                        dtBnftSubtract.Rows.Add(0, 0, b.BenefitNameAr, decimal.ToDouble(amount), b.BenefitType, calc, 0, DBNull.Value, b.BenefitId, 0, 1, CompCalc, CompAmount);

                    totalSalary -= amount;
                }
            }*/


            #region Income Tax
            if (st_store.IncomeTaxAvailable && Employee.CalcIncomeTax)
            {
                foreach (ST_IncomeTaxDiscount d in lst_IncomeTaxDiscount)
                {
                    totalSalary -= d.TaxDisAnnualValue / 12;
                }

                decimal AnnualTaxableSalary = totalSalary * 12;
                decimal tax = 0;

                foreach (ST_IncomeTaxLevel d in lst_IncomeTaxLevel)
                {
                    if (AnnualTaxableSalary >= (d.To - d.From))
                    {
                        AnnualTaxableSalary -= (d.To - d.From);
                        tax += (d.To - d.From) * d.Ratio;
                        continue;
                    }
                    if (AnnualTaxableSalary < (d.To - d.From))
                    {
                        tax += AnnualTaxableSalary * d.Ratio;
                        break;
                    }
                    if (AnnualTaxableSalary < d.From)
                        break;

                }

                if (tax > 0)
                    dtBnftSubtract.Rows.Add(0, 0, Shared.IsEnglish ? ResHREn.incomeTax : ResHRAr.incomeTax,
                        decimal.ToDouble(tax / 12), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount,
                        0,
                        DBNull.Value,
                        0,
                        1,
                        0, null, 0);


            }
            #endregion

            #endregion

            #region  Action = New : get forms in this period
            decimal vacation_ = 0, overtime_ = 0, delay_ = 0, absence_ = 0, reward_ = 0, penalty_ = 0;
            decimal MinutesInHour = 60;
            //overtime
            if (isSecondPayslip == false)
            {
                var vacations = (from o in DB.HR_Vacations
                                 join v in DB.HR_VacationsExtraTypes
                                 on o.OtherVacId equals v.VacId
                                 where o.EmpId == Employee.EmpId &&
                                 o.StartDate.Date >= startDate &&
                                 o.StartDate.Date <= endDate &&
                                 v.DayDiscRatio > 0
                                 select new
                                 {
                                     o.DeductWholeDay,
                                     o.Duration,
                                     o.StartDate,
                                     o.EndDate,
                                     v.DayDiscRatio,
                                 }).ToList();

                foreach (var v in vacations)
                {
                    vacation_ += Employee.Day * v.DayDiscRatio * Convert.ToDecimal(v.EndDate.Date.Subtract(v.StartDate.Date).TotalDays + 1) *
                        (v.DeductWholeDay ? (decimal)1 : (decimal)0.5);
                }

                if (vacation_ > 0)
                    dtBnftSubtract.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Vacations : ResHRAr.Vacations//اجازات
                        , decimal.ToDouble(vacation_), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, DBNull.Value, 0, 0, 0, null, 0);
                if (DB.HR_Employees.Where(x => x.EmpId == Employee.EmpId).Select(x => x.OverTimeRuleId).FirstOrDefault() == null)
                {
                    var overtime = (from o in DB.HR_OverTimes
                                    where o.EmpId == Employee.EmpId &&
                                    o.OverTimeDay.Date >= startDate &&
                                    o.OverTimeDay.Date <= endDate &&
                                    o.Payed == true
                                    select o.OverTimeMinutes).ToList().DefaultIfEmpty(0).Sum();
                    if (overtime > 0)
                        overtime_ = overtime / MinutesInHour * Employee.Hour;

                }
                else
                {
                    // overtime 
                    overtime_ = CalculateOverPerRole(Employee.EmpId, startDate, endDate);
                }
                if (overtime_ > 0)
                    dtBnftAdd.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Overtime : ResHRAr.Overtime//"اضافي"
                        , decimal.ToDouble(overtime_), true,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, 0, false, false, 0, null, 0);
                //delay
                var delay = (from o in DB.HR_Delays
                             where o.EmpId == Employee.EmpId &&
                             o.DelayDay.Date >= startDate &&
                             o.DelayDay.Date <= endDate &&
                             o.Payed == false
                             select o.DelayMinutes).ToList().DefaultIfEmpty(0).Sum();
                if (delay > 0)
                    delay_ = delay / MinutesInHour * Employee.Hour;

                if (delay_ > 0)
                    dtBnftSubtract.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Delay : ResHRAr.Delay//"تاخير"
                        , decimal.ToDouble(delay_), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, DBNull.Value, 0, 0, 0, null, 0);

                //absence
                var absence = (from o in DB.HR_Absences
                               where o.EmpId == Employee.EmpId &&
                               (o.StartDate.Date >= startDate &&
                               o.StartDate.Date <= endDate
                               ||
                               o.EndDate.Date >= startDate &&
                               o.EndDate.Date <= endDate) &&
                               o.Payed == false
                               select o).ToList();
                foreach (var a in absence)
                {
                    int absDays = 0;

                    if (a.StartDate.Date >= startDate && a.EndDate.Date <= endDate)
                        absDays = a.Duration;
                    else if (a.StartDate.Date >= startDate && a.EndDate.Date > endDate)
                        absDays = Convert.ToInt32(endDate.Subtract(a.StartDate).TotalDays + 1);
                    else if (a.StartDate.Date < startDate && a.EndDate.Date < endDate)
                        absDays = Convert.ToInt32(a.EndDate.Subtract(startDate).TotalDays + 1);

                    if (a.WithPermission)
                    {
                        if (Employee.AbsensePenaltyDay > 0 && absDays > 0)
                            absence_ += (absDays * Employee.AbsensePenaltyDay) * Employee.Day;
                        else if (Employee.AbsensePenaltyValue > 0 && absDays > 0)
                            absence_ += absDays * Employee.AbsensePenaltyValue;
                        else
                            absence_ += 0;
                    }
                    else
                    {
                        //mohammad 14-08-2017                                   
                        if (Employee.AbsenseNoPermPenaltyDay > 0 && absDays > 0)// absence_ > 0)
                            absence_ += (absDays * Employee.AbsenseNoPermPenaltyDay) * Employee.Day;
                        else if (Employee.AbsenseNoPermPenaltyValue > 0 && absDays > 0)//absence_ > 0)
                            absence_ += absDays * Employee.AbsenseNoPermPenaltyValue;
                        else//mohammad 14-08-2017
                            absence_ += absDays;
                        //absence_ += 0;
                    }
                }


                if (absence_ > 0)
                    dtBnftSubtract.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Absence : ResHRAr.Absence//"غياب"
                        , decimal.ToDouble(absence_), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, DBNull.Value, 0, 0, 0, null, 0);

                //rewards
                var reward = (from o in DB.HR_Rewards
                              where o.EmpId == Employee.EmpId &&
                              o.RewardDay.Date >= startDate &&
                               o.RewardDay.Date <= endDate
                              where o.IsNow != true
                              select o.RewardValue).ToList().DefaultIfEmpty(0).Sum();
                reward_ = reward;

                if (reward_ > 0)
                    dtBnftAdd.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Reward : ResHRAr.Reward//"مكافأة "                    
                        , decimal.ToDouble(reward_), true,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, 0, false, false, 0, null, 0);

                //penalty
                var penalty = (from o in DB.HR_Penalties
                               where o.EmpId == Employee.EmpId &&
                               o.PenaltyDay.Date >= startDate &&
                                o.PenaltyDay.Date <= endDate
                               select o.PenaltyValue).ToList().DefaultIfEmpty(0).Sum();
                penalty_ = penalty;

                if (penalty_ > 0)
                    dtBnftSubtract.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.Penalty : ResHRAr.Penalty//"جزاء"
                        , decimal.ToDouble(penalty_), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, 0, DBNull.Value, 0, 0, 0, null, 0);
            }
            #endregion

            #region get Loans or Sales commsion
            if (isSecondPayslip == false /* --> Added by mohammad -->*/ && Employee.AccountId.HasValue)
            {
                decimal loanOrCommision = HelperAcc.Get_account_balance(Employee.AccountId.Value);
                if (loanOrCommision < 0) //debit
                {
                    //get next loan installment
                    var nextLoanPart = (from l in DB.HR_Loans
                                        join d in DB.HR_LoanDetails
                                        on l.LoanId equals d.LoanId
                                        where l.EmpId == Employee.EmpId
                                        where d.Payed == false
                                        //update  7/5/2018 alaa
                                        where d.DuePaymentDate == null

                                        orderby d.LoanDetailId
                                        select new
                                        {
                                            d.Amount,
                                            d.LoanDetailId,
                                            d.LoanId
                                        }).ToList();
                    //update 7/5/2018 alaa

                    var loanWithDueDate = (from l in DB.HR_Loans
                                           join d in DB.HR_LoanDetails
                                           on l.LoanId equals d.LoanId
                                           where l.EmpId == Employee.EmpId
                                           where d.Payed == false
                                           where d.DuePaymentDate.HasValue ? /*d.DuePaymentDate.Value.Date >= startDate.Date && */d.DuePaymentDate.Value.Date <= endDate.Date : false
                                           select new
                                           {
                                               d.Amount,
                                               d.LoanDetailId,
                                               d.LoanId
                                           }).ToList();


                    //Mohammad 27-01-2021
                    var all_LaterLoans_Except_Other_Debits = (from l in DB.HR_Loans
                                                              join d in DB.HR_LoanDetails
                                                              on l.LoanId equals d.LoanId
                                                              where l.EmpId == Employee.EmpId
                                                              where d.Payed == false
                                                              where d.DuePaymentDate > endDate.Date
                                                              select d.Amount).ToList().DefaultIfEmpty().Sum() + loanOrCommision;

                    if (nextLoanPart.Count == 0 && loanWithDueDate.Count == 0 && all_LaterLoans_Except_Other_Debits < 0) // loan not recorded by loan screen
                    {
                        dtBnftSubtract.Rows.Add(0, 0,
                            Shared.IsEnglish == true ? ResHREn.loan : ResHRAr.loan//سلفة
                            , decimal.ToDouble(Math.Abs(loanOrCommision)), false,
                            Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, "1", DBNull.Value, 0, 0, 0, null, 0);
                    }
                    else //loan recorded by loan screen
                    {
                        if (nextLoanPart.Count > 0)
                        {
                            dtBnftSubtract.Rows.Add(0, 0,
                                                    Shared.IsEnglish == true ? ResHREn.loan : ResHRAr.loan//سلفة
                                                    , decimal.ToDouble(Math.Abs(nextLoanPart[0].Amount)), false,
                                                    Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, "1",
                                                    nextLoanPart[0].LoanDetailId, 0, false, 0, null, 0);
                        }
                        foreach (var item in loanWithDueDate)
                        {
                            dtBnftSubtract.Rows.Add(0, 0,
                                                Shared.IsEnglish == true ? ResHREn.loan : ResHRAr.loan//سلفة
                                                , decimal.ToDouble(Math.Abs(item.Amount)), false,
                                                Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, "1",
                                                item.LoanDetailId, 0, false, 0, null, 0);
                        }

                    }
                }
                else if (loanOrCommision > 0)//credit
                    dtBnftAdd.Rows.Add(0, 0,
                        Shared.IsEnglish == true ? ResHREn.OtherBenefits : ResHRAr.OtherBenefits//مستحقات أخرى
                        , decimal.ToDouble(Math.Abs(loanOrCommision)), false,
                        Shared.IsEnglish == true ? ResHREn.FixedAmount : ResHRAr.FixedAmount, "1", 0, false, false, 0, null, 0);
            }
            #endregion
        }

        public static void GetPayDetails(int payId, ref DataTable dtBenefitAdd, ref DataTable dtBenefitSubtract)
        {
            dtBenefitAdd.Rows.Clear();
            dtBenefitSubtract.Rows.Clear();

            ERPDataContext DB = new ERPDataContext();
            var details = (from d in DB.HR_PayDetails
                           join b in DB.HR_Benefits
                           on d.BenefitId equals b.BenefitId
                           where d.PayId == payId
                           select new { d, b }).ToList();


            var details_ManualEntry = (from d in DB.HR_PayDetails
                                       where DB.HR_Benefits.Where(b=>b.BenefitId == d.BenefitId).Count() <=0
                                       where d.PayId == payId
                                       select new { d }).ToList();

            foreach (var d in details)
            {
                DataRow row;
                if (d.d.BenefitType == true)
                    row = dtBenefitAdd.NewRow();
                else
                    row = dtBenefitSubtract.NewRow();

                row["PayDetailId"] = d.d.PayDetailId;
                row["PayId"] = d.d.PayId;
                row["BenefitName"] = d.d.BenefitName;
                row["BenefitCalculation"] = d.d.BenefitCalculation;
                row["Amount"] = d.d.Amount;
                row["BenefitType"] = d.d.BenefitType;
                row["IsLoan"] = d.d.IsLoan == true ? "1" : "0";
                if (d.d.LoanDetailId.HasValue)
                    row["LoanDetailId"] = d.d.LoanDetailId.Value;

                row["BenefitId"] = d.d.BenefitId;
                row["IsTax"] = d.d.IsTax == true ? true : false;

                row["IsInsurance"] = d.d.IsInsurance == true ? "1" : "0"; ;
                row["CompanyInsuranceCalculation"] = d.d.CompanyInsuranceCalculation;

                row["CompanyInsuranceAmount"] = d.d.CompanyInsuranceAmount;
                if (d.b.subtractAcc != null)
                {
                    row["SubtractAcc"] = d.b.subtractAcc;
                }
                if (d.d.BenefitType == true)
                    dtBenefitAdd.Rows.Add(row);
                else
                {
                    row["SubtractType"] = d.d.IsLoan ? (byte)HR_SubtractType.Loan : (byte)HR_SubtractType.Default;
                    dtBenefitSubtract.Rows.Add(row);
                }
            }
            foreach (var d in details_ManualEntry)
            {
                DataRow row;
                if (d.d.BenefitType == true)
                    row = dtBenefitAdd.NewRow();
                else
                    row = dtBenefitSubtract.NewRow();

                row["PayDetailId"] = d.d.PayDetailId;
                row["PayId"] = d.d.PayId;
                row["BenefitName"] = d.d.BenefitName;
                row["BenefitCalculation"] = d.d.BenefitCalculation;
                row["Amount"] = d.d.Amount;
                row["BenefitType"] = d.d.BenefitType;
                row["IsLoan"] = d.d.IsLoan == true ? "1" : "0";
                if (d.d.LoanDetailId.HasValue)
                    row["LoanDetailId"] = d.d.LoanDetailId.Value;

                row["BenefitId"] = d.d.BenefitId;
                row["IsTax"] = d.d.IsTax == true ? true : false;

                row["IsInsurance"] = d.d.IsInsurance == true ? "1" : "0"; ;
                row["CompanyInsuranceCalculation"] = d.d.CompanyInsuranceCalculation;

                row["CompanyInsuranceAmount"] = d.d.CompanyInsuranceAmount;
                row["CompanyInsuranceAmount"] = d.d.CompanyInsuranceAmount;
                
                if (d.d.BenefitType == true)
                    dtBenefitAdd.Rows.Add(row);
                else
                {
                    row["SubtractType"] = d.d.IsLoan ? (byte)HR_SubtractType.Loan : d.d.SubtractType != null ? (byte)d.d.SubtractType : (byte)HR_SubtractType.Default;
                    dtBenefitSubtract.Rows.Add(row);
                }
            }

            dtBenefitAdd.AcceptChanges();
            dtBenefitSubtract.AcceptChanges();
        }

        public static int SavePay(bool paid, int empId, int storeId, DateTime DueDate, DateTime StartDate, DateTime EndDate,
            DateTime PayDate, string Notes, int drawerAccId, decimal basic, decimal variable, decimal HoursValue,
            decimal DaysValue, decimal total, int? costCntr, DataTable dtBenefitAdd, DataTable dtBenefitSubtract, bool isSecondPayslip,
            bool OfflinePostToGL, int? HrAccruedSalaryAccount, int? HrSalaryExpensesAccount, decimal? salaryNetTax, decimal? companyshare, decimal? empshare, decimal? incentive)
        {
            ERPDataContext DB = new ERPDataContext();

            HR_Employee employee = DB.HR_Employees.Where(m => m.EmpId == empId).First();
            int DueJournalId = 0;
            int? payJournalId = null;

            //save invoice
            DAL.HR_Pay pay = new HR_Pay();

            pay.EmpId = empId;
            pay.StoreId = storeId;
            pay.CrncId = 0;
            pay.CrncRate = 1;

            pay.DueDate = DueDate;
            pay.FromDay = StartDate;
            pay.ToDay = EndDate;
            pay.PayDate = PayDate;

            pay.PayPeriod = employee.PayPeriod;
            pay.Notes = Notes;
            pay.DrawerAccountId = drawerAccId;

            pay.Basic = basic;
            pay.Variable = variable;
            pay.HoursValue = HoursValue;
            pay.DaysValue = DaysValue;
            pay.Total = total;
            pay.UserId = Shared.UserId;
            pay.IsPaid = paid;
            pay.CostCenter = costCntr;
            pay.IsSecondPaySlip = isSecondPayslip;
            pay.NetSalaryTax = salaryNetTax;
            pay.companyshare = companyshare;
            pay.empshare = empshare;

            #region save Tax

            decimal taxSalary = 0;
            double TaxableBenfits = 0;
            if (dtBenefitAdd.Rows.Count > 0)
            {
                TaxableBenfits = dtBenefitAdd.AsEnumerable().Where(x => x.Field<bool>("IsTax") == true).Sum(x => x.Field<double>("Amount"));
            }

            taxSalary = basic - (empshare != null ? empshare.Value : 0) + Convert.ToDecimal(TaxableBenfits);
            decimal _incentive = CalcIncentive(employee);
            pay.businessgaintax = Convert.ToDecimal(calcBusinessGainTax(taxSalary+_incentive));
            pay.Incentive = _incentive;

            #endregion
            DB.HR_Pays.InsertOnSubmit(pay);

            MyHelper.UpdateST_UserLog(DB, pay.PayId.ToString(), employee.EmpName,
(int)FormAction.Add, (int)FormsNames.HR_Pay);

            DB.SubmitChanges();

            HrHelper.SavePayJournal(pay, out DueJournalId, out payJournalId, pay.StoreId.Value, pay.DrawerAccountId, employee, dtBenefitSubtract
                , OfflinePostToGL, HrAccruedSalaryAccount, HrSalaryExpensesAccount);

            pay.DueJournalId = DueJournalId;
            pay.PayJournalId = payJournalId;
            DB.SubmitChanges();

            for (int x = 0; x < dtBenefitAdd.Rows.Count; x++)
            {
                if (dtBenefitAdd.Rows[x].RowState == DataRowState.Deleted)
                    continue;
                DAL.HR_PayDetail detail = new DAL.HR_PayDetail();
                detail.PayId = pay.PayId;
                detail.BenefitName = dtBenefitAdd.Rows[x]["BenefitName"].ToString();
                detail.BenefitCalculation = dtBenefitAdd.Rows[x]["BenefitCalculation"].ToString();
                detail.Amount = Convert.ToDecimal(dtBenefitAdd.Rows[x]["Amount"]);
                detail.BenefitType = true;
                detail.IsLoan = false;

                if (dtBenefitAdd.Rows[x]["IsLoan"].ToString() == "1")//commission                
                    detail.IsLoan = true;
                else
                    detail.IsLoan = false;

                if (dtBenefitAdd.Rows[x]["BenefitId"].ToString() == "0")//not benefit
                    detail.BenefitId = 0;
                else
                    detail.BenefitId = Convert.ToInt32(dtBenefitAdd.Rows[x]["BenefitId"]);

                detail.IsTax = Convert.ToBoolean(dtBenefitAdd.Rows[x]["IsTax"]);

                if (dtBenefitAdd.Rows[x]["IsInsurance"].ToString() == "1")//commission                
                    detail.IsInsurance = true;
                else
                    detail.IsInsurance = false;

                detail.CompanyInsuranceCalculation = dtBenefitAdd.Rows[x]["CompanyInsuranceCalculation"].ToString();
                detail.CompanyInsuranceAmount = Convert.ToDecimal(dtBenefitAdd.Rows[x]["CompanyInsuranceAmount"]);


                DB.HR_PayDetails.InsertOnSubmit(detail);
                DB.SubmitChanges();
            }

            for (int x = 0; x < dtBenefitSubtract.Rows.Count; x++)
            {
                if (dtBenefitSubtract.Rows[x].RowState == DataRowState.Deleted)
                    continue;
                DAL.HR_PayDetail detail = new DAL.HR_PayDetail();
                detail.PayId = pay.PayId;
                detail.BenefitName = dtBenefitSubtract.Rows[x]["BenefitName"].ToString();
                detail.BenefitCalculation = dtBenefitSubtract.Rows[x]["BenefitCalculation"].ToString();
                detail.Amount = Convert.ToDecimal(dtBenefitSubtract.Rows[x]["Amount"]);
                detail.BenefitType = false;

                if (dtBenefitSubtract.Rows[x]["IsLoan"].ToString() == "1")
                {
                    detail.IsLoan = true;
                    if (dtBenefitSubtract.Rows[x]["LoanDetailId"] != null && dtBenefitSubtract.Rows[x]["LoanDetailId"].ToString() != string.Empty)
                    {
                        //update installment
                        var loanDetail = DB.HR_LoanDetails.Where(z => z.LoanDetailId == Convert.ToInt32(dtBenefitSubtract.Rows[x]["LoanDetailId"])).First();
                        loanDetail.Payed = true;
                        loanDetail.PayDate = MyHelper.Get_Server_DateTime();

                        //get installment not paid yet
                        var notPaidInst = DB.HR_LoanDetails.Where(d => d.LoanId == loanDetail.LoanId &&
                                            d.Payed == false).Count();

                        //update loan
                        if (notPaidInst == 0)//mean the last installment
                            DB.HR_Loans.Where(z => z.LoanId == loanDetail.LoanId).First().Payed = true;

                        DB.SubmitChanges();

                        detail.LoanDetailId = Convert.ToInt32(dtBenefitSubtract.Rows[x]["LoanDetailId"]);
                    }
                }
                else
                    detail.IsLoan = false;

                if (dtBenefitSubtract.Rows[x]["BenefitId"].ToString() == "0")//not benefit
                    detail.BenefitId = 0;
                else
                    detail.BenefitId = Convert.ToInt32(dtBenefitSubtract.Rows[x]["BenefitId"]);

                if (dtBenefitSubtract.Rows[x]["IsTax"].ToString() == "1")
                    detail.IsTax = true;
                else
                    detail.IsTax = false;

                if (dtBenefitSubtract.Rows[x]["IsInsurance"].ToString() == "1")//commission                
                    detail.IsInsurance = true;
                else
                    detail.IsInsurance = false;

                detail.CompanyInsuranceCalculation = dtBenefitSubtract.Rows[x]["CompanyInsuranceCalculation"].ToString();
                detail.CompanyInsuranceAmount = Convert.ToDecimal(dtBenefitSubtract.Rows[x]["CompanyInsuranceAmount"]);

                DB.HR_PayDetails.InsertOnSubmit(detail);
                DB.SubmitChanges();
            }

            return pay.PayId;
        }

        public static int UpdatePay(ERPDataContext DB, bool paid, int payId, HR_Pay hrPay, int empId, int storeId, DateTime dueDate, DateTime fromDay,
            DateTime toDay, DateTime payDate, string notes, int DrawerAccountId, decimal Basic, decimal Variable, decimal HoursValue
            , decimal DaysValue, decimal Total, int? CostCntr, DataTable dtBenefitSubtract, DataTable dtBenefitAdd,
            bool OfflinePostToGL, int? HrAccruedSalaryAccount, int? HrSalaryExpensesAccount, decimal? salaryNetTax, decimal? companyshare, decimal? empshare)
        {
            int DueJournalId = 0;
            int? payJournalId = null;

            //save invoice            
            DAL.HR_Pay pay = hrPay;
            HR_Employee emp = DB.HR_Employees.Where(m => m.EmpId == empId).Select(m => m).First();
            //if (pay == null)
            //    return -1;

            pay.EmpId = empId;
            pay.StoreId = storeId;
            pay.CrncId = 0;
            pay.CrncRate = 1;

            pay.DueDate = dueDate;
            pay.FromDay = fromDay;
            pay.ToDay = toDay;
            pay.PayDate = payDate;
            pay.PayPeriod = emp.PayPeriod;
            pay.Notes = notes;
            pay.DrawerAccountId = DrawerAccountId;

            pay.Basic = Basic;
            pay.Variable = Variable;
            pay.HoursValue = HoursValue;
            pay.DaysValue = DaysValue;
            pay.Total = Total;
            pay.LastUpdateUserId = Shared.UserId;
            pay.LastUpdateDate = DateTime.Now;
            pay.IsPaid = paid;
            pay.CostCenter = CostCntr;

            pay.NetSalaryTax = salaryNetTax;
            pay.companyshare = companyshare;
            pay.empshare = empshare;

            MyHelper.UpdateST_UserLog(DB, pay.PayId.ToString(), emp.EmpName,
(int)FormAction.Edit, (int)FormsNames.HR_Pay);

            DB.SubmitChanges();

            HrHelper.SavePayJournal(pay, out DueJournalId, out payJournalId,
                pay.StoreId.Value, pay.DrawerAccountId, emp, dtBenefitSubtract, OfflinePostToGL, HrAccruedSalaryAccount, HrSalaryExpensesAccount);

            pay.DueJournalId = DueJournalId;
            pay.PayJournalId = payJournalId;
            DB.SubmitChanges();

            var payDetails = DB.HR_PayDetails.Where(x => x.PayId == pay.PayId);
            DB.HR_PayDetails.DeleteAllOnSubmit(payDetails);
            DB.SubmitChanges();
            /****************/

            for (int x = 0; x < dtBenefitAdd.Rows.Count; x++)
            {
                if (dtBenefitAdd.Rows[x].RowState == DataRowState.Deleted)
                    continue;
                DAL.HR_PayDetail detail = new DAL.HR_PayDetail();
                detail.PayId = pay.PayId;
                detail.BenefitName = dtBenefitAdd.Rows[x]["BenefitName"].ToString();
                detail.BenefitCalculation = dtBenefitAdd.Rows[x]["BenefitCalculation"].ToString();
                detail.Amount = Convert.ToDecimal(dtBenefitAdd.Rows[x]["Amount"]);
                detail.BenefitType = true;
                detail.IsLoan = false;

                if (dtBenefitAdd.Rows[x]["IsLoan"].ToString() == "1")//commission
                {
                    detail.IsLoan = true;
                }
                else
                    detail.IsLoan = false;

                if (dtBenefitAdd.Rows[x]["BenefitId"].ToString() == "0")//not benefit
                    detail.BenefitId = 0;
                else
                    detail.BenefitId = Convert.ToInt32(dtBenefitAdd.Rows[x]["BenefitId"]);

                detail.IsTax = Convert.ToBoolean(dtBenefitAdd.Rows[x]["IsTax"]);

                if (dtBenefitAdd.Rows[x]["IsInsurance"].ToString() == "1")//commission                
                    detail.IsInsurance = true;
                else
                    detail.IsInsurance = false;

                detail.CompanyInsuranceCalculation = dtBenefitAdd.Rows[x]["CompanyInsuranceCalculation"].ToString();
                detail.CompanyInsuranceAmount = Convert.ToDecimal(dtBenefitAdd.Rows[x]["CompanyInsuranceAmount"]);


                DB.HR_PayDetails.InsertOnSubmit(detail);
                DB.SubmitChanges();
            }
            for (int x = 0; x < dtBenefitSubtract.Rows.Count; x++)
            {
                if (dtBenefitSubtract.Rows[x].RowState == DataRowState.Deleted)
                    continue;
                DAL.HR_PayDetail detail = new DAL.HR_PayDetail();
                detail.PayId = pay.PayId;
                detail.BenefitName = dtBenefitSubtract.Rows[x]["BenefitName"].ToString();
                detail.BenefitCalculation = dtBenefitSubtract.Rows[x]["BenefitCalculation"].ToString();
                detail.Amount = Convert.ToDecimal(dtBenefitSubtract.Rows[x]["Amount"]);
                detail.BenefitType = false;

                if (dtBenefitSubtract.Rows[x]["IsLoan"].ToString() == "1")// loan
                {
                    detail.IsLoan = true;

                    if (dtBenefitSubtract.Rows[x]["LoanDetailId"] != null && dtBenefitSubtract.Rows[x]["LoanDetailId"].ToString() != string.Empty)
                        detail.LoanDetailId = Convert.ToInt32(dtBenefitSubtract.Rows[x]["LoanDetailId"]);
                }
                else
                    detail.IsLoan = false;

                if (dtBenefitSubtract.Rows[x]["BenefitId"].ToString() == "0")//not benefit
                    detail.BenefitId = 0;
                else
                    detail.BenefitId = Convert.ToInt32(dtBenefitSubtract.Rows[x]["BenefitId"]);

                if (dtBenefitSubtract.Rows[x]["IsTax"].ToString() == "1")
                    detail.IsTax = true;
                else
                    detail.IsTax = false;

                if (dtBenefitSubtract.Rows[x]["IsInsurance"].ToString() == "1")//commission                
                    detail.IsInsurance = true;
                else
                    detail.IsInsurance = false;

                detail.CompanyInsuranceCalculation = dtBenefitSubtract.Rows[x]["CompanyInsuranceCalculation"].ToString();
                detail.CompanyInsuranceAmount = Convert.ToDecimal(dtBenefitSubtract.Rows[x]["CompanyInsuranceAmount"]);


                DB.HR_PayDetails.InsertOnSubmit(detail);
                DB.SubmitChanges();
            }

            return pay.PayId;
        }

        public static void SavePayJournal(DAL.HR_Pay pay, out int dueJrnl, out int? payJrnl, int storeId, int drawerAccId, HR_Employee emp,
            DataTable dtBenefitSubtract, bool OfflinePostToGL, int? HrAccruedSalaryAccount, int? HrSalaryExpensesAccount)
        {
            ERPDataContext erpDB = new ERPDataContext();
            payJrnl = null;

            decimal loanValue = 0;
            var loanRow = (from DataRow dr in dtBenefitSubtract.Rows
                           where dr.RowState != DataRowState.Deleted
                           where dr["IsLoan"].ToString() == "1"
                           //select  dr).FirstOrDefault();
                           select
                            Convert.ToDecimal(dr["Amount"])).ToList().Sum();

            //if (loanRow != null && loanRow["IsLoan"] != DBNull.Value && loanRow["IsLoan"] != null)

            loanValue = loanRow;

            decimal SubtractValue = 0, PenaltyRow = 0, VacationRow = 0, AbsenceRow = 0;

            if (Shared.st_Store.ACC_PenaltyAcount != null)
            {
                PenaltyRow = (from DataRow dr in dtBenefitSubtract.Rows
                              where dr.RowState != DataRowState.Deleted
                              where dr["IsLoan"].ToString() == "0"
                              where Convert.ToInt32(dr["SubtractAcc"]) == 0
                              where Convert.ToInt32(dr["SubtractType"]) == (byte)HR_SubtractType.Penalty
                              //select  dr).FirstOrDefault();
                              select
                               Convert.ToDecimal(dr["Amount"])).ToList().Sum();
            }

            if (Shared.st_Store.ACC_VacationAcount != null)
            {
                VacationRow = (from DataRow dr in dtBenefitSubtract.Rows
                               where dr.RowState != DataRowState.Deleted
                               where dr["IsLoan"].ToString() == "0"
                               where Convert.ToInt32(dr["SubtractAcc"]) == 0
                               where Convert.ToInt32(dr["SubtractType"]) == (byte)HR_SubtractType.Vacation
                               //select  dr).FirstOrDefault();
                               select
                                Convert.ToDecimal(dr["Amount"])).ToList().Sum();
            }

            if (Shared.st_Store.ACC_AbsenceAcount != null)
            {
                AbsenceRow = (from DataRow dr in dtBenefitSubtract.Rows
                                  where dr.RowState != DataRowState.Deleted
                                  where dr["IsLoan"].ToString() == "0"
                                  where Convert.ToInt32(dr["SubtractAcc"]) == 0
                                  where Convert.ToInt32(dr["SubtractType"]) == (byte)HR_SubtractType.Absence
                                  //select  dr).FirstOrDefault();
                                  select
                                   Convert.ToDecimal(dr["Amount"])).ToList().Sum();
            }

            var subtractOther = (from DataRow dr in dtBenefitSubtract.Rows
                                 where dr.RowState != DataRowState.Deleted
                                 where dr["IsLoan"].ToString() == "0"
                                 where Convert.ToInt32(dr["SubtractAcc"]) != 0
                                 //select  dr).FirstOrDefault();
                                 select
                                  Convert.ToDecimal(dr["Amount"])).ToList().Sum();
            //if (loanRow != null && loanRow["IsLoan"] != DBNull.Value && loanRow["IsLoan"] != null)

            SubtractValue = subtractOther + VacationRow + PenaltyRow + AbsenceRow;

            bool DueIsNew = false;
            bool PayIsNew = false;
            ACC_Journal dueJrnl_ = null;
            ACC_Journal payJrnl_ = null;

            string dueNote = (Shared.IsEnglish ? ResHREn.Payslip : ResHRAr.Payslip) + " " + pay.PayId;
            dueNote += "\r\n" + (Shared.IsEnglish ? ResHREn.empDue : ResHRAr.empDue);// +" " + emp.EmpName;

            string payNote = (Shared.IsEnglish ? ResHREn.Payslip : ResHRAr.Payslip) + " " + pay.PayId;
            payNote += "\r\n" + (Shared.IsEnglish ? ResHREn.empPay : ResHRAr.empPay);// +" " + emp.EmpName;

            string payLoan = (Shared.IsEnglish ? ResHREn.Payslip : ResHRAr.Payslip) + " " + pay.PayId;
            payLoan += "\r\n" + (Shared.IsEnglish ? ResHREn.empLoadPay : ResHRAr.empLoadPay);// +" " + emp.EmpName;

            decimal netTaxValue = 0;

            #region Calc Net Tax Value
            if (emp.IsNetTax == true && pay.NetSalaryTax != null)
            {
                decimal totalbeforeNet = (100 * pay.Total) / (100 - Convert.ToDecimal(pay.NetSalaryTax));

                netTaxValue = totalbeforeNet > 2000 ? Math.Round(totalbeforeNet * pay.NetSalaryTax.GetValueOrDefault(0) / 100, 2, MidpointRounding.AwayFromZero) : 0;
            }
            #endregion

            #region Save due Journal
            if (pay.DueJournalId == null)
            {
                dueJrnl_ = new ACC_Journal();
                dueJrnl_.InsertDate = pay.DueDate;
                dueJrnl_.InsertUser = Shared.UserId;
                dueJrnl_.JCode = HelperAcc.Get_Jornal_Code();
                dueJrnl_.JNumber = pay.PayId.ToString();
                dueJrnl_.JNotes = dueNote;
                dueJrnl_.ProcessId = (int)Process.PaySalary;
                dueJrnl_.SourceId = pay.PayId;
                dueJrnl_.IsPosted = !OfflinePostToGL;
                dueJrnl_.StoreId = storeId;
                dueJrnl_.CrncId = 0;
                dueJrnl_.CrncRate = 1;
                dueJrnl_.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(dueJrnl_.InsertDate, dueJrnl_.ProcessId);

                DueIsNew = true;
            }
            else
            {
                dueJrnl_ = erpDB.ACC_Journals.Where(j => j.JournalId == pay.DueJournalId).First();
                dueJrnl_.JNumber = pay.PayId.ToString();
                dueJrnl_.StoreId = storeId;
                dueJrnl_.InsertDate = pay.DueDate;
                dueJrnl_.LastUpdateDate = MyHelper.Get_Server_DateTime();
                dueJrnl_.LastUpdateUser = Shared.UserId;
            }

            if (DueIsNew)
                erpDB.ACC_Journals.InsertOnSubmit(dueJrnl_);
            else
            {
                //delete due journal details
                erpDB.ACC_JournalDetails.DeleteAllOnSubmit(erpDB.ACC_JournalDetails.Where(x => x.JournalId ==
                    dueJrnl_.JournalId));

                //delete pay journal
                if (pay.IsPaid == false && pay.PayJournalId.HasValue)
                {
                    erpDB.ACC_JournalDetails.DeleteAllOnSubmit(erpDB.ACC_JournalDetails.
                        Where(x => x.JournalId == pay.PayJournalId.Value));
                    erpDB.ACC_Journals.DeleteAllOnSubmit(erpDB.ACC_Journals.
                       Where(x => x.JournalId == pay.PayJournalId.Value));

                    payJrnl = null;
                }
            }
            erpDB.SubmitChanges();
            dueJrnl = dueJrnl_.JournalId;

            #region Debit-Emp Salary Expenses
            ACC_JournalDetail jornal_Detail_2 = new ACC_JournalDetail();
            jornal_Detail_2.JournalId = dueJrnl_.JournalId;
            jornal_Detail_2.AccountId =
                emp.ExpensesAccount.HasValue ? emp.ExpensesAccount.Value : HrSalaryExpensesAccount.Value;

            jornal_Detail_2.Credit = 0;
            jornal_Detail_2.Debit = Shared.st_Store.EgyptionLaw == true ? (pay.Total + SubtractValue + loanValue + pay.empshare.GetValueOrDefault(0) + pay.companyshare.GetValueOrDefault(0) + netTaxValue + pay.businessgaintax.GetValueOrDefault(0)) : pay.Total + loanValue + pay.Incentive.GetValueOrDefault(0);
            jornal_Detail_2.Notes = dueNote;
            jornal_Detail_2.CostCenter = pay.CostCenter;
            jornal_Detail_2.CrncId = 0;
            jornal_Detail_2.CrncRate = 1;

            erpDB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_2);
            #endregion

            #region Credit-Emp Account
            ACC_JournalDetail loanJrnl = new ACC_JournalDetail();
            loanJrnl.JournalId = dueJrnl_.JournalId;
            loanJrnl.AccountId = emp.AccruedAccount.HasValue ? emp.AccruedAccount.Value : HrAccruedSalaryAccount.Value;
            loanJrnl.Credit = pay.Total + loanValue + pay.Incentive.GetValueOrDefault(0);
            loanJrnl.Debit = 0;
            loanJrnl.Notes = dueNote;
            loanJrnl.CrncId = 0;
            loanJrnl.CrncRate = 1;
            erpDB.ACC_JournalDetails.InsertOnSubmit(loanJrnl);

            if (Shared.st_Store.EgyptionLaw == true)
            {
                if ((pay.empshare.GetValueOrDefault(0) + pay.companyshare.GetValueOrDefault(0)) > 0)
                {
                    ACC_JournalDetail insuranceJrnl = new ACC_JournalDetail();
                    insuranceJrnl.JournalId = dueJrnl_.JournalId;
                    insuranceJrnl.AccountId = emp.InsuranceAcc.HasValue ? emp.InsuranceAcc.Value : Shared.st_Store.InsuranceAcc.Value;
                    insuranceJrnl.Credit = pay.empshare.GetValueOrDefault(0) + pay.companyshare.GetValueOrDefault(0);
                    insuranceJrnl.Debit = 0;
                    insuranceJrnl.Notes = Shared.IsEnglish ? dueNote : dueNote + "\r\n التأمينـات";
                    insuranceJrnl.CrncId = 0;
                    insuranceJrnl.CrncRate = 1;
                    erpDB.ACC_JournalDetails.InsertOnSubmit(insuranceJrnl);
                }
                if (pay.businessgaintax.GetValueOrDefault(0) > 0)
                {
                    ACC_JournalDetail BusTaxJrnl = new ACC_JournalDetail();
                    BusTaxJrnl.JournalId = dueJrnl_.JournalId;
                    BusTaxJrnl.AccountId = Shared.st_Store.BusinessGainAcc.Value;
                    BusTaxJrnl.Credit = pay.businessgaintax.GetValueOrDefault(0);
                    BusTaxJrnl.Debit = 0;
                    BusTaxJrnl.Notes = Shared.IsEnglish ? dueNote : dueNote + "\r\n ضريبة كسب العمل";
                    BusTaxJrnl.CrncId = 0;
                    BusTaxJrnl.CrncRate = 1;
                    erpDB.ACC_JournalDetails.InsertOnSubmit(BusTaxJrnl);
                }
                if (pay.NetSalaryTax.GetValueOrDefault(0) > 0)
                {
                    ACC_JournalDetail BusTaxJrnl = new ACC_JournalDetail();
                    BusTaxJrnl.JournalId = dueJrnl_.JournalId;
                    BusTaxJrnl.AccountId = Shared.st_Store.NetTaxAcc.Value;
                    BusTaxJrnl.Credit = netTaxValue;
                    BusTaxJrnl.Debit = 0;
                    BusTaxJrnl.Notes = Shared.IsEnglish ? dueNote : "الضريبة التكافلية";
                    BusTaxJrnl.CrncId = 0;
                    BusTaxJrnl.CrncRate = 1;
                    erpDB.ACC_JournalDetails.InsertOnSubmit(BusTaxJrnl);
                }
                
                //if (SubtractValue > 0 && emp.HR_SubtractAcc.GetValueOrDefault(0) != 0)
                //{
                //    ACC_JournalDetail BusTaxJrnl = new ACC_JournalDetail();
                //    BusTaxJrnl.JournalId = dueJrnl_.JournalId;
                //    BusTaxJrnl.AccountId = Shared.st_Store.HR_SubtractAcc.Value;
                //    BusTaxJrnl.Credit = SubtractValue;
                //    BusTaxJrnl.Debit = 0;
                //    BusTaxJrnl.Notes = Shared.IsEnglish ? dueNote : "استـقطـاعـات";
                //    BusTaxJrnl.CrncId = 0;
                //    BusTaxJrnl.CrncRate = 1;
                //    erpDB.ACC_JournalDetails.InsertOnSubmit(BusTaxJrnl);
                //}

                if (VacationRow > 0)
                {
                    ACC_JournalDetail BusTaxJrnl = new ACC_JournalDetail();
                    BusTaxJrnl.JournalId = dueJrnl_.JournalId;
                    BusTaxJrnl.AccountId = Shared.st_Store.ACC_VacationAcount.Value;
                    BusTaxJrnl.Credit = VacationRow;
                    BusTaxJrnl.Debit = 0;
                    BusTaxJrnl.Notes = Shared.IsEnglish ? dueNote : dueNote + "\r\n أجازات";
                    BusTaxJrnl.CrncId = 0;
                    BusTaxJrnl.CrncRate = 1;
                    erpDB.ACC_JournalDetails.InsertOnSubmit(BusTaxJrnl);
                }

                if (PenaltyRow > 0)
                {
                    ACC_JournalDetail BusTaxJrnl = new ACC_JournalDetail();
                    BusTaxJrnl.JournalId = dueJrnl_.JournalId;
                    BusTaxJrnl.AccountId = Shared.st_Store.ACC_PenaltyAcount.Value;
                    BusTaxJrnl.Credit = PenaltyRow;
                    BusTaxJrnl.Debit = 0;
                    BusTaxJrnl.Notes = Shared.IsEnglish ? dueNote : dueNote + "\r\n جزاءات";
                    BusTaxJrnl.CrncId = 0;
                    BusTaxJrnl.CrncRate = 1;
                    erpDB.ACC_JournalDetails.InsertOnSubmit(BusTaxJrnl);
                }

                if (AbsenceRow > 0)
                {
                    ACC_JournalDetail BusTaxJrnl = new ACC_JournalDetail();
                    BusTaxJrnl.JournalId = dueJrnl_.JournalId;
                    BusTaxJrnl.AccountId = Shared.st_Store.ACC_AbsenceAcount.Value;
                    BusTaxJrnl.Credit = AbsenceRow;
                    BusTaxJrnl.Debit = 0;
                    BusTaxJrnl.Notes = Shared.IsEnglish ? dueNote : dueNote + "\r\n غيابات";
                    BusTaxJrnl.CrncId = 0;
                    BusTaxJrnl.CrncRate = 1;
                    erpDB.ACC_JournalDetails.InsertOnSubmit(BusTaxJrnl);
                }

                erpDB.SubmitChanges();
                var subtractOtherAcc = (from DataRow dr in dtBenefitSubtract.Rows
                                        where dr.RowState != DataRowState.Deleted
                                        where dr["IsLoan"].ToString() == "0"
                                        where Convert.ToInt32(dr["SubtractAcc"]) != 0
                                        select dr);
                //select new {
                //    amount = Convert.ToDecimal(dr["Amount"],
                //    accId = Convert.ToInt32(dr["SubtractAcc"]),
                //    subname = Convert.ToString(dr["BenefitName"])
                // }).ToList();
                foreach (var dr in subtractOtherAcc)
                {

                    ACC_JournalDetail subJrnl = new ACC_JournalDetail();
                    subJrnl.JournalId = dueJrnl_.JournalId;
                    subJrnl.AccountId = Convert.ToInt32(dr["SubtractAcc"]);
                    subJrnl.Credit = Convert.ToDecimal(dr["Amount"]);
                    subJrnl.Debit = 0;
                    subJrnl.Notes = Shared.IsEnglish ? Convert.ToString(dr["BenefitName"]) : Convert.ToString(dr["BenefitName"]);
                    subJrnl.CrncId = 0;
                    subJrnl.CrncRate = 1;
                    erpDB.ACC_JournalDetails.InsertOnSubmit(subJrnl);
                    erpDB.SubmitChanges();
                }
            }
            #endregion

            erpDB.SubmitChanges();
            #endregion

            #region Save Pay Journal
            if (pay.IsPaid)
            {
                if (pay.PayJournalId == null)
                {
                    payJrnl_ = new ACC_Journal();
                    payJrnl_.InsertDate = pay.PayDate.Value;
                    payJrnl_.InsertUser = Shared.UserId;
                    payJrnl_.JCode = HelperAcc.Get_Jornal_Code();
                    payJrnl_.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(payJrnl_.InsertDate, payJrnl_.ProcessId);
                    payJrnl_.JNumber = pay.PayId.ToString();
                    payJrnl_.JNotes = payNote;
                    payJrnl_.ProcessId = (int)Process.PaySalary;
                    payJrnl_.SourceId = pay.PayId;
                    payJrnl_.IsPosted = !OfflinePostToGL;
                    payJrnl_.StoreId = storeId;
                    payJrnl_.CrncId = 0;
                    payJrnl_.CrncRate = 1;

                    PayIsNew = true;
                }
                else
                {
                    payJrnl_ = erpDB.ACC_Journals.Where(j => j.JournalId == pay.PayJournalId).First();
                    payJrnl_.JNumber = pay.PayId.ToString();
                    payJrnl_.StoreId = storeId;
                    payJrnl_.InsertDate = pay.PayDate.Value;
                    payJrnl_.LastUpdateDate = MyHelper.Get_Server_DateTime();
                    payJrnl_.LastUpdateUser = Shared.UserId;
                }

                if (PayIsNew)
                    erpDB.ACC_Journals.InsertOnSubmit(payJrnl_);
                else
                {
                    //delete pay journal details
                    erpDB.ACC_JournalDetails.DeleteAllOnSubmit(
                        erpDB.ACC_JournalDetails.Where(x => x.JournalId == payJrnl_.JournalId));
                }
                erpDB.SubmitChanges();

                payJrnl = payJrnl_.JournalId;

                #region Debit-Emp Accrued Salary Account
                ACC_JournalDetail jd = new ACC_JournalDetail();
                jd.JournalId = payJrnl_.JournalId;
                jd.AccountId = emp.AccruedAccount.HasValue ? emp.AccruedAccount.Value : HrAccruedSalaryAccount.Value;
                jd.Debit = pay.Total + loanValue + pay.Incentive.GetValueOrDefault(0);
                jd.Credit = 0;
                jd.Notes = payNote;
                jd.CrncId = 0;
                jd.CrncRate = 1;

                erpDB.ACC_JournalDetails.InsertOnSubmit(jd);
                #endregion

                #region Credit-Emp Account
                if (loanValue > 0)
                {
                    ACC_JournalDetail loanJrnl_ = new ACC_JournalDetail();
                    loanJrnl_.JournalId = payJrnl_.JournalId;
                    loanJrnl_.AccountId = emp.AccountId.Value;
                    loanJrnl_.Debit = 0;
                    loanJrnl_.Credit = loanValue;
                    loanJrnl_.Notes = payLoan;
                    loanJrnl_.CrncId = 0;
                    loanJrnl_.CrncRate = 1;

                    erpDB.ACC_JournalDetails.InsertOnSubmit(loanJrnl_);
                }
                #endregion

                #region credit-Drawer
                ACC_JournalDetail DrawerJrnl = new ACC_JournalDetail();
                DrawerJrnl.JournalId = payJrnl_.JournalId;
                DrawerJrnl.AccountId = drawerAccId; // حساب الخزينة
                DrawerJrnl.Debit = 0;
                DrawerJrnl.Credit = pay.Total + pay.Incentive.GetValueOrDefault(0);
                DrawerJrnl.Notes = payNote;
                DrawerJrnl.CrncId = 0;
                DrawerJrnl.CrncRate = 1;
                erpDB.ACC_JournalDetails.InsertOnSubmit(DrawerJrnl);
                #endregion

                erpDB.SubmitChanges();
            }

            #endregion
        }

        public static void DeletePaySlip(ERPDataContext DB, int payId)
        {
            var pay = DB.HR_Pays.Where(s => s.PayId == payId).First();

            var payDetail = DB.HR_PayDetails.Where(s => s.PayId == payId).Select(s => s);

            foreach (HR_PayDetail d in payDetail)//تحديث قسط السلفة
            {
                if (d.LoanDetailId.HasValue)
                {
                    var loadDetail = DB.HR_LoanDetails.Where(s => s.LoanDetailId == d.LoanDetailId.Value).FirstOrDefault();
                    if (loadDetail != null)
                    {
                        loadDetail.PayDate = null;
                        loadDetail.Payed = false;
                    }
                }
            }

            DB.HR_PayDetails.DeleteAllOnSubmit(payDetail);

            DB.ACC_JournalDetails.DeleteAllOnSubmit
                (DB.ACC_JournalDetails.Where(s => s.JournalId == pay.DueJournalId));
            DB.ACC_Journals.DeleteAllOnSubmit(DB.ACC_Journals.Where(s => s.JournalId == pay.DueJournalId));

            if (pay.PayJournalId.HasValue)
            {
                DB.ACC_JournalDetails.DeleteAllOnSubmit
                (DB.ACC_JournalDetails.Where(s => s.JournalId == pay.PayJournalId));
                DB.ACC_Journals.DeleteAllOnSubmit(DB.ACC_Journals.Where(s => s.JournalId == pay.PayJournalId));
            }

            MyHelper.UpdateST_UserLog(DB, pay.PayId.ToString(), DB.HR_Employees.Where(x => x.EmpId == pay.EmpId).Select(x => x.EmpName).FirstOrDefault(),
(int)FormAction.Delete, (int)FormsNames.HR_Pay);

            DB.HR_Pays.DeleteOnSubmit(pay);

            DB.SubmitChanges();
        }

        #endregion

        public static int GetShiftDayMatch(List<EmpShift> thisShift, DateTime theDay)
        {
            DateTime shiftStartDate = thisShift.Select(h => h.StartDateTime).First();
            int diff = theDay.Subtract(shiftStartDate).Days;
            int dayMatch = Math.Abs(diff) % thisShift.Count;
            //if (dayMatch == 0)
            //    dayMatch = thisShift.Count - 1;//last day in shift
            //else
            //    dayMatch = dayMatch;

            if (diff >= 0)
                return dayMatch;
            else
                return dayMatch > 0 ? thisShift.Count - dayMatch : dayMatch;

            //if (dayMatch == 0)
            //    return dayMatch;
            //else if (dayMatch > 0 && diff < thisShift.Count)
            //    return diff;
            //else if (dayMatch > 0 && diff > thisShift.Count)
            //    return Math.Abs(dayMatch - thisShift.Count);
        }

        public static List<EmpShift> GetEmpsShifts()
        {
            ERPDataContext DB = new ERPDataContext();
            return (from s in DB.HR_Shifts
                    from d in DB.HR_ShiftDetails.Where(d => d.ShiftId == s.ShiftId).DefaultIfEmpty()

                    from t in DB.HR_TimeTables.Where(t => d != null && t.TimeId == d.TimeTableId).DefaultIfEmpty()

                    select new EmpShift
                    {
                        ShiftId = s.ShiftId,
                        ShiftName = s.ShiftName,
                        StartDateTime = s.StartDateTime,
                        ShiftType = s.ShiftType,
                        IsShiftPerDay = s.IsShiftPerDay,
                        ShiftDetailId = d == null ? -1 : d.ShiftDetailId,
                        TimeId = t == null ? 0 : t.TimeId,
                        TimeName = t == null ? (Shared.IsEnglish ? ResHREn.Weekend : ResHRAr.Weekend) : t.TimeName,
                        AttendStart = t == null ? null : (DateTime?)t.AttendStart,
                        AttendTime = t == null ? null : (DateTime?)t.AttendTime,
                        AttendEnd = t == null ? null : (DateTime?)t.AttendEnd,
                        LeaveStart = t == null ? null : (DateTime?)t.LeaveStart,
                        LeaveTime = t == null ? null : (DateTime?)t.LeaveTime,
                        LeaveEnd = t == null ? null : (DateTime?)t.LeaveEnd,
                    }).OrderBy(x => x.ShiftId).ThenBy(x => x.ShiftDetailId).ToList();
        }

        public static List<AdvLogRecord> LoadEmpAdvLog(ERPDataContext DB, List<EmpShift> Shift1, List<EmpShift> Shift2, List<EmpShift> Shift3, emp4Att emp, DateTime dt_From, DateTime dt_To,
            out double totalWorkMinutes, out double totalDelayMinutes, out double totalOvertimeMinutes, out decimal workDays, bool calcDelayForAttendOnly,
            out List<ShiftRec> lst_ShiftsTimes)
        {
            totalWorkMinutes = totalDelayMinutes = totalOvertimeMinutes = 0;
            workDays = 0;

            List<AdvLogRecord> lst_advLogRecords = new List<AdvLogRecord>();

            int shiftType4PerDayEmp = 0;
            if (Shift1 == null && Shift2 == null)//per day shift
            {
                lst_ShiftsTimes = GetShiftsTimePeriodsPerDay(DB, emp, dt_From, dt_To);
                shiftType4PerDayEmp = DB.HR_Shifts.Where(x => x.ShiftId == emp.Shift1Id).Select(x => x.ShiftType).First();
            }
            else
                lst_ShiftsTimes = GetShiftsTimePeriods(Shift1, Shift2, Shift3, dt_From, dt_To);


            #region Emps Trans
            var DelayOverDocs = (from f in DB.HR_Delays
                                 where
                                  (f.DelayDay.Date >= dt_From && f.DelayDay.Date <= dt_To)
                                 select new
                                 {
                                     IsDelay = true,
                                     Id = f.DelayId,
                                     f.EmpId,
                                     Date = f.DelayDay,
                                     FpInId = f.FpInId,
                                     FpOutId = f.FpOutId,
                                 }).
                               Union(from f in DB.HR_OverTimes
                                     where
                                     (f.OverTimeDay.Date >= dt_From && f.OverTimeDay.Date <= dt_To)
                                     select new
                                     {
                                         IsDelay = false,
                                         Id = f.OverTimeId,
                                         f.EmpId,
                                         Date = f.OverTimeDay,
                                         FpInId = f.FpInId,
                                         FpOutId = f.FpOutId,
                                     }).ToList();
            #endregion

            //get fingerprints
            var Time = (from a in DB.HR_AttendLogAdvs
                        where a.Time.Date >= dt_From.Date &&
                        a.Time.Date <= dt_To.Date &&
                        a.EmpId == emp.EmpId

                        orderby a.Time
                        select new
                        {
                            a.AttendLogAdvId,
                            Time = a.Time,
                            a.IsIn,
                        }).ToList();

            //Add fingerprints to datatable, 
            foreach (var t in Time)
            {
                DateTime? shifttime = null;
                DateTime fpTime = DateTime.Now;
                bool isIn = true;
                double workMinutes = 0;
                double delayMinutes = 0;
                double overTimeMinutes = 0;
                bool isWeekEnd = false;
                string shiftName = string.Empty;

                #region Match Actual times to Shift Times, and Calculate delay & Overtime

                ShiftRec Shift1Match = null;
                ShiftRec Shift2Match = null;
                ShiftRec Shift3Match = null;
                Shift1Match = lst_ShiftsTimes.Where(x => x.Shift1start <= t.Time && x.Shift1end >= t.Time).FirstOrDefault();
                if (Shift2 != null)
                    Shift2Match = lst_ShiftsTimes.Where(x => x.Shift2start <= t.Time && x.Shift2end >= t.Time).FirstOrDefault();
                if (Shift3 != null)
                    Shift3Match = lst_ShiftsTimes.Where(x => x.Shift3start <= t.Time && x.Shift3end >= t.Time).FirstOrDefault();



                if (Shift1Match == null && Shift2Match == null && Shift3Match == null)
                {
                    #region WeekEnd or PerHour shift or out of shift time ranges
                    shifttime = null;
                    fpTime = t.Time;
                    isIn = t.IsIn;
                    if (Shift1 != null)
                    {
                        if (Shift1[0].ShiftType == (int)ShiftType.PerHour)
                            isWeekEnd = false;
                        else
                            isWeekEnd = true;
                    }
                    else //per day
                    {
                        if (shiftType4PerDayEmp == (int)ShiftType.PerHour)
                            isWeekEnd = false;
                        else
                            isWeekEnd = true;
                    }

                    if (isIn == false)//calculate work hours
                    {
                        if (lst_advLogRecords.Count > 0 && Convert.ToBoolean(lst_advLogRecords[lst_advLogRecords.Count - 1].IsIn) == true)
                        {
                            workMinutes = Convert.ToInt32(t.Time.Subtract(Convert.ToDateTime(lst_advLogRecords[lst_advLogRecords.Count - 1].Time)).TotalMinutes);
                            totalWorkMinutes += workMinutes;
                            workDays++;

                            if (Shift1 != null)
                            {
                                if (Shift1[0].ShiftType != (int)ShiftType.PerHour)
                                {
                                    if (workMinutes > emp.OvertimeFreeMinutes)
                                    {
                                        overTimeMinutes = workMinutes;
                                        totalOvertimeMinutes += workMinutes;
                                    }
                                }
                            }
                            else
                            {
                                if (shiftType4PerDayEmp != (int)ShiftType.PerHour)
                                {
                                    if (workMinutes > emp.OvertimeFreeMinutes)
                                    {
                                        overTimeMinutes = workMinutes;
                                        totalOvertimeMinutes += workMinutes;
                                    }
                                }
                            }
                        }
                    }
                    #endregion
                }
                else
                {
                    fpTime = t.Time;
                    isIn = t.IsIn;
                    isWeekEnd = false;

                    if (t.IsIn)
                    {
                        if (Shift1Match != null && Shift2Match == null)
                        {
                            shifttime = Shift1Match.Shift1In;
                            shiftName = Shift1Match.Shift1Name;
                        }
                        else if (Shift1Match == null && Shift2Match != null)
                        {
                            shifttime = Shift2Match.Shift2In;
                            shiftName = Shift2Match.Shift2Name;
                        }
                        else if (Shift1Match == null && Shift2Match == null && Shift3Match != null)
                        {
                            shifttime = Shift3Match.Shift3In;
                            shiftName = Shift3Match.Shift3Name;
                        }
                        else if (Shift1Match != null && Shift2Match != null)
                        {
                            shifttime = Shift1Match.Shift1In;
                            shiftName = Shift1Match.Shift1Name;
                        }
                    }
                    else
                    {
                        if (Shift1Match != null && Shift2Match == null)
                        {
                            shifttime = Shift1Match.Shift1Out;
                            shiftName = Shift1Match.Shift1Name;
                        }
                        else if (Shift1Match == null && Shift2Match != null)
                        {
                            shifttime = Shift2Match.Shift2Out;
                            shiftName = Shift2Match.Shift2Name;
                        }
                        else if (Shift1Match == null && Shift2Match == null && Shift3Match != null)
                        {
                            shifttime = Shift3Match.Shift3Out;
                            shiftName = Shift3Match.Shift3Name;
                        }
                        else if (Shift1Match != null && Shift2Match != null)
                        {
                            shifttime = Shift1Match.Shift1Out;
                            shiftName = Shift1Match.Shift1Name;
                        }

                        if (lst_advLogRecords.Count > 0 && Convert.ToBoolean(lst_advLogRecords[lst_advLogRecords.Count - 1].IsIn) == true &&
                            lst_advLogRecords[lst_advLogRecords.Count - 1].ShiftTime != null)
                        {
                            workMinutes = Convert.ToInt32(t.Time.Subtract(Convert.ToDateTime(lst_advLogRecords[lst_advLogRecords.Count - 1].Time)).TotalMinutes);
                            totalWorkMinutes += workMinutes;
                            workDays++;

                            #region Calc Delay/Overtime for Flexi & Fixed Times shift types
                            if ((Shift1 != null && Shift1[0].ShiftType == (int)ShiftType.FlexTime)
                                || (Shift1 == null && shiftType4PerDayEmp == (int)ShiftType.FlexTime))
                            {
                                double shiftMinutes = 0;
                                if (Shift1Match != null && Shift2Match == null)
                                    shiftMinutes = Shift1Match.shift1TotalMinutes;
                                else if (Shift1Match == null && Shift2Match != null)
                                    shiftMinutes = Shift2Match.shift2TotalMinutes;
                                else if (Shift1Match == null && Shift2Match == null && Shift3Match != null)
                                    shiftMinutes = Shift2Match.shift2TotalMinutes;
                                else if (Shift1Match != null && Shift2Match != null)
                                    shiftMinutes = Shift1Match.shift1TotalMinutes;


                                if (workMinutes > shiftMinutes && (workMinutes - shiftMinutes) > emp.OvertimeFreeMinutes)
                                {
                                    overTimeMinutes = workMinutes - shiftMinutes/* - emp.OvertimeFreeMinutes*/;
                                    totalOvertimeMinutes += overTimeMinutes;
                                    delayMinutes = 0;
                                }
                                else if (workMinutes < shiftMinutes && (shiftMinutes - workMinutes) > emp.DelayFreeMinutes)
                                {
                                    delayMinutes = shiftMinutes - workMinutes/* - emp.DelayFreeMinutes*/;
                                    totalDelayMinutes += delayMinutes;
                                    overTimeMinutes = 0;
                                }
                                else
                                    delayMinutes = overTimeMinutes = 0;
                            }
                            else if ((Shift1 != null && Shift1[0].ShiftType == (int)ShiftType.FixedTimes)
                                || (Shift1 == null && shiftType4PerDayEmp == (int)ShiftType.FixedTimes))
                            {
                                double attDelay = Convert.ToDateTime(lst_advLogRecords[lst_advLogRecords.Count - 1].Time).
                                    Subtract(Convert.ToDateTime(lst_advLogRecords[lst_advLogRecords.Count - 1].ShiftTime)).TotalMinutes;

                                double leaveDelay = shifttime.Value.Subtract(t.Time).TotalMinutes;
                                leaveDelay = leaveDelay > 0 ? (calcDelayForAttendOnly ? 0 : leaveDelay) : leaveDelay;

                                double totalDelay = (attDelay > 0 ? attDelay : 0) + (leaveDelay > 0 ? leaveDelay : 0);
                                double totalOvertime = (attDelay < 0 ? (attDelay * -1) : 0) + (leaveDelay < 0 ? (leaveDelay * -1) : 0);

                                if (totalDelay > 0 && totalDelay > emp.DelayFreeMinutes)
                                {
                                    delayMinutes = totalDelay - emp.DelayFreeMinutes;
                                    totalDelayMinutes += delayMinutes;
                                }
                                if (totalOvertime > 0 && totalOvertime > emp.OvertimeFreeMinutes)
                                {
                                    overTimeMinutes = totalOvertime - emp.OvertimeFreeMinutes;
                                    totalOvertimeMinutes += overTimeMinutes;
                                }
                            }
                            #endregion
                        }
                    }
                }

                #endregion

                lst_advLogRecords.Add(new AdvLogRecord
                {
                    AttendLogAdvId = t.AttendLogAdvId,
                    ShiftTime = shifttime,
                    Time = fpTime,
                    IsIn = isIn,
                    Hours = workMinutes,
                    Delay = delayMinutes,
                    OverTime = overTimeMinutes,
                    IsWeekEnd = isWeekEnd,

                    DelayDocId = isIn ? DelayOverDocs.Where(x => x.IsDelay && x.FpInId == t.AttendLogAdvId).Select(x => x.Id).FirstOrDefault()
                    : DelayOverDocs.Where(x => x.IsDelay && x.FpOutId == t.AttendLogAdvId).Select(x => x.Id).FirstOrDefault(),

                    OvertimeDocId = isIn ? DelayOverDocs.Where(x => x.IsDelay == false && x.FpInId == t.AttendLogAdvId).Select(x => x.Id).FirstOrDefault()
                    : DelayOverDocs.Where(x => x.IsDelay == false && x.FpOutId == t.AttendLogAdvId).Select(x => x.Id).FirstOrDefault(),
                    ShiftName = shiftName,
                });
            }

            if (emp.Shift1Id.HasValue == false && emp.Shift2Id.HasValue == false)
                workDays = 0;
            //commented by mohammad 26-07-2020
            //else if (emp.Shift1Id.HasValue && emp.Shift2Id.HasValue)
            //    workDays = workDays / 2;

            return lst_advLogRecords;
        }


        public static List<ShiftRec> GetShiftsTimePeriods(List<EmpShift> Shift1, List<EmpShift> Shift2, List<EmpShift> Shift3, DateTime dt_From, DateTime dt_To)
        {
            double daysCount = dt_To.Date.Subtract(dt_From.Date).TotalDays + 1;

            List<ShiftRec> lst_ShiftsTimes = new List<ShiftRec>();

            for (int x = 0; x < daysCount; x++)
            {
                DateTime? shift1Start = null;
                DateTime? shift1In = null;
                DateTime? shift1Out = null;
                DateTime? shift1End = null;
                double shift1TotalMinutes = 0;

                DateTime? shift2Start = null;
                DateTime? shift2In = null;
                DateTime? shift2Out = null;
                DateTime? shift2End = null;
                double shift2TotalMinutes = 0;

                DateTime? shift3Start = null;
                DateTime? shift3In = null;
                DateTime? shift3Out = null;
                DateTime? shift3End = null;
                double shift3TotalMinutes = 0;
                string shift1Name = string.Empty, shift2Name = string.Empty, shift3Name = string.Empty;

                int dayMatch = HrHelper.GetShiftDayMatch(Shift1, dt_From.Date.AddDays(x));
                if (Shift1[dayMatch].TimeId > 0)
                {
                    shift1Name = Shift1[dayMatch].ShiftName;
                    #region Calc Shift1 DateTime Ranges
                    shift1In = dt_From.Date.AddDays(x).
                                    AddHours(Shift1[dayMatch].AttendTime.Value.Hour).AddMinutes(Shift1[dayMatch].AttendTime.Value.Minute);

                    if (Shift1[dayMatch].LeaveTime.Value.TimeOfDay.CompareTo(Shift1[dayMatch].AttendTime.Value.TimeOfDay) > 0)
                        shift1Out = dt_From.Date.AddDays(x).
                            AddHours(Shift1[dayMatch].LeaveTime.Value.Hour).AddMinutes(Shift1[dayMatch].LeaveTime.Value.Minute);
                    else if (Shift1[dayMatch].LeaveTime.Value.TimeOfDay.CompareTo(Shift1[dayMatch].AttendTime.Value.TimeOfDay) < 0)
                        shift1Out = dt_From.Date.AddDays(x + 1).
                            AddHours(Shift1[dayMatch].LeaveTime.Value.Hour).AddMinutes(Shift1[dayMatch].LeaveTime.Value.Minute);
                    else
                        shift1Out = dt_From.Date.AddDays(x + 1).
                            AddHours(Shift1[dayMatch].LeaveTime.Value.Hour).AddMinutes(Shift1[dayMatch].LeaveTime.Value.Minute);

                    shift1TotalMinutes = shift1Out.Value.Subtract(shift1In.Value).TotalMinutes;

                    if (Shift1[dayMatch].AttendTime.Value.CompareTo(Shift1[dayMatch].AttendStart.Value) > 0)
                        shift1Start = dt_From.Date.AddDays(x).
                            AddHours(Shift1[dayMatch].AttendStart.Value.Hour).AddMinutes(Shift1[dayMatch].AttendStart.Value.Minute);
                    else if (Shift1[dayMatch].AttendTime.Value.CompareTo(Shift1[dayMatch].AttendStart.Value) < 0)
                        shift1Start = dt_From.Date.AddDays(x - 1).
                            AddHours(Shift1[dayMatch].AttendStart.Value.Hour).AddMinutes(Shift1[dayMatch].AttendStart.Value.Minute);
                    else
                        shift1Start = dt_From.Date.AddDays(x).
                            AddHours(Shift1[dayMatch].AttendTime.Value.Hour).AddMinutes(Shift1[dayMatch].AttendTime.Value.Minute);


                    if (Shift1[dayMatch].LeaveTime.Value.CompareTo(Shift1[dayMatch].LeaveEnd.Value) < 0)
                        shift1End = shift1Out.Value.Date.AddHours(Shift1[dayMatch].LeaveEnd.Value.Hour).AddMinutes(Shift1[dayMatch].LeaveEnd.Value.Minute);
                    else if (Shift1[dayMatch].LeaveTime.Value.CompareTo(Shift1[dayMatch].LeaveEnd.Value) > 0)
                        shift1End = shift1Out.Value.AddHours(Shift1[dayMatch].LeaveEnd.Value.Hour).AddMinutes(Shift1[dayMatch].LeaveEnd.Value.Minute);
                    else
                        shift1End = shift1Out;
                    #endregion
                }

                if (Shift2.Count > 0)
                {
                    int dayMatchShift2 = HrHelper.GetShiftDayMatch(Shift2, dt_From.Date.AddDays(x));
                    if (Shift2[dayMatchShift2].TimeId > 0)
                    {
                        shift2Name = Shift2[dayMatchShift2].ShiftName;

                        #region Calc Shift2 DateTime Ranges
                        shift2In = dt_From.Date.AddDays(x).
                                        AddHours(Shift2[dayMatchShift2].AttendTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].AttendTime.Value.Minute);

                        if (Shift2[dayMatchShift2].LeaveTime.Value.TimeOfDay.CompareTo(Shift2[dayMatchShift2].AttendTime.Value.TimeOfDay) > 0)
                            shift2Out = dt_From.Date.AddDays(x).
                                AddHours(Shift2[dayMatchShift2].LeaveTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveTime.Value.Minute);
                        else if (Shift2[dayMatchShift2].LeaveTime.Value.TimeOfDay.CompareTo(Shift2[dayMatchShift2].AttendTime.Value.TimeOfDay) < 0)
                            shift2Out = dt_From.Date.AddDays(x + 1).
                                AddHours(Shift2[dayMatchShift2].LeaveTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveTime.Value.Minute);
                        else
                            shift2Out = dt_From.Date.AddDays(x + 1).
                                AddHours(Shift2[dayMatchShift2].LeaveTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveTime.Value.Minute);

                        shift2TotalMinutes = shift2Out.Value.Subtract(shift2In.Value).TotalMinutes;

                        if (Shift2[dayMatchShift2].AttendTime.Value.CompareTo(Shift2[dayMatchShift2].AttendStart.Value) > 0)
                            shift2Start = dt_From.Date.AddDays(x).
                                AddHours(Shift2[dayMatchShift2].AttendStart.Value.Hour).AddMinutes(Shift2[dayMatchShift2].AttendStart.Value.Minute);
                        else if (Shift2[dayMatchShift2].AttendTime.Value.CompareTo(Shift2[dayMatchShift2].AttendStart.Value) < 0)
                            shift2Start = dt_From.Date.AddDays(x - 1).
                                AddHours(Shift2[dayMatchShift2].AttendStart.Value.Hour).AddMinutes(Shift2[dayMatchShift2].AttendStart.Value.Minute);
                        else
                            shift2Start = dt_From.Date.AddDays(x).
                                AddHours(Shift2[dayMatchShift2].AttendTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].AttendTime.Value.Minute);



                        if (Shift2[dayMatchShift2].LeaveTime.Value.CompareTo(Shift2[dayMatchShift2].LeaveEnd.Value) < 0)
                            shift2End = shift2Out.Value.Date.AddHours(Shift2[dayMatchShift2].LeaveEnd.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveEnd.Value.Minute);
                        else if (Shift2[dayMatchShift2].LeaveTime.Value.CompareTo(Shift2[dayMatchShift2].LeaveEnd.Value) > 0)
                            shift2End = shift2Out.Value.AddHours(Shift2[dayMatchShift2].LeaveEnd.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveEnd.Value.Minute);
                        else
                            shift2End = shift2Out;
                        #endregion
                    }
                }

                if (Shift3.Count > 0)
                {
                    int dayMatchShift3 = HrHelper.GetShiftDayMatch(Shift3, dt_From.Date.AddDays(x));
                    if (Shift3[dayMatchShift3].TimeId > 0)
                    {
                        shift3Name = Shift3[dayMatchShift3].ShiftName;

                        #region Calc Shift3 DateTime Ranges
                        shift3In = dt_From.Date.AddDays(x).
                                        AddHours(Shift3[dayMatchShift3].AttendTime.Value.Hour).AddMinutes(Shift3[dayMatchShift3].AttendTime.Value.Minute);

                        if (Shift3[dayMatchShift3].LeaveTime.Value.TimeOfDay.CompareTo(Shift3[dayMatchShift3].AttendTime.Value.TimeOfDay) > 0)
                            shift3Out = dt_From.Date.AddDays(x).
                                AddHours(Shift3[dayMatchShift3].LeaveTime.Value.Hour).AddMinutes(Shift3[dayMatchShift3].LeaveTime.Value.Minute);
                        else if (Shift3[dayMatchShift3].LeaveTime.Value.TimeOfDay.CompareTo(Shift3[dayMatchShift3].AttendTime.Value.TimeOfDay) < 0)
                            shift3Out = dt_From.Date.AddDays(x + 1).
                                AddHours(Shift3[dayMatchShift3].LeaveTime.Value.Hour).AddMinutes(Shift3[dayMatchShift3].LeaveTime.Value.Minute);
                        else
                            shift3Out = dt_From.Date.AddDays(x + 1).
                                AddHours(Shift3[dayMatchShift3].LeaveTime.Value.Hour).AddMinutes(Shift3[dayMatchShift3].LeaveTime.Value.Minute);

                        shift3TotalMinutes = shift3Out.Value.Subtract(shift3In.Value).TotalMinutes;

                        if (Shift3[dayMatchShift3].AttendTime.Value.CompareTo(Shift3[dayMatchShift3].AttendStart.Value) > 0)
                            shift3Start = dt_From.Date.AddDays(x).
                                AddHours(Shift3[dayMatchShift3].AttendStart.Value.Hour).AddMinutes(Shift3[dayMatchShift3].AttendStart.Value.Minute);
                        else if (Shift3[dayMatchShift3].AttendTime.Value.CompareTo(Shift3[dayMatchShift3].AttendStart.Value) < 0)
                            shift3Start = dt_From.Date.AddDays(x - 1).
                                AddHours(Shift3[dayMatchShift3].AttendStart.Value.Hour).AddMinutes(Shift3[dayMatchShift3].AttendStart.Value.Minute);
                        else
                            shift3Start = dt_From.Date.AddDays(x).
                                AddHours(Shift3[dayMatchShift3].AttendTime.Value.Hour).AddMinutes(Shift3[dayMatchShift3].AttendTime.Value.Minute);



                        if (Shift3[dayMatchShift3].LeaveTime.Value.CompareTo(Shift3[dayMatchShift3].LeaveEnd.Value) < 0)
                            shift3End = shift3Out.Value.Date.AddHours(Shift3[dayMatchShift3].LeaveEnd.Value.Hour).AddMinutes(Shift3[dayMatchShift3].LeaveEnd.Value.Minute);
                        else if (Shift3[dayMatchShift3].LeaveTime.Value.CompareTo(Shift3[dayMatchShift3].LeaveEnd.Value) > 0)
                            shift3End = shift3Out.Value.AddHours(Shift3[dayMatchShift3].LeaveEnd.Value.Hour).AddMinutes(Shift3[dayMatchShift3].LeaveEnd.Value.Minute);
                        else
                            shift3End = shift3Out;
                        #endregion
                    }
                }

                lst_ShiftsTimes.Add(new ShiftRec
                {
                    day = dt_From.Date.AddDays(x),
                    Shift1start = shift1Start,
                    Shift1In = shift1In,
                    Shift1Out = shift1Out,
                    Shift1end = shift1End,
                    shift1TotalMinutes = shift1TotalMinutes,

                    Shift2start = shift2Start,
                    Shift2In = shift2In,
                    Shift2Out = shift2Out,
                    Shift2end = shift2End,
                    shift2TotalMinutes = shift2TotalMinutes,

                    Shift3start = shift2Start,
                    Shift3In = shift2In,
                    Shift3Out = shift2Out,
                    Shift3end = shift2End,
                    shift3TotalMinutes = shift2TotalMinutes,
                    Shift1Name = shift1Name,
                    Shift2Name = shift2Name,
                    Shift3Name = shift1Name
                });
            }
            return lst_ShiftsTimes;
        }

        public static List<ShiftRec> GetShiftsTimePeriodsPerDay(ERPDataContext DB, emp4Att emp, DateTime dt_From, DateTime dt_To)
        {
            var ss = (from s in DB.HR_Shifts
                      join d in DB.HR_ShiftDetailDays
                      on s.ShiftId equals d.ShiftId
                      join t in DB.HR_TimeTables
                      on d.TimeTableId equals t.TimeId
                      where s.IsShiftPerDay
                      where s.ShiftId == emp.Shift1Id
                      where d.Day.Date >= dt_From.Date && d.Day <= dt_To.Date
                      orderby d.Day
                      select new
                      {
                          s.ShiftId,
                          s.ShiftName,
                          d.Day,
                          t.AttendTime,
                          t.AttendStart,
                          t.AttendEnd,
                          t.LeaveTime,
                          t.LeaveStart,
                          t.LeaveEnd
                      }).ToList();

            double daysCount = dt_To.Date.Subtract(dt_From.Date).TotalDays + 1;

            List<ShiftRec> lst_ShiftsTimes = new List<ShiftRec>();

            foreach (var x in ss)
            {
                DateTime? shift1Start = null;
                DateTime? shift1In = null;
                DateTime? shift1Out = null;
                DateTime? shift1End = null;
                double shift1TotalMinutes = 0;

                DateTime? shift2Start = null;
                DateTime? shift2In = null;
                DateTime? shift2Out = null;
                DateTime? shift2End = null;
                double shift2TotalMinutes = 0;
                string shift1Name = string.Empty, shift2Name = string.Empty;

                #region Calc Shift1 DateTime Ranges
                shift1Name = x.ShiftName;

                shift1In = x.Day.Date.
                                AddHours(x.AttendTime.Hour).AddMinutes(x.AttendTime.Minute);

                if (x.LeaveTime.TimeOfDay.CompareTo(x.AttendTime.TimeOfDay) > 0)
                    shift1Out = x.Day.Date.
                        AddHours(x.LeaveTime.Hour).AddMinutes(x.LeaveTime.Minute);
                else if (x.LeaveTime.TimeOfDay.CompareTo(x.AttendTime.TimeOfDay) < 0)
                    shift1Out = x.Day.Date.AddDays(1).
                        AddHours(x.LeaveTime.Hour).AddMinutes(x.LeaveTime.Minute);
                else
                    shift1Out = x.Day.AddDays(1).
                        AddHours(x.LeaveTime.Hour).AddMinutes(x.LeaveTime.Minute);

                shift1TotalMinutes = shift1Out.Value.Subtract(shift1In.Value).TotalMinutes;

                if (x.AttendTime.CompareTo(x.AttendStart) > 0)
                    shift1Start = x.Day.Date.
                        AddHours(x.AttendStart.Hour).AddMinutes(x.AttendStart.Minute);
                else if (x.AttendTime.CompareTo(x.AttendStart) < 0)
                    shift1Start = x.Day.Date.AddDays(-1).
                        AddHours(x.AttendStart.Hour).AddMinutes(x.AttendStart.Minute);
                else
                    shift1Start = x.Day.Date.
                        AddHours(x.AttendTime.Hour).AddMinutes(x.AttendTime.Minute);


                if (x.LeaveTime.CompareTo(x.LeaveEnd) < 0)
                    shift1End = shift1Out.Value.Date.AddHours(x.LeaveEnd.Hour).AddMinutes(x.LeaveEnd.Minute);
                else if (x.LeaveTime.CompareTo(x.LeaveEnd) > 0)
                    shift1End = shift1Out.Value.AddHours(x.LeaveEnd.Hour).AddMinutes(x.LeaveEnd.Minute);
                else
                    shift1End = shift1Out;
                #endregion

                #region shift 2
                //if (Shift2.Count > 0)
                //{
                //    int dayMatchShift2 = HrHelper.GetShiftDayMatch(Shift2, dt_From.Date.AddDays(x));
                //    if (Shift2[dayMatch].TimeId > 0)
                //    {
                //        #region Calc Shift2 DateTime Ranges
                //        shift2In = dt_From.Date.AddDays(x).
                //                        AddHours(Shift2[dayMatchShift2].AttendTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].AttendTime.Value.Minute);

                //        if (Shift2[dayMatchShift2].LeaveTime.Value.TimeOfDay.CompareTo(Shift2[dayMatchShift2].AttendTime.Value.TimeOfDay) > 0)
                //            shift2Out = dt_From.Date.AddDays(x).
                //                AddHours(Shift2[dayMatchShift2].LeaveTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveTime.Value.Minute);
                //        else if (Shift2[dayMatchShift2].LeaveTime.Value.TimeOfDay.CompareTo(Shift2[dayMatchShift2].AttendTime.Value.TimeOfDay) < 0)
                //            shift2Out = dt_From.Date.AddDays(x + 1).
                //                AddHours(Shift2[dayMatchShift2].LeaveTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveTime.Value.Minute);
                //        else
                //            shift2Out = dt_From.Date.AddDays(x + 1).
                //                AddHours(Shift2[dayMatchShift2].LeaveTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveTime.Value.Minute);

                //        shift2TotalMinutes = shift2Out.Value.Subtract(shift2In.Value).TotalMinutes;

                //        if (Shift2[dayMatchShift2].AttendTime.Value.CompareTo(Shift2[dayMatchShift2].AttendStart.Value) > 0)
                //            shift2Start = dt_From.Date.AddDays(x).
                //                AddHours(Shift2[dayMatchShift2].AttendStart.Value.Hour).AddMinutes(Shift2[dayMatchShift2].AttendStart.Value.Minute);
                //        else if (Shift2[dayMatchShift2].AttendTime.Value.CompareTo(Shift2[dayMatchShift2].AttendStart.Value) < 0)
                //            shift2Start = dt_From.Date.AddDays(x - 1).
                //                AddHours(Shift2[dayMatchShift2].AttendStart.Value.Hour).AddMinutes(Shift2[dayMatchShift2].AttendStart.Value.Minute);
                //        else
                //            shift2Start = dt_From.Date.AddDays(x).
                //                AddHours(Shift2[dayMatchShift2].AttendTime.Value.Hour).AddMinutes(Shift2[dayMatchShift2].AttendTime.Value.Minute);



                //        if (Shift2[dayMatchShift2].LeaveTime.Value.CompareTo(Shift2[dayMatchShift2].LeaveEnd.Value) < 0)
                //            shift2End = shift2Out.Value.Date.AddHours(Shift2[dayMatchShift2].LeaveEnd.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveEnd.Value.Minute);
                //        else if (Shift2[dayMatchShift2].LeaveTime.Value.CompareTo(Shift2[dayMatchShift2].LeaveEnd.Value) > 0)
                //            shift2End = shift2Out.Value.AddHours(Shift2[dayMatchShift2].LeaveEnd.Value.Hour).AddMinutes(Shift2[dayMatchShift2].LeaveEnd.Value.Minute);
                //        else
                //            shift2End = shift2Out;
                //        #endregion
                //    } 
                //}
                #endregion

                lst_ShiftsTimes.Add(new ShiftRec
                {
                    day = x.Day.Date,
                    Shift1start = shift1Start,
                    Shift1In = shift1In,
                    Shift1Out = shift1Out,
                    Shift1end = shift1End,
                    shift1TotalMinutes = shift1TotalMinutes,

                    Shift2start = shift2Start,
                    Shift2In = shift2In,
                    Shift2Out = shift2Out,
                    Shift2end = shift2End,
                    shift2TotalMinutes = shift2TotalMinutes,
                    Shift1Name = shift1Name,
                });
            }
            return lst_ShiftsTimes;
        }

        public static DataTable LoadEmpDays(ERPDataContext DB, emp4Att emp, DateTime start, DateTime end, List<EmpShift> Shift1, List<EmpShift> Shift2,
            List<ShiftRec> lst_ShiftsTimes)
        {
            #region dtAttend
            DataTable dtAttend = new DataTable();
            dtAttend.Columns.Add("Day");
            dtAttend.Columns.Add("EmpId");
            dtAttend.Columns.Add("Comment");
            dtAttend.Columns.Add("IsWeekEnd").DataType = typeof(bool);
            dtAttend.Columns.Add("FormalVacationId").DataType = typeof(int);
            dtAttend.Columns.Add("EmpVacationId").DataType = typeof(int);
            dtAttend.Columns.Add("EmpAbsenceId").DataType = typeof(int);
            dtAttend.Columns.Add("EmpDelayId").DataType = typeof(int);
            dtAttend.Columns.Add("EmpOverTimeId").DataType = typeof(int);
            dtAttend.Columns.Add("EmpMissionId").DataType = typeof(int);
            dtAttend.Columns.Add("WeekDay");
            dtAttend.Columns.Add("shiftReplaceId").DataType = typeof(int);

            #endregion

            int EmpId = emp.EmpId;

            #region Empoyee Transactions
            var empTrans = (from f in DB.HR_Vacations
                            join v in DB.HR_VacationsExtraTypes
                            on f.OtherVacId equals v.VacId
                            where f.EmpId == EmpId
                            && f.StartDate > Shared.minDate
                            && f.StartDate < Shared.maxDate
                            select new
                            {
                                Type = "Vac",
                                Id = f.VacationId,
                                StartDate = f.StartDate,
                                EndDate = f.EndDate,
                                VacationType = v.VacNAme

                                //(Shared.IsEnglish == true ? ResHREn.OtherVacation : ResHRAr.OtherVacation)//"أجازه أخري"
                            }).ToList().Union(
                            from f in DB.HR_Absences
                            where f.EmpId == EmpId
                            && f.StartDate > Shared.minDate
                            && f.StartDate < Shared.maxDate
                            select new
                            {
                                Type = "Abs",
                                Id = f.AbsenceId,
                                StartDate = f.StartDate,
                                EndDate = f.EndDate,
                                VacationType = ""
                            }).ToList().Union(
                                   from f in DB.HR_Delays
                                   where f.EmpId == EmpId
                                   && f.DelayDay.Date >= start
                                   && f.DelayDay.Date <= end
                                   select new
                                   {
                                       Type = "Dly",
                                       Id = f.DelayId,
                                       StartDate = f.DelayDay,
                                       EndDate = f.DelayDay,
                                       VacationType = ""
                                   }).ToList().Union(
                                            from f in DB.HR_OverTimes
                                            where f.EmpId == EmpId
                                            && f.OverTimeDay.Date >= start.Date
                                            && f.OverTimeDay.Date <= end.Date
                                            select new
                                            {
                                                Type = "Ovr",
                                                Id = f.OverTimeId,
                                                StartDate = f.OverTimeDay,
                                                EndDate = f.OverTimeDay,
                                                VacationType = ""
                                            }).Union(
                                                     from f in DB.HR_Missions
                                                     where f.EmpId == EmpId
                                                     where f.StartDate.Date >= start.Date && f.EndDate.Date <= end.Date
                                                     select new
                                                     {
                                                         Type = "Msn",
                                                         Id = f.MissionId,
                                                         StartDate = f.StartDate,
                                                         EndDate = f.EndDate,
                                                         VacationType = string.Empty
                                                     }).Union(
                                                     from f in DB.HR_ShiftReplaces
                                                     where f.EmpId == EmpId
                                                     && f.shiftReplaceDay.Date >= start.Date
                                                     && f.shiftReplaceDay.Date <= end.Date
                                                     select new
                                                     {
                                                         Type = "Repl",
                                                         Id = f.shiftReplaceId,
                                                         StartDate = f.shiftReplaceDay,
                                                         EndDate = f.shiftReplaceDay,
                                                         VacationType = f.ISIn ? "Add" : "Subtract"
                                                     }
                                                     ).ToList();

            #endregion

            var FrmlVacs = (from f in DB.HR_FormalVacations
                            select f).ToList();

            DateTime day = start.Date;
            for (int x = 0; x < end.Date.Subtract(start.Date).TotalDays + 1; x++)
            {
                DateTime theDay = day.AddDays(x);
                bool isWeekend = false;
                string WeekEndNote = string.Empty;

                #region FormalVacation in this day
                int? FormalVacationId = null;
                string FormalVacationName = string.Empty;
                string notes = string.Empty;

                var FVac = (from f in FrmlVacs
                            where f.StartDate <= theDay && f.EndDate >= theDay
                            select new
                            {
                                f.FormalVacationId,
                                f.FormalVacationNameAr
                            }).FirstOrDefault();
                if (FVac != null)
                {
                    FormalVacationId = FVac.FormalVacationId;
                    FormalVacationName = FVac.FormalVacationNameAr;
                    notes += FormalVacationName + " ; ";
                }
                #endregion

                #region EmplpoyeeVacation in this day
                int? EmpVacationId = null;
                string vacationNote = string.Empty;

                var empVac = (from f in empTrans
                              where f.Type == "Vac" &&
                              f.StartDate.Date <= theDay && f.EndDate.Date >= theDay
                              select new
                              {
                                  f.Id,
                                  VacationType = f.VacationType
                              }).FirstOrDefault();
                if (empVac != null)
                {
                    EmpVacationId = empVac.Id;
                    vacationNote = empVac.VacationType + " ; ";
                }
                #endregion

                #region EmplpoyeeAbsence in this day
                int? EmpAbsenceId = null;
                string absenceNote = string.Empty;

                var empAbs = (from f in empTrans
                              where f.Type == "Abs" &&
                              f.StartDate.Date <= theDay && f.EndDate.Date >= theDay
                              select f.Id).FirstOrDefault();
                if (empAbs > 0)
                {
                    EmpAbsenceId = empAbs;
                    absenceNote = (Shared.IsEnglish == true ? ResHREn.Absence : ResHRAr.Absence)//"غياب" 
                        + " ; ";
                }
                #endregion

                #region EmplpoyeeDelay in this day
                int? EmpDelayId = null;
                string delayNote = string.Empty;

                var empDelay = (from f in empTrans
                                where f.Type == "Dly"
                                && f.StartDate.Date == theDay
                                select f.Id).FirstOrDefault();
                if (empDelay > 0)
                {
                    EmpDelayId = empDelay;
                    delayNote = (Shared.IsEnglish == true ? ResHREn.Delay : ResHRAr.Delay)//"تأخير" 
                        + " ; ";
                }
                #endregion

                #region EmplpoyeeOverTime in this day
                int? EmpOverTimeId = null;
                string OverTimeNote = string.Empty;

                var empOverTime = (from f in empTrans
                                   where f.Type == "Ovr"
                                   && f.StartDate.Date == theDay
                                   select f.Id).FirstOrDefault();
                if (empOverTime > 0)
                {
                    EmpOverTimeId = empOverTime;
                    OverTimeNote = (Shared.IsEnglish == true ? ResHREn.Overtime : ResHRAr.Overtime)//"اضافي" 
                        + " ; ";
                }
                #endregion

                #region EmplpoyeeMission in this day
                int? EmpMissionId = null;
                string MissionNote = string.Empty;


                var empMsn = empTrans.Where(m => m.StartDate.Date <= theDay && m.EndDate.Date >= theDay && m.Type == "Msn").FirstOrDefault();
                if (empMsn != null)
                {
                    EmpMissionId = empMsn.Id;
                    MissionNote = (Shared.IsEnglish == true ? ResHREn.Mission : ResHRAr.Mission) + " ; ";
                }
                #endregion

                #region Shift Replacement in this day
                int? shiftReplaceId = null;
                string shiftReplaceNote = string.Empty;

                var shiftReplace = (from f in empTrans
                                    where f.Type == "Repl"
                                    && f.StartDate.Date == theDay
                                    select new
                                    {
                                        f.Id,
                                        f.VacationType
                                    }).FirstOrDefault();
                if (shiftReplace != null)
                {
                    shiftReplaceId = shiftReplace.Id;
                    shiftReplaceNote = (shiftReplace.VacationType == "Add" ? (Shared.IsEnglish == true ? ResHREn.ShiftReplaceAdd : ResHRAr.ShiftReplaceAdd) :
                        (Shared.IsEnglish == true ? ResHREn.ShiftReplaceWithDraw : ResHRAr.ShiftReplaceWithDraw)) + " ; ";
                }
                #endregion

                #region Weekend
                if (lst_ShiftsTimes == null)//repeated shift
                {
                    int shift1DayMatch = HrHelper.GetShiftDayMatch(Shift1, theDay);

                    if (Shift1[shift1DayMatch].TimeId == 0 && Shift1[shift1DayMatch].ShiftDetailId != -1)
                    {
                        string shiftName = Shift1.Where(z => z.TimeId > 0).Select(z => z.ShiftName).FirstOrDefault();
                        WeekEndNote = (Shared.IsEnglish == true ? ResHREn.Weekend : ResHRAr.Weekend) + " " + shiftName + " ; ";
                        isWeekend = true;
                    }
                    if (emp.Shift2Id != null && Shift2.Count > 0)
                    {
                        int shift2DayMatch = HrHelper.GetShiftDayMatch(Shift2, theDay);

                        if (Shift2[shift2DayMatch].TimeId == 0 && Shift2[shift2DayMatch].ShiftDetailId != -1)
                        {
                            string shiftName = Shift2.Where(z => z.TimeId > 0).Select(z => z.ShiftName).FirstOrDefault();
                            WeekEndNote += (Shared.IsEnglish == true ? ResHREn.Weekend : ResHRAr.Weekend) + " " + shiftName + " ; ";
                            isWeekend = true;
                        }
                    }
                }
                else
                {
                    if (lst_ShiftsTimes.Where(z => z.day.Date == theDay.Date).Count() < 1)
                    {
                        WeekEndNote = (Shared.IsEnglish == true ? ResHREn.Weekend : ResHRAr.Weekend) + " ; ";
                        isWeekend = true;
                    }
                }
                #endregion

                string totalNote = (notes + WeekEndNote + vacationNote + absenceNote + delayNote + OverTimeNote + MissionNote + shiftReplaceNote).Trim();
                totalNote = totalNote == string.Empty ? string.Empty : totalNote.Remove(totalNote.Length - 1);//remove last semicolon


                dtAttend.Rows.Add(theDay.ToShortDateString(), emp.EmpId, totalNote,
                isWeekend, FormalVacationId, EmpVacationId, EmpAbsenceId, EmpDelayId, EmpOverTimeId, EmpMissionId, HrHelper.Weekday(theDay), shiftReplaceId);
            }
            return dtAttend;
        }

        public static void GetWorkedDaysHours(ERPDataContext DB, bool FpDependOnInOut, HR_Employee Employee, DateTime startDate, DateTime endDate,
            out decimal days, out double minutes, bool calcDelayForAttendOnly)
        {
            days = 0;
            minutes = 0;

            if (FpDependOnInOut == false)
            {
                var att = (from h in DB.HR_Attendances
                           where h.EmpId == Employee.EmpId &&
                           h.Day >= startDate &&
                           h.Day <= endDate
                           &&
                           (h.Shift1Attend.HasValue || h.Shift1Leave.HasValue
                           || h.Shift2Attend.HasValue || h.Shift2Leave.HasValue)
                           select h).ToList();

                foreach (var h in att)
                {
                    if (h.Shift1Attend.HasValue && h.Shift1Leave.HasValue)
                    {
                        minutes += h.Shift1Leave.Value.Subtract(h.Shift1Attend.Value).TotalMinutes;
                    }
                    if (h.Shift2Attend.HasValue && h.Shift2Leave.HasValue)
                    {
                        minutes += h.Shift2Leave.Value.Subtract(h.Shift2Attend.Value).TotalMinutes;
                    }
                }
                days = att.Count;
            }
            else
            {
                List<EmpShift> lst_empsShifts = HrHelper.GetEmpsShifts();
                List<ShiftRec> lst_ShiftsTimes = null;

                emp4Att emp = (from m in DB.HR_Employees
                               where m.EmpId == Employee.EmpId
                               from d in DB.HR_DelayOverTimeRules.Where(d => d.IsDelay && d.RuleId == m.DelayRuleId).DefaultIfEmpty()
                               from o in DB.HR_DelayOverTimeRules.Where(o => o.IsDelay == false && o.RuleId == m.OverTimeRuleId).DefaultIfEmpty()
                               select new emp4Att
                               {
                                   EmpId = m.EmpId,
                                   Shift1Id = m.Shift1Id,
                                   Shift2Id = m.Shift2Id,
                                   Shift3Id = m.Shift3Id,
                                   DelayFreeMinutes = d == null ? 0 : d.FreeMinutes,
                                   OvertimeFreeMinutes = o == null ? 0 : o.FreeMinutes
                               }).First();

                //Get shifts
                List<EmpShift> Shift1 = lst_empsShifts.Where(h => h.ShiftId == emp.Shift1Id).ToList();
                List<EmpShift> Shift2 = lst_empsShifts.Where(h => h.ShiftId == emp.Shift2Id).ToList();
                List<EmpShift> Shift3 = lst_empsShifts.Where(h => h.ShiftId == emp.Shift3Id).ToList();

                if (emp.Shift1Id.HasValue == false)
                    return;

                double totalWorkMinutes = 0, totalDelayMinutes = 0, totalOvertimeMinutes = 0;
                decimal workDays = 0;
                List<AdvLogRecord> lst_advLogRecords = HrHelper.LoadEmpAdvLog(DB, Shift1, Shift2, Shift3, emp, startDate.Date, endDate.Date,
                    out totalWorkMinutes, out totalDelayMinutes, out totalOvertimeMinutes, out workDays, calcDelayForAttendOnly,
                    out lst_ShiftsTimes);

                minutes = totalWorkMinutes;
                days = workDays;
            }

            var query = from w in DB.HR_WorkingDays
                        where w.EmpId == Employee.EmpId
                        where w.DateFrom.Date >= startDate.Date && w.DateTo.Date <= endDate.Date
                        group w by w.WorkType into grp
                        select new
                        {
                            Count = grp.Select(x => x.Count).Sum(),
                            Type = grp.Key
                        };
            if (query != null)
            {
                days += query.Where(x => x.Type == 1).Select(x => x.Count).FirstOrDefault();
                minutes += Convert.ToDouble(query.Where(x => x.Type == 0).Select(x => x.Count).FirstOrDefault());
            }
        }
        /// <summary>
        /// Calcualte overtime per role type
        /// if role type is "1" -per period-
        /// or role type is "2" -duplcate minutes-
        /// </summary>
        /// <param name="EmpId"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns>Calculated Overtime</returns>
        public static decimal CalculateOverPerRole(int EmpId, DateTime startDate, DateTime endDate)
        {
            decimal overtime = 0; // overtime minutes
            decimal overcost = 0; // overtime cost
            ERPDataContext DB = new ERPDataContext();
            var query = from p in DB.HR_Employees
                        join r in DB.HR_DelayOverTimeRules on p.OverTimeRuleId equals r.RuleId
                        join o in DB.HR_OverTimes on p.EmpId equals o.EmpId
                        from d in DB.HR_DelayOverTimeRuleDetails
                        where p.OverTimeRuleId == d.RuleId
                        where p.EmpId == EmpId
                        where r.IsDelay == false
                        where o.OverTimeDay.Date >= startDate &&
                                o.OverTimeDay.Date <= endDate &&
                                o.Payed == true
                        select new { o.OverTimeMinutes, p.Hour, r.Type, r.RuleId, d.FromMinute, d.ToMinute, d.CalcMinute };
            foreach (var v in query)
            {
                overtime = v.OverTimeMinutes;
                if (overtime <= 0) return 0;
                if (v.Type == 2)
                {
                    overcost += (overtime / 60) * v.Hour * v.CalcMinute.Value;
                }
                else if (v.Type == 1)
                {
                    if (overtime > v.FromMinute)
                    {
                        if (overtime > v.ToMinute)
                        {
                            overcost += ((decimal)(v.ToMinute - v.FromMinute) / 60) * (v.Hour * v.CalcMinute.Value);
                        }
                        else
                        {
                            overcost += ((overtime - v.FromMinute) / 60) * v.Hour * v.CalcMinute.Value;
                        }
                    }
                    else break;
                }
            }
            return overcost;
        }

        #region Ask For Permission
        public static bool AskForPermission(FormAction f, int userId, int Pid, int invoiceId)
        {
            ERPDataContext DB = new ERPDataContext();

            HR_AuthorizePermission prm = DB.HR_AuthorizePermissions.Where(x => x.UserId == userId).Where(x => x.Pid == Pid).Where(x => x.FormAction == (int)f).FirstOrDefault();

            if (prm == null || prm.HR_AuthorizeRequests.Where(x => x.isConfirmed == false && x.SourceId == invoiceId).Count() > 0)
            {
                return false;
            }

            HR_AuthorizeRequest req = new HR_AuthorizeRequest();
            req.isConfirmed = false;
            req.isUsed = false;
            req.UserId = userId;
            req.RequestDate = MyHelper.Get_Server_DateTime();
            req.AuthorizeId = prm.AuthorizePermissionId;
            req.SourceId = invoiceId;
            DB.HR_AuthorizeRequests.InsertOnSubmit(req);
            DB.SubmitChanges();
            return true;
        }

        public static bool CheckPermission(FormAction f, int Pid, int? invoiceId)
        {
            ERPDataContext DB = new ERPDataContext();

            List<HR_AuthorizeRequest> requests = new List<HR_AuthorizeRequest>();

            if (invoiceId != null)
            {
                requests = DB.HR_AuthorizeRequests
                    .Where(
                        x => x.isConfirmed
                        && !x.isUsed
                        && x.UserId == Shared.UserId
                        && x.SourceId == invoiceId
                        && x.HR_AuthorizePermission.FormAction == (int)f
                        && x.HR_AuthorizePermission.Pid == Pid
                    ).ToList();
                return requests.Count() > 0 ? true : false;
            }

            return false;
        }

        public static bool CheckPermission(int Pid, int? invoiceId)
        {
            ERPDataContext DB = new ERPDataContext();

            List<HR_AuthorizeRequest> requests = new List<HR_AuthorizeRequest>();

            if (invoiceId != null)
            {
                requests = DB.HR_AuthorizeRequests
                    .Where(
                        x => x.isConfirmed
                        && !x.isUsed
                        && x.UserId == Shared.UserId
                        && x.SourceId == invoiceId
                        && x.HR_AuthorizePermission.Pid == Pid
                    ).ToList();
                return requests.Count() > 0 ? true : false;
            }

            return false;
        }

        public static void UsePermission(FormAction f, int Pid, int? invoiceId)
        {
            ERPDataContext DB = new ERPDataContext();

            HR_AuthorizeRequest request = DB.HR_AuthorizeRequests.Where(x => x.isConfirmed && x.UserId == Shared.UserId && !x.isUsed && x.SourceId == invoiceId && x.HR_AuthorizePermission.FormAction == (int)f).FirstOrDefault();
            if (request != null)
                request.isUsed = true;
            DB.SubmitChanges();
        }

        #endregion

        public static void CalcInsurance(decimal salary, decimal totalbenfits, out decimal compShare, out decimal empShare)
        {
            compShare = 0;
            empShare = 0;

            
            if (totalbenfits > 0)
            {
                decimal diff = salary - totalbenfits;
                decimal ratio = diff * Shared.st_Store.ExemptionRatio.Value / 100;
                salary = (ratio <= totalbenfits && ratio > 0 )? salary - ratio : salary - totalbenfits;
                
                if (salary < Shared.st_Store.MinInsureSalary.Value)
                {
                    salary = Shared.st_Store.MinInsureSalary.Value;
                }
                else
                {
                    salary = salary > Shared.st_Store.MaxInsureSalary.Value ? Shared.st_Store.MaxInsureSalary.Value : salary;
                }
            }
            else
            {
                salary = (salary > Shared.st_Store.MaxInsureSalary.Value ? Shared.st_Store.MaxInsureSalary.Value : salary * (100 - Shared.st_Store.ExemptionRatio.Value) / 100);
            }

            // خصم 10 جنيه من المرتب قبل حساب التامينات
            //compShare = (salary-10) /* (100-Shared.st_Store.ExemptionRatio.Value) / 100*/ * (Shared.st_Store.companyShare.Value/100);
            //empShare = (salary-10) /* (100 - Shared.st_Store.ExemptionRatio.Value) / 10*/ * (Shared.st_Store.EmpShare.Value/100);

            compShare = salary/*(round(salary)) /* (100-Shared.st_Store.ExemptionRatio.Value) / 100*/ * (Shared.st_Store.companyShare.Value / 100);
            empShare = salary/*(round(salary)) /* (100 - Shared.st_Store.ExemptionRatio.Value) / 10*/ * (Shared.st_Store.EmpShare.Value / 100);
        }
        /*static int round(decimal x)
        {
            int n = Convert.ToInt32((x-x%10));
            // Smaller multiple
            int a = (n / 10) * 10;

            // Larger multiple
            int b = a + 10;

            // Return of closest of two
            return (n - a > b - n) ? b : a;
        }*/

        public static decimal CalcAbsenceBenfit(HR_Employee emp, bool type, bool absence = true)
        {
            decimal AbsenceDayCost = absence ? emp.Day : 0;
            decimal Basicsalary = Shared.st_Store.EgyptionLaw == true ? emp.SalaryBasic + emp.SalaryVariable : emp.SalaryBasic;
            //decimal basicDayCost = emp.Day;
            ERPDataContext DB = new ERPDataContext();
            var query = (from b in DB.HR_EmployeeBenefits

                         join a in DB.HR_AbsenceBenfits
                         on b.BenefitId equals a.BenfitId

                         join f in DB.HR_Benefits
                         on b.BenefitId equals f.BenefitId

                         where a.type == type
                         where b.EmpId == emp.EmpId && f.BenefitType == true
                         select new
                         {
                             a.BenfitId,
                             b.Value,
                             f.BenefitCalculation,
                             f.IsWorkingDays,
                             a.DayOrDeduct
                         }).ToList();

            foreach (var ben in query)
            {

                if (ben.BenefitCalculation == 0)
                {
                    if (ben.DayOrDeduct == false)
                    {
                        AbsenceDayCost += ben.IsWorkingDays == true ? ben.Value : (ben.Value / Shared.st_Store.TotalMonthDays_HR.Value);
                    }
                    else
                    {
                        AbsenceDayCost += ben.IsWorkingDays == true ? ben.Value : (ben.Value / Shared.st_Store.TotalMonthDays_HR.Value);
                    }
                }
                else if (ben.BenefitCalculation == 1)
                {
                    if (ben.DayOrDeduct == false)
                    {
                        AbsenceDayCost += ben.IsWorkingDays == true ? ((ben.Value * Basicsalary) / 100) : (((ben.Value * Basicsalary) / 100) / Shared.st_Store.TotalMonthDays_HR.Value);
                    }
                    else
                    {
                        AbsenceDayCost += ben.IsWorkingDays == true ? ((ben.Value * Basicsalary) / 100) : (((ben.Value * Basicsalary) / 100) / Shared.st_Store.TotalMonthDays_HR.Value);
                    }
                }
            }
            return AbsenceDayCost;
        }


        public static double calcBusinessGainTax(decimal total)
        {
            ERPDataContext DB = new ERPDataContext();
            decimal taxSalary = 0;
            //decimal total = 0;
            decimal buTax = 0;
            //double nonTaxableBenfits = 0;
            /*if (dtBenefitAdd.Rows.Count > 0)
            {
                nonTaxableBenfits = dtBenefitAdd.AsEnumerable().Where(x => x.Field<bool>("IsTax") != true).Sum(x => x.Field<double>("Amount"));
            }
            
            total = Convert.ToDecimal(txtTotalBefore.EditValue) - Convert.ToDecimal(txtEmployeeShare.EditValue) - Convert.ToDecimal(nonTaxableBenfits) - Convert.ToDecimal(txtVariable.EditValue) + Convert.ToDecimal(colAmountSubtract.SummaryItem.SummaryValue);
            */

            var btax = DB.HR_BusinessTaxes.Where(x => (total * 12) >= x.MinAnnualSalary && (total * 12) <= x.MaxAnnualSalary).FirstOrDefault();
            taxSalary = ((total * 12) - 9000) - (((total * 12) - 9000) % 10);
            if (btax != null && taxSalary > 0)
            {
                var bTaxDetail = DB.HR_BusinessTaxDetails.Where(x => x.BusinessTaxId == btax.id);
                foreach (var detail in bTaxDetail)
                {
                    if (taxSalary > detail.EndSalary)
                    {
                        buTax += (detail.EndSalary - detail.StartSalary) * (detail.Percentage / 100);
                    }
                    else if (taxSalary <= detail.EndSalary)
                    {
                        buTax += (taxSalary - detail.StartSalary) * (detail.Percentage / 100);
                        break;
                    }
                }
            }
            return decimal.ToDouble(buTax / 12);
        }

        public static decimal CalcIncentive(HR_Employee emp)
        {
            if (emp.hasIncentive == true)
            {
                ERPDataContext DB = new ERPDataContext();
                decimal intencive = 0;
                intencive += emp.SalaryBasic;
                foreach (var inc in DB.HR_Incentives.Select(x => x.BenefitId))
                {
                    intencive += DB.HR_EmployeeBenefits.Where(x => x.EmpId == emp.EmpId && x.BenefitId == inc).Select(x => x.Value).FirstOrDefault();
                }
                return Convert.ToDecimal(Convert.ToDouble(intencive) * 2.50);
            }
            else
            {
                return 0;
            }
        }
    }
    //VacationType
    // Annual = true
    //casual = false
    //other = null

    public enum MaritalStatus
    {
        Single = 0,
        Married = 1,
        Divorced = 2,
        Widower = 3
    }

    public enum Gender
    {
        Male = 0,
        Female = 1
    }

    public enum MilitaryStatus
    {
        Finished = 0,
        Exempted = 1,
        Deferred = 2,
        NoService = 3
    }

    public enum PayPeriod
    {
        Hour = 0,
        Week = 1,
        TwoWeeks = 2,
        Month = 3,
        Piece = 4,
        Daily = 5
    }

    public enum BenefitType
    {
        Subtract = 0,
        Add = 1
    }

    public enum BenefitCalculation
    {
        Amount = 0,
        RatioOfBasic = 1,
        Ratioovariable = 2
    }

    public enum HrProcess
    {
        Vacation = 1,
        Absense = 2,
        Mission = 3,
        Delay = 4,
        Overtime = 5,
        FormalVacaion = 6,
    }

    public enum ShiftType
    {
        FixedTimes = 1,
        FlexTime = 2,
        PerHour = 3
    }

    public enum DelayOverCalcType
    {
        PerPeriod = 1,
        MultiplyMinutes = 2,
    }
    public class EmpShift
    {
        public int ShiftId { get; set; }
        public string ShiftName { get; set; }
        public DateTime StartDateTime { get; set; }
        public int ShiftDetailId { get; set; }
        public int TimeId { get; set; }
        public string TimeName { get; set; }
        public DateTime? AttendStart { get; set; }
        public DateTime? AttendTime { get; set; }
        public DateTime? AttendEnd { get; set; }
        public DateTime? LeaveStart { get; set; }
        public DateTime? LeaveTime { get; set; }
        public DateTime? LeaveEnd { get; set; }
        public int ShiftType { get; set; }
        public bool IsShiftPerDay { get; set; }

    }

    public class EmpRecord
    {
        public int DeptId;
        public string DeptNameAr;
        public int JobId;
        public string JobNameAr;
        public string Group;
        public int EmpId;
        public string EmpCode;
        public string EmpName;
        public string PayPeriod;
        public string EmpAddress;
        public string Tel;
        public string Gender;
        public string Marital;
        public bool EmpState;
        public int? AccountId;
        public int EnrollNumber;
        public DateTime? AppointmentDate;
        public string CountryName;
        public string NathName;
        public string RlgnName;
        public string QlfcName;
        public string SponosrName;
        public DateTime? IdCardEndDate;
        public DateTime? PassportEndDate;
        public DateTime? ResidenceEndDate;
        public TimeSpan? Shift1Attend;
        public TimeSpan? Shift1Leave;
        public TimeSpan? Shift2Attend;
        public TimeSpan? Shift2Leave;
        public int? Shift1Id;
        public int? Shift2Id;
        public string Shift1Name;
        public string Shift2Name;
        public string DelayRuleName;
        public string OvertimeRuleName;
        public int? DelayRuleId;
        public int? OvertimeRuleId;

        public DateTime? ContractEndDate;
        public DateTime? DrivingEndDate;
        public DateTime? InsuranceDate;
        public string BankName;
        public string BankAccountNum;
        public int WorkEntityId;
        public string EntNameAr;
        public string branchAr;
    }

    public class UserIdName
    {
        public int UserId { get; set; }
        public string Name { get; set; }
    }

    public class emp4Att
    {
        public int EmpId { get; set; }
        public int? Shift1Id { get; set; }
        public int? Shift2Id { get; set; }
        public int? Shift3Id { get; set; }
        public int DelayFreeMinutes { get; set; }
        public int OvertimeFreeMinutes { get; set; }
    }

    public class ShiftRec
    {
        public DateTime day { get; set; }
        public DateTime? Shift1start { get; set; }
        public DateTime? Shift1In { get; set; }
        public DateTime? Shift1Out { get; set; }
        public DateTime? Shift1end { get; set; }
        public double shift1TotalMinutes { get; set; }

        public DateTime? Shift2start { get; set; }
        public DateTime? Shift2In { get; set; }
        public DateTime? Shift2Out { get; set; }
        public DateTime? Shift2end { get; set; }
        public double shift2TotalMinutes { get; set; }

        public DateTime? Shift3start { get; set; }
        public DateTime? Shift3In { get; set; }
        public DateTime? Shift3Out { get; set; }
        public DateTime? Shift3end { get; set; }
        public double shift3TotalMinutes { get; set; }
        public string Shift1Name { get; set; }
        public string Shift2Name { get; set; }
        public string Shift3Name { get; set; }
    }

    public class AdvLogRecord
    {
        public int AttendLogAdvId { get; set; }
        public DateTime? ShiftTime { get; set; }
        public DateTime Time { get; set; }
        public bool IsIn { get; set; }
        public double Hours { get; set; }
        public double Delay { get; set; }
        public double OverTime { get; set; }
        public bool IsWeekEnd { get; set; }
        public int DelayDocId { get; set; }
        public int OvertimeDocId { get; set; }

        public string ShiftName { get; set; }
    }
}