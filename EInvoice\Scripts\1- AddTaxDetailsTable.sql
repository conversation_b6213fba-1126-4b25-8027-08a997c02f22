﻿--/****** Object:  Table [dbo].[SL_TaxDetails]    Script Date: 28/11/2021 02:09:46 م ******/
--SET ANSI_NULLS ON
--GO

--SET QUOTED_IDENTIFIER ON
--GO

--CREATE TABLE [dbo].[SL_TaxDetails](
--	[SlInvoiceDetailId] [int] NOT NULL,
--	[TaxId] [int] NOT NULL,
--	[TaxDetailId] [int] IDENTITY(1,1) NOT NULL,
--	[TaxRatio] [decimal](20, 6) NOT NULL,
--	[TaxAmount] [decimal](20, 6) NOT NULL,
-- CONSTRAINT [PK_TaxDetails] PRIMARY KEY CLUSTERED 
--(
--	[SlInvoiceDetailId] ASC,
--	[TaxId] ASC
--)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
--) ON [PRIMARY]

--GO

--ALTER TABLE [dbo].[SL_TaxDetails]  WITH CHECK ADD  CONSTRAINT [FK_SL_TaxDetails_E_TaxableType] FOREIGN KEY([TaxId])
--REFERENCES [dbo].[E_TaxableType] ([E_TaxableTypeId])
--GO

--ALTER TABLE [dbo].[SL_TaxDetails] CHECK CONSTRAINT [FK_SL_TaxDetails_E_TaxableType]
--GO

--ALTER TABLE [dbo].[SL_TaxDetails]  WITH CHECK ADD  CONSTRAINT [FK_TaxDetails_SL_InvoiceDetail_SlInvoiceDetailId] FOREIGN KEY([SlInvoiceDetailId])
--REFERENCES [dbo].[SL_InvoiceDetail] ([SL_InvoiceDetailId])
--ON DELETE CASCADE
--GO

--ALTER TABLE [dbo].[SL_TaxDetails] CHECK CONSTRAINT [FK_TaxDetails_SL_InvoiceDetail_SlInvoiceDetailId]
--GO



if not exists(select * from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME='SL_InvoiceDetail' and column_name='bonusDiscount')
Alter Table SL_InvoiceDetail  Add bonusDiscount  decimal(20,6) null
GO

IF OBJECT_ID(N'dbo.[SL_InvoiceDetailSubTaxValue]', N'U') IS NULL BEGIN   

SET ANSI_NULLS ON

SET QUOTED_IDENTIFIER ON

CREATE TABLE [dbo].[SL_InvoiceDetailSubTaxValue](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[InvoiceDetailId] [int] NOT NULL,
	[esubTypeId] [int] NOT NULL,
	[value] [decimal](20, 6) NOT NULL,
 CONSTRAINT [PK_SL_InvoiceDetailSubTaxValue] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

End


if not exists(select * from INFORMATION_SCHEMA.COLUMNS where TABLE_NAME='SL_InvoiceDetailSubTaxValue' and column_name='TaxRatio')
Alter Table SL_InvoiceDetailSubTaxValue  Add TaxRatio  decimal(20,6) null
GO

