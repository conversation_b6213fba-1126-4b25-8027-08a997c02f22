﻿using EInvoice.BL;
using EInvoice.Models;
using Microsoft.AspNetCore.Mvc;
using Models_1.ViewModels.InvoiceVM;
using System.Collections.Generic;
using System.Linq;
using Models_1.ViewModels;
using Zatca;

namespace EInvoice.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class ZatcaController : ControllerBase
    {
        private readonly ERPEinvoiceContext _DB;

        public ZatcaController(ERPEinvoiceContext DB)
        {
            _DB = DB;
        }

        [HttpPost]
        [Route("SubmitDocument")]
        public IActionResult Submit(List<Note4Post> invoicesList)
        {
            var invoices = InvoiceBL.GetInvoiceModel(invoicesList, _DB);

            //var zatcaInvoicesVM = invoices
            //                      .Select(i => i.ToZatcaStandardInvoice())
            //                      .ToList();


            //var certificatePath = @"C:\Users\<USER>\Desktop\Test Zatca\ZatcaCertificate.txt";
            //var privateKeyPath = @"C:\Users\<USER>\Desktop\Test Zatca\privatekey.txt";
            //var result = ZatcaHelpers.SignDocument(zatcaInvoicesVM[0], certificatePath, privateKeyPath);

            return Ok();
        }
    }
}
