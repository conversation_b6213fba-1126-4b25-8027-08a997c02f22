{"info": {"name": "EInvoice SaveDocument API", "description": "Collection for testing the SaveDocument API endpoint with mandatory and optional field validation", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}, {"key": "api_path", "value": "/api/EInvoice", "type": "string"}, {"key": "company_id", "value": "1", "type": "string"}, {"key": "store_id", "value": "1", "type": "string"}, {"key": "customer_id", "value": "1", "type": "string"}], "item": [{"name": "Valid Invoice Submission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"companyId\": {{company_id}},\n    \"storeId\": {{store_id}},\n    \"storeName\": \"Main Store\",\n    \"customerId\": {{customer_id}},\n    \"customerName\": \"Test Customer\",\n    \"date\": \"{{$isoTimestamp}}\",\n    \"invoiceId\": null,\n    \"documentType\": \"I\",\n    \"TotalDiscount\": 50.00,\n    \"TotalTax\": 70.00,\n    \"Net\": 500.00,\n    \"invoiceCode\": \"INV-{{$timestamp}}\",\n    \"PurchaseOrderNumber\": \"PO-2024-001\",\n    \"ProformaNumber\": \"\",\n    \"DeliveryDate\": \"2024-12-25\",\n    \"Uuid\": [],\n    \"invoiceDetails\": [\n      {\n        \"Id\": 0,\n        \"itemId\": 1,\n        \"itemName\": \"Test Product\",\n        \"quantity\": 2,\n        \"currencyId\": 1,\n        \"currencyName\": \"EGP\",\n        \"exchangeRate\": 1.0,\n        \"amount\": 250.00,\n        \"uomId\": 1,\n        \"uomName\": \"Piece\",\n        \"Description\": \"Test product description\",\n        \"discountAfterTax\": 0.00,\n        \"totalAmount\": 500.00,\n        \"discount\": {\n          \"type\": 1,\n          \"amount\": 25.00,\n          \"rate\": 5.0\n        },\n        \"discounts\": [\n          {\n            \"type\": 1,\n            \"amount\": 25.00,\n            \"rate\": 5.0\n          }\n        ],\n        \"taxes\": [\n          {\n            \"taxId\": 1,\n            \"taxName\": \"T1\",\n            \"subTaxId\": 1,\n            \"subTaxName\": \"VAT\",\n            \"type\": 1,\n            \"rate\": 14.0,\n            \"amount\": 70.00\n          }\n        ]\n      }\n    ]\n  }\n]"}, "url": {"raw": "{{base_url}}{{api_path}}/SaveInvoice", "host": ["{{base_url}}"], "path": ["{{api_path}}", "SaveInvoice"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response indicates success\", function () {", "    pm.expect(pm.response.text()).to.include(\"Document Saved\");", "});", "", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"], "type": "text/javascript"}}]}, {"name": "Invalid Invoice - Missing <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"companyId\": {{company_id}},\n    \"storeId\": {{store_id}},\n    \"customerId\": {{customer_id}},\n    \"date\": \"{{$isoTimestamp}}\",\n    \"documentType\": \"I\",\n    \"invoiceCode\": \"\",\n    \"invoiceDetails\": []\n  }\n]"}, "url": {"raw": "{{base_url}}{{api_path}}/SaveInvoice", "host": ["{{base_url}}"], "path": ["{{api_path}}", "SaveInvoice"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response contains validation errors\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property(\"Messages\");", "});"], "type": "text/javascript"}}]}, {"name": "Credit Note Submission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"companyId\": {{company_id}},\n    \"storeId\": {{store_id}},\n    \"storeName\": \"Main Store\",\n    \"customerId\": {{customer_id}},\n    \"customerName\": \"Test Customer\",\n    \"date\": \"{{$isoTimestamp}}\",\n    \"invoiceId\": null,\n    \"documentType\": \"C\",\n    \"TotalDiscount\": 0.00,\n    \"TotalTax\": 35.00,\n    \"Net\": 250.00,\n    \"invoiceCode\": \"CN-{{$timestamp}}\",\n    \"PurchaseOrderNumber\": \"\",\n    \"ProformaNumber\": \"\",\n    \"DeliveryDate\": \"\",\n    \"Uuid\": [\"original-invoice-uuid\"],\n    \"invoiceDetails\": [\n      {\n        \"Id\": 0,\n        \"itemId\": 1,\n        \"itemName\": \"Test Product\",\n        \"quantity\": 1,\n        \"currencyId\": 1,\n        \"currencyName\": \"EGP\",\n        \"exchangeRate\": 1.0,\n        \"amount\": 250.00,\n        \"uomId\": 1,\n        \"uomName\": \"Piece\",\n        \"Description\": \"Credit note for returned item\",\n        \"discountAfterTax\": 0.00,\n        \"totalAmount\": 250.00,\n        \"discount\": {\n          \"type\": 0,\n          \"amount\": 0.00,\n          \"rate\": 0.0\n        },\n        \"discounts\": [],\n        \"taxes\": [\n          {\n            \"taxId\": 1,\n            \"taxName\": \"T1\",\n            \"subTaxId\": 1,\n            \"subTaxName\": \"VAT\",\n            \"type\": 1,\n            \"rate\": 14.0,\n            \"amount\": 35.00\n          }\n        ]\n      }\n    ]\n  }\n]"}, "url": {"raw": "{{base_url}}{{api_path}}/SaveInvoice", "host": ["{{base_url}}"], "path": ["{{api_path}}", "SaveInvoice"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response indicates success\", function () {", "    pm.expect(pm.response.text()).to.include(\"Document Saved\");", "});"], "type": "text/javascript"}}]}, {"name": "Multiple Items Invoice", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"companyId\": {{company_id}},\n    \"storeId\": {{store_id}},\n    \"storeName\": \"Main Store\",\n    \"customerId\": {{customer_id}},\n    \"customerName\": \"Test Customer\",\n    \"date\": \"{{$isoTimestamp}}\",\n    \"invoiceId\": null,\n    \"documentType\": \"I\",\n    \"TotalDiscount\": 100.00,\n    \"TotalTax\": 140.00,\n    \"Net\": 1000.00,\n    \"invoiceCode\": \"INV-MULTI-{{$timestamp}}\",\n    \"PurchaseOrderNumber\": \"PO-2024-002\",\n    \"ProformaNumber\": \"\",\n    \"DeliveryDate\": \"2024-12-30\",\n    \"Uuid\": [],\n    \"invoiceDetails\": [\n      {\n        \"Id\": 0,\n        \"itemId\": 1,\n        \"itemName\": \"Product A\",\n        \"quantity\": 2,\n        \"currencyId\": 1,\n        \"currencyName\": \"EGP\",\n        \"exchangeRate\": 1.0,\n        \"amount\": 250.00,\n        \"uomId\": 1,\n        \"uomName\": \"Piece\",\n        \"Description\": \"First product\",\n        \"discountAfterTax\": 0.00,\n        \"totalAmount\": 500.00,\n        \"discount\": {\n          \"type\": 1,\n          \"amount\": 25.00,\n          \"rate\": 5.0\n        },\n        \"discounts\": [],\n        \"taxes\": [\n          {\n            \"taxId\": 1,\n            \"taxName\": \"T1\",\n            \"subTaxId\": 1,\n            \"subTaxName\": \"VAT\",\n            \"type\": 1,\n            \"rate\": 14.0,\n            \"amount\": 70.00\n          }\n        ]\n      },\n      {\n        \"Id\": 0,\n        \"itemId\": 2,\n        \"itemName\": \"Product B\",\n        \"quantity\": 1,\n        \"currencyId\": 1,\n        \"currencyName\": \"EGP\",\n        \"exchangeRate\": 1.0,\n        \"amount\": 500.00,\n        \"uomId\": 1,\n        \"uomName\": \"Piece\",\n        \"Description\": \"Second product\",\n        \"discountAfterTax\": 0.00,\n        \"totalAmount\": 500.00,\n        \"discount\": {\n          \"type\": 0,\n          \"amount\": 50.00,\n          \"rate\": 0.0\n        },\n        \"discounts\": [],\n        \"taxes\": [\n          {\n            \"taxId\": 1,\n            \"taxName\": \"T1\",\n            \"subTaxId\": 1,\n            \"subTaxName\": \"VAT\",\n            \"type\": 1,\n            \"rate\": 14.0,\n            \"amount\": 70.00\n          }\n        ]\n      }\n    ]\n  }\n]"}, "url": {"raw": "{{base_url}}{{api_path}}/SaveInvoice", "host": ["{{base_url}}"], "path": ["{{api_path}}", "SaveInvoice"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response indicates success\", function () {", "    pm.expect(pm.response.text()).to.include(\"Document Saved\");", "});"], "type": "text/javascript"}}]}]}