﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Faccount" xml:space="preserve">
    <value>الحساب: </value>
  </data>
  <data name="Fcat" xml:space="preserve">
    <value>الفئه: </value>
  </data>
  <data name="FcatAll" xml:space="preserve">
    <value>الفئه: الكل </value>
  </data>
  <data name="Fcomp" xml:space="preserve">
    <value>المجموعة: </value>
  </data>
  <data name="FcompAll" xml:space="preserve">
    <value>المجموعة: الكل </value>
  </data>
  <data name="FcostCenter" xml:space="preserve">
    <value>مركز التكلفة: </value>
  </data>
  <data name="Fcustomer" xml:space="preserve">
    <value>العميل: </value>
  </data>
  <data name="FcustomerAll" xml:space="preserve">
    <value>العميل: الكل </value>
  </data>
  <data name="FcustomList" xml:space="preserve">
    <value>قائمة حسابات مخصصة: </value>
  </data>
  <data name="FDate" xml:space="preserve">
    <value>بتاريخ: </value>
  </data>
  <data name="FdateBefore" xml:space="preserve">
    <value>قبل تاريخ: </value>
  </data>
  <data name="FFrom" xml:space="preserve">
    <value> من </value>
  </data>
  <data name="FfromDate" xml:space="preserve">
    <value>من تاريخ: </value>
  </data>
  <data name="FitemName" xml:space="preserve">
    <value>اسم الصنف: </value>
  </data>
  <data name="FitemsAll" xml:space="preserve">
    <value>اسم الصنف: الكل </value>
  </data>
  <data name="Fstore" xml:space="preserve">
    <value>المخزن: </value>
  </data>
  <data name="FstoreAll" xml:space="preserve">
    <value>المخزن: الكل  </value>
  </data>
  <data name="FTo" xml:space="preserve">
    <value> الى </value>
  </data>
  <data name="FtoDate" xml:space="preserve">
    <value>حتي تاريخ: </value>
  </data>
  <data name="Fvendor" xml:space="preserve">
    <value>المورد: </value>
  </data>
  <data name="FvendorAll" xml:space="preserve">
    <value>المورد: الكل </value>
  </data>
  <data name="path" xml:space="preserve">
    <value>برجاء تحديد مسار صحيح لمجلد تصاميم التقارير، في شاشة اعدادات البرنامج</value>
  </data>
  <data name="path2" xml:space="preserve">
    <value>بعد الانتهاء من تعديل تصميم التقرير، يجب عمل حفظ للتقرير بدون تغير اسم التقرير، في المجلد</value>
  </data>
  <data name="rpt_Acc_Account_CostCenters" xml:space="preserve">
    <value>كشف مجمع لمراكز تكلفة حساب</value>
  </data>
  <data name="rpt_Acc_Balance" xml:space="preserve">
    <value>المركز المالي</value>
  </data>
  <data name="rpt_Acc_CostCenterTotalBalances" xml:space="preserve">
    <value>إجمالي أرصدة مراكز التكلفة</value>
  </data>
  <data name="rpt_Acc_CostCenter_AccDetails" xml:space="preserve">
    <value>كشف حساب مجمع لمركز التكلفة</value>
  </data>
  <data name="rpt_Acc_CustomAccListDetails" xml:space="preserve">
    <value>كشف تفصيلي لقائمة مخصصة</value>
  </data>
  <data name="rpt_Acc_Income" xml:space="preserve">
    <value>قائمة الدخل</value>
  </data>
  <data name="rpt_Acc_PR_AccountDetails" xml:space="preserve">
    <value>كشف حساب تفصيلي</value>
  </data>
  <data name="rpt_Acc_PR_AccountsBalances" xml:space="preserve">
    <value>اجمالى حسابات الموردين</value>
  </data>
  <data name="rpt_Acc_SL_AccountDetails" xml:space="preserve">
    <value>كشف حساب تفصيلي</value>
  </data>
  <data name="rpt_Acc_SL_AccountsBalances" xml:space="preserve">
    <value>اجمالى حسابات العملاء</value>
  </data>
  <data name="rpt_IC_ItemOpenInOutClose" xml:space="preserve">
    <value>تقرير وارد و صادر أصناف</value>
  </data>
  <data name="rpt_IC_ItemsExpired" xml:space="preserve">
    <value>أصناف تنتهي صلاحيتها</value>
  </data>
  <data name="rpt_IC_ItemsMaxSell" xml:space="preserve">
    <value>الاصناف الاكثر مبيعا</value>
  </data>
  <data name="rpt_IC_ItemsMinSell" xml:space="preserve">
    <value>الاصناف الاقل مبيعا</value>
  </data>
  <data name="rpt_IC_ItemsNotSold" xml:space="preserve">
    <value>أصناف لم تباع مطلقا</value>
  </data>
  <data name="rpt_IC_ItemsOpenBalance" xml:space="preserve">
    <value>الارصده الافتتاحيه</value>
  </data>
  <data name="rpt_IC_ItemsQty" xml:space="preserve">
    <value>أرصدة الأصناف</value>
  </data>
  <data name="rpt_IC_ItemsReorder" xml:space="preserve">
    <value>أصناف وصلت لحد الطلب</value>
  </data>
  <data name="rpt_IC_ItemsTotals" xml:space="preserve">
    <value>إجماليات حركة الاصناف</value>
  </data>
  <data name="rpt_IC_ItemTransactions" xml:space="preserve">
    <value>حركة صنف تفصيلي بالتكلفة</value>
  </data>
  <data name="rpt_IC_ItemTransactionsDetails" xml:space="preserve">
    <value>جرد محتويات مخزن</value>
  </data>
  <data name="rpt_IC_SoldItemsCost" xml:space="preserve">
    <value>تكلفة البضاعـــة المبـــاعة</value>
  </data>
  <data name="rpt_ItemPriceChangings" xml:space="preserve">
    <value>تغييرات أسعار الاصناف</value>
  </data>
  <data name="rpt_PR_InvoicesHeaders" xml:space="preserve">
    <value>اجمالي فواتير المشتريات</value>
  </data>
  <data name="rpt_PR_ItemsPurchases" xml:space="preserve">
    <value>كميات شراء وأرصدة الأصناف</value>
  </data>
  <data name="rpt_PR_ItemsReturns" xml:space="preserve">
    <value>كميات مردود شراء وأرصدة الأصناف</value>
  </data>
  <data name="rpt_PR_ReturnHeaders" xml:space="preserve">
    <value>اجمالي فواتير مردود المشتريات</value>
  </data>
  <data name="rpt_SL_InvoicesHeaders" xml:space="preserve">
    <value>اجمالي فواتير المبيعات</value>
  </data>
  <data name="rpt_SL_ItemsReturn" xml:space="preserve">
    <value>كميات مردود بيع وأرصدة الأصناف</value>
  </data>
  <data name="rpt_SL_ItemsSales" xml:space="preserve">
    <value>كميات بيع وأرصدة الأصناف</value>
  </data>
  <data name="rpt_SL_ItemTrade" xml:space="preserve">
    <value>ربح او خسارة صنف توزيع</value>
  </data>
  <data name="rpt_SL_ReturnHeaders" xml:space="preserve">
    <value>اجمالي فواتير مردود المبيعات</value>
  </data>
  <data name="ValAccount" xml:space="preserve">
    <value>يجب اختيار الحساب</value>
  </data>
  <data name="ValCostCenter" xml:space="preserve">
    <value>يجب اختيار مركز التكلفة</value>
  </data>
  <data name="ValCustomer" xml:space="preserve">
    <value>يجب اختيار العميل</value>
  </data>
  <data name="ValCustomList" xml:space="preserve">
    <value>يجب اختيار قائمة الحسابات المخصصة</value>
  </data>
  <data name="ValDateFromTo" xml:space="preserve">
    <value>يجب اختيار تاريخ البداية والنهاية</value>
  </data>
  <data name="ValItem" xml:space="preserve">
    <value>يجب اختيار الصنف</value>
  </data>
  <data name="ValStore" xml:space="preserve">
    <value>يجب اختيار المخزن</value>
  </data>
  <data name="ValVendor" xml:space="preserve">
    <value>يجب اختيار المورد</value>
  </data>
  <data name="Credit" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="Debit" xml:space="preserve">
    <value>مدين</value>
  </data>
  <data name="MsgPrv" xml:space="preserve">
    <value>عفواً، غير مصرح لك فتح هذا التقرير</value>
  </data>
  <data name="rpt_Acc_CustomAccListBalances" xml:space="preserve">
    <value>كشف اجمالي قوائم مخصصة</value>
  </data>
  <data name="rpt_PR_VendorItemsPurchases" xml:space="preserve">
    <value>مشتريات الأصناف من الموردين</value>
  </data>
  <data name="rpt_SL_CustomerItemsSales" xml:space="preserve">
    <value>مبيعات الأصناف للعملاء</value>
  </data>
  <data name="Fuser" xml:space="preserve">
    <value> المستخدم: </value>
  </data>
  <data name="FuserAll" xml:space="preserve">
    <value> المستخدم: الكل </value>
  </data>
  <data name="FsalesEmp" xml:space="preserve">
    <value>مندوب المبيعات:</value>
  </data>
  <data name="FsalesEmpAll" xml:space="preserve">
    <value>مندوب المبيعات: الكل</value>
  </data>
  <data name="rpt_HR_SalesEmpCommission" xml:space="preserve">
    <value>عمولات مندوب حسب الهدف والتحصيل</value>
  </data>
  <data name="rpt_IC_ItemsQtyDetails" xml:space="preserve">
    <value>أرصدة الأصناف تفصيلي</value>
  </data>
  <data name="Batch" xml:space="preserve">
    <value> باتش: </value>
  </data>
  <data name="rpt_IC_ItemsQtyWithPrices" xml:space="preserve">
    <value>تقييم المخزون</value>
  </data>
  <data name="rpt_HR_DeliveryCommision" xml:space="preserve">
    <value>عمولات أصناف أذون الصرف</value>
  </data>
  <data name="valSalesEmp" xml:space="preserve">
    <value>يجب إختيار الموظف</value>
  </data>
  <data name="rpt_HR_SalesCommision" xml:space="preserve">
    <value>عمولات مباشرة علي فواتير البيع</value>
  </data>
  <data name="rpt_Acc_DailyIncome" xml:space="preserve">
    <value>وارد يومية</value>
  </data>
  <data name="rpt_Acc_DailyPayments" xml:space="preserve">
    <value>مدفوعات يومية</value>
  </data>
  <data name="rpt_SL_CustomerTrans" xml:space="preserve">
    <value>بيان عمليات عميل</value>
  </data>
  <data name="FCustGroup" xml:space="preserve">
    <value>فئة العميل: </value>
  </data>
  <data name="FcustGroupAll" xml:space="preserve">
    <value> فئة العميل: الكل </value>
  </data>
  <data name="rpt_SL_CustomerItemsSales_OutTrns" xml:space="preserve">
    <value>مبيعات وأذونات صرف</value>
  </data>
  <data name="rpt_HR_ManfCommision" xml:space="preserve">
    <value>انتاجية الموظف</value>
  </data>
  <data name="rpt_Acc_PR_AccountsBalancesWithNotes" xml:space="preserve">
    <value>اجمالى حسابات الموردين بالأوراق التجارية</value>
  </data>
  <data name="rpt_Acc_SL_AccountsBalancesWithNotes" xml:space="preserve">
    <value>اجمالى حسابات العملاء بالأوراق التجارية</value>
  </data>
  <data name="Loss" xml:space="preserve">
    <value>خسارة</value>
  </data>
  <data name="Profit" xml:space="preserve">
    <value>ربح</value>
  </data>
  <data name="rpt_IC_ItemsQtyWithSalesPrice" xml:space="preserve">
    <value>تقييم المخزون بسعر البيع</value>
  </data>
  <data name="frm_Acc_AccountDetails" xml:space="preserve">
    <value>كشف حساب عميل تفصيلي</value>
  </data>
  <data name="frm_Acc_PR_AccountDetails" xml:space="preserve">
    <value>كشف حساب مورد تفصيلي</value>
  </data>
  <data name="rpt_IC_ItemTransactionsNoCost" xml:space="preserve">
    <value>حركة صنف تفصيلي</value>
  </data>
  <data name="mtrx1" xml:space="preserve">
    <value>تصنيف1</value>
  </data>
  <data name="mtrx2" xml:space="preserve">
    <value>تصنيف2</value>
  </data>
  <data name="mtrx3" xml:space="preserve">
    <value>تصنيف3</value>
  </data>
  <data name="FjoDept" xml:space="preserve">
    <value>الجهة:</value>
  </data>
  <data name="FjoDeptAll" xml:space="preserve">
    <value>الجهة: الكل</value>
  </data>
  <data name="FjoPriority" xml:space="preserve">
    <value>الاهمية:</value>
  </data>
  <data name="FjoPriorityAll" xml:space="preserve">
    <value>الاهمية: الكل</value>
  </data>
  <data name="FjoStatus" xml:space="preserve">
    <value>الحالة:</value>
  </data>
  <data name="FjoStatusAll" xml:space="preserve">
    <value>الحالة: الكل</value>
  </data>
  <data name="FitemType" xml:space="preserve">
    <value>نوع الصنف:</value>
  </data>
  <data name="FitemTypeAll" xml:space="preserve">
    <value>نوع الصنف: الكل</value>
  </data>
  <data name="frm_SL_JobOrderInv" xml:space="preserve">
    <value>مبيعات أوامر عمل</value>
  </data>
  <data name="frm_SL_SalesOrderItems" xml:space="preserve">
    <value>أصناف أوامر بيع - محجوزة</value>
  </data>
  <data name="rpt_HR_SalesInvItemsCommision" xml:space="preserve">
    <value>عمولات أصناف فواتير البيع</value>
  </data>
  <data name="frm_SL_ItemsSalesDetails" xml:space="preserve">
    <value>إجمالي مبيعات ومردودات الاصناف</value>
  </data>
  <data name="frm_SL_ItemsSalesTotals" xml:space="preserve">
    <value>اجمالي مبيعات الأصناف</value>
  </data>
  <data name="rpt_SL_CustomerItemsSalesReturn" xml:space="preserve">
    <value>مردود مبيعات الأصناف من العملاء</value>
  </data>
  <data name="FInvBook" xml:space="preserve">
    <value>دفتر الفواتير:</value>
  </data>
  <data name="FInvBookAll" xml:space="preserve">
    <value>دفتر الفواتير: الكل </value>
  </data>
  <data name="FempGroup" xml:space="preserve">
    <value> مجموعة الموظف: </value>
  </data>
  <data name="FempGroupAll" xml:space="preserve">
    <value> مجموعة الموظف: الكل </value>
  </data>
  <data name="rpt_IC_ItemOpenInOutCloseQty" xml:space="preserve">
    <value>إجمالى حركات الأصناف بالمخازن</value>
  </data>
  <data name="rpt_PR_VendorItemsPurchasesReturns" xml:space="preserve">
    <value>مردود مشتريات الأصناف من الموردين</value>
  </data>
  <data name="DueEarlierPeriod" xml:space="preserve">
    <value>سابق</value>
  </data>
  <data name="DueLaterPeriod" xml:space="preserve">
    <value>لاحق</value>
  </data>
  <data name="Due" xml:space="preserve">
    <value>مستحق</value>
  </data>
  <data name="frm_MrAllSales" xml:space="preserve">
    <value>توزيع الأصناف حسب فئة العميل</value>
  </data>
  <data name="frm_HR_AllPays" xml:space="preserve">
    <value>كشف المرتبات</value>
  </data>
  <data name="rpt_SL_CustomerItemsSalesReturns" xml:space="preserve">
    <value>مبيعات و مردودات الأصناف تفصيلي</value>
  </data>
  <data name="frm_ItemsPr_and_Sl" xml:space="preserve">
    <value>مبيعات ومشتريات الأصناف</value>
  </data>
  <data name="frm_SalesOrderAndAchievement" xml:space="preserve">
    <value>أوامر البيع ونسب التحقيق</value>
  </data>
  <data name="FvenGroup" xml:space="preserve">
    <value>فئة المورد: </value>
  </data>
  <data name="FvenGroupAll" xml:space="preserve">
    <value> فئة المورد: الكل </value>
  </data>
  <data name="frm_PR_ContractorExtract" xml:space="preserve">
    <value>مستخلص مقاول</value>
  </data>
  <data name="frm_SL_SalesOrderItemsAndBalance" xml:space="preserve">
    <value>أصناف أوامر بيع بالأرصدة الحالية</value>
  </data>
  <data name="frm_ManfItems" xml:space="preserve">
    <value>منتجات و خامات عمليات الانتاج</value>
  </data>
  <data name="frm_PurchaseOrderAndAchievement" xml:space="preserve">
    <value>أوامر الشراء ونسب التحقيق</value>
  </data>
  <data name="rpt_PR_VendorItemsPurchases_InTrns" xml:space="preserve">
    <value>مشتريات و اذونات اضافة</value>
  </data>
  <data name="frm_HR_Insurance" xml:space="preserve">
    <value>تأمينات الموظفين</value>
  </data>
  <data name="frm_HR_AllExpectedPays" xml:space="preserve">
    <value>رواتب متوقعة</value>
  </data>
  <data name="frm_SL_Warranty" xml:space="preserve">
    <value>انتهاء ضمان الأصناف</value>
  </data>
  <data name="frm_HR_VacationBal" xml:space="preserve">
    <value>أرصدة الاجازات</value>
  </data>
  <data name="frm_IC_ItemsQtyH" xml:space="preserve">
    <value>أرصدة الأصناف بالمخازن الأفقي</value>
  </data>
  <data name="frm_IC_ItemsTurnOver" xml:space="preserve">
    <value>معدل دوران المخزون</value>
  </data>
  <data name="rpt_DelegatesSales" xml:space="preserve">
    <value>تقرير مبيعات المندوبين</value>
  </data>
  <data name="rpt_PercentageOfCapitalInDeferredInvoices" xml:space="preserve">
    <value>نسبة رأس المال في فواتير الآجل</value>
  </data>
  <data name="rpt_SL_DeliveryOfficialsSales" xml:space="preserve">
    <value>مبيعات مسؤولي التسليم</value>
  </data>
  <data name="rpt_IC_ItemsMinLevel" xml:space="preserve">
    <value>أصناف وصلت للحد الأدنى للطلب</value>
  </data>
  <data name="rpt_SL_Profit_Loss" xml:space="preserve">
    <value>ربح أو خسارة فواتير البيع</value>
  </data>
  <data name="rpt_Acc_DailyPaymentsAndIncomePayments" xml:space="preserve">
    <value>كشف مدفوعات و ايرادات يومية</value>
  </data>
  <data name="rpt_PR_InvoicesDiscountTaxHeaders" xml:space="preserve">
    <value>نموذج ٤١ ضرائب</value>
  </data>
  <data name="rpt_Acc_Payments" xml:space="preserve">
    <value>المدفوعات</value>
  </data>
  <data name="rpt_ACC_SubLedger" xml:space="preserve">
    <value>تقرير استاذ مساعد</value>
  </data>
  <data name="frm_HR_Att" xml:space="preserve">
    <value>الحضور والانصراف</value>
  </data>
  <data name="frm_HR_Vacations" xml:space="preserve">
    <value>كشف الأجازات</value>
  </data>
  <data name="frm_SalesRep_DaySummary" xml:space="preserve">
    <value>يومية مندوب</value>
  </data>
  <data name="frm_Customers_Debit" xml:space="preserve">
    <value>مديونية العملاء</value>
  </data>
  <data name="frm_SL_Car_Weights" xml:space="preserve">
    <value>حمولات السيارات</value>
  </data>
  <data name="CustomerVisits" xml:space="preserve">
    <value>كشف زيارات العملاء</value>
  </data>
  <data name="rpt_IC_Item_In_Out_Balance" xml:space="preserve">
    <value>وارد وصادر عمليات الصنف بالمخزن</value>
  </data>
  <data name="frm_IC_ItemsQtyHNoCost" xml:space="preserve">
    <value>تقرير أرصدة الأصناف أفقى بدون تكلفة</value>
  </data>
  <data name="frm_SL_ItemsNetSalesDetails" xml:space="preserve">
    <value>تقرير صافي مييعات ومردود مبيعات</value>
  </data>
  <data name="frm_SL_ItemsNetPurchaseDetails" xml:space="preserve">
    <value>تقرير صافي المشتريات ومردودات المشتريات</value>
  </data>
  <data name="rpt_SL_Invoices_Due" xml:space="preserve">
    <value>تاريخ استحقاق فواتير المبيعات الآجلة</value>
  </data>
  <data name="frm_SL_DelegatesSales_ItemCategory" xml:space="preserve">
    <value>تقرير مبيعات المندوبين ومجموعات الأصناف</value>
  </data>
  <data name="rpt_InvoiceDetails" xml:space="preserve">
    <value>تفاصيل الفواتير</value>
  </data>
</root>