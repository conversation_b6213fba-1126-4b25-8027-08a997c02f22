{"RootPath": "D:\\Work\\Projects\\E-Invoice\\LinkIT-E-Invoice\\Models_1", "ProjectFileName": "Models_1.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Program.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "ViewModels\\Address.cs"}, {"SourceFile": "ViewModels\\ChangeDocumentStatusVM\\UpdateDocumentStatus.cs"}, {"SourceFile": "ViewModels\\ChangeDocumentStatusVM\\ChangeStatus.cs"}, {"SourceFile": "ViewModels\\DatabaseVM\\HrCountryVM.cs"}, {"SourceFile": "ViewModels\\DatabaseVM\\IcItemVM.cs"}, {"SourceFile": "ViewModels\\DatabaseVM\\IcStoreVM.cs"}, {"SourceFile": "ViewModels\\DatabaseVM\\IcUomVM.cs"}, {"SourceFile": "ViewModels\\DatabaseVM\\SlCustomerVM.cs"}, {"SourceFile": "ViewModels\\DatabaseVM\\StCompanyInfoVM.cs"}, {"SourceFile": "ViewModels\\DatabaseVM\\StCurrencyVM.cs"}, {"SourceFile": "ViewModels\\DebitCreditDocument.cs"}, {"SourceFile": "ViewModels\\DebitCreditDocuments.cs"}, {"SourceFile": "ViewModels\\Delivery.cs"}, {"SourceFile": "ViewModels\\Discount.cs"}, {"SourceFile": "ViewModels\\Document.cs"}, {"SourceFile": "ViewModels\\documentpackage.cs"}, {"SourceFile": "ViewModels\\Documents.cs"}, {"SourceFile": "ViewModels\\Enums.cs"}, {"SourceFile": "ViewModels\\InvoiceLine.cs"}, {"SourceFile": "ViewModels\\InvoiceVM\\DebitNote4Post.cs"}, {"SourceFile": "ViewModels\\InvoiceVM\\Invoice.cs"}, {"SourceFile": "ViewModels\\InvoiceVM\\InvoiceDetail.cs"}, {"SourceFile": "ViewModels\\Issuer.cs"}, {"SourceFile": "ViewModels\\ItemCodes.cs"}, {"SourceFile": "ViewModels\\Login.cs"}, {"SourceFile": "ViewModels\\Payment.cs"}, {"SourceFile": "ViewModels\\QueryParameters.cs"}, {"SourceFile": "ViewModels\\Receiver.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\ChangeStatusErrorVM\\ErrorResult.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\GetDocumentVM\\DocumentValidationResults.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\GetDocumentVM\\GetDocumentResponse.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\GetDocumentVM\\InvoiceLineItemCode.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\GetDocumentVM\\ValidationStepResult.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\GetRecentDocumentsVM\\GetRecentDocumentsRespones.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\GetRecentDocumentsVM\\Metadata.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\GetRecentDocumentsVM\\Result.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\SubmitDocumentVM\\DocumentAccepted.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\SubmitDocumentVM\\DocumentRejected.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\SubmitDocumentVM\\Error.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\SubmitDocumentVM\\SubmissionError.cs"}, {"SourceFile": "ViewModels\\ResponseVM\\SubmitDocumentVM\\SubmitDocumentResponse.cs"}, {"SourceFile": "ViewModels\\Signature.cs"}, {"SourceFile": "ViewModels\\SignuatureVM\\DocumentsToSignVM.cs"}, {"SourceFile": "ViewModels\\SignuatureVM\\SignuatureVM.cs"}, {"SourceFile": "ViewModels\\TaxableItem.cs"}, {"SourceFile": "ViewModels\\TaxCalculatorVM\\TaxCalculatorVM.cs"}, {"SourceFile": "ViewModels\\TaxTotal.cs"}, {"SourceFile": "ViewModels\\TokenVM\\TokenVM.cs"}, {"SourceFile": "ViewModels\\UnitValue.cs"}, {"SourceFile": "ViewModels\\ValidationVM\\ValidateModel.cs"}, {"SourceFile": "ViewModels\\ValidationVM\\ValidationMessage.cs"}, {"SourceFile": "ViewModels\\ValidationVM\\ValidationMessages.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.6.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Windows\\Microsoft.Net\\assembly\\GAC_MSIL\\netstandard\\v4.0_2.0.0.0__cc7b13ffcd2ddd51\\netstandard.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\IIS\\Microsoft Web Deploy V3\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\Work\\Projects\\E-Invoice\\LinkIT-E-Invoice\\Models_1\\bin\\Debug\\Models_1.dll", "OutputItemRelativePath": "Models_1.dll"}, {"OutputItemFullPath": "D:\\Work\\Projects\\E-Invoice\\LinkIT-E-Invoice\\Models_1\\bin\\Debug\\Models_1.pdb", "OutputItemRelativePath": "Models_1.pdb"}], "CopyToOutputEntries": []}