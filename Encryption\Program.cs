﻿using Encryption.BL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Encryption
{
    class Program
    {
        static void Main(string[] args)
        {
            #region Rosita consultancy
           /* Client Id
55c2b03c - 383b - 4a3b - 823e-55ac81e3edb1

      Client Secret 1
8ec7af49 - baf3 - 46f8 - 8ef9 - b5b454773628

Client Secret 2
ea303a9a - bdc4 - 4afb - 9ef8 - 5a61b1504a89*/

            var cID= Crypto.EncryptStringAES("55c2b03c-383b-4a3b-823e-55ac81e3edb1", Crypto.Key);
            var Secret= Crypto.EncryptStringAES("8ec7af49-baf3-46f8-8ef9-b5b454773628", Crypto.Key);
            var Pin= Crypto.EncryptStringAES("53337965", Crypto.Key);
            #endregion

            #region AGA

            var AgaDonglePin = "71474670";

            #region Pre-Production
            var AgaClientIdPreProduction = "fe784188-56ab-42b4-9df6-56cb7f18e587";     //PreProduction
            var AgaClientSecretPreProduction = "687f0e39-f0af-44d4-bd62-980c52d9f06d"; //PreProduction

            var encryptedAgaClientIdPre = Crypto.EncryptStringAES(AgaClientIdPreProduction, Crypto.Key);         //Pre-Production Client Id
            var encryptedAgaClientSecretPre = Crypto.EncryptStringAES(AgaClientSecretPreProduction, Crypto.Key); //Pre-Production Client Secret
            var encryptedAgaDonglePin = Crypto.EncryptStringAES(AgaDonglePin, Crypto.Key);
            
            //To Ensure Only
            var decryptAgaClientId = Crypto.DecryptStringAES(encryptedAgaClientIdPre, Crypto.Key);
            var decryptAgaClientSecret = Crypto.DecryptStringAES(encryptedAgaClientSecretPre, Crypto.Key);        //Pre-Production Client Id
            var decryptAgaDonglePin = Crypto.DecryptStringAES(encryptedAgaDonglePin, Crypto.Key);                //Pre-Production Client Secret
            #endregion


            #region Production
            var AgaClientIdProduction = "1e884ce2-f366-48c6-a8e8-cb18bb512553";     //Production
            var AgaClientSecretProduction = "7e5323ce-2de1-49e3-b2e3-af5d6588633f"; //Production

            var encryptedAgaClientIdPoduction = Crypto.EncryptStringAES(AgaClientIdProduction, Crypto.Key);          //Production Client Id
            var encryptedAgaClientSecretPoduction = Crypto.EncryptStringAES(AgaClientSecretProduction, Crypto.Key);  //Production Client Secret
                encryptedAgaDonglePin = Crypto.EncryptStringAES(AgaDonglePin, Crypto.Key);

            //To Ensure Only
            var decryptAgaClientIdPoduction = Crypto.DecryptStringAES(encryptedAgaClientIdPoduction, Crypto.Key);
            var decryptAgaClientSecretPoduction = Crypto.DecryptStringAES(encryptedAgaClientSecretPoduction, Crypto.Key);        
                decryptAgaDonglePin = Crypto.DecryptStringAES(encryptedAgaDonglePin, Crypto.Key);
            #endregion


            #endregion

            #region MultiCare

            var MultiCareDonglePin = "13949671";

            #region Pre-Production
            var MultiCareClientIdPreProduction = "fe784188-56ab-42b4-9df6-56cb7f18e587";     //Pre-Production
            var MultiCareClientSecretPreProduction = "687f0e39-f0af-44d4-bd62-980c52d9f06d"; //Pre-Production
            var encryptedDonglePin = Crypto.EncryptStringAES(MultiCareDonglePin, Crypto.Key);

            var encryptedMultiCareClientIdPre = Crypto.EncryptStringAES(MultiCareClientIdPreProduction, Crypto.Key);         //Pre-Production Client Id
            var encryptedMultiCareClientSecretPre = Crypto.EncryptStringAES(MultiCareClientSecretPreProduction, Crypto.Key); //Pre-Production Client Secret
            var decryptDonglePin = Crypto.DecryptStringAES(encryptedDonglePin, Crypto.Key);

            //To Ensure Only
            var decryptMultiCareClientId = Crypto.DecryptStringAES(encryptedMultiCareClientIdPre, Crypto.Key);                //Pre-Production Client Id
            var decryptMultiCareClientSecret = Crypto.DecryptStringAES(encryptedMultiCareClientSecretPre, Crypto.Key);        //Pre-Production Client Secret Client Id

            #endregion


            #region Production

            var MultiCareClientIdProduction = "e594a1de-fc48-42a2-a9bc-5e357c9be6d6";     //Production
            var MultiCareClientSecretProduction = "ea0cced1-dd31-477e-a8df-592b955aaf83"; //Production
            //ClientSecret 2 :ea0cced1-dd31-477e-a8df-592b955aaf83
            encryptedDonglePin = Crypto.EncryptStringAES(MultiCareDonglePin, Crypto.Key);

            var encryptedMultiCareClientId = Crypto.EncryptStringAES(MultiCareClientIdProduction, Crypto.Key);         //Production Client Id
            var encryptedMultiCareClientSecret = Crypto.EncryptStringAES(MultiCareClientSecretProduction, Crypto.Key); //Production Client Secret

            //To Ensure Only
            var decryptMultiCareClientIdProduction = Crypto.DecryptStringAES(encryptedMultiCareClientId, Crypto.Key);                //Production Client Id
            var decryptMultiCareClientSecretProduction = Crypto.DecryptStringAES(encryptedMultiCareClientSecret, Crypto.Key);        //Production Client Secret Client Id

            #endregion

            #endregion

            #region Premier
            var PremierDonglePin = "64791276";
            var encryptedPremierDonglePin = Crypto.EncryptStringAES(PremierDonglePin, Crypto.Key);

            #region Production
            var PremierProductionclientId = "69cc2859-6d24-423f-9df3-4790013f17f9";
            var PremierProductionclientSecret = "0fb00b6b-048c-488d-b00f-0fc6dbee886c";

            var encryptedPremierClientId = Crypto.EncryptStringAES(PremierProductionclientId, Crypto.Key);         //Pre-Production Client Id
            var encryptedPremierClientSecret = Crypto.EncryptStringAES(PremierProductionclientSecret, Crypto.Key); //Pre-Production Client Secret
            var decryptPremierDonglePin = Crypto.DecryptStringAES(encryptedPremierDonglePin, Crypto.Key);

            //To Ensure Only
            var decryptPremierClientId = Crypto.DecryptStringAES(encryptedPremierClientId, Crypto.Key);                //Pre-Production Client Id
            var decryptPremierClientSecret = Crypto.DecryptStringAES(encryptedPremierClientSecret, Crypto.Key);        //Pre-Production Client Secret Client Id

            #endregion

            #region PreProduction
            var PremierPreProductionclientId = "5959b12f-ee7a-4eee-b853-e6411708f824";
            var PremierPreProductionclientSecret = "fbf19d77-fcaa-4245-98dd-5ddbc959d02d";

            var encryptedPremierClientIdPre = Crypto.EncryptStringAES(PremierPreProductionclientId, Crypto.Key);         //Pre-Production Client Id
            var encryptedPremierClientSecretPre = Crypto.EncryptStringAES(PremierPreProductionclientSecret, Crypto.Key); //Pre-Production Client Secret
             decryptPremierDonglePin = Crypto.DecryptStringAES(encryptedPremierDonglePin, Crypto.Key);

            //To Ensure Only
            var decryptPremierPreProductionClientId = Crypto.DecryptStringAES(encryptedPremierClientIdPre, Crypto.Key);                //Pre-Production Client Id
                decryptPremierClientSecret = Crypto.DecryptStringAES(encryptedPremierClientSecretPre, Crypto.Key);        //Pre-Production Client Secret Client Id
            #endregion

            #endregion

            #region AlZahra
            var AlZahraDonglePin = "45221579";
            var encryptedAlZahraDonglePin = Crypto.EncryptStringAES(AlZahraDonglePin, Crypto.Key);

            #region Production
            var AlZahraProductionclientId = "739fb420-b97c-40b1-9516-befbf3a52fbe";
            var AlZahraProductionclientSecret = "2e90506e-169a-491d-96c8-2fecb120028e";

            var encryptedAlZahraClientId = Crypto.EncryptStringAES(AlZahraProductionclientId, Crypto.Key);         //Production Client Id
            var encryptedAlZahraClientSecret = Crypto.EncryptStringAES(AlZahraProductionclientSecret, Crypto.Key); //Production Client Secret
            //To Ensure Only
            var decryptAlZahraDonglePin = Crypto.DecryptStringAES(encryptedAlZahraDonglePin, Crypto.Key);
            var decryptAlZahraClientId = Crypto.DecryptStringAES(encryptedAlZahraClientId, Crypto.Key);                //Production Client Id
            var decryptAlZahraClientSecret = Crypto.DecryptStringAES(encryptedAlZahraClientSecret, Crypto.Key);        //Production Client Secret Client Id

            #endregion
            #region PreProduction
            var AlZahraPre_ProductionclientId = "5302f043-a52f-4e27-97f1-1ee662643bd3";
            var AlZahraPre_ProductionclientSecret = "b5c5f58d-b6a1-422f-82b1-3c1a8c095a51";

            var encryptedPre_AlZahraClientId = Crypto.EncryptStringAES(AlZahraPre_ProductionclientId, Crypto.Key);         //Production Client Id
            var encryptedPre_AlZahraClientSecret = Crypto.EncryptStringAES(AlZahraPre_ProductionclientSecret, Crypto.Key); //Production Client Secret
            //To Ensure Only
            var decryptPre_AlZahraDonglePin = Crypto.DecryptStringAES(encryptedAlZahraDonglePin, Crypto.Key);
            var decryptPre_AlZahraClientId = Crypto.DecryptStringAES(encryptedPre_AlZahraClientId, Crypto.Key);                //Production Client Id
            var decryptPre_AlZahraClientSecret = Crypto.DecryptStringAES(encryptedPre_AlZahraClientSecret, Crypto.Key);        //Production Client Secret Client Id
            #endregion

            #endregion
            #region TechnoTrade
            var TechnoTradeDonglePin = "78639912";
            var encryptedTechnoTradeDonglePin = Crypto.EncryptStringAES(TechnoTradeDonglePin, Crypto.Key);

            #region Production
            var TechnoTradeProductionclientId = "d3802f93-e255-4a32-a3b1-0c862756824a";
            var TechnoTradeProductionclientSecret = "1faf6e07-0140-4358-b114-4aecec403ac1";

            var encryptedTechnoTradeClientId = Crypto.EncryptStringAES(TechnoTradeProductionclientId, Crypto.Key);         //Production Client Id
            var encryptedTechnoTradeClientSecret = Crypto.EncryptStringAES(TechnoTradeProductionclientSecret, Crypto.Key); //Production Client Secret
            //To Ensure Only
            var decryptTechnoTradeDonglePin = Crypto.DecryptStringAES(encryptedTechnoTradeDonglePin, Crypto.Key);
            var decryptTechnoTradeClientId = Crypto.DecryptStringAES(encryptedTechnoTradeClientId, Crypto.Key);                //Production Client Id
            var decryptvClientSecret = Crypto.DecryptStringAES(encryptedTechnoTradeClientSecret, Crypto.Key);        //Production Client Secret Client Id

            #endregion
            #region PreProduction
            var TechnoTradePre_ProductionclientId = "7da4d913-9bc4-45f1-8d9c-2e692f6cb983";
            var TechnoTradePre_ProductionclientSecret = "7d6d2019-b8d9-494d-b3e9-1082db934aa9";

            var encryptedPre_TechnoTradeClientId = Crypto.EncryptStringAES(TechnoTradePre_ProductionclientId, Crypto.Key);         //Production Client Id
            var encryptedPreTechnoTradeClientSecret = Crypto.EncryptStringAES(TechnoTradePre_ProductionclientSecret, Crypto.Key); //Production Client Secret
            //To Ensure Only
            var decryptPre_TechnoTradeDonglePin = Crypto.DecryptStringAES(encryptedTechnoTradeDonglePin, Crypto.Key);
            var decryptPre_TechnoTradeClientId = Crypto.DecryptStringAES(encryptedPre_TechnoTradeClientId, Crypto.Key);                //Production Client Id
            var decryptPre_TechnoTradeClientSecret = Crypto.DecryptStringAES(encryptedPreTechnoTradeClientSecret, Crypto.Key);        //Production Client Secret Client Id
            #endregion

            #endregion

        }
    }
}


