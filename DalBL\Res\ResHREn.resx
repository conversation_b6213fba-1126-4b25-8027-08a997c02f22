﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="txtFrom" xml:space="preserve">
    <value> From </value>
  </data>
  <data name="txtFromDate" xml:space="preserve">
    <value> From Date </value>
  </data>
  <data name="MsgNameExist" xml:space="preserve">
    <value>This name already exists</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>some data are incorrect</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>You made some changes, do you want to save</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>Info</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>Sorry, you don't have privilege to edit record</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>Sorry, you don't have privilege to add new record</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>Question</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>Saved Successfully</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="txtTo" xml:space="preserve">
    <value> To </value>
  </data>
  <data name="txtToDate" xml:space="preserve">
    <value> To Date </value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>Deleted Successfully</value>
  </data>
  <data name="txtDrawer" xml:space="preserve">
    <value> Drawer </value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>delete row ?</value>
  </data>
  <data name="MsgNumExist" xml:space="preserve">
    <value>This number already exists</value>
  </data>
  <data name="MsgAskDel" xml:space="preserve">
    <value> Are you sure you want to delete </value>
  </data>
  <data name="txtAssembly" xml:space="preserve">
    <value>Assembly</value>
  </data>
  <data name="MsgEmpNoAccount" xml:space="preserve">
    <value>This employee has no account</value>
  </data>
  <data name="no" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="txtDivorced" xml:space="preserve">
    <value>Divorced</value>
  </data>
  <data name="txtFemale" xml:space="preserve">
    <value>Female</value>
  </data>
  <data name="txtFortnightly" xml:space="preserve">
    <value>Fortnightly</value>
  </data>
  <data name="txtHourly" xml:space="preserve">
    <value>Hourly</value>
  </data>
  <data name="txtMale" xml:space="preserve">
    <value>Male</value>
  </data>
  <data name="txtMarried" xml:space="preserve">
    <value>Married</value>
  </data>
  <data name="txtMonthly" xml:space="preserve">
    <value>Monthly</value>
  </data>
  <data name="txtPerTask" xml:space="preserve">
    <value>Per Task</value>
  </data>
  <data name="txtSingle" xml:space="preserve">
    <value>Single</value>
  </data>
  <data name="txtWeekly" xml:space="preserve">
    <value>Weekly</value>
  </data>
  <data name="txtWidowed" xml:space="preserve">
    <value>Widowed</value>
  </data>
  <data name="yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="txtHour" xml:space="preserve">
    <value> Hour </value>
  </data>
  <data name="MsgSysAdminDel" xml:space="preserve">
    <value>Sorry, you can't delete system administrator</value>
  </data>
  <data name="MsgUserDel" xml:space="preserve">
    <value>Are you sure you want to delete this user</value>
  </data>
  <data name="txtFullAccess" xml:space="preserve">
    <value>Full Access</value>
  </data>
  <data name="txtLimitedAccess" xml:space="preserve">
    <value>Limited Access</value>
  </data>
  <data name="txtNoAccess" xml:space="preserve">
    <value>Access Denied</value>
  </data>
  <data name="txtOther" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="txtSysAdmin" xml:space="preserve">
    <value>System Administrator</value>
  </data>
  <data name="txtVacAnnual" xml:space="preserve">
    <value>Annual</value>
  </data>
  <data name="txtVacCasual" xml:space="preserve">
    <value>Casual</value>
  </data>
  <data name="MsgContinue" xml:space="preserve">
    <value> Do you want to continue </value>
  </data>
  <data name="MsgDelAbsence" xml:space="preserve">
    <value>Are you sure you want to delete this absence</value>
  </data>
  <data name="ValAbsenceDats" xml:space="preserve">
    <value>End date should be larger than start date</value>
  </data>
  <data name="ValAbsenceEndDate" xml:space="preserve">
    <value>Please enter end date</value>
  </data>
  <data name="ValAbsenceExist" xml:space="preserve">
    <value>There is an absence for the employee in this period</value>
  </data>
  <data name="ValAbsenceStartDate" xml:space="preserve">
    <value>Please enter start date</value>
  </data>
  <data name="ValEmp" xml:space="preserve">
    <value>Please Select employee</value>
  </data>
  <data name="ValLastPaySlip" xml:space="preserve">
    <value> Last salary paid to the employee was till </value>
  </data>
  <data name="ValVacationExist" xml:space="preserve">
    <value>There is a vacation for the employee in this period</value>
  </data>
  <data name="MsgBenefitRecheck" xml:space="preserve">
    <value>Please, check this benefit for the employees</value>
  </data>
  <data name="MsgDelBenefit" xml:space="preserve">
    <value>Are you sure you want to delete this benefit</value>
  </data>
  <data name="MsgDelBenefitDenied" xml:space="preserve">
    <value>Sorry, you can't delete this benefit, it is used by an employee</value>
  </data>
  <data name="ValBenefitName" xml:space="preserve">
    <value>Please enter benefit name</value>
  </data>
  <data name="MsgAbsenceExistAsk" xml:space="preserve">
    <value>There is an absence for the employee in this day, do you want to contiunue</value>
  </data>
  <data name="MsgDelayExist" xml:space="preserve">
    <value>There is a delay for the employee in this day</value>
  </data>
  <data name="MsgDelDelay" xml:space="preserve">
    <value>Are you sure you want to delete this delay</value>
  </data>
  <data name="MsgDelDept" xml:space="preserve">
    <value>Are you sure you want to delete this department</value>
  </data>
  <data name="MsgDelVacation" xml:space="preserve">
    <value>Are you sure you want to delete this vacation</value>
  </data>
  <data name="MsgDeptDelChilds" xml:space="preserve">
    <value>Subsidiary Departments must be deleted before</value>
  </data>
  <data name="MsgDeptMoveEmps" xml:space="preserve">
    <value>Sorry you can't delete this department its already in use</value>
  </data>
  <data name="MsgDeptName" xml:space="preserve">
    <value>Please enter department name</value>
  </data>
  <data name="MsgFormalExist" xml:space="preserve">
    <value>There is a formal vacation in this period</value>
  </data>
  <data name="MsgVacationExistAsk" xml:space="preserve">
    <value>There is a vacation for the employee in this day, do you want to contiunue</value>
  </data>
  <data name="ValDates" xml:space="preserve">
    <value>End date should be larger than start date</value>
  </data>
  <data name="ValDate" xml:space="preserve">
    <value>Please enter date</value>
  </data>
  <data name="ValDelayPeriod" xml:space="preserve">
    <value>Please enter delay period</value>
  </data>
  <data name="ValVacationName" xml:space="preserve">
    <value>Please enter vacation name</value>
  </data>
  <data name="MsgDelOvertime" xml:space="preserve">
    <value>Are you sure you want to delete this Overtime</value>
  </data>
  <data name="MsgGroupDel" xml:space="preserve">
    <value>Are you sure you want to delete this group</value>
  </data>
  <data name="MsgGroupMoveEmp" xml:space="preserve">
    <value>Employees in this group Should move to another group first</value>
  </data>
  <data name="MsgJobDel" xml:space="preserve">
    <value>Are you sure you want to delete this job</value>
  </data>
  <data name="MsgJobDelMoveEmp" xml:space="preserve">
    <value>Sorry you can't delete this job it's already in use</value>
  </data>
  <data name="MsgleaveAsk" xml:space="preserve">
    <value>Do you want to get the employee out of work</value>
  </data>
  <data name="MsgLeaveDelAsk" xml:space="preserve">
    <value>Are you sure you want to delete leave work note for this employee</value>
  </data>
  <data name="MsgLeaveWork" xml:space="preserve">
    <value>The employee is out of work</value>
  </data>
  <data name="MsgOvertimeExist" xml:space="preserve">
    <value>There is an overtime for the employee in this day</value>
  </data>
  <data name="MsgPnltDelAsk" xml:space="preserve">
    <value>Are you sure you want to delete this penalty</value>
  </data>
  <data name="MsgReturnAsk" xml:space="preserve">
    <value>Do you want to get the employee back to work</value>
  </data>
  <data name="MsgReturnDelAsk" xml:space="preserve">
    <value>Are you sure you want to delete return work note for this employee</value>
  </data>
  <data name="MsgReturnWork" xml:space="preserve">
    <value>The employee is back to work</value>
  </data>
  <data name="MsgRwdExist" xml:space="preserve">
    <value>There is a reward for the employee in this day, do you want to contiunue</value>
  </data>
  <data name="MsgRwrdDelAsk" xml:space="preserve">
    <value>Are you sure you want to delete this reward</value>
  </data>
  <data name="MsgVacationAnnualBalance" xml:space="preserve">
    <value>The employee exceeded his vacations balance, Do you want to continue</value>
  </data>
  <data name="MsgVacationDelAsk" xml:space="preserve">
    <value>Are you sure you want to delete this vacation</value>
  </data>
  <data name="ValAmount" xml:space="preserve">
    <value>Please enter amount</value>
  </data>
  <data name="ValGroupName" xml:space="preserve">
    <value>Please enter group name</value>
  </data>
  <data name="ValJobName" xml:space="preserve">
    <value>Please enter job name</value>
  </data>
  <data name="ValLeaveDate" xml:space="preserve">
    <value>Please enter leave work date</value>
  </data>
  <data name="ValLeaveOut" xml:space="preserve">
    <value>The employee already left work</value>
  </data>
  <data name="ValOvertimeDate" xml:space="preserve">
    <value>Please enter overtime date</value>
  </data>
  <data name="ValOvertimePeriod" xml:space="preserve">
    <value>Please enter overtime period</value>
  </data>
  <data name="ValPnltDate" xml:space="preserve">
    <value>Please enter penalty date</value>
  </data>
  <data name="ValPnltyExist" xml:space="preserve">
    <value>There is a penalty for the employee in this day, do you want to contiunue</value>
  </data>
  <data name="ValreturnDate" xml:space="preserve">
    <value>Please enter work return date</value>
  </data>
  <data name="ValReturnIn" xml:space="preserve">
    <value>The employee already in work</value>
  </data>
  <data name="ValRwrdDate" xml:space="preserve">
    <value>Please enter reward date</value>
  </data>
  <data name="ValVacationDates" xml:space="preserve">
    <value>End date should be larger than start date</value>
  </data>
  <data name="ValVacationEndDate" xml:space="preserve">
    <value>Please enter end date</value>
  </data>
  <data name="ValVacationStartDate" xml:space="preserve">
    <value>Please enter start date</value>
  </data>
  <data name="Absence" xml:space="preserve">
    <value>Absence</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Annual" xml:space="preserve">
    <value>Annual</value>
  </data>
  <data name="attendance" xml:space="preserve">
    <value>Attendance</value>
  </data>
  <data name="Casual" xml:space="preserve">
    <value>Casual</value>
  </data>
  <data name="customizeAccess" xml:space="preserve">
    <value>Customize Access Rights</value>
  </data>
  <data name="Day" xml:space="preserve">
    <value>Day</value>
  </data>
  <data name="Delay" xml:space="preserve">
    <value>Delay</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="MsgBenefitName" xml:space="preserve">
    <value>Please enter benefit name</value>
  </data>
  <data name="MsgDateError" xml:space="preserve">
    <value>Please enter date correctly</value>
  </data>
  <data name="MsgDelItem" xml:space="preserve">
    <value>Delete Entry?</value>
  </data>
  <data name="MsgPassword" xml:space="preserve">
    <value>Please enter password</value>
  </data>
  <data name="MsgPay1" xml:space="preserve">
    <value> The employee received his salary form the period </value>
  </data>
  <data name="MsgPay2" xml:space="preserve">
    <value> Payslip No.Of</value>
  </data>
  <data name="MsgPay3" xml:space="preserve">
    <value>Please complete earnings data</value>
  </data>
  <data name="MsgPay4" xml:space="preserve">
    <value>Please complete deductions data</value>
  </data>
  <data name="MsgPayDel" xml:space="preserve">
    <value>Are you sure you want to delete this payslip</value>
  </data>
  <data name="MsgPayNum" xml:space="preserve">
    <value>Please enter Payslip number</value>
  </data>
  <data name="MsgUserName" xml:space="preserve">
    <value>Please enter user name</value>
  </data>
  <data name="msgYearPeriod" xml:space="preserve">
    <value>The period can't be more than one year</value>
  </data>
  <data name="MsgZeroValue" xml:space="preserve">
    <value>Value must be larger than Zero</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="OtherVacation" xml:space="preserve">
    <value>Other Vacation</value>
  </data>
  <data name="Overtime" xml:space="preserve">
    <value>Overtime</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Weekend" xml:space="preserve">
    <value>Day Off</value>
  </data>
  <data name="FixedAmount" xml:space="preserve">
    <value>Fixed Amount</value>
  </data>
  <data name="OfBasic" xml:space="preserve">
    <value> % Of Basic </value>
  </data>
  <data name="OfVariable" xml:space="preserve">
    <value> % Of Variable </value>
  </data>
  <data name="Penalty" xml:space="preserve">
    <value>Penalty</value>
  </data>
  <data name="Ratio" xml:space="preserve">
    <value> Ratio </value>
  </data>
  <data name="Reward" xml:space="preserve">
    <value>Reward</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="BasicRatio" xml:space="preserve">
    <value>Ratio of Basic</value>
  </data>
  <data name="leftwork" xml:space="preserve">
    <value>Left Work</value>
  </data>
  <data name="MsgCodeExist" xml:space="preserve">
    <value>This code exists before</value>
  </data>
  <data name="MsgEmpCode" xml:space="preserve">
    <value>Please enter employee code</value>
  </data>
  <data name="MsgEmpCodeZero" xml:space="preserve">
    <value>Employee code can't be zero</value>
  </data>
  <data name="MsgEmpDel" xml:space="preserve">
    <value>Sorry, you can't delete employee</value>
  </data>
  <data name="MsgEmpDelAsk" xml:space="preserve">
    <value>Are you sure you want to delete this employee</value>
  </data>
  <data name="MsgEmpJournal" xml:space="preserve">
    <value>Journals related to this employee should be deleted first</value>
  </data>
  <data name="MsgEmpName" xml:space="preserve">
    <value>Please enter employee name</value>
  </data>
  <data name="MsgSelectDept" xml:space="preserve">
    <value>Please select Department</value>
  </data>
  <data name="MsgSelectJob" xml:space="preserve">
    <value>Please select Job</value>
  </data>
  <data name="PicDel" xml:space="preserve">
    <value>Are you sure you want to delete this picture</value>
  </data>
  <data name="PicExist" xml:space="preserve">
    <value>Picture name aleardy exists, please use another name</value>
  </data>
  <data name="PicSelect" xml:space="preserve">
    <value>Please select a picture and write a description</value>
  </data>
  <data name="PicSize" xml:space="preserve">
    <value>Max picture size is 10 megabyte</value>
  </data>
  <data name="StillWork" xml:space="preserve">
    <value>Still Working</value>
  </data>
  <data name="VariableRation" xml:space="preserve">
    <value>Ratio of Variable</value>
  </data>
  <data name="Payslip" xml:space="preserve">
    <value>Payslip Number</value>
  </data>
  <data name="MsgMasterAccount" xml:space="preserve">
    <value>Please select employees loans master account from settings </value>
  </data>
  <data name="MsgMasterExpAccount" xml:space="preserve">
    <value>Please select employees salary master account from settings </value>
  </data>
  <data name="loan" xml:space="preserve">
    <value>Loan</value>
  </data>
  <data name="PayslipPayment" xml:space="preserve">
    <value>Payslip Payment Number</value>
  </data>
  <data name="Commission" xml:space="preserve">
    <value>Commission</value>
  </data>
  <data name="MsgCommission" xml:space="preserve">
    <value>Please, enter sales commission correctly</value>
  </data>
  <data name="MsgAccruedAcc" xml:space="preserve">
    <value>Please select employees accrued salary account from settings </value>
  </data>
  <data name="MsgEmpTarget" xml:space="preserve">
    <value>Please review target table</value>
  </data>
  <data name="empDue" xml:space="preserve">
    <value>due to employee</value>
  </data>
  <data name="empLoadPay" xml:space="preserve">
    <value>loan payment for employee</value>
  </data>
  <data name="empPay" xml:space="preserve">
    <value>paid to employee</value>
  </data>
  <data name="PicEdit" xml:space="preserve">
    <value>Are you sure you want to edit this picture</value>
  </data>
  <data name="MsgItemExist" xml:space="preserve">
    <value>you can't select item twice</value>
  </data>
  <data name="MsgMissionDelAsk" xml:space="preserve">
    <value>Are you sure you want to delete this mission</value>
  </data>
  <data name="ValMissionExist" xml:space="preserve">
    <value>There is a mission for the employee in this period</value>
  </data>
  <data name="Mission" xml:space="preserve">
    <value>Mission </value>
  </data>
  <data name="ValVacType" xml:space="preserve">
    <value>Please select vacation type</value>
  </data>
  <data name="MainPayslip" xml:space="preserve">
    <value>Main Payslip</value>
  </data>
  <data name="SecondaryPayslip" xml:space="preserve">
    <value>Secondary Payslip</value>
  </data>
  <data name="frmPayMain" xml:space="preserve">
    <value>Employee Main Payslip</value>
  </data>
  <data name="frmPaySecond" xml:space="preserve">
    <value>Employee Secondary Payslip</value>
  </data>
  <data name="LoanDel" xml:space="preserve">
    <value>Are you sure you want to delete this loan</value>
  </data>
  <data name="LoanDelDenied" xml:space="preserve">
    <value>Sorry, you can't delete this loan, there is installments paid</value>
  </data>
  <data name="LoanJrnl" xml:space="preserve">
    <value>Loan No.</value>
  </data>
  <data name="OtherBenefits" xml:space="preserve">
    <value>Other Benefits</value>
  </data>
  <data name="enrollNo" xml:space="preserve">
    <value>Attendance machine enroll Number is aready in use for another employee</value>
  </data>
  <data name="OneEmpAtLeast" xml:space="preserve">
    <value>Please register one employee at least to proceed</value>
  </data>
  <data name="AppDelete" xml:space="preserve">
    <value>Are you sure you want to delete this document ?</value>
  </data>
  <data name="DelSponsor" xml:space="preserve">
    <value>Are you sure you want to delete this sponsor</value>
  </data>
  <data name="DegreeRequired" xml:space="preserve">
    <value>Please enter degree</value>
  </data>
  <data name="DelEval" xml:space="preserve">
    <value>Are you sure you want to delete the Evaluation Note</value>
  </data>
  <data name="EvalItem" xml:space="preserve">
    <value>Please select evaluation item</value>
  </data>
  <data name="NeedEvalItem" xml:space="preserve">
    <value>Please enter one evaluation item at least</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Days</value>
  </data>
  <data name="DelRule" xml:space="preserve">
    <value>Are you sure you want to delete this rule</value>
  </data>
  <data name="InvalidRuleRow" xml:space="preserve">
    <value>Please enter times correctly</value>
  </data>
  <data name="DelRuleDenied" xml:space="preserve">
    <value>Sorry you can't delete this rule, it's already in use</value>
  </data>
  <data name="PenaltyValue" xml:space="preserve">
    <value>The value is unknown, please enter value, or be sure that employee's day value is registered</value>
  </data>
  <data name="SaveEmpFirst" xml:space="preserve">
    <value>Please save employee data first</value>
  </data>
  <data name="ShiftsOverlapped" xml:space="preserve">
    <value>New shifts dates are overlapped with old shifts for employees</value>
  </data>
  <data name="MsgEmpShiftMove" xml:space="preserve">
    <value>Employees in this shift Should move to another shift first</value>
  </data>
  <data name="ValTimeTable" xml:space="preserve">
    <value>Please select time table correctly</value>
  </data>
  <data name="SelectShift" xml:space="preserve">
    <value>Please select shifts correctly</value>
  </data>
  <data name="NoVacBefore" xml:space="preserve">
    <value>Please ensure that no vacations registered for selected employees before</value>
  </data>
  <data name="hr" xml:space="preserve">
    <value>HRMS</value>
  </data>
  <data name="DelFpUsed" xml:space="preserve">
    <value>Sorry, you can't complete this operation,  this FP is already in use by Delya/Overtime document</value>
  </data>
  <data name="hourvalue" xml:space="preserve">
    <value>Please enter hour value</value>
  </data>
  <data name="hrVacReqMonth" xml:space="preserve">
    <value>Employee didn't pass required appointment period yet</value>
  </data>
  <data name="MsgSelectFromTo" xml:space="preserve">
    <value>Please select period correctly</value>
  </data>
  <data name="incomeTax" xml:space="preserve">
    <value>Income Tax</value>
  </data>
  <data name="Vacations" xml:space="preserve">
    <value>Vacations</value>
  </data>
  <data name="ValCompanyInsurance" xml:space="preserve">
    <value>Please enter company insurance share ratio</value>
  </data>
  <data name="ArchivedVac" xml:space="preserve">
    <value>Sorry, you can't edit this Vacation, it is archived</value>
  </data>
  <data name="ShiftReplaceAdd" xml:space="preserve">
    <value>Shift Replacement Add</value>
  </data>
  <data name="ShiftReplaceWithDraw" xml:space="preserve">
    <value>Shift Replacement withdraw</value>
  </data>
  <data name="DeleteConfirm" xml:space="preserve">
    <value>Are you sure you want to delete this document</value>
  </data>
  <data name="ShiftReplaceExist" xml:space="preserve">
    <value>There is a registered Shift Replacement in this day, do you want to continue ?</value>
  </data>
  <data name="ValDuration" xml:space="preserve">
    <value>Please Select Duration</value>
  </data>
  <data name="DateFromTo" xml:space="preserve">
    <value>You must choose Date FROM and TO</value>
  </data>
  <data name="MsgWDexist" xml:space="preserve">
    <value>There's Working Days exist in this period, Do you want to Continue?</value>
  </data>
  <data name="InsuranceSettings" xml:space="preserve">
    <value>please make sure you inserted insurance settings.</value>
  </data>
  <data name="MsgBusinessGainAcc" xml:space="preserve">
    <value>Please select Business Gain Tax account from settings</value>
  </data>
  <data name="MsgInsuranceAcc" xml:space="preserve">
    <value>Please select Insurance account from settings</value>
  </data>
  <data name="MsgNetTax" xml:space="preserve">
    <value>Please select Net Tax account from settings</value>
  </data>
  <data name="Daily" xml:space="preserve">
    <value>Daily</value>
  </data>
</root>