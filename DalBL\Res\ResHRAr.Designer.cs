﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResHRAr {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResHRAr() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResHRAr", typeof(ResHRAr).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to غياب.
        /// </summary>
        public static string Absence {
            get {
                return ResourceManager.GetString("Absence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اضافه.
        /// </summary>
        public static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبلغ.
        /// </summary>
        public static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اعتيادي.
        /// </summary>
        public static string Annual {
            get {
                return ResourceManager.GetString("Annual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف البيان ؟.
        /// </summary>
        public static string AppDelete {
            get {
                return ResourceManager.GetString("AppDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم أرشفة الاستمارة، لايمكن التعديل عليها.
        /// </summary>
        public static string ArchivedVac {
            get {
                return ResourceManager.GetString("ArchivedVac", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حضور وانصراف.
        /// </summary>
        public static string attendance {
            get {
                return ResourceManager.GetString("attendance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نسبة من الأساسي.
        /// </summary>
        public static string BasicRatio {
            get {
                return ResourceManager.GetString("BasicRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عارضه.
        /// </summary>
        public static string Casual {
            get {
                return ResourceManager.GetString("Casual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عمولة.
        /// </summary>
        public static string Commission {
            get {
                return ResourceManager.GetString("Commission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  هل تريد الاستمرار .
        /// </summary>
        public static string Continue {
            get {
                return ResourceManager.GetString("Continue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تخصيص الصلاحيات.
        /// </summary>
        public static string customizeAccess {
            get {
                return ResourceManager.GetString("customizeAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يومي.
        /// </summary>
        public static string Daily {
            get {
                return ResourceManager.GetString("Daily", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل التاريخ من وإلى.
        /// </summary>
        public static string DateFromTo {
            get {
                return ResourceManager.GetString("DateFromTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اليوم.
        /// </summary>
        public static string Day {
            get {
                return ResourceManager.GetString("Day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عدد أيام.
        /// </summary>
        public static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل الدرجة.
        /// </summary>
        public static string DegreeRequired {
            get {
                return ResourceManager.GetString("DegreeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأخير.
        /// </summary>
        public static string Delay {
            get {
                return ResourceManager.GetString("Delay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا المستند ؟.
        /// </summary>
        public static string DeleteConfirm {
            get {
                return ResourceManager.GetString("DeleteConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد انك تريد حذف استمارة التققيم هذه.
        /// </summary>
        public static string DelEval {
            get {
                return ResourceManager.GetString("DelEval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكنك الإستمرار، البصمة مستخدمة بمستند إضافي/تأخير.
        /// </summary>
        public static string DelFpUsed {
            get {
                return ResourceManager.GetString("DelFpUsed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه اللائحة.
        /// </summary>
        public static string DelRule {
            get {
                return ResourceManager.GetString("DelRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف اللائحة فهي مستخدمة بالفعل.
        /// </summary>
        public static string DelRuleDenied {
            get {
                return ResourceManager.GetString("DelRuleDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا الكفيل.
        /// </summary>
        public static string DelSponsor {
            get {
                return ResourceManager.GetString("DelSponsor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تعديل.
        /// </summary>
        public static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مستحقة للموظف.
        /// </summary>
        public static string empDue {
            get {
                return ResourceManager.GetString("empDue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سداد سلفة للموظف.
        /// </summary>
        public static string empLoadPay {
            get {
                return ResourceManager.GetString("empLoadPay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الموظف.
        /// </summary>
        public static string Employee {
            get {
                return ResourceManager.GetString("Employee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مسددة للموظف.
        /// </summary>
        public static string empPay {
            get {
                return ResourceManager.GetString("empPay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود الموظف بماكينة البصمة مستخدم بالفعل لموظف اخر.
        /// </summary>
        public static string enrollNo {
            get {
                return ResourceManager.GetString("enrollNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل بند التقييم.
        /// </summary>
        public static string EvalItem {
            get {
                return ResourceManager.GetString("EvalItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مبلغ ثابت.
        /// </summary>
        public static string FixedAmount {
            get {
                return ResourceManager.GetString("FixedAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to استمارة راتب اساسي لموظف.
        /// </summary>
        public static string frmPayMain {
            get {
                return ResourceManager.GetString("frmPayMain", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to استمارة راتب إضافي لموظف.
        /// </summary>
        public static string frmPaySecond {
            get {
                return ResourceManager.GetString("frmPaySecond", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال قيمة الساعة.
        /// </summary>
        public static string hourvalue {
            get {
                return ResourceManager.GetString("hourvalue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نظام ادارة الموارد البشرية.
        /// </summary>
        public static string hr {
            get {
                return ResourceManager.GetString("hr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الموظف لم يتجاوز مدة التعيين المطلوبة لإستحقاق الأجازات.
        /// </summary>
        public static string hrVacReqMonth {
            get {
                return ResourceManager.GetString("hrVacReqMonth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ضريبة دخل.
        /// </summary>
        public static string incomeTax {
            get {
                return ResourceManager.GetString("incomeTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to من فضلك تأكد من إدخال إعدادات التأمينات..
        /// </summary>
        public static string InsuranceSettings {
            get {
                return ResourceManager.GetString("InsuranceSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب إدخال التوقيتات بشكل سليم.
        /// </summary>
        public static string InvalidRuleRow {
            get {
                return ResourceManager.GetString("InvalidRuleRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ترك العمل.
        /// </summary>
        public static string leftwork {
            get {
                return ResourceManager.GetString("leftwork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سلفة.
        /// </summary>
        public static string loan {
            get {
                return ResourceManager.GetString("loan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد انك تريد حذف السلفة.
        /// </summary>
        public static string LoanDel {
            get {
                return ResourceManager.GetString("LoanDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف السلفة بعد سداد أقساط منها.
        /// </summary>
        public static string LoanDelDenied {
            get {
                return ResourceManager.GetString("LoanDelDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سلفة رقم.
        /// </summary>
        public static string LoanJrnl {
            get {
                return ResourceManager.GetString("LoanJrnl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الراتب الأساسي.
        /// </summary>
        public static string MainPayslip {
            get {
                return ResourceManager.GetString("MainPayslip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مأمورية.
        /// </summary>
        public static string Mission {
            get {
                return ResourceManager.GetString("Mission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد غياب مسجل للموظف في هذا اليوم, هل تريد الاستمرار.
        /// </summary>
        public static string MsgAbsenceExistAsk {
            get {
                return ResourceManager.GetString("MsgAbsenceExistAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب استحقاق الرواتب للموظفين من شاشة الإعدادات.
        /// </summary>
        public static string MsgAccruedAcc {
            get {
                return ResourceManager.GetString("MsgAccruedAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف.
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل اسم البند.
        /// </summary>
        public static string MsgBenefitName {
            get {
                return ResourceManager.GetString("MsgBenefitName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء مراجعة هذا البند للموظفين.
        /// </summary>
        public static string MsgBenefitRecheck {
            get {
                return ResourceManager.GetString("MsgBenefitRecheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب ضريبة كسب العمل من شاشة الإعدادات.
        /// </summary>
        public static string MsgBusinessGainAcc {
            get {
                return ResourceManager.GetString("MsgBusinessGainAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الكود مسجل من قبل.
        /// </summary>
        public static string MsgCodeExist {
            get {
                return ResourceManager.GetString("MsgCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تسجيل عمولة المبيعات بشكل صحيح.
        /// </summary>
        public static string MsgCommission {
            get {
                return ResourceManager.GetString("MsgCommission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال التاريخ بشكل صحيح.
        /// </summary>
        public static string MsgDateError {
            get {
                return ResourceManager.GetString("MsgDateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحذف بنجاح.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا الغياب.
        /// </summary>
        public static string MsgDelAbsence {
            get {
                return ResourceManager.GetString("MsgDelAbsence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد تأخير مسجل للموظف في هذا اليوم.
        /// </summary>
        public static string MsgDelayExist {
            get {
                return ResourceManager.GetString("MsgDelayExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا البند.
        /// </summary>
        public static string MsgDelBenefit {
            get {
                return ResourceManager.GetString("MsgDelBenefit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا, لايمكن حذف البند, فهو مخصص بالفعل لموظفين.
        /// </summary>
        public static string MsgDelBenefitDenied {
            get {
                return ResourceManager.GetString("MsgDelBenefitDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا التأخير.
        /// </summary>
        public static string MsgDelDelay {
            get {
                return ResourceManager.GetString("MsgDelDelay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا القسم.
        /// </summary>
        public static string MsgDelDept {
            get {
                return ResourceManager.GetString("MsgDelDept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف بند ؟.
        /// </summary>
        public static string MsgDelItem {
            get {
                return ResourceManager.GetString("MsgDelItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا الاضافي.
        /// </summary>
        public static string MsgDelOvertime {
            get {
                return ResourceManager.GetString("MsgDelOvertime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف صف ؟.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه الاجازه.
        /// </summary>
        public static string MsgDelVacation {
            get {
                return ResourceManager.GetString("MsgDelVacation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف الأقسام الفرعية اولا.
        /// </summary>
        public static string MsgDeptDelChilds {
            get {
                return ResourceManager.GetString("MsgDeptDelChilds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف القسم فهو مستخدم بالفعل.
        /// </summary>
        public static string MsgDeptMoveEmps {
            get {
                return ResourceManager.GetString("MsgDeptMoveEmps", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال اسم القسم.
        /// </summary>
        public static string MsgDeptName {
            get {
                return ResourceManager.GetString("MsgDeptName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال كود الموظف.
        /// </summary>
        public static string MsgEmpCode {
            get {
                return ResourceManager.GetString("MsgEmpCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود الموظف لايمكن أن يساوي صفر.
        /// </summary>
        public static string MsgEmpCodeZero {
            get {
                return ResourceManager.GetString("MsgEmpCodeZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا, لايمكن حذف الموظف.
        /// </summary>
        public static string MsgEmpDel {
            get {
                return ResourceManager.GetString("MsgEmpDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد انك تريد حذف هذ الموظف؟.
        /// </summary>
        public static string MsgEmpDelAsk {
            get {
                return ResourceManager.GetString("MsgEmpDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف القيود الخاصه بهذا الموظف أولا.
        /// </summary>
        public static string MsgEmpJournal {
            get {
                return ResourceManager.GetString("MsgEmpJournal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال اسم الموظف.
        /// </summary>
        public static string MsgEmpName {
            get {
                return ResourceManager.GetString("MsgEmpName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد حساب لهذا الموظف.
        /// </summary>
        public static string MsgEmpNoAccount {
            get {
                return ResourceManager.GetString("MsgEmpNoAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب نقل الموظفين بهذه الوردية اولا.
        /// </summary>
        public static string MsgEmpShiftMove {
            get {
                return ResourceManager.GetString("MsgEmpShiftMove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء مراجعة بيانات العمولة.
        /// </summary>
        public static string MsgEmpTarget {
            get {
                return ResourceManager.GetString("MsgEmpTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد اجازات رسميه في هذه الفتره.
        /// </summary>
        public static string MsgFormalExist {
            get {
                return ResourceManager.GetString("MsgFormalExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه المجموعه.
        /// </summary>
        public static string MsgGroupDel {
            get {
                return ResourceManager.GetString("MsgGroupDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب نقل الموظفين بهذه المجموعه اولا.
        /// </summary>
        public static string MsgGroupMoveEmp {
            get {
                return ResourceManager.GetString("MsgGroupMoveEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من صحة البيانات.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب التأمينات من شاشة الإعدادات.
        /// </summary>
        public static string MsgInsuranceAcc {
            get {
                return ResourceManager.GetString("MsgInsuranceAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن اختيار الصنف أكثر من مرة.
        /// </summary>
        public static string MsgItemExist {
            get {
                return ResourceManager.GetString("MsgItemExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه الوظيفه.
        /// </summary>
        public static string MsgJobDel {
            get {
                return ResourceManager.GetString("MsgJobDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف الوظيفة فهي مستخدمة بالفعل.
        /// </summary>
        public static string MsgJobDelMoveEmp {
            get {
                return ResourceManager.GetString("MsgJobDelMoveEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد اخراج الموظف من العمل.
        /// </summary>
        public static string MsgleaveAsk {
            get {
                return ResourceManager.GetString("MsgleaveAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف استمارة ترك عمل لموظف.
        /// </summary>
        public static string MsgLeaveDelAsk {
            get {
                return ResourceManager.GetString("MsgLeaveDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم اخراج الموظف من العمل بنجاح.
        /// </summary>
        public static string MsgLeaveWork {
            get {
                return ResourceManager.GetString("MsgLeaveWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء اختيار حساب الذمم الرئيسي من شاشة الاعدادات.
        /// </summary>
        public static string MsgMasterAccount {
            get {
                return ResourceManager.GetString("MsgMasterAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء اختيار حساب الرواتب الرئيسي من شاشة الاعدادات.
        /// </summary>
        public static string MsgMasterExpAccount {
            get {
                return ResourceManager.GetString("MsgMasterExpAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه المأمورية.
        /// </summary>
        public static string MsgMissionDelAsk {
            get {
                return ResourceManager.GetString("MsgMissionDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الاسم مسجل من قبل.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب الضريبة التكافلية من شاشة الإعدادات.
        /// </summary>
        public static string MsgNetTax {
            get {
                return ResourceManager.GetString("MsgNetTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الرقم مسجل من قبل.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد اضافي مسجل للموظف في هذا اليوم.
        /// </summary>
        public static string MsgOvertimeExist {
            get {
                return ResourceManager.GetString("MsgOvertimeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال الرقم السري.
        /// </summary>
        public static string MsgPassword {
            get {
                return ResourceManager.GetString("MsgPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الموظف استلم راتب من الفترة .
        /// </summary>
        public static string MsgPay1 {
            get {
                return ResourceManager.GetString("MsgPay1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to باستماره رقم.
        /// </summary>
        public static string MsgPay2 {
            get {
                return ResourceManager.GetString("MsgPay2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء اكمال بيانات بنود الاستحقاق.
        /// </summary>
        public static string MsgPay3 {
            get {
                return ResourceManager.GetString("MsgPay3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء اكمال بيانات بنود الاستقطاع.
        /// </summary>
        public static string MsgPay4 {
            get {
                return ResourceManager.GetString("MsgPay4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف استمارة المرتب هذه.
        /// </summary>
        public static string MsgPayDel {
            get {
                return ResourceManager.GetString("MsgPayDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل رقم الاستماره.
        /// </summary>
        public static string MsgPayNum {
            get {
                return ResourceManager.GetString("MsgPayNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا الجزاء.
        /// </summary>
        public static string MsgPnltDelAsk {
            get {
                return ResourceManager.GetString("MsgPnltDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية تعديل هذا البيان.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية اضافة بيان جديد.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد اعادة الموظف للعمل.
        /// </summary>
        public static string MsgReturnAsk {
            get {
                return ResourceManager.GetString("MsgReturnAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف استمارة عودة  موظف للعمل.
        /// </summary>
        public static string MsgReturnDelAsk {
            get {
                return ResourceManager.GetString("MsgReturnDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم اعادة الموظف للعمل بنجاح.
        /// </summary>
        public static string MsgReturnWork {
            get {
                return ResourceManager.GetString("MsgReturnWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد مكافأه مسجله للموظف في هذا اليوم, هل تريد الاستمرار.
        /// </summary>
        public static string MsgRwdExist {
            get {
                return ResourceManager.GetString("MsgRwdExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه المكافأه.
        /// </summary>
        public static string MsgRwrdDelAsk {
            get {
                return ResourceManager.GetString("MsgRwrdDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحفظ بنجاح.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار القسم.
        /// </summary>
        public static string MsgSelectDept {
            get {
                return ResourceManager.GetString("MsgSelectDept", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء اختيار الفترة بشكل صحيح.
        /// </summary>
        public static string MsgSelectFromTo {
            get {
                return ResourceManager.GetString("MsgSelectFromTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الوظيفة.
        /// </summary>
        public static string MsgSelectJob {
            get {
                return ResourceManager.GetString("MsgSelectJob", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا, لايمكن حذف مدير النظام.
        /// </summary>
        public static string MsgSysAdminDel {
            get {
                return ResourceManager.GetString("MsgSysAdminDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to معلومة.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سؤال.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تحذير.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا المستخدم.
        /// </summary>
        public static string MsgUserDel {
            get {
                return ResourceManager.GetString("MsgUserDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال اسم المستخدم.
        /// </summary>
        public static string MsgUserName {
            get {
                return ResourceManager.GetString("MsgUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الموظف تجاوز رصيد الأجازات هل تريد الاستمرار.
        /// </summary>
        public static string MsgVacationAnnualBalance {
            get {
                return ResourceManager.GetString("MsgVacationAnnualBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه الاجازه.
        /// </summary>
        public static string MsgVacationDelAsk {
            get {
                return ResourceManager.GetString("MsgVacationDelAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد اجازه مسجلة للموظف في هذا اليوم, هل تريد الاستمرار.
        /// </summary>
        public static string MsgVacationExistAsk {
            get {
                return ResourceManager.GetString("MsgVacationExistAsk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد أيام عمل مسجله للموظف في هذه الفترة, هل تريد الاستمرار.
        /// </summary>
        public static string MsgWDexist {
            get {
                return ResourceManager.GetString("MsgWDexist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الفترة لا يمكن ان تزيد عن سنة.
        /// </summary>
        public static string msgYearPeriod {
            get {
                return ResourceManager.GetString("msgYearPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to القيمه يجب ان تكون اكبر من الصفر.
        /// </summary>
        public static string MsgZeroValue {
            get {
                return ResourceManager.GetString("MsgZeroValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل بند تقييم واحد على الأقل.
        /// </summary>
        public static string NeedEvalItem {
            get {
                return ResourceManager.GetString("NeedEvalItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا.
        /// </summary>
        public static string no {
            get {
                return ResourceManager.GetString("no", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب التأكد من عدم إختيار موظف تم تحرير استمارة اجازه له مسبقا.
        /// </summary>
        public static string NoVacBefore {
            get {
                return ResourceManager.GetString("NoVacBefore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to   % من الاساسي.
        /// </summary>
        public static string OfBasic {
            get {
                return ResourceManager.GetString("OfBasic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  % من المتغير .
        /// </summary>
        public static string OfVariable {
            get {
                return ResourceManager.GetString("OfVariable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل موظف واحد على الأقل.
        /// </summary>
        public static string OneEmpAtLeast {
            get {
                return ResourceManager.GetString("OneEmpAtLeast", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اخري.
        /// </summary>
        public static string Other {
            get {
                return ResourceManager.GetString("Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مستحقات أخرى.
        /// </summary>
        public static string OtherBenefits {
            get {
                return ResourceManager.GetString("OtherBenefits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجازة اخري.
        /// </summary>
        public static string OtherVacation {
            get {
                return ResourceManager.GetString("OtherVacation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اضافي.
        /// </summary>
        public static string Overtime {
            get {
                return ResourceManager.GetString("Overtime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to P--.
        /// </summary>
        public static string P {
            get {
                return ResourceManager.GetString("P", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to استمارة مرتب رقم.
        /// </summary>
        public static string Payslip {
            get {
                return ResourceManager.GetString("Payslip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سداد استمارة مرتب رقم.
        /// </summary>
        public static string PayslipPayment {
            get {
                return ResourceManager.GetString("PayslipPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to جزاء.
        /// </summary>
        public static string Penalty {
            get {
                return ResourceManager.GetString("Penalty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قيمة المستند غير معروفة، برجاء تسجيل القيمة، او تسجيل قيمة اليوم للموظف ببيانات الموظفين.
        /// </summary>
        public static string PenaltyValue {
            get {
                return ResourceManager.GetString("PenaltyValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد بالفعل حذف هذه الصوره؟.
        /// </summary>
        public static string PicDel {
            get {
                return ResourceManager.GetString("PicDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد بالفعل تعديل هذه الصوره؟.
        /// </summary>
        public static string PicEdit {
            get {
                return ResourceManager.GetString("PicEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد صوره بهذا الاسم, يجب تغيير اسم الصوره.
        /// </summary>
        public static string PicExist {
            get {
                return ResourceManager.GetString("PicExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى اختيار صورة وإدخال وصف مناسب لها.
        /// </summary>
        public static string PicSelect {
            get {
                return ResourceManager.GetString("PicSelect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يمكن تحميل صوره اكبر من 10 ميجابايت.
        /// </summary>
        public static string PicSize {
            get {
                return ResourceManager.GetString("PicSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to طباعه.
        /// </summary>
        public static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  نسبة  .
        /// </summary>
        public static string Ratio {
            get {
                return ResourceManager.GetString("Ratio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مكافأة.
        /// </summary>
        public static string Reward {
            get {
                return ResourceManager.GetString("Reward", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حفظ.
        /// </summary>
        public static string save {
            get {
                return ResourceManager.GetString("save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حفظ بيانات الموظف اولا.
        /// </summary>
        public static string SaveEmpFirst {
            get {
                return ResourceManager.GetString("SaveEmpFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الراتب الإضافي.
        /// </summary>
        public static string SecondaryPayslip {
            get {
                return ResourceManager.GetString("SecondaryPayslip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء اختيار الورديات بشكل سليم.
        /// </summary>
        public static string SelectShift {
            get {
                return ResourceManager.GetString("SelectShift", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إضافة بدل وردية.
        /// </summary>
        public static string ShiftReplaceAdd {
            get {
                return ResourceManager.GetString("ShiftReplaceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد بدل وردية مسجل في هذا اليوم، هل تريد الاستمرار ؟.
        /// </summary>
        public static string ShiftReplaceExist {
            get {
                return ResourceManager.GetString("ShiftReplaceExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to صرف بدل وردية.
        /// </summary>
        public static string ShiftReplaceWithDraw {
            get {
                return ResourceManager.GetString("ShiftReplaceWithDraw", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الورديات الجديدة متداخلة مع ورديات قديمة للموظفين.
        /// </summary>
        public static string ShiftsOverlapped {
            get {
                return ResourceManager.GetString("ShiftsOverlapped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مستمر.
        /// </summary>
        public static string StillWork {
            get {
                return ResourceManager.GetString("StillWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to من فضلك تأكد من ادخال اعدادات التامينات..
        /// </summary>
        public static string String1 {
            get {
                return ResourceManager.GetString("String1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تام.
        /// </summary>
        public static string txtAssembly {
            get {
                return ResourceManager.GetString("txtAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مطلق.
        /// </summary>
        public static string txtDivorced {
            get {
                return ResourceManager.GetString("txtDivorced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  خزينه .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أنثي.
        /// </summary>
        public static string txtFemale {
            get {
                return ResourceManager.GetString("txtFemale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كل اسبوعين.
        /// </summary>
        public static string txtFortnightly {
            get {
                return ResourceManager.GetString("txtFortnightly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من تاريخ .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دخول كامل.
        /// </summary>
        public static string txtFullAccess {
            get {
                return ResourceManager.GetString("txtFullAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  ساعة .
        /// </summary>
        public static string txtHour {
            get {
                return ResourceManager.GetString("txtHour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بالساعة.
        /// </summary>
        public static string txtHourly {
            get {
                return ResourceManager.GetString("txtHourly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دخول مخصص.
        /// </summary>
        public static string txtLimitedAccess {
            get {
                return ResourceManager.GetString("txtLimitedAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ذكر.
        /// </summary>
        public static string txtMale {
            get {
                return ResourceManager.GetString("txtMale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to متزوج.
        /// </summary>
        public static string txtMarried {
            get {
                return ResourceManager.GetString("txtMarried", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to شهري.
        /// </summary>
        public static string txtMonthly {
            get {
                return ResourceManager.GetString("txtMonthly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الدخول ممنوع.
        /// </summary>
        public static string txtNoAccess {
            get {
                return ResourceManager.GetString("txtNoAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أخري.
        /// </summary>
        public static string txtOther {
            get {
                return ResourceManager.GetString("txtOther", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بالقطعة.
        /// </summary>
        public static string txtPerTask {
            get {
                return ResourceManager.GetString("txtPerTask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أعزب.
        /// </summary>
        public static string txtSingle {
            get {
                return ResourceManager.GetString("txtSingle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مدير النظام.
        /// </summary>
        public static string txtSysAdmin {
            get {
                return ResourceManager.GetString("txtSysAdmin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى تاريخ .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اعتيادي.
        /// </summary>
        public static string txtVacAnnual {
            get {
                return ResourceManager.GetString("txtVacAnnual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عارضه.
        /// </summary>
        public static string txtVacCasual {
            get {
                return ResourceManager.GetString("txtVacCasual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بالاسبوع.
        /// </summary>
        public static string txtWeekly {
            get {
                return ResourceManager.GetString("txtWeekly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أرمل.
        /// </summary>
        public static string txtWidowed {
            get {
                return ResourceManager.GetString("txtWidowed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أجازات.
        /// </summary>
        public static string Vacations {
            get {
                return ResourceManager.GetString("Vacations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ نهاية الغياب يجب ان يكون اكبر من تاريخ بدء الغياب.
        /// </summary>
        public static string ValAbsenceDats {
            get {
                return ResourceManager.GetString("ValAbsenceDats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ نهاية الغياب.
        /// </summary>
        public static string ValAbsenceEndDate {
            get {
                return ResourceManager.GetString("ValAbsenceEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد غياب مسجل للموظف في هذه الفتره.
        /// </summary>
        public static string ValAbsenceExist {
            get {
                return ResourceManager.GetString("ValAbsenceExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ بدء الغياب.
        /// </summary>
        public static string ValAbsenceStartDate {
            get {
                return ResourceManager.GetString("ValAbsenceStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل المبلغ.
        /// </summary>
        public static string ValAmount {
            get {
                return ResourceManager.GetString("ValAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل اسم البند.
        /// </summary>
        public static string ValBenefitName {
            get {
                return ResourceManager.GetString("ValBenefitName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال نسبة حصة الشركة من التأمينات.
        /// </summary>
        public static string ValCompanyInsurance {
            get {
                return ResourceManager.GetString("ValCompanyInsurance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل التاريخ.
        /// </summary>
        public static string ValDate {
            get {
                return ResourceManager.GetString("ValDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ البدايه يجب ان يكون اصغر من تاريخ النهايه.
        /// </summary>
        public static string ValDates {
            get {
                return ResourceManager.GetString("ValDates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل مدة التاخير.
        /// </summary>
        public static string ValDelayPeriod {
            get {
                return ResourceManager.GetString("ValDelayPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  يجب اختيار المدة.
        /// </summary>
        public static string ValDuration {
            get {
                return ResourceManager.GetString("ValDuration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الموظف.
        /// </summary>
        public static string ValEmp {
            get {
                return ResourceManager.GetString("ValEmp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل اسم المجموعه.
        /// </summary>
        public static string ValGroupName {
            get {
                return ResourceManager.GetString("ValGroupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل اسم الوظيفه.
        /// </summary>
        public static string ValJobName {
            get {
                return ResourceManager.GetString("ValJobName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  اخر راتب استلمه الموظف كان حتي تاريخ .
        /// </summary>
        public static string ValLastPaySlip {
            get {
                return ResourceManager.GetString("ValLastPaySlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ ترك العمل.
        /// </summary>
        public static string ValLeaveDate {
            get {
                return ResourceManager.GetString("ValLeaveDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الموظف بالفعل تارك للعمل.
        /// </summary>
        public static string ValLeaveOut {
            get {
                return ResourceManager.GetString("ValLeaveOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد مأموريات للموظف في هذه الفتره.
        /// </summary>
        public static string ValMissionExist {
            get {
                return ResourceManager.GetString("ValMissionExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ الاضافي.
        /// </summary>
        public static string ValOvertimeDate {
            get {
                return ResourceManager.GetString("ValOvertimeDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل مدة الاضافي.
        /// </summary>
        public static string ValOvertimePeriod {
            get {
                return ResourceManager.GetString("ValOvertimePeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ الجزاء.
        /// </summary>
        public static string ValPnltDate {
            get {
                return ResourceManager.GetString("ValPnltDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد جزاء مسجل للموظف في هذا اليوم, هل تريد الاستمرار.
        /// </summary>
        public static string ValPnltyExist {
            get {
                return ResourceManager.GetString("ValPnltyExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ  العودة للعمل.
        /// </summary>
        public static string ValreturnDate {
            get {
                return ResourceManager.GetString("ValreturnDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الموظف بالفعل موجود بالعمل.
        /// </summary>
        public static string ValReturnIn {
            get {
                return ResourceManager.GetString("ValReturnIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ المكافأه.
        /// </summary>
        public static string ValRwrdDate {
            get {
                return ResourceManager.GetString("ValRwrdDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال التوقيتات بشكل سليم.
        /// </summary>
        public static string ValTimeTable {
            get {
                return ResourceManager.GetString("ValTimeTable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ النهاية يجب ان يكون اكبر من تاريخ البدء.
        /// </summary>
        public static string ValVacationDates {
            get {
                return ResourceManager.GetString("ValVacationDates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ النهاية.
        /// </summary>
        public static string ValVacationEndDate {
            get {
                return ResourceManager.GetString("ValVacationEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يوجد اجازات للموظف في هذه الفتره.
        /// </summary>
        public static string ValVacationExist {
            get {
                return ResourceManager.GetString("ValVacationExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل اسم الأجازة.
        /// </summary>
        public static string ValVacationName {
            get {
                return ResourceManager.GetString("ValVacationName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ البدء.
        /// </summary>
        public static string ValVacationStartDate {
            get {
                return ResourceManager.GetString("ValVacationStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد نوع الأجازة.
        /// </summary>
        public static string ValVacType {
            get {
                return ResourceManager.GetString("ValVacType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نسبه من المتغير.
        /// </summary>
        public static string VariableRatio {
            get {
                return ResourceManager.GetString("VariableRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to راحة.
        /// </summary>
        public static string Weekend {
            get {
                return ResourceManager.GetString("Weekend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نعم.
        /// </summary>
        public static string yes {
            get {
                return ResourceManager.GetString("yes", resourceCulture);
            }
        }
    }
}
