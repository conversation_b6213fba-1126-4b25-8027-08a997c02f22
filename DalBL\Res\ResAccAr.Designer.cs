﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResAccAr {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResAccAr() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResAccAr", typeof(ResAccAr).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دائن.
        /// </summary>
        public static string acTypeCredit {
            get {
                return ResourceManager.GetString("acTypeCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مدين.
        /// </summary>
        public static string acTypeDebit {
            get {
                return ResourceManager.GetString("acTypeDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to غير محدد.
        /// </summary>
        public static string acTypeWithout {
            get {
                return ResourceManager.GetString("acTypeWithout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب أن يتساوى مجموع نسب راس المال مع إجمالى رأس المال.
        /// </summary>
        public static string capitalDisError {
            get {
                return ResourceManager.GetString("capitalDisError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إجباري.
        /// </summary>
        public static string ccTypeMandatory {
            get {
                return ResourceManager.GetString("ccTypeMandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to إختياري.
        /// </summary>
        public static string ccTypeOptional {
            get {
                return ResourceManager.GetString("ccTypeOptional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to بدون.
        /// </summary>
        public static string ccTypeWithout {
            get {
                return ResourceManager.GetString("ccTypeWithout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اشعار دائن.
        /// </summary>
        public static string CreditNote {
            get {
                return ResourceManager.GetString("CreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قائمة الاشعارات الدائنة.
        /// </summary>
        public static string CreditNoteList {
            get {
                return ResourceManager.GetString("CreditNoteList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اشعار مدين.
        /// </summary>
        public static string DebitNote {
            get {
                return ResourceManager.GetString("DebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قائمة الاشعارات المدينة.
        /// </summary>
        public static string DebitNoteList {
            get {
                return ResourceManager.GetString("DebitNoteList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف الحساب فهو مستخدم بنظام الانتاج.
        /// </summary>
        public static string DelAccBom {
            get {
                return ResourceManager.GetString("DelAccBom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف الحساب فهو مستخددم لأحد المخازن.
        /// </summary>
        public static string DelAccContinualStore {
            get {
                return ResourceManager.GetString("DelAccContinualStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكنك حذف مركز التكلفه، يجب حذف مراكز التكلفه الفرعيه اولا.
        /// </summary>
        public static string delCCChild {
            get {
                return ResourceManager.GetString("delCCChild", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف الأصل، يوجد قيود مرتبطة به.
        /// </summary>
        public static string DelFaDenied {
            get {
                return ResourceManager.GetString("DelFaDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف فئة الأصول الثابتة هذه.
        /// </summary>
        public static string DelFaGrp {
            get {
                return ResourceManager.GetString("DelFaGrp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف هذه الفئة، يوجد أصول مرتبطة بها.
        /// </summary>
        public static string DelFaGrp2 {
            get {
                return ResourceManager.GetString("DelFaGrp2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to انت على وشك حذف هذا العنصر !! هل انت متأكد؟.
        /// </summary>
        public static string delManfExpWarn {
            get {
                return ResourceManager.GetString("delManfExpWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الفرق.
        /// </summary>
        public static string diff {
            get {
                return ResourceManager.GetString("diff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to موظف.
        /// </summary>
        public static string emp {
            get {
                return ResourceManager.GetString("emp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فائض.
        /// </summary>
        public static string excess {
            get {
                return ResourceManager.GetString("excess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ الصلاحية.
        /// </summary>
        public static string ExpireDate {
            get {
                return ResourceManager.GetString("ExpireDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار حساب الأصول الثابتة وحساب مجمع الاهلاك من شاشة الإعدادات.
        /// </summary>
        public static string FaAccs {
            get {
                return ResourceManager.GetString("FaAccs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، لا يمكن حذف هذا الحساب.
        /// </summary>
        public static string Msg01 {
            get {
                return ResourceManager.GetString("Msg01", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف القيود الخاصه بهذا الحساب اولا.
        /// </summary>
        public static string Msg02 {
            get {
                return ResourceManager.GetString("Msg02", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف الحسابات الفرعية اولا.
        /// </summary>
        public static string Msg03 {
            get {
                return ResourceManager.GetString("Msg03", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا الحساب.
        /// </summary>
        public static string Msg04 {
            get {
                return ResourceManager.GetString("Msg04", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لا يمكن انشاء حساب فرعي.
        /// </summary>
        public static string Msg05 {
            get {
                return ResourceManager.GetString("Msg05", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال اسم الحساب.
        /// </summary>
        public static string Msg06 {
            get {
                return ResourceManager.GetString("Msg06", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يمكن تعديل هذا الحساب.
        /// </summary>
        public static string Msg09 {
            get {
                return ResourceManager.GetString("Msg09", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا, لايمكن حذف الخزينه الرئيسيه.
        /// </summary>
        public static string Msg10 {
            get {
                return ResourceManager.GetString("Msg10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذه الخزنه.
        /// </summary>
        public static string Msg11 {
            get {
                return ResourceManager.GetString("Msg11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف سند الدفع النقدي.
        /// </summary>
        public static string Msg12 {
            get {
                return ResourceManager.GetString("Msg12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف سند الاستلام النقدي.
        /// </summary>
        public static string Msg13 {
            get {
                return ResourceManager.GetString("Msg13", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف سند التحويل النقدي.
        /// </summary>
        public static string Msg14 {
            get {
                return ResourceManager.GetString("Msg14", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، لا يمكن حذف هذا القيد من هنا, يجب ان يحذف من مصدره.
        /// </summary>
        public static string Msg15 {
            get {
                return ResourceManager.GetString("Msg15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا القيد.
        /// </summary>
        public static string Msg16 {
            get {
                return ResourceManager.GetString("Msg16", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال الرقم الدفتري.
        /// </summary>
        public static string Msg17 {
            get {
                return ResourceManager.GetString("Msg17", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، لا يمكن تعديل هذا القيد من هنا, يجب ان يعدل من مصدره.
        /// </summary>
        public static string Msg18 {
            get {
                return ResourceManager.GetString("Msg18", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل القيود.
        /// </summary>
        public static string Msg19 {
            get {
                return ResourceManager.GetString("Msg19", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ان يتساوي الطرف الدائن مع الطرف المدين.
        /// </summary>
        public static string Msg20 {
            get {
                return ResourceManager.GetString("Msg20", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن استخدام حسابات نهاية المدة في قيود, فهي تحسب بشكل تلقائي.
        /// </summary>
        public static string Msg21 {
            get {
                return ResourceManager.GetString("Msg21", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف ورق الدفع.
        /// </summary>
        public static string Msg22 {
            get {
                return ResourceManager.GetString("Msg22", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف ورق القبض.
        /// </summary>
        public static string Msg23 {
            get {
                return ResourceManager.GetString("Msg23", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سيتم سداد جميع أوراق الدفع المختارة، هل تريد المتابعة.
        /// </summary>
        public static string Msg24 {
            get {
                return ResourceManager.GetString("Msg24", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم سداد أوراق الدفع بنجاح.
        /// </summary>
        public static string Msg25 {
            get {
                return ResourceManager.GetString("Msg25", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء اختيار خزينة سداد الكمبيالات و الضغط على متابعة.
        /// </summary>
        public static string Msg26 {
            get {
                return ResourceManager.GetString("Msg26", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سيتم تحديث بيانات أوراق القبض المختارة، هل تريد المتابعة.
        /// </summary>
        public static string Msg27 {
            get {
                return ResourceManager.GetString("Msg27", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء اختيار خزينة السداد و الضغط على متابعة.
        /// </summary>
        public static string Msg28 {
            get {
                return ResourceManager.GetString("Msg28", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم تحديث بيانات أوراق القبض بنجاح.
        /// </summary>
        public static string Msg29 {
            get {
                return ResourceManager.GetString("Msg29", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا الايراد.
        /// </summary>
        public static string Msg30 {
            get {
                return ResourceManager.GetString("Msg30", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف هذا المصروف.
        /// </summary>
        public static string Msg31 {
            get {
                return ResourceManager.GetString("Msg31", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف قائمة الحساب المخصصة.
        /// </summary>
        public static string Msg32 {
            get {
                return ResourceManager.GetString("Msg32", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل الحسابات.
        /// </summary>
        public static string Msg33 {
            get {
                return ResourceManager.GetString("Msg33", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف هذا الحساب من قوائم الحسابات المخصصة اولا.
        /// </summary>
        public static string Msg34 {
            get {
                return ResourceManager.GetString("Msg34", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل اسم قائمة الحسابات.
        /// </summary>
        public static string Msg35 {
            get {
                return ResourceManager.GetString("Msg35", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لقد تم تجاوز عدد الحسابات المسموح بها في هذا المستوى من شجرة الحسابات.
        /// </summary>
        public static string MsgAccLevel {
            get {
                return ResourceManager.GetString("MsgAccLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الحساب مستخدم في إعدادات الحسابات، لايمكن حذفه.
        /// </summary>
        public static string MsgAccSettng {
            get {
                return ResourceManager.GetString("MsgAccSettng", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف.
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب البنوك وأوراق القبض والدفع من شاشة الإعدادات.
        /// </summary>
        public static string MsgBankAcc {
            get {
                return ResourceManager.GetString("MsgBankAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لا يمكن انشاء مركز تكلفة فرعي.
        /// </summary>
        public static string MsgCantAddCCC {
            get {
                return ResourceManager.GetString("MsgCantAddCCC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجي ادخال كود مركز التكلفة.
        /// </summary>
        public static string MsgCCCode {
            get {
                return ResourceManager.GetString("MsgCCCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف مركز التكلفة.
        /// </summary>
        public static string MsgccDelDenie {
            get {
                return ResourceManager.GetString("MsgccDelDenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجي ادخال اسم مركز التكلفة.
        /// </summary>
        public static string MsgCCName {
            get {
                return ResourceManager.GetString("MsgCCName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحذف بنجاح.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف مركز التكلفة هذا.
        /// </summary>
        public static string MsgDelCC {
            get {
                return ResourceManager.GetString("MsgDelCC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف القيود الخاصه بمركز التكلفة هذا اولا.
        /// </summary>
        public static string MsgDelCCDenied {
            get {
                return ResourceManager.GetString("MsgDelCCDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف سند اشعار دائن.
        /// </summary>
        public static string MsgDelCrdtNote {
            get {
                return ResourceManager.GetString("MsgDelCrdtNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد أنك تريد حذف سند اشعار مدين.
        /// </summary>
        public static string MsgDelDbtNote {
            get {
                return ResourceManager.GetString("MsgDelDbtNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف صف ؟.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب الخزانات الرئيسي من شاشة الإعدادات.
        /// </summary>
        public static string MsgDrawerAcc {
            get {
                return ResourceManager.GetString("MsgDrawerAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل حساب خزينة او بنك أولا.
        /// </summary>
        public static string MsgDrwrBank {
            get {
                return ResourceManager.GetString("MsgDrwrBank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار تاريخ انتهاء السنة المالية من شاشة بيانات المنشأة.
        /// </summary>
        public static string MsgFiscalYearEndDate {
            get {
                return ResourceManager.GetString("MsgFiscalYearEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف هذا الحساب، الحساب مستخدم من قبل النظام، يمكنكم تغيير ذلك من الاعدادات.
        /// </summary>
        public static string MsgHRAccount {
            get {
                return ResourceManager.GetString("MsgHRAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء إختيار حسابات قائمة الدخل في شاشة الإعدادات.
        /// </summary>
        public static string MsgIncomAccs {
            get {
                return ResourceManager.GetString("MsgIncomAccs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من صحة البيانات.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب المتاجرة من شاشة الإعدادات.
        /// </summary>
        public static string MsgMerchendaisingAcc {
            get {
                return ResourceManager.GetString("MsgMerchendaisingAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الاسم مسجل من قبل.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب أوراق الدفع من شاشة الإعدادات.
        /// </summary>
        public static string MsgNotesPayableAcc {
            get {
                return ResourceManager.GetString("MsgNotesPayableAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب أوراق القبض من شاشة الإعدادات.
        /// </summary>
        public static string MsgNotesReceivableAcc {
            get {
                return ResourceManager.GetString("MsgNotesReceivableAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب أوراق تحت التحصيل من شاشة الإعدادات.
        /// </summary>
        public static string MsgNotesReceivableUnderCollect {
            get {
                return ResourceManager.GetString("MsgNotesReceivableUnderCollect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الرقم مسجل من قبل.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية تعديل هذا البيان.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية اضافة بيان جديد.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحفظ بنجاح.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to معلومة.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سؤال.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تحذير.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذه الخزينة مخصصة كخزينة افتراضية لبعض المستخدمين.
        /// </summary>
        public static string msgUserDefault {
            get {
                return ResourceManager.GetString("msgUserDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد حساب الفيزات وأوراق القبض والدفع من شاشة الإعدادات.
        /// </summary>
        public static string MsgVisaAcc {
            get {
                return ResourceManager.GetString("MsgVisaAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكود لايمكن أن يساوي صفر.
        /// </summary>
        public static string MsgZeroCode {
            get {
                return ResourceManager.GetString("MsgZeroCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to صافي الخسارة.
        /// </summary>
        public static string NetLoss {
            get {
                return ResourceManager.GetString("NetLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to صافي الربح.
        /// </summary>
        public static string NetProfit {
            get {
                return ResourceManager.GetString("NetProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد ربح أو خسارة.
        /// </summary>
        public static string NoLossNoProfit {
            get {
                return ResourceManager.GetString("NoLossNoProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مجمل الربح البيع النقدى.
        /// </summary>
        public static string SalesAfterCost {
            get {
                return ResourceManager.GetString("SalesAfterCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to صافي المبيعات.
        /// </summary>
        public static string SalesNet {
            get {
                return ResourceManager.GetString("SalesNet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء تحديد الفترة بشكل سليم.
        /// </summary>
        public static string SelectPeriod {
            get {
                return ResourceManager.GetString("SelectPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عجز.
        /// </summary>
        public static string shortage {
            get {
                return ResourceManager.GetString("shortage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رقم تشغيلة.
        /// </summary>
        public static string st_Batch {
            get {
                return ResourceManager.GetString("st_Batch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم المنشأه.
        /// </summary>
        public static string st_CompName {
            get {
                return ResourceManager.GetString("st_CompName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تليفون المنشأه.
        /// </summary>
        public static string st_CompTel {
            get {
                return ResourceManager.GetString("st_CompTel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود الصنف.
        /// </summary>
        public static string st_ItemCode {
            get {
                return ResourceManager.GetString("st_ItemCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود 2.
        /// </summary>
        public static string st_ItemCode2 {
            get {
                return ResourceManager.GetString("st_ItemCode2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم الصنف.
        /// </summary>
        public static string st_ItemName {
            get {
                return ResourceManager.GetString("st_ItemName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سعر الصنف.
        /// </summary>
        public static string st_ItemPrice {
            get {
                return ResourceManager.GetString("st_ItemPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رقم فاتورة المشتريات.
        /// </summary>
        public static string st_PurchaseInvoice {
            get {
                return ResourceManager.GetString("st_PurchaseInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية.
        /// </summary>
        public static string st_Qty {
            get {
                return ResourceManager.GetString("st_Qty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل عدد الأيام.
        /// </summary>
        public static string st_SetDaysCount {
            get {
                return ResourceManager.GetString("st_SetDaysCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود المورد.
        /// </summary>
        public static string st_vendorCode {
            get {
                return ResourceManager.GetString("st_vendorCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مجمل الخساره.
        /// </summary>
        public static string TotalLoss {
            get {
                return ResourceManager.GetString("TotalLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مجمل الربح.
        /// </summary>
        public static string TotalProfit {
            get {
                return ResourceManager.GetString("TotalProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الحســاب.
        /// </summary>
        public static string txt_Account {
            get {
                return ResourceManager.GetString("txt_Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to د.
        /// </summary>
        public static string txt_C {
            get {
                return ResourceManager.GetString("txt_C", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to م.
        /// </summary>
        public static string txt_D {
            get {
                return ResourceManager.GetString("txt_D", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to وارد.
        /// </summary>
        public static string txt_incoming {
            get {
                return ResourceManager.GetString("txt_incoming", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قيد يومية رقم .
        /// </summary>
        public static string txt_JournalNumber {
            get {
                return ResourceManager.GetString("txt_JournalNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رقــم.
        /// </summary>
        public static string txt_Number {
            get {
                return ResourceManager.GetString("txt_Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to صادر.
        /// </summary>
        public static string txt_outgoing {
            get {
                return ResourceManager.GetString("txt_outgoing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل رقم الحساب.
        /// </summary>
        public static string txt1 {
            get {
                return ResourceManager.GetString("txt1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم عمل الحساب من شاشة الايرادات.
        /// </summary>
        public static string txt10 {
            get {
                return ResourceManager.GetString("txt10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم عمل الحساب من شاشة المصروفات.
        /// </summary>
        public static string txt11 {
            get {
                return ResourceManager.GetString("txt11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حساب المتاجره - مجمل الخساره.
        /// </summary>
        public static string txt2 {
            get {
                return ResourceManager.GetString("txt2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حساب المتاجره - مجمل الربح.
        /// </summary>
        public static string txt3 {
            get {
                return ResourceManager.GetString("txt3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تسوية الحسابات.
        /// </summary>
        public static string txt4 {
            get {
                return ResourceManager.GetString("txt4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حساب المتاجره مدين ب.
        /// </summary>
        public static string txt5 {
            get {
                return ResourceManager.GetString("txt5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حساب المتاجره دائن ب.
        /// </summary>
        public static string txt6 {
            get {
                return ResourceManager.GetString("txt6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الحساب مدين ب.
        /// </summary>
        public static string txt7 {
            get {
                return ResourceManager.GetString("txt7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الحساب دائن ب.
        /// </summary>
        public static string txt8 {
            get {
                return ResourceManager.GetString("txt8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to متضمن الحسابات الفرعية.
        /// </summary>
        public static string txt9 {
            get {
                return ResourceManager.GetString("txt9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حساب.
        /// </summary>
        public static string txtAcc {
            get {
                return ResourceManager.GetString("txtAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اسم الحساب.
        /// </summary>
        public static string txtAccName {
            get {
                return ResourceManager.GetString("txtAccName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  بنك .
        /// </summary>
        public static string txtBank {
            get {
                return ResourceManager.GetString("txtBank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حساب بنك.
        /// </summary>
        public static string txtBankAccount {
            get {
                return ResourceManager.GetString("txtBankAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  كمبيالة .
        /// </summary>
        public static string txtBill {
            get {
                return ResourceManager.GetString("txtBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رقم الكمبيالة.
        /// </summary>
        public static string txtBillNum {
            get {
                return ResourceManager.GetString("txtBillNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ الرد.
        /// </summary>
        public static string txtBounceDate {
            get {
                return ResourceManager.GetString("txtBounceDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ورقة القبض.
        /// </summary>
        public static string txtCashNote {
            get {
                return ResourceManager.GetString("txtCashNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند دفع نقدي.
        /// </summary>
        public static string txtCashPay {
            get {
                return ResourceManager.GetString("txtCashPay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قائمة سندات دفع نقدي.
        /// </summary>
        public static string txtCashPayList {
            get {
                return ResourceManager.GetString("txtCashPayList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سندات الدفع النقدي.
        /// </summary>
        public static string txtCashPayNotes {
            get {
                return ResourceManager.GetString("txtCashPayNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند قبض نقدي.
        /// </summary>
        public static string txtCashReceive {
            get {
                return ResourceManager.GetString("txtCashReceive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قائمة سندات قبض نقدي.
        /// </summary>
        public static string txtCashReceiveList {
            get {
                return ResourceManager.GetString("txtCashReceiveList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سندات القبض النقدي.
        /// </summary>
        public static string txtCashReceiveNotes {
            get {
                return ResourceManager.GetString("txtCashReceiveNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند تحويل نقدي.
        /// </summary>
        public static string txtCashTrnsfr {
            get {
                return ResourceManager.GetString("txtCashTrnsfr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  شيك .
        /// </summary>
        public static string txtCheck {
            get {
                return ResourceManager.GetString("txtCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رقم الشيك.
        /// </summary>
        public static string txtCheckNum {
            get {
                return ResourceManager.GetString("txtCheckNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مركز تكلفة.
        /// </summary>
        public static string txtCostCenter {
            get {
                return ResourceManager.GetString("txtCostCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دائن.
        /// </summary>
        public static string txtCredit {
            get {
                return ResourceManager.GetString("txtCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to العميل.
        /// </summary>
        public static string txtCustomer {
            get {
                return ResourceManager.GetString("txtCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قائمة مخصصة.
        /// </summary>
        public static string txtCustomList {
            get {
                return ResourceManager.GetString("txtCustomList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  اسم المتعامل.
        /// </summary>
        public static string txtDealerNm {
            get {
                return ResourceManager.GetString("txtDealerNm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مدين.
        /// </summary>
        public static string txtDebit {
            get {
                return ResourceManager.GetString("txtDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خصم.
        /// </summary>
        public static string txtDiscount {
            get {
                return ResourceManager.GetString("txtDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  خزينه .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مظهر لشخص اخر.
        /// </summary>
        public static string txtEndorsed {
            get {
                return ResourceManager.GetString("txtEndorsed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ التظهير.
        /// </summary>
        public static string txtEndorseDate {
            get {
                return ResourceManager.GetString("txtEndorseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تسجيل مصروفات.
        /// </summary>
        public static string txtExp {
            get {
                return ResourceManager.GetString("txtExp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند مصروف.
        /// </summary>
        public static string txtExpense {
            get {
                return ResourceManager.GetString("txtExpense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قائمة المصروفات.
        /// </summary>
        public static string txtExpList {
            get {
                return ResourceManager.GetString("txtExpList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مصروف رقم  .
        /// </summary>
        public static string txtExpNum {
            get {
                return ResourceManager.GetString("txtExpNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نوع المصروف.
        /// </summary>
        public static string txtExpType {
            get {
                return ResourceManager.GetString("txtExpType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اضافة نوع مصروف.
        /// </summary>
        public static string txtExpTypeAdd {
            get {
                return ResourceManager.GetString("txtExpTypeAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من تاريخ .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to رد.
        /// </summary>
        public static string txtNoteBounce {
            get {
                return ResourceManager.GetString("txtNoteBounce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  تظهير .
        /// </summary>
        public static string txtNoteEndorse {
            get {
                return ResourceManager.GetString("txtNoteEndorse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مستحق.
        /// </summary>
        public static string txtNoteOutstanding {
            get {
                return ResourceManager.GetString("txtNoteOutstanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  سداد .
        /// </summary>
        public static string txtNotePay {
            get {
                return ResourceManager.GetString("txtNotePay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  رصيد افتتاحي .
        /// </summary>
        public static string txtOpenBalance {
            get {
                return ResourceManager.GetString("txtOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سداد جزئي.
        /// </summary>
        public static string txtPartialPayment {
            get {
                return ResourceManager.GetString("txtPartialPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تاريخ السداد.
        /// </summary>
        public static string txtPayDate {
            get {
                return ResourceManager.GetString("txtPayDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مسدد لحساب بنك.
        /// </summary>
        public static string txtPayedBank {
            get {
                return ResourceManager.GetString("txtPayedBank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مسدد للخزينة.
        /// </summary>
        public static string txtPayedDrwr {
            get {
                return ResourceManager.GetString("txtPayedDrwr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  سند دفع.
        /// </summary>
        public static string txtPayNote {
            get {
                return ResourceManager.GetString("txtPayNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خصم مكتسب.
        /// </summary>
        public static string txtPurDisc {
            get {
                return ResourceManager.GetString("txtPurDisc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  سند قبض.
        /// </summary>
        public static string txtReceiveNote {
            get {
                return ResourceManager.GetString("txtReceiveNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تسجيل ايرادات.
        /// </summary>
        public static string txtRev {
            get {
                return ResourceManager.GetString("txtRev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سند إيراد.
        /// </summary>
        public static string txtRevenue {
            get {
                return ResourceManager.GetString("txtRevenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to قائمة الايرادات.
        /// </summary>
        public static string txtRevList {
            get {
                return ResourceManager.GetString("txtRevList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ايراد رقم .
        /// </summary>
        public static string txtRevNum {
            get {
                return ResourceManager.GetString("txtRevNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نوع الايراد.
        /// </summary>
        public static string txtRevType {
            get {
                return ResourceManager.GetString("txtRevType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اضافة نوع ايراد.
        /// </summary>
        public static string txtRevTypeAdd {
            get {
                return ResourceManager.GetString("txtRevTypeAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خصم مسموح به.
        /// </summary>
        public static string txtSelDisc {
            get {
                return ResourceManager.GetString("txtSelDisc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى تاريخ .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المورد.
        /// </summary>
        public static string txtVendor {
            get {
                return ResourceManager.GetString("txtVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا,لا يمكن عرض الارشيف في اكثر من سنة.
        /// </summary>
        public static string valArchive {
            get {
                return ResourceManager.GetString("valArchive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الكود مسجل من قبل.
        /// </summary>
        public static string valCodeExist {
            get {
                return ResourceManager.GetString("valCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المعامل يجب ان يكون اكبر من صفر.
        /// </summary>
        public static string ValCrncRate {
            get {
                return ResourceManager.GetString("ValCrncRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف هذا الحساب من شاشة دليل الحسابات.
        /// </summary>
        public static string ValDelAccDenied {
            get {
                return ResourceManager.GetString("ValDelAccDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى التأكد من اختيار حساب المخزون البيعي من الإعدادات.
        /// </summary>
        public static string valIntrmdtStorActId {
            get {
                return ResourceManager.GetString("valIntrmdtStorActId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفواً، هذا المصروف مستخدم في تشغيلة اخرى..
        /// </summary>
        public static string valManfExp {
            get {
                return ResourceManager.GetString("valManfExp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن عمل حساب فرعي لحساب مستخدم بالفعل.
        /// </summary>
        public static string ValParentAcc {
            get {
                return ResourceManager.GetString("ValParentAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أوراق الدفع.
        /// </summary>
        public static string ValPayableNotes {
            get {
                return ResourceManager.GetString("ValPayableNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن حذف المصروف فهو مستخدم بفاتورة مشتريات رقم.
        /// </summary>
        public static string valprExp {
            get {
                return ResourceManager.GetString("valprExp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن إضافة المصروف فهو مستخدم بفاتورة مشتريات رقم.
        /// </summary>
        public static string valprExp1 {
            get {
                return ResourceManager.GetString("valprExp1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار حساب الاشعار.
        /// </summary>
        public static string ValSelectDbtCrdtAcc {
            get {
                return ResourceManager.GetString("ValSelectDbtCrdtAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار ورقة القبض.
        /// </summary>
        public static string valSourceNoteError {
            get {
                return ResourceManager.GetString("valSourceNoteError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن إضافة المصروف فهو مستخدم بنقل مخزن رقم.
        /// </summary>
        public static string valstoreExp1 {
            get {
                return ResourceManager.GetString("valstoreExp1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل ورقة واحدة علي الأقل.
        /// </summary>
        public static string Valtxt1 {
            get {
                return ResourceManager.GetString("Valtxt1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ان يتساوي المبلغ الاجمالي مع مجموع الأوراق.
        /// </summary>
        public static string VAltxt2 {
            get {
                return ResourceManager.GetString("VAltxt2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل اسم الايراد بشكل صحيح.
        /// </summary>
        public static string ValTxt3 {
            get {
                return ResourceManager.GetString("ValTxt3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل اسم المصروف بشكل صحيح.
        /// </summary>
        public static string ValTxt4 {
            get {
                return ResourceManager.GetString("ValTxt4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الحساب.
        /// </summary>
        public static string ValtxtAcc {
            get {
                return ResourceManager.GetString("ValtxtAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الحساب موجود مسبقا.
        /// </summary>
        public static string ValTxtAccExist {
            get {
                return ResourceManager.GetString("ValTxtAccExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل المبلغ.
        /// </summary>
        public static string ValtxtAmount {
            get {
                return ResourceManager.GetString("ValtxtAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار البنك.
        /// </summary>
        public static string valtxtbank {
            get {
                return ResourceManager.GetString("valtxtbank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الخزينة المسدد لها الكمبيالة.
        /// </summary>
        public static string ValTxtBillDrwr {
            get {
                return ResourceManager.GetString("ValTxtBillDrwr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تحديد تاريخ الرد.
        /// </summary>
        public static string ValtxtBounce {
            get {
                return ResourceManager.GetString("ValtxtBounce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار حساب السداد للورقة.
        /// </summary>
        public static string ValTxtCheckDrwr {
            get {
                return ResourceManager.GetString("ValTxtCheckDrwr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار حساب فرعي.
        /// </summary>
        public static string valtxtChildAcc {
            get {
                return ResourceManager.GetString("valtxtChildAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل الكود.
        /// </summary>
        public static string valtxtCode {
            get {
                return ResourceManager.GetString("valtxtCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار حافظة ايداع البنك.
        /// </summary>
        public static string ValTxtCollectBankAcc {
            get {
                return ResourceManager.GetString("ValTxtCollectBankAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ الايداع بالبنك.
        /// </summary>
        public static string ValTxtCollectDate {
            get {
                return ResourceManager.GetString("ValTxtCollectDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار مركز التكلفة.
        /// </summary>
        public static string ValTxtCstCntr {
            get {
                return ResourceManager.GetString("ValTxtCstCntr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار قائمة حسابات مخصصة.
        /// </summary>
        public static string ValTxtCstmLst {
            get {
                return ResourceManager.GetString("ValTxtCstmLst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الخزنه.
        /// </summary>
        public static string ValtxtDrwr {
            get {
                return ResourceManager.GetString("ValtxtDrwr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال تاريخ الاستحقاق.
        /// </summary>
        public static string ValtxtDueDate {
            get {
                return ResourceManager.GetString("ValtxtDueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الطرف المظهر له الورقة.
        /// </summary>
        public static string ValTxtEndorse {
            get {
                return ResourceManager.GetString("ValTxtEndorse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال قيمه في حقل مدين أو دائن.
        /// </summary>
        public static string ValtxtJrnl1 {
            get {
                return ResourceManager.GetString("ValtxtJrnl1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  أحد الحقلين لابد ان يكون بصفر.
        /// </summary>
        public static string ValtxtJrnl2 {
            get {
                return ResourceManager.GetString("ValtxtJrnl2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ استحقاق الورقة.
        /// </summary>
        public static string ValTxtMaturity {
            get {
                return ResourceManager.GetString("ValTxtMaturity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن ادخال قيم سالبه.
        /// </summary>
        public static string ValtxtMinus {
            get {
                return ResourceManager.GetString("ValtxtMinus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل الاسم.
        /// </summary>
        public static string ValTxtName {
            get {
                return ResourceManager.GetString("ValTxtName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب ادخال رقم الورقة.
        /// </summary>
        public static string ValtxtNoteNum {
            get {
                return ResourceManager.GetString("ValtxtNoteNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل تاريخ سداد الورقة.
        /// </summary>
        public static string ValTxtPayDate {
            get {
                return ResourceManager.GetString("ValTxtPayDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل المبلغ الاجمالي.
        /// </summary>
        public static string ValtxtTotlAmnt {
            get {
                return ResourceManager.GetString("ValtxtTotlAmnt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايمكن اضافة الحساب تحت حساب مستخدم.
        /// </summary>
        public static string valTxtUsedAccount {
            get {
                return ResourceManager.GetString("valTxtUsedAccount", resourceCulture);
            }
        }
    }
}
