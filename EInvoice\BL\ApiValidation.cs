﻿using EInvoice.Models;
using Models_1.ViewModels.InvoiceVM;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Models_1.ViewModels.ValidationVM;
using Models_1.ViewModels.DatabaseVM;
using AutoMapper;

namespace EInvoice.BL
{
    public static class ApiValidation
    {
        public static int row = 0;
        public static ValidationMessages validationMessages = new ValidationMessages();
        public static ValidateModel Validate(List<Invoice> invoices, ERPEinvoiceContext DB, IMapper _mapper)
        {
            ValidateModel model = new ValidateModel();
            model.UnValidDocumnents = validationMessages;
            validationMessages.Messages = new List<ValidationMessage>();
            if (!invoices.Any())
            {
                var LogMessage = "List of document should have at least one document.";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, "", LogMessage,1, 1);

                var LogMessageAr = "يجب ان يكون هناك مستند واحد علي الاقل";
                List<string> messagesList = new List<string>() { LogMessageAr };

                AddValidationMessage(string.Empty, messagesList);
                model.IsValid = false;
                model.Message = LogMessage;
                return model;
            }

            //model.IsValid = true;
            model.ValidDocumnents = new List<InvoiceData>();
            foreach (var invoice in invoices)
            {
                row = 0;
                var messagesList = new List<string>();
                InvoiceData invoiceData = new InvoiceData();
                invoiceData.InvoiceDetailData = new List<InvoiceDetailData>();
                invoiceData.PurchaseOrderNumber = invoice.PurchaseOrderNumber;
                var result = _mapper.Map<StCompanyInfoVM>(DB.StCompanyInfo.FirstOrDefault());
                invoiceData.CompanyData = result;


                var documentData = ApiValidation.ValidateCode(invoice.documentType, invoice.invoiceCode, invoice.date, DB);
                if (!documentData.IsValid)
                {
                    messagesList.AddRange(documentData.MessagesAr);
                }


                var storeData = ApiValidation.ValidateStore(invoice.storeName, invoice.invoiceCode, DB);
                if (storeData.IsValid)
                {
                    var storeResult = _mapper.Map<IcStoreVM>(storeData.Data);
                    invoiceData.StoreData = storeResult;
                }
                else
                {
                    messagesList.AddRange(storeData.MessagesAr);
                }

                var receiverData = ApiValidation.ValidateCustomer(invoice.customerName, invoice.invoiceCode, DB);
                if (receiverData.IsValid)
                {
                    var receiverResult = _mapper.Map<SlCustomerVM>(receiverData.Data);
                    invoiceData.CustomerData = receiverResult;
                }
                else
                {
                    messagesList.AddRange(receiverData.MessagesAr);
                }

                var DateData = ApiValidation.ValidateDate(invoice.date, invoice.invoiceCode, DB);
                if (DateData.IsValid)
                {
                    invoiceData.InvoiceDate = DateData.Data;
                }
                else
                {
                    messagesList.AddRange(DateData.MessagesAr);
                }

                invoiceData.invoiceId = invoice.invoiceId??0;
                invoiceData.invoiceCode = invoice.invoiceCode;

                var documentTypeData = ApiValidation.ValidateDocumentType(invoice.documentType, invoice.invoiceCode, DB);
                if (documentTypeData.IsValid)
                {
                    invoiceData.DocumentType = invoice.documentType;
                }
                else
                {
                    messagesList.AddRange(documentTypeData.MessagesAr);
                }

                foreach (var detail in invoice.invoiceDetails)
                {
                    row++;
                    InvoiceDetailData invoiceDetail = new InvoiceDetailData();
                    invoiceDetail.IsValid = true;
                    var itemData = ApiValidation.ValidateItem(detail.itemName, invoice.invoiceCode, DB);
                    if (itemData.IsValid)
                    {
                        var itemResult = _mapper.Map<IcItemVM>(itemData.Data);
                        invoiceDetail.ItemData = itemResult;
                    }
                    else
                    {
                        invoiceDetail.IsValid = false;
                        messagesList.AddRange(itemData.MessagesAr);
                    }

                    var currencyData = ApiValidation.ValidateCurrency(detail.currencyName, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                    if (currencyData.IsValid)
                    {
                        var currencyResult = _mapper.Map<StCurrencyVM>(currencyData.Data);
                        invoiceDetail.CurrencyData = currencyResult;
                    }
                    else
                    {
                        invoiceDetail.IsValid = false;
                        messagesList.AddRange(currencyData.MessagesAr);
                    }

                    var uomData = ApiValidation.ValidateUOM(detail.uomName, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                    if (uomData.IsValid)
                    {
                        var uomDataResult = _mapper.Map<IcUomVM>(uomData.Data);
                        invoiceDetail.UomData = uomDataResult;
                    }
                    else
                    {
                        invoiceDetail.IsValid = false;
                        messagesList.AddRange(uomData.MessagesAr);
                    }

                    var quantityData = ApiValidation.ValidateQuantity(detail.quantity, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                    if (quantityData.IsValid)
                    {
                        invoiceDetail.Quantity = quantityData.Data;
                    }
                    else
                    {
                        invoiceDetail.IsValid = false;
                        messagesList.AddRange(quantityData.MessagesAr);
                    }

                    //var totalAmountData = ApiValidation.ValidateTotalAmount(detail.totalAmount, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                    //if (!totalAmountData.IsValid)
                    //{
                    //    invoiceDetail.IsValid = false;
                    //    messagesList.AddRange(totalAmountData.MessagesAr);
                    //}
                    //var descriptionData = Validation.ValidateItemDescription(detail.description);
                    //if (descriptionData.IsValid)
                    //{
                    //    invoiceDetail.Description = descriptionData.Data;
                    //}
                    //else
                    //{
                    //    model.IsValid = false;
                    //    model.Message = descriptionData.Message;
                    //    break;
                    //}

                    var taxList = new List<Tax>();
                    if (detail.taxes != null)
                    {
                        foreach (var tax in detail.taxes)
                        {
                            var taxData = ValidateTax(tax, invoice.invoiceCode, itemData.Data.ItemNameAr, DB);
                            if (taxData.IsValid)
                            {
                                var taxmodel = taxData.Data;
                                taxList.Add(taxmodel);
                            }
                            else
                            {
                                invoiceDetail.IsValid = false;
                                messagesList.AddRange(taxData.MessagesAr);
                            }
                        }
                    }

                    if (invoice.documentType.ToUpper() == "D" || invoice.documentType.ToUpper() == "C")
                    {
                        if (invoice.Uuid != null)
                        {
                            var ReferenceInvioces = RefernceInvoices(invoice.Uuid);

                            var ValidationData = ApiValidation.ValidateItemForDebitCredit(ReferenceInvioces, detail.itemId);
                            if (!ValidationData.IsValid)
                            {
                                model.IsValid = false;
                                messagesList.AddRange(ValidationData.MessagesAr);
                            }

                        }


                    }

                    invoiceDetail.Taxes = taxList;
                    invoiceDetail.Amount = detail.amount;
                    invoiceDetail.ExchangeRate = detail.exchangeRate;
                    invoiceDetail.Discount = detail.discount;
                    invoiceDetail.ItemsDiscount = detail.discountAfterTax;
                    invoiceDetail.Discounts = detail.discounts;
                    invoiceData.InvoiceDetailData.Add(invoiceDetail);
                }

                if (invoice.documentType.ToUpper() == "D" || invoice.documentType.ToUpper() == "C")
                {
                    if (invoice.Uuid != null)
                    {
                        var ReferenceInvioces = RefernceInvoices(invoice.Uuid);


                        var ValidateCustomer = ApiValidation.ValidateCustomerForDebitCredite(ReferenceInvioces);

                        if (!ValidateCustomer.IsValid)
                        {
                            model.IsValid = false;
                            messagesList.AddRange(ValidateCustomer.MessagesAr);
                        }

                        if (invoice.documentType.ToUpper() == "C")
                        {
                            var ValidateAmount = ApiValidation.ValidateAmountForInvoiceReference(invoice.invoiceDetails, ReferenceInvioces);
                            if (!ValidateAmount.IsValid)
                            {
                                model.IsValid = false;
                                messagesList.AddRange(ValidateCustomer.MessagesAr);
                            }
                        }
                    }

                    invoiceData.Uuid = invoice.Uuid;

                }

                invoiceData.TotalDiscount = invoice.TotalDiscount;
                invoiceData.TotalTax = invoice.TotalTax;
                invoiceData.Net = invoice.Net;
                if (invoiceData.InvoiceDetailData.Any() && invoiceData.InvoiceDetailData.All(x => x.IsValid == true && !messagesList.Any()))
                {
                    model.ValidDocumnents.Add(invoiceData);
                }

                if (messagesList.Any())
                {
                    AddValidationMessage(invoice.invoiceCode, messagesList);
                }
            }

            model.IsValid = model.ValidDocumnents.Any() ? true : false;
            return model;
        }
        public static Validation<StCompanyInfo> ValidateCompany(int companyId, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<StCompanyInfo> model = new Validation<StCompanyInfo>();
            model.MessagesAr = new List<string>();
            var companyData = DB.StCompanyInfo.FirstOrDefault(x => x.CompanyId == companyId);
            if (companyData == null)
            {
                var LogMessage = $"CompanyId {companyId} Not Valid In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage,1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"بيانات الشركة غير صحيحة";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(companyData.ClientId))
                {
                    var LogMessage = $"Company Client Id Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"1 بيانات الشركة غير موجودة";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(companyData.ClientSecret))
                {
                    var LogMessage = $"Company Client Secret Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"2 بيانات الشركة غير موجودة";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(companyData.DonglePin))
                {
                    var LogMessage = $"Company Dongle Pin Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"بيانات التوكن غير موجودة";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(companyData.CmpNameAr))
                {
                    var LogMessage = $"Company Name Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"اسم الشركة غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(companyData.TaxCard))
                {
                    var LogMessage = $"Company Tax Registration Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"رقم التسجيل للشركة غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }
                else
                {
                    if (companyData.TaxCard.Count() != 9)
                    {
                        var LogMessage = $"Company Tax Registration Should be 9 numbers";
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                        var LogMessageAr = $"رقم التسجيل للشركة يجب ان يكون 9 ارقام";
                        model.MessagesAr.Add(LogMessageAr);
                    }
                }

              //  if (string.IsNullOrEmpty(companyData.CommercialBook))
                    //        {
                    //            var LogMessage = $"Company Tax Registration Is Null Or Empty";
                    //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    //            return model;
                    //        }

                    if (string.IsNullOrEmpty(companyData.ActivityType))
                {
                    var LogMessage = $"Company Activity Type Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود نشاط الشركة غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                model.Data = companyData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<IcStore> ValidateStore(string StoreName, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<IcStore> model = new Validation<IcStore>();
            model.MessagesAr = new List<string>();
            var storeData = DB.IcStore.Include(x => x.Country).FirstOrDefault(x => x.StoreNameAr == StoreName.TrimEnd().TrimStart() /*|| x.StoreNameEn == StoreName.TrimEnd().TrimStart()*/);
            if (storeData == null)
            {
                var LogMessage = $"Store {StoreName} Not Valid In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"بيانات الفرع غير صحيحة";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(storeData.Ecode))
                {
                    var LogMessage = $"Branch Id Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود الفرع غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.Country.Ecode))
                {
                    var LogMessage = $"Company Country Ecode Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (الدولة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.Governate))
                {
                    var LogMessage = $"Company Governate Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (المحافظة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.Street))
                {
                    var LogMessage = $"Company Street Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (الشارع) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.RegionCity))
                {
                    var LogMessage = $"Company Region City Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (المدينة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(storeData.BuildingNumber))
                {
                    var LogMessage = $"Company Building Number Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان الشركة (رقم العمارة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = storeData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<SlCustomer> ValidateCustomer(string customerName, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<SlCustomer> model = new Validation<SlCustomer>();
            model.MessagesAr = new List<string>();
            var customerData = DB.SlCustomer.Include(x => x.Country).FirstOrDefault(x => x.CusNameAr == customerName.TrimEnd().TrimStart() /*|| x.CusNameEn == customerName.TrimEnd().TrimStart()*/);
            if (customerData == null)
            {
                var LogMessage = $"Customer {customerName} Not Valid In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"بيانات المشتري غير صحيحة";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(customerData.CusNameAr))
                {
                    var LogMessage = $"Customer Name Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"اسم العميل غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (customerData.CsType == (int)MyHelper.Utilities.Type.B)
                {
                    if (string.IsNullOrEmpty(customerData.TaxCardNumber))
                    {
                        var LogMessage = $"Customer Tax Card Number Is Null Or Empty";
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                        var LogMessageAr = $"رقم التسجيل للعميل غير موجود";
                        model.MessagesAr.Add(LogMessageAr);
                    }
                    else
                    {
                        if (customerData.TaxCardNumber.Count() != 9)
                        {
                            var LogMessage = $"Customer Tax Card Number Should be 9 numbers";
                            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                            var LogMessageAr = $"رقم التسجيل للعميل يجب ان يكون 9 ارقام";
                            model.MessagesAr.Add(LogMessageAr);
                        }
                    }
                }
                if (customerData.CsType == (int)MyHelper.Utilities.Type.F)
                {
                    if (string.IsNullOrEmpty(customerData.IdNumber))
                    {
                        var LogMessage = $"Customer Id Number Is Null Or Empty";
                        MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                        var LogMessageAr = $"الرقم المدني للعميل غير موجود";
                        model.MessagesAr.Add(LogMessageAr);
                    }
                }
                if (string.IsNullOrEmpty(customerData.Country.Ecode))
                {
                    var LogMessage = $"Customer Country Ecode Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان العميل (الدولة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(customerData.Governate))
                {
                    var LogMessage = $"Customer Governate Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان العميل (المحافظة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(customerData.Street))
                {
                    var LogMessage = $"Customer Street Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان العميل (الشارع) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(customerData.City))
                {
                    var LogMessage = $"Customer Region City Is Null Or Empty";
                    var LogMessageAr = $"عنوان العميل (المدينة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }

                if (string.IsNullOrEmpty(customerData.BuildingNumber))
                {
                    var LogMessage = $"Customer Building Number Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"عنوان العميل (رقم العمارة) غير موجود";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = customerData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<DateTime> ValidateDate(DateTime invoiceDate, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<DateTime> model = new Validation<DateTime>();
            model.MessagesAr = new List<string>();
            var InvoiceValidationDays = DB.StCompanyInfo.FirstOrDefault().InvoiceDateValidationDays;

            if (invoiceDate.ToUniversalTime() > DateTime.UtcNow)
            {
                var LogMessage = $"Date and Time {invoiceDate} cannot be in future. In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"تاريخ المستند لا يجب ان يكون اكبر من تاريخ اليوم";
                model.MessagesAr.Add(LogMessageAr);
            }
            else if (invoiceDate.Date < DateTime.Now.AddDays(InvoiceValidationDays.GetValueOrDefault() * -1))
            {
                var LogMessage = $"Date should not exceed {InvoiceValidationDays} days In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"تاريخ المستند لا يجب ان يتجاوزايام{InvoiceValidationDays}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                model.IsValid = true;
                var date = invoiceDate.ToUniversalTime();
                date = date.Date + new TimeSpan(date.TimeOfDay.Hours, date.TimeOfDay.Minutes, date.TimeOfDay.Seconds);
                model.Data = date;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<string> ValidateDocumentType(string documentType, string invoiceCode, ERPEinvoiceContext DB)
        {
            Validation<string> model = new Validation<string>();
            model.MessagesAr = new List<string>();
            if (documentType.ToUpper() != "I" && documentType.ToUpper() != "D" && documentType.ToUpper() != "C")
            {
                var LogMessage = $"Document tType {documentType} Not Valid In Document : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"نوع المستند غير صحيح";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                model.Data = documentType;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<IcItem> ValidateItem(string ItemName, string invoiceCode, ERPEinvoiceContext DB)
        {

            Validation<IcItem> model = new Validation<IcItem>();
            model.MessagesAr = new List<string>();
            var itemData = DB.IcItem.FirstOrDefault(x => x.ItemNameAr == ItemName.TrimEnd().TrimStart() /*|| x.ItemNameEn == ItemName.TrimEnd().TrimStart()*/);
            if (itemData == null)
            {
                var LogMessage = $"Item {ItemName} Not Valid In Document : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"الصنف رقم {row} غير صحيح";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(itemData.ItemNameAr))
                {
                    var LogMessage = $"Item Name Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"اسم الصنف رقم {row} غير صحيح";
                    model.MessagesAr.Add(LogMessageAr);
                }
                if (string.IsNullOrEmpty(itemData.ItemEtype))
                {
                    var LogMessage = $"Item Type Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"نوع كود الصنف رقم {row} غير صحيح";
                    model.MessagesAr.Add(LogMessageAr);
                }
                if (string.IsNullOrEmpty(itemData.ItemEcode))
                {
                    var LogMessage = $"Item Code Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود الصنف رقم {row} غير صحيح";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = itemData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }
        public static List<SlInvoice> RefernceInvoices(List<string> Uuid)
        {
            using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
            {
                var model = (from slInvoice in DB.SlInvoice
                             join uuid in Uuid on slInvoice.Uuid equals uuid
                             select slInvoice).ToList();


                return model;

            }

        }


        public static Validation<SlInvoiceDetail> ValidateAmountForInvoiceReference(List<InvoiceDetail> InvoiceDetails, List<SlInvoice> InvoiceReferences)
        {
            using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
            {

                Validation<SlInvoiceDetail> model = new Validation<SlInvoiceDetail>();

                var SlInvoiceDetails = (from d in DB.SlInvoiceDetail
                                        join slinvoice in InvoiceReferences on d.SlInvoiceId equals slinvoice.SlInvoiceId
                                        select d).ToList();
                var ReferenceAmount = SlInvoiceDetails.Sum(c => c.TotalSellPrice);
                var Amount = InvoiceDetails.Sum(c => c.amount * c.quantity);

                if (Amount > ReferenceAmount)
                {
                    model.Message = $"Total Amount Larger Than Refernce Amount";
                }
                else
                {
                    model.IsValid = true;

                }
                return model;
            }
        }


        public static Validation<SlInvoiceDetail> ValidateItemForDebitCredit(List<SlInvoice> SlInvoice, int ItemId)
        {
            using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
            {
                var SlInvoiceDetails = (from d in DB.SlInvoiceDetail
                                        join slinvoice in SlInvoice on d.SlInvoiceId equals slinvoice.SlInvoiceId
                                        select d).ToList();
                Validation<SlInvoiceDetail> model = new Validation<SlInvoiceDetail>();
                var itemData = SlInvoiceDetails.FirstOrDefault(x => x.ItemId == ItemId);
                if (itemData == null)
                {
                    model.Message = $"Item {ItemId} Not Exist in the original Invoice";
                }
                else
                {
                    model.IsValid = true;

                }
                return model;
            }
        }



        public static Validation<SlInvoice> ValidateCustomerForDebitCredite(List<SlInvoice> invoices)
        {
            using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
            {
                Validation<SlInvoice> model = new Validation<SlInvoice>();
                var Data = (from invoice in invoices
                            group invoices by invoice.CustomerId
                           into ii
                            select ii.Key).ToList();
                if (Data.Count() > 1)
                {
                    model.Message = $"Reference Invoices Donot Have The Same Client";
                }
                else
                {
                    model.IsValid = true;

                }
                return model;
            }
        }





        public static Validation<StCurrency> ValidateCurrency(string CrncName, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {
            Validation<StCurrency> model = new Validation<StCurrency>();
            model.MessagesAr = new List<string>();
            var currencyData = DB.StCurrency.FirstOrDefault(x => x.CrncName == CrncName.TrimEnd().TrimStart() );
            if (currencyData == null)
            {
                var LogMessage = $"Currency {CrncName} Not Valid In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"العملة غير صحيحة في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(currencyData.Ecode))
                {
                    var LogMessage = $"Currency Ecode Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود العملة غير صحيح في الصنف {itemName} رقم {row}";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = currencyData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<IcUom> ValidateUOM(string uomName, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {

            Validation<IcUom> model = new Validation<IcUom>();
            model.MessagesAr = new List<string>();
            var uomData = DB.IcUom.FirstOrDefault(x => x.Uom == uomName.TrimEnd().TrimStart());
            if (uomData == null)
            {
                var LogMessage = $"uom {uomName} Not Valid In In Item {itemName} Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"كود وحدةالقياس غير صحيحة في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(uomData.Ecode))
                {
                    var LogMessage = $"UOM Ecode Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود وحدة القياس غير صحيح في الصنف {itemName} رقم {row}";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.Data = uomData;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<decimal> ValidateQuantity(decimal quantity, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {
            Validation<decimal> model = new Validation<decimal>();
            model.MessagesAr = new List<string>();
            if (quantity <= 0)
            {
                var LogMessage = $"Quantity is {quantity}  should be larger than 0 In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage,1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"الكمية يجب ان تكون اكبر من صفر في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                model.Data = quantity;
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<decimal> ValidateTotalAmount(decimal totalAmount, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {
            Validation<decimal> model = new Validation<decimal>();
            model.MessagesAr = new List<string>();
            if (totalAmount < 0)
            {
                var LogMessage = $"Total Amount is {totalAmount} should be larger than 0 In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage,1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"الاجمالي يجب ان يكون اكبر من صفر في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static Validation<Tax> ValidateTax(Tax tax, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        {

            Validation<Tax> model = new Validation<Tax>();
            model.MessagesAr = new List<string>();
            var subTaxData = DB.ETaxableType.FirstOrDefault(x => x.Code == tax.subTaxName.TrimStart().TrimEnd());
            if (subTaxData == null)
            {
                var LogMessage = $"SubTaxId {tax.subTaxId} Not Valid In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"نوع الضريبة الفرعية غير صحيح في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else if (subTaxData.ParentTaxId == null)
            {
                var LogMessage = $"{tax.subTaxId} is Not SubTaxId In Item {itemName} In Invoice : {invoiceCode}";
                EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage,1, 1);
                model.Message = LogMessage;
                var LogMessageAr = $"نوع الضريبة غير صحيح في الصنف {itemName} رقم {row}";
                model.MessagesAr.Add(LogMessageAr);
            }
            else
            {
                if (string.IsNullOrEmpty(subTaxData.Code))
                {
                    var LogMessage = $"Sub Tax Code Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود الضريبة الفرعية غير صحيح في الصنف {itemName} رقم {row}";
                    model.MessagesAr.Add(LogMessageAr);
                }
                var taxData = DB.ETaxableType.FirstOrDefault(x => x.ETaxableTypeId == subTaxData.ParentTaxId);
                if (string.IsNullOrEmpty(taxData.Code))
                {
                    var LogMessage = $"Tax Code Is Null Or Empty";
                    MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
                    var LogMessageAr = $"كود الضريبة غير صحيح في الصنف {itemName} رقم {row}";
                    model.MessagesAr.Add(LogMessageAr);
                }
                model.IsValid = true;
                model.Data = new Tax()
                {
                    taxId = subTaxData.ETaxableTypeId,
                    taxName = taxData.Code,
                    subTaxId = tax.subTaxId,
                    subTaxName = subTaxData.Code,
                    amount = tax.amount,
                    type = tax.type,
                    rate = tax.rate
                };
            }
            model.IsValid = model.MessagesAr.Any() ? false : true;
            return model;
        }

        public static void AddValidationMessage(string documnetCode, List<string> messages)
        {
            var validationMessage = new ValidationMessage() { invoiceCode = documnetCode, Message = messages, Islocal = true };
            validationMessages.Messages.Add(validationMessage);
        }

        public static ValidationMessages ValidateTotalDocument(List<Models_1.ViewModels.Document> docs, ValidationMessages validationMessage, ERPEinvoiceContext DB)
        {
            var pDocs = docs.Where(x => x.receiver.type == Enum.GetName(typeof(MyHelper.Utilities.Type), MyHelper.Utilities.Type.P)).ToList();
            var documentThreshold = DB.StStore.FirstOrDefault()?.DocumentThreshold ?? 50000;
            List<string> messages = new List<string>();
            messages.Add("اجمالي الفاتورة تخطي الحد الاقصي , يجب ادخال الرقم القومي للعميل");
            foreach (var doc in pDocs)
            {
                if (doc.totalAmount >= documentThreshold && string.IsNullOrEmpty(doc.receiver.id))
                {
                    validationMessage.Messages.Add(new ValidationMessage() { Islocal = true, invoiceCode = doc.internalID, Message = messages });
                }
            }
            return validationMessage;
        }

        //public static Validation<StCompanyInfo> ValidateCompany(int companyId, string invoiceCode, ERPEinvoiceContext DB)
        //{

        //    Validation<StCompanyInfo> model = new Validation<StCompanyInfo>();
        //    var companyData = DB.StCompanyInfo.FirstOrDefault(x => x.CompanyId == companyId);
        //    if (companyData == null)
        //    {
        //        var LogMessage = $"CompanyId {companyId} Not Valid In Document : {invoiceCode}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        if (string.IsNullOrEmpty(companyData.ClientId))
        //        {
        //            var LogMessage = $"Company Client Id Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(companyData.ClientSecret))
        //        {
        //            var LogMessage = $"Company Client Secret Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(companyData.DonglePin))
        //        {
        //            var LogMessage = $"Company Dongle Pin Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(companyData.CmpNameAr))
        //        {
        //            var LogMessage = $"Company Name Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(companyData.CommercialBook))
        //        {
        //            var LogMessage = $"Company Tax Registration Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        model.IsValid = true;
        //        model.Data = companyData;
        //    }
        //    return model;

        //}

        //public static Validation<IcStore> ValidateStore(string storeName, string invoiceCode, ERPEinvoiceContext DB)
        //{
        //    Validation<IcStore> model = new Validation<IcStore>();
        //    var storeData = DB.IcStore.Include(x => x.Country).FirstOrDefault(x => x.StoreNameAr == storeName.TrimEnd().TrimStart() || x.StoreNameEn == storeName.TrimEnd().TrimStart());
        //    if (storeData == null)
        //    {
        //        var LogMessage = $"Store {storeName} Not Valid In Document : {invoiceCode}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        if (string.IsNullOrEmpty(storeData.Ecode))
        //        {
        //            var LogMessage = $"Branch Id Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(storeData.Country.Ecode))
        //        {
        //            var LogMessage = $"Company Country Ecode Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(storeData.Governate))
        //        {
        //            var LogMessage = $"Company Governate Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(storeData.Street))
        //        {
        //            var LogMessage = $"Company Street Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(storeData.RegionCity))
        //        {
        //            var LogMessage = $"Company Region City Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(storeData.BuildingNumber))
        //        {
        //            var LogMessage = $"Company Building Number Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        model.IsValid = true;
        //        model.Data = storeData;
        //    }
        //    return model;
        //}

        //public static Validation<SlCustomer> ValidateCustomer(string customerName, string invoiceCode, ERPEinvoiceContext DB)
        //{
        //    Validation<SlCustomer> model = new Validation<SlCustomer>();
        //    var customerData = DB.SlCustomer.Include(x => x.Country).FirstOrDefault(x => x.CusNameAr == customerName.TrimEnd().TrimStart() || x.CusNameEn == customerName.TrimEnd().TrimStart());
        //    if (customerData == null)
        //    {
        //        var LogMessage = $"Customer {customerName} Not Valid In Document : {invoiceCode}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        if (string.IsNullOrEmpty(customerData.CusNameAr))
        //        {
        //            var LogMessage = $"Customer Name Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (customerData.CsType == (int)MyHelper.Utilities.Type.B)
        //        {
        //            if (string.IsNullOrEmpty(customerData.TaxCardNumber))
        //            {
        //                var LogMessage = $"Customer Tax Card Number Is Null Or Empty";
        //                MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //                return model;
        //            }
        //        }
        //        if (string.IsNullOrEmpty(customerData.Country.Ecode))
        //        {
        //            var LogMessage = $"Customer Country Ecode Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(customerData.Governate))
        //        {
        //            var LogMessage = $"Customer Governate Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(customerData.Street))
        //        {
        //            var LogMessage = $"Customer Street Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(customerData.City))
        //        {
        //            var LogMessage = $"Customer Region City Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        if (string.IsNullOrEmpty(customerData.BuildingNumber))
        //        {
        //            var LogMessage = $"Customer Building Number Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        model.IsValid = true;
        //        model.Data = customerData;
        //    }
        //    return model;
        //}

        //public static Validation<DateTime> ValidateDate(DateTime invoiceDate, string invoiceCode, ERPEinvoiceContext DB)
        //{
        //    Validation<DateTime> model = new Validation<DateTime>();
        //    if (invoiceDate.ToUniversalTime() > DateTime.UtcNow)
        //    {
        //        var LogMessage = $"Date and Time {invoiceDate} cannot be in future. In Document : {invoiceCode}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        model.IsValid = true;
        //        var date = invoiceDate.ToUniversalTime();
        //        date = date.Date + new TimeSpan(date.TimeOfDay.Hours, date.TimeOfDay.Minutes, date.TimeOfDay.Seconds);
        //        model.Data = date;
        //    }
        //    return model;
        //}

        public static Validation<bool> ValidateCode(string documentType, string invoiceCode, DateTime invoiceDate, ERPEinvoiceContext DB)
        {
            Validation<bool> model = new Validation<bool>();
            model.MessagesAr = new List<string>();
            if (documentType.ToUpper() == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.I))
            {
                var invoice = DB.SlInvoice.FirstOrDefault(x => x.InvoiceCode == invoiceCode && x.InvoiceDate.Date == invoiceDate.Date);
                if (invoice != null)
                {
                    var LogMessage = $"There is already Invoice with Same Code : {invoiceCode}";
                    EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                    model.Message = LogMessage;
                    
                    var LogMessageAr = $"هذه الفاتورة تم ادخالها بالفعل  : {invoiceCode}";
                    model.MessagesAr.Add(LogMessageAr);
                }
                else
                {
                    model.IsValid = true;
                }
            }
            if (documentType.ToUpper() == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.C))
            {                 
                var creditNote = DB.SlReturn.FirstOrDefault(x => x.ReturnCode == invoiceCode && x.ReturnDate.Date == invoiceDate.Date);
                if (creditNote != null)
                {
                    var LogMessage = $"There is already Credit Note with Same Code : {invoiceCode}";
                    EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                    model.Message = LogMessage;
                }
                else
                {
                    model.IsValid = true;
                }
            }
            if (documentType.ToUpper() == Enum.GetName(typeof(Models_1.ViewModels.DocumentType), Models_1.ViewModels.DocumentType.D))
            {
                var debitNote = DB.SlAdd.FirstOrDefault(x => x.ReturnCode == invoiceCode && x.ReturnDate.Date == invoiceDate.Date);
                if (debitNote != null)
                {
                    var LogMessage = $"There is already Debit Note with Same Code : {invoiceCode}";
                    EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
                    model.Message = LogMessage;
                }
                else
                {
                    model.IsValid = true;
                }
            }
            return model;
        }

        //public static Validation<string> ValidateDocumentType(string documentType, string invoiceCode, ERPEinvoiceContext DB)
        //{
        //    Validation<string> model = new Validation<string>();
        //    if (documentType.ToUpper() != "I" && documentType.ToUpper() != "D" && documentType.ToUpper() != "C")
        //    {
        //        var LogMessage = $"Document tType {documentType} Not Valid In Document : {invoiceCode}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        model.IsValid = true;
        //        model.Data = documentType;
        //    }
        //    return model;
        //}

        //public static Validation<IcItem> ValidateItem(string itemName, string invoiceCode, ERPEinvoiceContext DB)
        //{

        //    Validation<IcItem> model = new Validation<IcItem>();
        //    var itemData = DB.IcItem.FirstOrDefault(x => x.ItemNameAr == itemName.TrimEnd().TrimStart() || x.ItemNameEn == itemName.TrimEnd().TrimStart());
        //    if (itemData == null)
        //    {
        //        var LogMessage = $"Item {itemName} Not Valid In Document : {invoiceCode} in Row : {row}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        if (string.IsNullOrEmpty(itemData.ItemNameAr))
        //        {
        //            var LogMessage = $"Item Name Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }
        //        if (string.IsNullOrEmpty(itemData.ItemEtype))
        //        {
        //            var LogMessage = $"Item Type Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }
        //        if (string.IsNullOrEmpty(itemData.ItemEcode))
        //        {
        //            var LogMessage = $"Item Code Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        model.IsValid = true;
        //        model.Data = itemData;
        //    }
        //    return model;
        //}


        //public static List<SlInvoice> RefernceInvoices(List<string> Uuid)
        //{
        //    using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
        //    {
        //        var model = (from slInvoice in DB.SlInvoice
        //                     join uuid in Uuid on slInvoice.Uuid equals uuid
        //                     select slInvoice).ToList();


        //        return model;

        //    }

        //}


        //public static Validation<SlInvoiceDetail> ValidateAmountForInvoiceReference(List<InvoiceDetail> InvoiceDetails, List<SlInvoice> InvoiceReferences)
        //{
        //    using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
        //    {

        //        Validation<SlInvoiceDetail> model = new Validation<SlInvoiceDetail>();

        //        var SlInvoiceDetails = (from d in DB.SlInvoiceDetail
        //                                join slinvoice in InvoiceReferences on d.SlInvoiceId equals slinvoice.SlInvoiceId
        //                                select d).ToList();
        //        var ReferenceAmount = SlInvoiceDetails.Sum(c => c.TotalSellPrice);
        //        var Amount = InvoiceDetails.Sum(c => c.amount * c.quantity);

        //        if (Amount > ReferenceAmount)
        //        {
        //            model.Message = $"Total Amount Larger Than Refernce Amount";
        //        }
        //        else
        //        {
        //            model.IsValid = true;

        //        }
        //        return model;
        //    }
        //}


        //public static Validation<SlInvoiceDetail> ValidateItemForDebitCredit(List<SlInvoice> SlInvoice, int ItemId)
        //{
        //    using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
        //    {
        //        var SlInvoiceDetails = (from d in DB.SlInvoiceDetail
        //                                join slinvoice in SlInvoice on d.SlInvoiceId equals slinvoice.SlInvoiceId
        //                                select d).ToList();
        //        Validation<SlInvoiceDetail> model = new Validation<SlInvoiceDetail>();
        //        var itemData = SlInvoiceDetails.FirstOrDefault(x => x.ItemId == ItemId);
        //        if (itemData == null)
        //        {
        //            model.Message = $"Item {ItemId} Not Exist in the original Invoice";
        //        }
        //        else
        //        {
        //            model.IsValid = true;

        //        }
        //        return model;
        //    }
        //}



        //public static Validation<SlInvoice> ValidateCustomerForDebitCredite(List<SlInvoice> invoices)
        //{
        //    using (ERPEinvoiceContext DB = new ERPEinvoiceContext())
        //    {
        //        Validation<SlInvoice> model = new Validation<SlInvoice>();
        //        var Data = (from invoice in invoices
        //                    group invoices by invoice.CustomerId
        //                   into ii
        //                    select ii.Key).ToList();
        //        if (Data.Count() > 1)
        //        {
        //            model.Message = $"Reference Invoices Donot Have The Same Client";
        //        }
        //        else
        //        {
        //            model.IsValid = true;

        //        }
        //        return model;
        //    }
        //}





        //public static Validation<StCurrency> ValidateCurrency(string currencyName, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        //{
        //    Validation<StCurrency> model = new Validation<StCurrency>();
        //    var currencyData = DB.StCurrency.FirstOrDefault(x => x.CrncName == currencyName.TrimStart().TrimEnd());
        //    if (currencyData == null)
        //    {
        //        var LogMessage = $"Currency {currencyName} Not Valid In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        if (string.IsNullOrEmpty(currencyData.Ecode))
        //        {
        //            var LogMessage = $"Currency Ecode Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        model.IsValid = true;
        //        model.Data = currencyData;
        //    }
        //    return model;
        //}

        //public static Validation<IcUom> ValidateUOM(string uomName, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        //{

        //    Validation<IcUom> model = new Validation<IcUom>();
        //    var uomData = DB.IcUom.FirstOrDefault(x => x.Uom == uomName.TrimEnd().TrimStart());
        //    if (uomData == null)
        //    {
        //        var LogMessage = $"Uom {uomName} Not Valid In In Item {itemName} Invoice : {invoiceCode} in Row : {row}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        if (string.IsNullOrEmpty(uomData.Ecode))
        //        {
        //            var LogMessage = $"UOM Ecode Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }

        //        model.IsValid = true;
        //        model.Data = uomData;
        //    }
        //    return model;

        //}

        //public static Validation<decimal> ValidateQuantity(decimal quantity, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        //{
        //    Validation<decimal> model = new Validation<decimal>();
        //    if (quantity <= 0)
        //    {
        //        var LogMessage = $"Quantity is {quantity}  should be larger than 0 In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        model.IsValid = true;
        //        model.Data = quantity;
        //    }
        //    return model;
        //}

        ////public static Validation<string> ValidateItemDescription(string description)
        ////{
        ////    Validation<string> model = new Validation<string>();
        //    if (string.IsNullOrEmpty(description))
        //    {
        //        model.Message = $"Item Dscription is Empty";
        //    }
        //    else
        //    {
        //        model.IsValid = true;
        //        model.Data = description;
        //    }
        //    return model;
        //}

        //public static Validation<Tax> ValidateTax(Tax tax, string invoiceCode, string itemName, ERPEinvoiceContext DB)
        //{

        //    Validation<Tax> model = new Validation<Tax>();
        //    var subTaxData = DB.ETaxableType.FirstOrDefault(x => x.Code == tax.subTaxName.TrimStart().TrimEnd());
        //    if (subTaxData == null)
        //    {
        //        var LogMessage = $"SubTaxId {tax.subTaxId} Not Valid In Item {itemName} In Invoice : {invoiceCode} in Row : {row}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else if (subTaxData.ParentTaxId == null)
        //    {
        //        var LogMessage = $"{tax.subTaxId} is Not SubTaxId In Item {itemName} In Invoice : {invoiceCode}";
        //        EInvoice.MyHelper.Utilities.UpdateST_UserLog(DB, invoiceCode, LogMessage, 1, 1);
        //        model.Message = LogMessage;
        //    }
        //    else
        //    {
        //        if (string.IsNullOrEmpty(subTaxData.Code))
        //        {
        //            var LogMessage = $"Sub Tax Code Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }
        //        var taxData = DB.ETaxableType.FirstOrDefault(x => x.ETaxableTypeId == subTaxData.ParentTaxId);
        //        if (string.IsNullOrEmpty(taxData.Code))
        //        {
        //            var LogMessage = $"Tax Code Is Null Or Empty";
        //            MyHelper.Utilities.UpdateST_UserLog(DB, "1", $"Document With Code {invoiceCode} Not Valid : {LogMessage}", 1, 1);
        //            return model;
        //        }
        //        model.IsValid = true;
        //        model.Data = new Tax()
        //        {
        //            taxId = subTaxData.ETaxableTypeId,
        //            taxName = taxData.Code,
        //            subTaxId = tax.subTaxId,
        //            subTaxName = subTaxData.Code,
        //            amount = tax.amount,
        //            type = tax.type,
        //            rate = tax.rate
        //        };
        //    }
        //    return model;

        //}
    }
}
