﻿using System;
using System.Collections.Generic;

namespace EInvoice.Models
{
    public partial class HrCountry
    {
        public HrCountry()
        {
            IcStore = new HashSet<IcStore>();
            SlCustomer = new HashSet<SlCustomer>();
        }

        public int CountryId { get; set; }
        public string CountryName { get; set; }
        public string Ecode { get; set; }
        public string CountryNameEn { get; set; }

        public virtual ICollection<IcStore> IcStore { get; set; }
        public virtual ICollection<SlCustomer> SlCustomer { get; set; }
    }
}
