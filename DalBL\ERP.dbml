﻿<?xml version="1.0" encoding="utf-8"?><Database Name="Nice_Food" Class="ERPDataContext" xmlns="http://schemas.microsoft.com/linqtosql/dbml/2007">
  <Table Name="dbo.ACC_Account" Member="ACC_Accounts">
    <Type Name="ACC_Account">
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="AcNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="AcNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ParentActId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AcType" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="AllowEdit" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="AllowChild" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CostCenter" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="OldName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="AcNumber" Type="System.String" DbType="VarChar(55) NOT NULL" CanBeNull="false" />
      <Column Name="[Level]" Member="Level" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Budget" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AccSecurityLevel" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_Bank" Member="ACC_Banks">
    <Type Name="ACC_Bank">
      <Column Name="BankId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BankAccountNumber" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="BankName" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="BranchName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Address" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Tel" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertUser" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_CashExpenses" Member="ACC_CashExpenses">
    <Type Name="ACC_CashExpense">
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Column Name="Percentage" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_CashNote" Member="ACC_CashNotes">
    <Type Name="ACC_CashNote">
      <Column Name="NoteId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NoteSerial" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IsPay" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsVendor" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="DealerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DealerAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NoteDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NoteCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Registered" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="BookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CollectEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsMultiple" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_CashNoteDetail" Member="ACC_CashNoteDetails">
    <Type Name="ACC_CashNoteDetail">
      <Column Name="CashDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CashNoteId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(18,0) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_CashTransfer" Member="ACC_CashTransfers">
    <Type Name="ACC_CashTransfer">
      <Column Name="CashTransferId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="TransferSerial" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FromAccIsDrawer" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="FromAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ToAccIsDrawer" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ToAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TransferDate" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="DrawerOnly" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="FromIsVendor" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="ToIsVendor" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="DealerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_CostCenter" Member="ACC_CostCenters">
    <Type Name="ACC_CostCenter">
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CostCenterCode" Type="System.String" DbType="NVarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ParentCCId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ccNumber" Type="System.String" DbType="VarChar(55) NOT NULL" CanBeNull="false" />
      <Column Name="[Level]" Member="Level" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AllowEdit" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="AllowChild" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_CrncTransfer" Member="ACC_CrncTransfers">
    <Type Name="ACC_CrncTransfer">
      <Column Name="CrncTransferId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="TransferSerial" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FromAccIsDrawer" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="FromAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FromCrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FromCrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ToAccIsDrawer" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ToAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ToCrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ToCrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TransferDate" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Acc_CustomAccList" Member="Acc_CustomAccLists">
    <Type Name="Acc_CustomAccList">
      <Column Name="CustomAccListId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CustomAccListCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomAccListName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CustomAccListNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CustomAccListNameDesc" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Association Name="Acc_CustomAccList_Acc_CustomAccListDetail" Member="Acc_CustomAccListDetails" ThisKey="CustomAccListId" OtherKey="CustomAccListId" Type="Acc_CustomAccListDetail" />
    </Type>
  </Table>
  <Table Name="dbo.Acc_CustomAccListDetail" Member="Acc_CustomAccListDetails">
    <Type Name="Acc_CustomAccListDetail">
      <Column Name="CustomAccListDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CustomAccListId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Association Name="Acc_CustomAccList_Acc_CustomAccListDetail" Member="Acc_CustomAccList" ThisKey="CustomAccListId" OtherKey="CustomAccListId" Type="Acc_CustomAccList" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_CustomizedIncome" Member="ACC_CustomizedIncomes">
    <Type Name="ACC_CustomizedIncome">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AccountType" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_DebitCreditNote" Member="ACC_DebitCreditNotes">
    <Type Name="ACC_DebitCreditNote">
      <Column Name="NoteId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="IsDebit" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="NoteCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NoteDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DebitCreditAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_DistCapital" Member="ACC_DistCapitals">
    <Type Name="ACC_DistCapital">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="AccountingPeriodFrom" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="AccountingPeriodTo" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Capital" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Income" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_DistCapitalDetail" Member="ACC_DistCapitalDetails">
    <Type Name="ACC_DistCapitalDetail">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CapitalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_Drawer" Member="ACC_Drawers">
    <Type Name="ACC_Drawer">
      <Column Name="DrawerId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="DrawerNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="DrawerNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="DrawerNotes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertUser" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_Journal" Member="ACC_Journals">
    <Type Name="ACC_Journal">
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="JCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JNotes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertUser" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUser" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsPosted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JNumber" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="BookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsRepeated" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Monthly_Code" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Acc_Journal_CostCenter" Member="Acc_Journal_CostCenters">
    <Type Name="Acc_Journal_CostCenter">
      <Column Name="Acc_Journal_CostCenter_ID" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Journal_Detail_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CostCenter_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Percentage" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_JournalArchive" Member="ACC_JournalArchives">
    <Type Name="ACC_JournalArchive">
      <Column Name="archiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="JournalArchiveId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JNotes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertUser" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUser" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsPosted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JNumber" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="BookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_JournalDetail" Member="ACC_JournalDetails">
    <Type Name="ACC_JournalDetail">
      <Column Name="JDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Debit" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Credit" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Is_Extended" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_JournalDetailArchive" Member="ACC_JournalDetailArchives">
    <Type Name="ACC_JournalDetailArchive">
      <Column Name="detailArchiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="JDetailArchiveId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Debit" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Credit" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_NoteRecivable_Detail" Member="ACC_NoteRecivable_Details">
    <Type Name="ACC_NoteRecivable_Detail">
      <Column Name="NoteRecivableDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NoteRecivableId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(18,0) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_NotesPayable" Member="ACC_NotesPayables">
    <Type Name="ACC_NotesPayable">
      <Column Name="PayNoteId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NoteType" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="NoteSerial" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NoteNumber" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="RegDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Provision" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="BankAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsVendor" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DealerId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DealerAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ResponseType" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RegJournalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayJournalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RespondDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsOpenBalance" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="OutTrnsCode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_NotesReceivable" Member="ACC_NotesReceivables">
    <Type Name="ACC_NotesReceivable">
      <Column Name="ReceiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NoteType" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="NoteSerial" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NoteNumber" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="RegDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Provision" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="IsVendor" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DealerId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DealerAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ResponseType" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BankAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DivertedIsVendor" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DivertedDealerId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DivertedDealerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RespondDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="RegJournalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayJournalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RecipientEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Is_Undercollect" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="collectBankAccId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UndercollectJournalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UndercollectDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsOpenBalance" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsMultiple" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="OutTrnsCode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Acc_RepeatJournal" Member="Acc_RepeatJournals">
    <Type Name="Acc_RepeatJournal">
      <Column Name="Repeat_JID" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="JID" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastInsertDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="RepeatRate" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IsDeleted" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_RevExpEntry" Member="ACC_RevExpEntries">
    <Type Name="ACC_RevExpEntry">
      <Column Name="RevExpId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="RevExpSerial" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IsRevenue" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="RevExpAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="EntryDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RevExpCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Registered" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Rep_EmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsCustody" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_Visa" Member="ACC_Visas">
    <Type Name="ACC_Visa">
      <Column Name="VisaId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="VisaNameAr" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="VisaNameEn" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="BankAccountNumber" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VisaAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertUser" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="AccId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CommisionType" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Commision" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ACC_VisaNote" Member="ACC_VisaNotes">
    <Type Name="ACC_VisaNote">
      <Column Name="NoteId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NoteSerial" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IsVendor" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="DealerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DealerAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NoteDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NoteCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Registered" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Commision" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Budget" Member="Budgets">
    <Type Name="Budget">
      <Column Name="BudgetId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Year" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="BudgetValue" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="Type" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Contract" Member="Contracts">
    <Type Name="Contract">
      <Column Name="ContractId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ContractNo" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ContractType" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ContractDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="ContactStartDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ContactEndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsVendor" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="DealerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ContractData" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesEmp" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ContractTemplate" Member="ContractTemplates">
    <Type Name="ContractTemplate">
      <Column Name="TemplateId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="TemplateCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TemplateName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="Template" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.FA_Exclude" Member="FA_Excludes">
    <Type Name="FA_Exclude">
      <Column Name="ExludeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="FAId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ExcludeAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DeprecianValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="MainValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="NetValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="EvaluationValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ExcludeDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.FA_FixedAsset" Member="FA_FixedAssets">
    <Type Name="FA_FixedAsset">
      <Column Name="FaId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="FaCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FaNameAr" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="FaNameEn" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="EmployeeName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="GroupId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="WarrantyPeriod" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="StartDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="DeprType" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DeprYearRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ScrapValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DefaultAge" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="FaAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FaAcNumber" Type="System.String" DbType="VarChar(55)" CanBeNull="true" />
      <Column Name="DprAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DprAcNumber" Type="System.String" DbType="VarChar(55)" CanBeNull="true" />
      <Column Name="ExpAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="NetCostValue" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="isPay" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="idVendor" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="jrnlId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SLJrnlId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SellValue" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DealerId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SellDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="GainAccId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="isExclude" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="isUnknown" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.FA_FixedAsset_Depreciation" Member="FA_FixedAsset_Depreciations">
    <Type Name="FA_FixedAsset_Depreciation">
      <Column Name="DepId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="AssetId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AssetValue" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="DepreciationDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DepreciationValue" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="DepreciatedMonths" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalDepreciations" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="IsPosted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Ratio" Type="System.Double" DbType="Float NOT NULL" CanBeNull="false" />
      <Column Name="MonthlyDepreciationValue" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.FA_FixedAssetGroup" Member="FA_FixedAssetGroups">
    <Type Name="FA_FixedAssetGroup">
      <Column Name="FaGrpId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="FaGrpCode" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="FaGrpNameAr" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="FaGrpNameEn" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="FaAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FaAcNumber" Type="System.String" DbType="VarChar(55)" CanBeNull="true" />
      <Column Name="DprAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DprAcNumber" Type="System.String" DbType="VarChar(55)" CanBeNull="true" />
      <Column Name="ParentGrpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.FA_FixedAssetPhoto" Member="FA_FixedAssetPhotos">
    <Type Name="FA_FixedAssetPhoto">
      <Column Name="PhotoId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="FixdAstId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Photo" Type="System.Data.Linq.Binary" DbType="Image NOT NULL" CanBeNull="false" UpdateCheck="Never" />
      <Column Name="PhotoDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(400) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Absence" Member="HR_Absences">
    <Type Name="HR_Absence">
      <Column Name="AbsenceId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StartDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Duration" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Payed" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="WithPermission" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_AbsenceBenfit" Member="HR_AbsenceBenfits">
    <Type Name="HR_AbsenceBenfit">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BenfitId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="type" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="DayOrDeduct" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Attendance" Member="HR_Attendances">
    <Type Name="HR_Attendance">
      <Column Name="Day" Type="System.DateTime" DbType="Date NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="Shift1Attend" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift1Leave" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift2Attend" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift2Leave" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="Shift1AttendRule" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift1LeaveRule" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift2AttendRule" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift2LeaveRule" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift3AttendRule" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift3LeaveRule" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift3Attend" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shift3Leave" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_AttendLog" Member="HR_AttendLogs">
    <Type Name="HR_AttendLog">
      <Column Name="AttendLogId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Time" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="IsArchived" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="MachineId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsIn" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_AttendLogAdv" Member="HR_AttendLogAdvs">
    <Type Name="HR_AttendLogAdv">
      <Column Name="AttendLogAdvId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Time" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="IsIn" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="EditTime" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_AuthorizePermission" Member="HR_AuthorizePermissions">
    <Type Name="HR_AuthorizePermission">
      <Column Name="AuthorizePermissionId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Authorization_Count" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Top_Auth_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Low_Auth_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FormAction" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Pid" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Association Name="HR_AuthorizePermission_HR_AuthorizeRequest" Member="HR_AuthorizeRequests" ThisKey="AuthorizePermissionId" OtherKey="AuthorizeId" Type="HR_AuthorizeRequest" />
    </Type>
  </Table>
  <Table Name="dbo.HR_AuthorizeRequest" Member="HR_AuthorizeRequests">
    <Type Name="HR_AuthorizeRequest">
      <Column Name="RequestId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="RequestDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AuthId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AuthDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="isConfirmed" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="AuthorizeId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="isUsed" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Association Name="HR_AuthorizePermission_HR_AuthorizeRequest" Member="HR_AuthorizePermission" ThisKey="AuthorizeId" OtherKey="AuthorizePermissionId" Type="HR_AuthorizePermission" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Benefit" Member="HR_Benefits">
    <Type Name="HR_Benefit">
      <Column Name="BenefitId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BenefitNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="BenefitNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="BenefitType" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="BenefitCalculation" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="BenefitNotes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UsedInSecondPaySlip" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsInsurance" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CompanyInsuranceRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="IsWorkingDays" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="BenfitGroup" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsTaxable" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="subtractAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="HR_Benefit_HR_Incentive" Member="HR_Incentive" ThisKey="BenefitId" OtherKey="Id" Type="HR_Incentive" Cardinality="One" />
    </Type>
  </Table>
  <Table Name="dbo.HR_BenfitGroup" Member="HR_BenfitGroups">
    <Type Name="HR_BenfitGroup">
      <Column Name="GroupId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="GroupNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="GroupNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="GroupNotes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Branch" Member="HR_Branches">
    <Type Name="HR_Branch">
      <Column Name="BranchId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BranchNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="BranchNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="BranchNotes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_BusinessTax" Member="HR_BusinessTaxes">
    <Type Name="HR_BusinessTax">
      <Column Name="id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Name" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="MinAnnualSalary" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="MaxAnnualSalary" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_BusinessTaxDetail" Member="HR_BusinessTaxDetails">
    <Type Name="HR_BusinessTaxDetail">
      <Column Name="id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BusinessTaxId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StartSalary" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="EndSalary" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Percentage" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Delay" Member="HR_Delays">
    <Type Name="HR_Delay">
      <Column Name="DelayId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DelayDay" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DelayMinutes" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Payed" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="RuleId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="FpInId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="FpOutId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ActualMinutes" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_DelayOverTimeRule" Member="HR_DelayOverTimeRules">
    <Type Name="HR_DelayOverTimeRule">
      <Column Name="RuleId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="RuleName" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="IsDelay" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="FreeMinutes" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Type" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="MultiplyMinutesBy" Type="System.Decimal" DbType="Decimal(10,4)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_DelayOverTimeRuleDetail" Member="HR_DelayOverTimeRuleDetails">
    <Type Name="HR_DelayOverTimeRuleDetail">
      <Column Name="RuleDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="RuleId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FromMinute" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ToMinute" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CalcMinute" Type="System.Decimal" DbType="Decimal(10,4)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Dept" Member="HR_Depts">
    <Type Name="HR_Dept">
      <Column Name="DeptId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="DeptNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="DeptNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ParentDeptId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ManagerName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EmpCertification" Member="HR_EmpCertifications">
    <Type Name="HR_EmpCertification">
      <Column Name="CertfId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CertificateName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="CertificatePlace" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="StartDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="EndDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EmpCommissionTarget" Member="HR_EmpCommissionTargets">
    <Type Name="HR_EmpCommissionTarget">
      <Column Name="EmpTargetId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Target" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Commision" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EmpExperience" Member="HR_EmpExperiences">
    <Type Name="HR_EmpExperience">
      <Column Name="ExperId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ExpName" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="ExpPlace" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="StartDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="EndDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="JobDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="QuittingReason" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EmpItemsCommision" Member="HR_EmpItemsCommisions">
    <Type Name="HR_EmpItemsCommision">
      <Column Name="ItemsCommisionId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="QtyMoreThan" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ProfitRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EmployeeBenefit" Member="HR_EmployeeBenefits">
    <Type Name="HR_EmployeeBenefit">
      <Column Name="EmpBenefitId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="BenefitId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Value" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EmpPermissiontoattendandleave" Member="HR_EmpPermissiontoattendandleaves">
    <Type Name="HR_EmpPermissiontoattendandleave">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="RuleId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="RuleType" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="Date" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Minutes" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Hours" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EmpPhoto" Member="HR_EmpPhotos">
    <Type Name="HR_EmpPhoto">
      <Column Name="PhotoId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Photo" Type="System.Data.Linq.Binary" DbType="Image NOT NULL" CanBeNull="false" UpdateCheck="Never" />
      <Column Name="PhotoDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EmpRoundTrip" Member="HR_EmpRoundTrips">
    <Type Name="HR_EmpRoundTrip">
      <Column Name="rId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="isIn" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Date" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EmpVacation" Member="HR_EmpVacations">
    <Type Name="HR_EmpVacation">
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="VacId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="OpenBalance" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Evaluation" Member="HR_Evaluations">
    <Type Name="HR_Evaluation">
      <Column Name="EvalId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="RegDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="FromDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="ToDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="AvgDegree" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EvaluationDetail" Member="HR_EvaluationDetails">
    <Type Name="HR_EvaluationDetail">
      <Column Name="EvalDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EvalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="EvalItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Degree" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_EvaluationItem" Member="HR_EvaluationItems">
    <Type Name="HR_EvaluationItem">
      <Column Name="EvalItemId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EvalItemName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_FormalVacation" Member="HR_FormalVacations">
    <Type Name="HR_FormalVacation">
      <Column Name="FormalVacationId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="FormalVacationNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="FormalVacationNameEn" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="StartDate" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="EndDate" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="FormalVacationNotes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Group" Member="HR_Groups">
    <Type Name="HR_Group">
      <Column Name="GroupId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="GroupNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="GroupNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="GroupNotes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Incentive" Member="HR_Incentives">
    <Type Name="HR_Incentive">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BenefitId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Association Name="HR_Benefit_HR_Incentive" Member="HR_Benefit" ThisKey="Id" OtherKey="BenefitId" Type="HR_Benefit" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_InOutRule" Member="HR_InOutRules">
    <Type Name="HR_InOutRule">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Name" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="Attendance" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="departure" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Job" Member="HR_Jobs">
    <Type Name="HR_Job">
      <Column Name="JobId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="JobNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="JobNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="JobNotes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Loan" Member="HR_Loans">
    <Type Name="HR_Loan">
      <Column Name="LoanId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="RegDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="EmpAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InstallmentsCount" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Payed" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="JournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InsertUser" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_LoanDetail" Member="HR_LoanDetails">
    <Type Name="HR_LoanDetail">
      <Column Name="LoanDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="LoanId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Payed" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PayDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="DuePaymentDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Mission" Member="HR_Missions">
    <Type Name="HR_Mission">
      <Column Name="MissionId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StartDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Duration" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Type" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Nationality" Member="HR_Nationalities">
    <Type Name="HR_Nationality">
      <Column Name="NathId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NathName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_OverTime" Member="HR_OverTimes">
    <Type Name="HR_OverTime">
      <Column Name="OverTimeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="OverTimeDay" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="OverTimeMinutes" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Payed" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="RuleId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="FpInId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="FpOutId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ActualMinutes" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Pay" Member="HR_Pays">
    <Type Name="HR_Pay">
      <Column Name="PayId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="PayPeriod" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="FromDay" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="ToDay" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="Basic" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Variable" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="HoursValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DaysValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Total" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DueJournalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="IsPaid" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayJournalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsSecondPaySlip" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="NetSalaryTax" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Column Name="companyshare" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="empshare" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="businessgaintax" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Incentive" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Association Name="HR_Pay_HR_PayDetail" Member="HR_PayDetails" ThisKey="PayId" OtherKey="PayId" Type="HR_PayDetail" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Penalty" Member="HR_Penalties">
    <Type Name="HR_Penalty">
      <Column Name="PenaltyId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PenaltyDay" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="PenaltyValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="DaysCount" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Privilege" Member="HR_Privileges">
    <Type Name="HR_Privilege">
      <Column Name="PId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="DeptName" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="FormNameEn" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="FormNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="HasAdd" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HasEdit" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HasDel" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HasPrint" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="FormName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Promotion" Member="HR_Promotions">
    <Type Name="HR_Promotion">
      <Column Name="PromId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CurrentDeptId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CurrentJobId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NewDeptId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NewJobId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Date" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="currentgrp" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="newgrp" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Qalification_Benifits" Member="HR_Qalification_Benifits">
    <Type Name="HR_Qalification_Benifit">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="QalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="BenfId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Qualification" Member="HR_Qualifications">
    <Type Name="HR_Qualification">
      <Column Name="QlfcId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="QlfcName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Religion" Member="HR_Religions">
    <Type Name="HR_Religion">
      <Column Name="RlgnId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="RlgnName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Reward" Member="HR_Rewards">
    <Type Name="HR_Reward">
      <Column Name="RewardId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="RewardDay" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="RewardValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="DaysCount" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsNow" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Shift" Member="HR_Shifts">
    <Type Name="HR_Shift">
      <Column Name="ShiftId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ShiftName" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="StartDateTime" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="ShiftType" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IsShiftPerDay" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_ShiftDetail" Member="HR_ShiftDetails">
    <Type Name="HR_ShiftDetail">
      <Column Name="ShiftDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ShiftId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TimeTableId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_ShiftDetailDays" Member="HR_ShiftDetailDays">
    <Type Name="HR_ShiftDetailDay">
      <Column Name="ShiftDetailDayId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ShiftId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TimeTableId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Day" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_ShiftReplace" Member="HR_ShiftReplaces">
    <Type Name="HR_ShiftReplace">
      <Column Name="shiftReplaceId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="shiftReplaceDay" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="ISIn" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="FpInId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="FpOutId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Sponosr" Member="HR_Sponosrs">
    <Type Name="HR_Sponosr">
      <Column Name="SponosrId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SponosrName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="SponosrFName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Tel" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Address" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_SponsorChange" Member="HR_SponsorChanges">
    <Type Name="HR_SponsorChange">
      <Column Name="SpnsrChngId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CurrentsponsorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="NewsponsorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Date" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_TimeTable" Member="HR_TimeTables">
    <Type Name="HR_TimeTable">
      <Column Name="TimeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="TimeName" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="AttendTime" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="AttendStart" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="AttendEnd" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="LeaveTime" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="LeaveStart" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="LeaveEnd" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Training" Member="HR_Trainings">
    <Type Name="HR_Training">
      <Column Name="TrainingId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CourseName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Lecturer" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Place" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="StartDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="EndDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_UserPrivilege" Member="HR_UserPrivileges">
    <Type Name="HR_UserPrivilege">
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="PId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="CanAdd" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CanEdit" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CanDel" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CanPrint" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Vacation" Member="HR_Vacations">
    <Type Name="HR_Vacation">
      <Column Name="VacationId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StartDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Duration" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="OtherVacId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DeductWholeDay" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsArchived" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="FormalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_VacationsExtraType" Member="HR_VacationsExtraTypes">
    <Type Name="HR_VacationsExtraType">
      <Column Name="VacId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="VacNAme" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="Balance" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AvailableAfterMonths" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DayDiscRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="VacStatus" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_WorkEntity" Member="HR_WorkEntities">
    <Type Name="HR_WorkEntity">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EntNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="EntNameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ParentEntId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ManagerName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_WorkingDays" Member="HR_WorkingDays">
    <Type Name="HR_WorkingDay">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DateFrom" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DateTo" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="WorkType" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="Count" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.HR_WorkLeave" Member="HR_WorkLeaves">
    <Type Name="HR_WorkLeave">
      <Column Name="WorkLeaveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="WorkLeaveDay" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="ReconcileAmount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_WorkReturn" Member="HR_WorkReturns">
    <Type Name="HR_WorkReturn">
      <Column Name="WorkReturnId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="WorkReturnDay" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_BOM" Member="IC_BOMs">
    <Type Name="IC_BOM">
      <Column Name="BOMId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BOM_Name" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="BOM_Description" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="ProductItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UomId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="WorkedHours" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_BOMDetail" Member="IC_BOMDetails">
    <Type Name="IC_BOMDetail">
      <Column Name="BOMDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="RawItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UomId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_BOMExpense" Member="IC_BOMExpenses">
    <Type Name="IC_BOMExpense">
      <Column Name="BOMExpId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Value" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_Category" Member="IC_Categories">
    <Type Name="IC_Category">
      <Column Name="CategoryId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CategoryNameAr" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="CategoryNameEn" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="CatNumber" Type="System.String" DbType="VarChar(55) NOT NULL" CanBeNull="false" />
      <Column Name="[Level]" Member="Level" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ParentId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SellAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="COGSAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SellReturnAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InvAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseReturnAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="OpenInventoryAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CloseInventoryAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Region" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Subegion" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="StreetName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="StreetNo" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Code2" Type="System.String" DbType="VarChar(55)" CanBeNull="true" />
      <Column Name="ExpensesAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="IC_Category_IC_Item" Member="IC_Items" ThisKey="CategoryId" OtherKey="Category" Type="IC_Item" />
    </Type>
  </Table>
  <Table Name="dbo.IC_Company" Member="IC_Companies">
    <Type Name="IC_Company">
      <Column Name="CompanyId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CompanyCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CompanyNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CompanyNameEn" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="Address" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="Tel" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Association Name="IC_Company_IC_Item" Member="IC_Items" ThisKey="CompanyId" OtherKey="Company" Type="IC_Item" />
    </Type>
  </Table>
  <Table Name="dbo.IC_CustomerItem" Member="IC_CustomerItems">
    <Type Name="IC_CustomerItem">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Association Name="IC_Item_IC_CustomerItem" Member="IC_Item" ThisKey="ItemId" OtherKey="ItemId" Type="IC_Item" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_Damaged" Member="IC_Damageds">
    <Type Name="IC_Damaged">
      <Column Name="DamagedId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="DamagedCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DamagedDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Association Name="IC_Damaged_IC_DamagedDetail" Member="IC_DamagedDetails" ThisKey="DamagedId" OtherKey="DamagedId" Type="IC_DamagedDetail" />
    </Type>
  </Table>
  <Table Name="dbo.IC_DamagedDetail" Member="IC_DamagedDetails">
    <Type Name="IC_DamagedDetail">
      <Column Name="DamagedDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="DamagedId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="IC_Damaged_IC_DamagedDetail" Member="IC_Damaged" ThisKey="DamagedId" OtherKey="DamagedId" Type="IC_Damaged" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_InternalRequest" Member="IC_InternalRequests">
    <Type Name="IC_InternalRequest">
      <Column Name="IC_RequestId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="RequestCode" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="RequestDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RespondDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="RespondUser" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RequestStatus" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ExpensesAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_InternalRequestDetail" Member="IC_InternalRequestDetails">
    <Type Name="IC_InternalRequestDetail">
      <Column Name="RequestDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="RequestId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOM" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Double" DbType="Float NOT NULL" CanBeNull="false" />
      <Column Name="Status" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_InternationalCode" Member="IC_InternationalCodes">
    <Type Name="IC_InternationalCode">
      <Column Name="InternationalCodeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InternationalCode" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Association Name="IC_Item_IC_InternationalCode" Member="IC_Item" ThisKey="ItemId" OtherKey="ItemId" Type="IC_Item" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_InTrnsArchive" Member="IC_InTrnsArchives">
    <Type Name="IC_InTrnsArchive">
      <Column Name="InTrnsArchiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InTrnsId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InTrnsCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InTrnsDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsVendor" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TotalQty" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="DetailIdBrcodPrntd" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="IsOpenBalance" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_PurchaseInv" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_InTrnsDetail" Member="IC_InTrnsDetails">
    <Type Name="IC_InTrnsDetail">
      <Column Name="InTrnsDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InTrnsId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Libra_Qty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="IC_InTrn_IC_InTrnsDetail" Member="IC_InTrn" ThisKey="InTrnsId" OtherKey="InTrnsId" Type="IC_InTrn" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_InTrnsDetailArchive" Member="IC_InTrnsDetailArchives">
    <Type Name="IC_InTrnsDetailArchive">
      <Column Name="InTrnsDetailArchiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InTrnsDetailId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InTrnsId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Libra_Qty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_Item_Location" Member="IC_Item_Locations">
    <Type Name="IC_Item_Location">
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Location" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_ItemMatrixDetails" Member="IC_ItemMatrixDetails">
    <Type Name="IC_ItemMatrixDetail">
      <Column Name="ItmMtrxDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="MatrixDetailId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="MatrixId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="available" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_ItemPriceChange" Member="IC_ItemPriceChanges">
    <Type Name="IC_ItemPriceChange">
      <Column Name="ItemPriceChangeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="OldPPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="NewPPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="OldSPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="NewSPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ChangeDate" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="PInvId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_ItemPricesPerQty" Member="IC_ItemPricesPerQties">
    <Type Name="IC_ItemPricesPerQty">
      <Column Name="ItemPriceId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="QtyFrom" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="QtyTo" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_ItemQtyChange" Member="IC_ItemQtyChanges">
    <Type Name="IC_ItemQtyChange">
      <Column Name="ItemQtyChangeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemSerial" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="OldQty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="NewQty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UOM" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ChangeDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="OldPiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="NewPiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_ItemStore" Member="IC_ItemStores">
    <Type Name="IC_ItemStore">
      <Column Name="ItemStoreId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IsInTrns" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="InsertTime" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ParentItemId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="M1" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="M2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="M3" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_ItemVendors" Member="IC_ItemVendors">
    <Type Name="IC_ItemVendor">
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_Matrix" Member="IC_Matrixes">
    <Type Name="IC_Matrix">
      <Column Name="MatrixId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="MatrixCode" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="MatrixName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Association Name="IC_Matrix_IC_MatrixDetail" Member="IC_MatrixDetails" ThisKey="MatrixId" OtherKey="MatrixId" Type="IC_MatrixDetail" />
    </Type>
  </Table>
  <Table Name="dbo.IC_MatrixDetail" Member="IC_MatrixDetails">
    <Type Name="IC_MatrixDetail">
      <Column Name="MatrixDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="MatrixId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="MDCode" Type="System.String" DbType="VarChar(10)" CanBeNull="true" />
      <Column Name="MDName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Association Name="IC_Matrix_IC_MatrixDetail" Member="IC_Matrix" ThisKey="MatrixId" OtherKey="MatrixId" Type="IC_Matrix" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_MultipleWeight" Member="IC_MultipleWeights">
    <Type Name="IC_MultipleWeight">
      <Column Name="idMultiple" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="idInvoice" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="idSource" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_MultipleWeight_Detail" Member="IC_MultipleWeight_Details">
    <Type Name="IC_MultipleWeight_Detail">
      <Column Name="idMultipleDetail" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="idMultipleWeight" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemCode" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="Libra" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Weight_kg" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Count" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_OpenBalance" Member="IC_OpenBalances">
    <Type Name="IC_OpenBalance">
      <Column Name="OpenBalanceId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="OpenDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_OutTrnsArchive" Member="IC_OutTrnsArchives">
    <Type Name="IC_OutTrnsArchive">
      <Column Name="OutTrnsArchiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="OutTrnsId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="OutTrnsCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="OutTrnsDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeliveryEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsVendor" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_SellInv" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_OutTrnsDetail" Member="IC_OutTrnsDetails">
    <Type Name="IC_OutTrnsDetail">
      <Column Name="OutTrnsDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="OutTrnsId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LibraQty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="IsOffer" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="VariableWeight" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="IC_OutTrn_IC_OutTrnsDetail" Member="IC_OutTrn" ThisKey="OutTrnsId" OtherKey="OutTrnsId" Type="IC_OutTrn" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_OutTrnsDetailArchive" Member="IC_OutTrnsDetailArchives">
    <Type Name="IC_OutTrnsDetailArchive">
      <Column Name="OutTrnsDetailArchiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="OutTrnsDetailId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="OutTrnsId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LibraQty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="IsOffer" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="VariableWeight" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_PriceLevel" Member="IC_PriceLevels">
    <Type Name="IC_PriceLevel">
      <Column Name="PriceLevelId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PLName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="IsRatio" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsRatioIncrease" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Ratio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Association Name="IC_PriceLevel_IC_PriceLevelDetail" Member="IC_PriceLevelDetails" ThisKey="PriceLevelId" OtherKey="PriceLevelId" Type="IC_PriceLevelDetail" />
    </Type>
  </Table>
  <Table Name="dbo.IC_PriceLevelDetail" Member="IC_PriceLevelDetails">
    <Type Name="IC_PriceLevelDetail">
      <Column Name="PriceLevelDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PriceLevelId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="smallUOMPrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="MediumUOMPrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="LargeUOMPrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Association Name="IC_PriceLevel_IC_PriceLevelDetail" Member="IC_PriceLevel" ThisKey="PriceLevelId" OtherKey="PriceLevelId" Type="IC_PriceLevel" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_PrPriceLevel" Member="IC_PrPriceLevels">
    <Type Name="IC_PrPriceLevel">
      <Column Name="PrPriceLevelId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PrPLName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="IsRatio" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsRatioIncrease" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Ratio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Association Name="IC_PrPriceLevel_IC_PrPriceLevelDetail" Member="IC_PrPriceLevelDetails" ThisKey="PrPriceLevelId" OtherKey="PrPriceLevelId" Type="IC_PrPriceLevelDetail" />
    </Type>
  </Table>
  <Table Name="dbo.IC_PrPriceLevelDetail" Member="IC_PrPriceLevelDetails">
    <Type Name="IC_PrPriceLevelDetail">
      <Column Name="PrPriceLevelDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PrPriceLevelId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="smallUOMPrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Association Name="IC_PrPriceLevel_IC_PrPriceLevelDetail" Member="IC_PrPriceLevel" ThisKey="PrPriceLevelId" OtherKey="PrPriceLevelId" Type="IC_PrPriceLevel" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_ReceiveGood" Member="IC_ReceiveGoods">
    <Type Name="IC_ReceiveGood">
      <Column Name="ReceiveGoodId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ReceiveGoodCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ReceiveGoodDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsVendor" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TotalQty" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="DetailIdBrcodPrntd" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_PurchaseInv" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Association Name="IC_ReceiveGood_IC_ReceiveGoodDetail" Member="IC_ReceiveGoodDetails" ThisKey="ReceiveGoodId" OtherKey="ReceiveGoodId" Type="IC_ReceiveGoodDetail" />
    </Type>
  </Table>
  <Table Name="dbo.IC_ReceiveGoodDetail" Member="IC_ReceiveGoodDetails">
    <Type Name="IC_ReceiveGoodDetail">
      <Column Name="ReceiveGoodDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ReceiveGoodId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Libra_Qty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="IC_ReceiveGood_IC_ReceiveGoodDetail" Member="IC_ReceiveGood" ThisKey="ReceiveGoodId" OtherKey="ReceiveGoodId" Type="IC_ReceiveGood" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_StockTaking" Member="IC_StockTakings">
    <Type Name="IC_StockTaking">
      <Column Name="IC_StockTaking_ID" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Date" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="StorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UserID" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsCommited" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Shortage" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Profit" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Replacement" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_StockTaking_Detail" Member="IC_StockTaking_Details">
    <Type Name="IC_StockTaking_Detail">
      <Column Name="StockTaking_Detail_ID" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="StockTaking_ID" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CurrentQty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SupposedQty" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="ItemID" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOM" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="UomIndex" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LibraQty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="ActualPieces" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="ActualWeight" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_StoreMove" Member="IC_StoreMoves">
    <Type Name="IC_StoreMove">
      <Column Name="StoreMoveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="StoreMoveCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="SourceStoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DestinationStoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StoreMoveDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="InvoiceDate_Arrive" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenter_Id" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="IC_StoreMove_IC_StoreMoveDetail" Member="IC_StoreMoveDetails" ThisKey="StoreMoveId" OtherKey="StoreMoveId" Type="IC_StoreMoveDetail" />
    </Type>
  </Table>
  <Table Name="dbo.IC_StoreMoveDetail" Member="IC_StoreMoveDetails">
    <Type Name="IC_StoreMoveDetail">
      <Column Name="StoreMoveDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="StoreMoveId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="LibraQty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="IsOffer" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="VariableWeight" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="IC_StoreMove_IC_StoreMoveDetail" Member="IC_StoreMove" ThisKey="StoreMoveId" OtherKey="StoreMoveId" Type="IC_StoreMove" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_User_Stores" Member="IC_User_Stores">
    <Type Name="IC_User_Store">
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Boucher" Member="ImExp_Bouchers">
    <Type Name="ImExp_Boucher">
      <Column Name="BoucherId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NameAr" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="NameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Country" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Tel" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Email" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Representative_Name" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Representative_Tel" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="code" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Number" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Brand" Member="ImExp_Brands">
    <Type Name="ImExp_Brand">
      <Column Name="BrandId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NameAr" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="NameEn" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Container" Member="ImExp_Containers">
    <Type Name="ImExp_Container">
      <Column Name="ContainerId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ContainerNumber" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="Chargingline" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PreformId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PolicyId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="HalalCertificateId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="VendorCommission" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="Total" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_ContractType" Member="ImExp_ContractTypes">
    <Type Name="ImExp_ContractType">
      <Column Name="ContractTypeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NameAr" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="NameEn" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Customs_Certificate" Member="ImExp_Customs_Certificates">
    <Type Name="ImExp_Customs_Certificate">
      <Column Name="CustomsCertificateId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CustomsCertificateCode" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="CustomsCertificateRegister" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="CheckNumber" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InvoiceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LabId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DrawDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="NumberOfContainers" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="CategoryId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProductionMonth" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Storage" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PortId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ReleaseDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="RejectiondDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CompletedWithdrawalDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_ExportConfirmation" Member="ImExp_ExportConfirmations">
    <Type Name="ImExp_ExportConfirmation">
      <Column Name="ConfirmId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ConfirmCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ConfirmDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="ConfirmValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ButcherId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Fine" Member="ImExp_Fines">
    <Type Name="ImExp_Fine">
      <Column Name="FineId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="FineDate" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="FineNumber" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="FineReason" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="journalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="RevAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Import_Approval" Member="ImExp_Import_Approvals">
    <Type Name="ImExp_Import_Approval">
      <Column Name="ImportApprovalId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ImportApprovalDate" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="Approvsl_Number" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Item_CategoryId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BoucherId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Invoice" Member="ImExp_Invoices">
    <Type Name="ImExp_Invoice">
      <Column Name="ImExp_InvoiceId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Is_Posted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PostDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_InTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DetailIdBrcodprntd" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LcAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="butcherId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ShiftAdd" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="brand" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InvoiceVendorCode" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LastUpDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Association Name="ImExp_Invoice_ImExp_InvoiceDetail" Member="ImExp_InvoiceDetails" ThisKey="ImExp_InvoiceId" OtherKey="ImExp_InvoiceId" Type="ImExp_InvoiceDetail" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_InvoiceContainer" Member="ImExp_InvoiceContainers">
    <Type Name="ImExp_InvoiceContainer">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ContainerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_InvoiceDetail" Member="ImExp_InvoiceDetails">
    <Type Name="ImExp_InvoiceDetail">
      <Column Name="ImExp_InvoiceDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ImExp_InvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemWeight" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="pr_price" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="ImExp_Invoice_ImExp_InvoiceDetail" Member="ImExp_Invoice" ThisKey="ImExp_InvoiceId" OtherKey="ImExp_InvoiceId" Type="ImExp_Invoice" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Policy" Member="ImExp_Policies">
    <Type Name="ImExp_Policy">
      <Column Name="PolicyId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PolicyCode" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="Code" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PolicyDate" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="ArrivalDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="InvoiceCode" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Port" Member="ImExp_Ports">
    <Type Name="ImExp_Port">
      <Column Name="PortId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NameAr" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="NameEn" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Country" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Abbrevaiton" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_PreInvoice" Member="ImExp_PreInvoices">
    <Type Name="ImExp_PreInvoice">
      <Column Name="ImExp_PreInvoiceId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="AttnMr_Job" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="DeliverDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Brand" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Butcher" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Port" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ContractType" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PreInvoiceStatus" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ACID" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AdvanceAmount" Type="System.Decimal" DbType="Decimal(20,8)" CanBeNull="true" />
      <Column Name="InvoiceAdvancedId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="ImExp_PreInvoice_ImExp_PreInvoiceConfirmDetail" Member="ImExp_PreInvoiceConfirmDetails" ThisKey="ImExp_PreInvoiceId" OtherKey="PreInvoiceId" Type="ImExp_PreInvoiceConfirmDetail" />
      <Association Name="ImExp_PreInvoice_ImExp_PreInvoiceDetail" Member="ImExp_PreInvoiceDetails" ThisKey="ImExp_PreInvoiceId" OtherKey="ImExp_PreInvId" Type="ImExp_PreInvoiceDetail" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_PreInvoiceConfirmDetail" Member="ImExp_PreInvoiceConfirmDetails">
    <Type Name="ImExp_PreInvoiceConfirmDetail">
      <Column Name="DetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PreInvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ConfirmId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ConfirmDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="ButcherId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CurrentAmount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Association Name="ImExp_PreInvoice_ImExp_PreInvoiceConfirmDetail" Member="ImExp_PreInvoice" ThisKey="PreInvoiceId" OtherKey="ImExp_PreInvoiceId" Type="ImExp_PreInvoice" IsForeignKey="true" DeleteRule="CASCADE" DeleteOnNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_PreInvoiceDetail" Member="ImExp_PreInvoiceDetails">
    <Type Name="ImExp_PreInvoiceDetail">
      <Column Name="ImExp_PreInvDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ImExp_PreInvId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Association Name="ImExp_PreInvoice_ImExp_PreInvoiceDetail" Member="ImExp_PreInvoice" ThisKey="ImExp_PreInvId" OtherKey="ImExp_PreInvoiceId" Type="ImExp_PreInvoice" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_ProformaType" Member="ImExp_ProformaTypes">
    <Type Name="ImExp_ProformaType">
      <Column Name="ProformaTypeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NameAr" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="NameEn" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.JO_Dept" Member="JO_Depts">
    <Type Name="JO_Dept">
      <Column Name="DeptId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Department" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.JO_JobOrder" Member="JO_JobOrders">
    <Type Name="JO_JobOrder">
      <Column Name="JobOrderId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="JOCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="SalesEmp" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Job" Type="System.String" DbType="NVarChar(300) NOT NULL" CanBeNull="false" />
      <Column Name="RegDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DeliveryDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Dept" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Status" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Priority" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="DeliveryEmp" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessType" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.JO_JobOrderDetail" Member="JO_JobOrderDetails">
    <Type Name="JO_JobOrderDetail">
      <Column Name="JoDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="JoId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ArCaption" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="EnCaption" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ArText" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="EnText" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.JO_JobOrderItem" Member="JO_JobOrderItems">
    <Type Name="JO_JobOrderItem">
      <Column Name="JoItemId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="JoId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="OrderStatus" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="DeliveryMode" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="DeliveryDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Job" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="Starch" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="Whitener" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="Folding" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="Remarks" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Spotify" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.JO_JobOrderTemplate" Member="JO_JobOrderTemplates">
    <Type Name="JO_JobOrderTemplate">
      <Column Name="ID" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ArCaption" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="EnCaption" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ArText" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="EnText" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.JO_Priority" Member="JO_Priorities">
    <Type Name="JO_Priority">
      <Column Name="PriorityId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Priority" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.JO_Status" Member="JO_Status">
    <Type Name="JO_Status">
      <Column Name="StatusId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Status" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="StatusColor" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="AllowAttention" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.LKP_Process" Member="LKP_Processes">
    <Type Name="LKP_Process">
      <Column Name="ProcessId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="ProcessName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="ProcessEnglishName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Mall_Due" Member="Mall_Dues">
    <Type Name="Mall_Due">
      <Column Name="DueId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="DueNameAr" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Mall_Invoice" Member="Mall_Invoices">
    <Type Name="Mall_Invoice">
      <Column Name="InvoiceId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="MonthlyDueId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TotalInvoice" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="DateFrom" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="DateTo" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Mall_InvoiceDetail" Member="Mall_InvoiceDetails">
    <Type Name="Mall_InvoiceDetail">
      <Column Name="InvoiceDetaialId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="MonthlySusfuct" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Mall_MonthlyDue" Member="Mall_MonthlyDues">
    <Type Name="Mall_MonthlyDue">
      <Column Name="MonthlyDueId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Code" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DateFrom" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DateTo" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Mall_MonthlyDueDetail" Member="Mall_MonthlyDueDetails">
    <Type Name="Mall_MonthlyDueDetail">
      <Column Name="MonthlyDueDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="MonthlyDueId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DueId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalDueValue" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="MeterDueValue" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_Assembly" Member="Manf_Assemblies">
    <Type Name="Manf_Assembly">
      <Column Name="AssemblyId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="AssemblyCode" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="AssemblyDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="IsAssembly" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ProductStoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_AssemblyProduct" Member="Manf_AssemblyProducts">
    <Type Name="Manf_AssemblyProduct">
      <Column Name="AssemblyProductId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="AssemblyId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="BOMId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_AssemblyRaws" Member="Manf_AssemblyRaws">
    <Type Name="Manf_AssemblyRaw">
      <Column Name="AssemblyRawId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="AssemblyId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_Expense" Member="Manf_Expenses">
    <Type Name="Manf_Expense">
      <Column Name="Manf_Expense_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Manf_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PayAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Expenses_Name" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Expenses_Value" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Is_direct" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="percentage" Type="System.Decimal" DbType="Decimal(10,4) NOT NULL" CanBeNull="false" />
      <Column Name="processId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="sourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Date" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="code" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_Order" Member="Manf_Orders">
    <Type Name="Manf_Order">
      <Column Name="Order_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Order_NO" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StartDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ProductSupposedTotalCost" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="IsFinished" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_OrderProduct" Member="Manf_OrderProducts">
    <Type Name="Manf_OrderProduct">
      <Column Name="ManfOrderProductId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ManfOrder_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SupposedTotalCost" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_OrderRawItem" Member="Manf_OrderRawItems">
    <Type Name="Manf_OrderRawItem">
      <Column Name="ManfOrderRawId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ManfOrder_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SupposedTotalCost" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_QC" Member="Manf_QCs">
    <Type Name="Manf_QC">
      <Column Name="Manf_QCId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Manf_QCCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="SourceStoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DestinationStoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Manf_QCDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="EmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="Manf_QC_Manf_QCDetail" Member="Manf_QCDetails" ThisKey="Manf_QCId" OtherKey="Manf_QCId" Type="Manf_QCDetail" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_QCDetail" Member="Manf_QCDetails">
    <Type Name="Manf_QCDetail">
      <Column Name="Manf_QCDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Manf_QCId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Association Name="Manf_QC_Manf_QCDetail" Member="Manf_QC" ThisKey="Manf_QCId" OtherKey="Manf_QCId" Type="Manf_QC" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.Manf_Wage" Member="Manf_Wages">
    <Type Name="Manf_Wage">
      <Column Name="Manf_WageId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Manf_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Emp_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Date" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="WorkedHours" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Total_Wage" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ProducedQty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ProductItemId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProductUOMId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ManfDamage" Member="ManfDamages">
    <Type Name="ManfDamage">
      <Column Name="ManfDamage_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Manf_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ManfDetail" Member="ManfDetails">
    <Type Name="ManfDetail">
      <Column Name="ManfDetail_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Manf_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="TotalCost" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ManfProduct" Member="ManfProducts">
    <Type Name="ManfProduct">
      <Column Name="ManfProductsId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Manf_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SupposedTotalCost" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ActualTotalCost" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="ActualQty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="OperationalWaste" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="BOMId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Mr_CustGrpItems" Member="Mr_CustGrpItems">
    <Type Name="Mr_CustGrpItem">
      <Column Name="CustGrpId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="TargetQty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Weight" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Date" Type="System.DateTime" DbType="Date NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="DiscR" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Mr_IndirectSale" Member="Mr_IndirectSales">
    <Type Name="Mr_IndirectSale">
      <Column Name="Mr_IndId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="IndCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="CompId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustGrpId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IndDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Association Name="Mr_IndirectSale_Mr_IndirectSaleDetail" Member="Mr_IndirectSaleDetails" ThisKey="Mr_IndId" OtherKey="Mr_IndId" Type="Mr_IndirectSaleDetail" />
    </Type>
  </Table>
  <Table Name="dbo.Mr_IndirectSaleDetail" Member="Mr_IndirectSaleDetails">
    <Type Name="Mr_IndirectSaleDetail">
      <Column Name="Mr_IndIdDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Mr_IndId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Association Name="Mr_IndirectSale_Mr_IndirectSaleDetail" Member="Mr_IndirectSale" ThisKey="Mr_IndId" OtherKey="Mr_IndId" Type="Mr_IndirectSale" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.Mr_InDirectSalesComp" Member="Mr_InDirectSalesComps">
    <Type Name="Mr_InDirectSalesComp">
      <Column Name="CompId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CompName" Type="System.String" DbType="NVarChar(150) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.PR_Invoice" Member="PR_Invoices">
    <Type Name="PR_Invoice">
      <Column Name="PR_InvoiceId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Is_Posted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PostDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_InTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DetailIdBrcodPrntd" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LcAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="VehicleNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ShiftAdd" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Association Name="PR_Invoice_PR_InvoiceDetail" Member="PR_InvoiceDetails" ThisKey="PR_InvoiceId" OtherKey="PR_InvoiceId" Type="PR_InvoiceDetail" />
    </Type>
  </Table>
  <Table Name="dbo.PR_InvoiceArchive" Member="PR_InvoiceArchives">
    <Type Name="PR_InvoiceArchive">
      <Column Name="archive_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_InvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Is_Posted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PostDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_InTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DetailIdBrcodPrntd" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LcAccId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="VehicleNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ShiftAdd" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_InvoiceDetail" Member="PR_InvoiceDetails">
    <Type Name="PR_InvoiceDetail">
      <Column Name="PR_InvoiceDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_InvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemWeight" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="pr_price" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="PR_Invoice_PR_InvoiceDetail" Member="PR_Invoice" ThisKey="PR_InvoiceId" OtherKey="PR_InvoiceId" Type="PR_Invoice" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_InvoiceDetailArchive" Member="PR_InvoiceDetailArchives">
    <Type Name="PR_InvoiceDetailArchive">
      <Column Name="archive_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_InvoiceDetailArchiveId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PR_InvoiceArchiveId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemWeight" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_InvoiceExpense" Member="PR_InvoiceExpenses">
    <Type Name="PR_InvoiceExpense">
      <Column Name="PR_InvoiceExpenseId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_InvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ExpenseAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ExpenseNote" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="isStoreMove" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_InvoiceOtherExpense" Member="PR_InvoiceOtherExpenses">
    <Type Name="PR_InvoiceOtherExpense">
      <Column Name="InvExpId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_InvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ExpensesJournalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IsStoreMove" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_Items_Costs" Member="PR_Items_Costs">
    <Type Name="PR_Items_Cost">
      <Column Name="Expenses_Destribution_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalQty" Type="System.Decimal" DbType="Decimal(18,4) NOT NULL" CanBeNull="false" />
      <Column Name="UnitCost" Type="System.Decimal" DbType="Decimal(18,4) NOT NULL" CanBeNull="false" />
      <Column Name="TotalCost" Type="System.Decimal" DbType="Decimal(18,4) NOT NULL" CanBeNull="false" />
      <Column Name="DistributionMethod" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="PR_Invoice_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="User_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Last_Update_Date" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.PR_LC" Member="PR_LCs">
    <Type Name="PR_LC">
      <Column Name="PR_LcId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="LcCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LcName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="LcValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="OpenDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CloseDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ShipDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="DeliverDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ShipMethod" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ShipPort" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="BillOfLading" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="PayMethod" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="LcIsOpen" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="LcAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.PR_LCPhoto" Member="PR_LCPhotos">
    <Type Name="PR_LCPhoto">
      <Column Name="PhotoId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_LcId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Photo" Type="System.Data.Linq.Binary" DbType="Image NOT NULL" CanBeNull="false" UpdateCheck="Never" />
      <Column Name="PhotoDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(400) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.PR_PurchaseOrder" Member="PR_PurchaseOrders">
    <Type Name="PR_PurchaseOrder">
      <Column Name="PR_POId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="AttnMr_Job" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="DeliverDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ShipTo" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Is_PurchaseInv" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Is_InTrns" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Closed_PO" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PurchaseOrderApproved" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PurchaseOrderApprovedByEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseOrderApprovedDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Association Name="PR_PurchaseOrder_PR_PurchaseOrderDetail" Member="PR_PurchaseOrderDetails" ThisKey="PR_POId" OtherKey="PR_POId" Type="PR_PurchaseOrderDetail" />
    </Type>
  </Table>
  <Table Name="dbo.PR_PurchaseOrderArchive" Member="PR_PurchaseOrderArchives">
    <Type Name="PR_PurchaseOrderArchive">
      <Column Name="PR_POArchiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_POId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="AttnMr_Job" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="DeliverDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ShipTo" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Is_PurchaseInv" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Is_InTrns" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Closed_PO" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseOrderApproved" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PurchaseOrderApprovedByEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseOrderApprovedDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_PurchaseOrderDetail" Member="PR_PurchaseOrderDetails">
    <Type Name="PR_PurchaseOrderDetail">
      <Column Name="PR_PODetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_POId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="PR_PurchaseOrder_PR_PurchaseOrderDetail" Member="PR_PurchaseOrder" ThisKey="PR_POId" OtherKey="PR_POId" Type="PR_PurchaseOrder" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_PurchaseOrderDetailArchive" Member="PR_PurchaseOrderDetailArchives">
    <Type Name="PR_PurchaseOrderDetailArchive">
      <Column Name="PR_PODetailArchiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_PODetailId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PR_POId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_PurchaseRequest" Member="PR_PurchaseRequests">
    <Type Name="PR_PurchaseRequest">
      <Column Name="PR_PurchaseRequestId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="RespondDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Status" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DepartmentId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="PR_PurchaseRequest_PR_PurchaseRequestDetail" Member="PR_PurchaseRequestDetails" ThisKey="PR_PurchaseRequestId" OtherKey="PR_PurchaseRequestId" Type="PR_PurchaseRequestDetail" />
    </Type>
  </Table>
  <Table Name="dbo.PR_PurchaseRequestDetail" Member="PR_PurchaseRequestDetails">
    <Type Name="PR_PurchaseRequestDetail">
      <Column Name="PR_PurchaseRequestDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_PurchaseRequestId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Association Name="PR_PurchaseRequest_PR_PurchaseRequestDetail" Member="PR_PurchaseRequest" ThisKey="PR_PurchaseRequestId" OtherKey="PR_PurchaseRequestId" Type="PR_PurchaseRequest" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_Quote" Member="PR_Quotes">
    <Type Name="PR_Quote">
      <Column Name="PR_QuoteId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseQuoteApproved" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PurchaseQuoteApprovedByEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseQuoteApprovedDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Association Name="PR_Quote_PR_QuoteDetail" Member="PR_QuoteDetails" ThisKey="PR_QuoteId" OtherKey="PR_QuoteId" Type="PR_QuoteDetail" />
    </Type>
  </Table>
  <Table Name="dbo.PR_QuoteDetail" Member="PR_QuoteDetails">
    <Type Name="PR_QuoteDetail">
      <Column Name="PR_QuoteDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_QuoteId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="lastPurchaseOffer" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Column Name="LastPriceOffer" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Association Name="PR_Quote_PR_QuoteDetail" Member="PR_Quote" ThisKey="PR_QuoteId" OtherKey="PR_QuoteId" Type="PR_Quote" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_Return" Member="PR_Returns">
    <Type Name="PR_Return">
      <Column Name="PR_ReturnId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ReturnCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ReturnDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Is_OutTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="VehicleNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Association Name="PR_Return_PR_ReturnDetail" Member="PR_ReturnDetails" ThisKey="PR_ReturnId" OtherKey="PR_ReturnId" Type="PR_ReturnDetail" />
    </Type>
  </Table>
  <Table Name="dbo.PR_ReturnArchive" Member="PR_ReturnArchives">
    <Type Name="PR_ReturnArchive">
      <Column Name="archive_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_ReturnId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ReturnCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ReturnDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Is_OutTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="VehicleNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.PR_ReturnDetail" Member="PR_ReturnDetails">
    <Type Name="PR_ReturnDetail">
      <Column Name="PR_ReturnDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_ReturnId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LibraQty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="IsOffer" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="VariableWeight" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="PR_Return_PR_ReturnDetail" Member="PR_Return" ThisKey="PR_ReturnId" OtherKey="PR_ReturnId" Type="PR_Return" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_ReturnDetailArchive" Member="PR_ReturnDetailArchives">
    <Type Name="PR_ReturnDetailArchive">
      <Column Name="archive_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_ReturnDetailId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PR_ReturnId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_ReturnExpense" Member="PR_ReturnExpenses">
    <Type Name="PR_ReturnExpense">
      <Column Name="PR_ReturnExpenseId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PR_ReturnId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ExpenseAccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ExpenseNote" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.PR_Vendor" Member="PR_Vendors">
    <Type Name="PR_Vendor">
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="VenCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VenNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="VenNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Tel" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Mobile" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Address" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ManagerName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="MaxCredit" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="City" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="HasSeparateAccount" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Email" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Fax" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Zip" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Shipping" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="DueDaysCount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PriceLevel" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Representative" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Representative_Job" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="RepFNAme" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="RepFJob" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="TaxCardNumber" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="TaxFileNumber" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="TradeRegistry" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="TaxDepartment" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="IsTaxable" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="GroupId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BankName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="BankAccNum" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="StoppedVendor" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.PR_VendorGroup" Member="PR_VendorGroups">
    <Type Name="PR_VendorGroup">
      <Column Name="VendorGroupId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="VendorGroupCode" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="VGNameAr" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="VGNameEn" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="MaxCredit" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorHasSeparateAccount" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="VendorDefaultAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ParentGroupId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.RS_Flat" Member="RS_Flats">
    <Type Name="RS_Flat">
      <Column Name="FlatId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ProjectId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FlatCode" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="FlatType" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Floor" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="FlatRoom" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NoOfRooms" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="North" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="South" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="East" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="West" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ElectricityCounterNo" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="WaterCounterNo" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="BuiltArea" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="BuiltMeterPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="GroundArea" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="GroundMeterPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="BasementArea" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="BasementMeterPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="GarageArea" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="GarageMeterPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="GardenArea" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="GardenMeterPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="RoofArea" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="RoofMeterPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SupposedArea" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="FlatPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.RS_FlatPhoto" Member="RS_FlatPhotos">
    <Type Name="RS_FlatPhoto">
      <Column Name="PhotoId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="FlatId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Photo" Type="System.Data.Linq.Binary" DbType="Image NOT NULL" CanBeNull="false" UpdateCheck="Never" />
      <Column Name="PhotoDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.RS_FlatRoom" Member="RS_FlatRooms">
    <Type Name="RS_FlatRoom">
      <Column Name="FlatRoomId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Name" Type="System.String" DbType="NVarChar(300) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.RS_FlatType" Member="RS_FlatTypes">
    <Type Name="RS_FlatType">
      <Column Name="FlatTypeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Name" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.RS_InstallmentType" Member="RS_InstallmentTypes">
    <Type Name="RS_InstallmentType">
      <Column Name="InstallmentTypeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Name" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.RS_Project" Member="RS_Projects">
    <Type Name="RS_Project">
      <Column Name="ProjectId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PrjCode" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="PrjNameAr" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="PrjNameEn" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="Address" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="LandArea" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="BuildingArea" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="North" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="South" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="East" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="West" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LicenseNo" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="LicenseAuthority" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="LicenseDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="LicenseRegisterDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ElectricityCounterNo" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="WaterCounterNo" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="CcId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="GroupId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CcNumber" Type="System.String" DbType="VarChar(55)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.RS_ProjectGroup" Member="RS_ProjectGroups">
    <Type Name="RS_ProjectGroup">
      <Column Name="ProjectGroupId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ProjectGroupCode" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="PGNameAr" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="PGNameEn" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="CCId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ParentGroupId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CcNumber" Type="System.String" DbType="VarChar(55)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.RS_ProjectPhoto" Member="RS_ProjectPhotos">
    <Type Name="RS_ProjectPhoto">
      <Column Name="PhotoId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ProjectId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Photo" Type="System.Data.Linq.Binary" DbType="Image NOT NULL" CanBeNull="false" UpdateCheck="Never" />
      <Column Name="PhotoDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.RS_ProjectVendor" Member="RS_ProjectVendors">
    <Type Name="RS_ProjectVendor">
      <Column Name="VendorId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="ProjectId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="Ratio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.RS_SellContract" Member="RS_SellContracts">
    <Type Name="RS_SellContract">
      <Column Name="RS_SellContractId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ContractCode" Type="System.String" DbType="NVarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="RegDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="InstallmentStartDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ProjectId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="FlatId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.RS_SellContractDetail" Member="RS_SellContractDetails">
    <Type Name="RS_SellContractDetail">
      <Column Name="RS_SellContractDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="RS_SellContractId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InstallmentTypeId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ScaleWeight" Member="ScaleWeights">
    <Type Name="ScaleWeight">
      <Column Name="ScaleWeightId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="IsVendor" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="DealerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IsIn" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CarNo" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="TrailerNo" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
      <Column Name="FirstWeight" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="FirstDateTime" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="FirstUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="FirstIsFromScale" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SecondWeight" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SecondDateTime" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SecondUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SecondIsFromScale" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="MoneyAmount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="BillFirstWeight" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="BillSecondWeight" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TransportOffice" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_CustomerGroup" Member="SL_CustomerGroups">
    <Type Name="SL_CustomerGroup">
      <Column Name="CustomerGroupId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CustomerGroupCode" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="CGNameAr" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="CGNameEn" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="MaxCredit" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomersHaveSeparateAccount" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CustomersDefaultAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ParentGroupId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_CustomerPhotos" Member="SL_CustomerPhotos">
    <Type Name="SL_CustomerPhoto">
      <Column Name="PhotoId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CustId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Photo" Type="System.Data.Linq.Binary" DbType="Image NOT NULL" CanBeNull="false" UpdateCheck="Never" />
      <Column Name="PhotoDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="[Desc]" Member="Desc" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.SL_CustomerRegion" Member="SL_CustomerRegions">
    <Type Name="SL_CustomerRegion">
      <Column Name="IdRegion" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="RegionName" Type="System.String" DbType="NVarChar(250) NOT NULL" CanBeNull="false" />
      <Column Name="RegionNameF" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_CustomerVisits" Member="SL_CustomerVisits">
    <Type Name="SL_CustomerVisit">
      <Column Name="idCsVisit" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="idCustomer" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="idDay" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Field" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Telephone" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Collect" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Sell" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="AM" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PM" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_Delivery" Member="SL_Deliveries">
    <Type Name="SL_Delivery">
      <Column Name="idDelivery" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Delivery" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="DeliveryF" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_Group_Customer" Member="SL_Group_Customers">
    <Type Name="SL_Group_Customer">
      <Column Name="GroupId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NameAr" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="NameEn" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ParentId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Code" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.SL_InvoiceArchive" Member="SL_InvoiceArchives">
    <Type Name="SL_InvoiceArchive">
      <Column Name="archiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_InvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="IsArchived" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SalesEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="DeliverDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shipping" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="PurchaseOrderNo" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ExpensesRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalCostPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Is_OutTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="JobOrderId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="VehicleNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Is_Posted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PostDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_StoreForEachRow" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsOffer" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AdvancePaymentRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AdvancePaymentValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="RetentionRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="RetentionValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ValidateUser" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ShiftAdd" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Approved_Invoice" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Approved_UserId" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Delivery" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_InvoiceDetailarchive" Member="SL_InvoiceDetailarchives">
    <Type Name="SL_InvoiceDetailarchive">
      <Column Name="archiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_InvoiceDetailArchiveId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SL_InvoiceArchiveId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CostPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_InvoicePost" Member="SL_InvoicePosts">
    <Type Name="SL_InvoicePost">
      <Column Name="SL_InvoicePostId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PostDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="OpenBalance" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesDeposits" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="OtherDeposits" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Withdraws" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CloseBalance" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.SL_Quote" Member="SL_Quotes">
    <Type Name="SL_Quote">
      <Column Name="SL_QuoteId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="QuoteCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="QuoteDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SalesEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DeliverDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="AttnMr_Job" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ExpensesRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Status" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="AdvancedPayment" Type="System.Decimal" DbType="Decimal(18,3)" CanBeNull="true" />
      <Association Name="SL_Quote_SL_QuoteDetail" Member="SL_QuoteDetails" ThisKey="SL_QuoteId" OtherKey="SL_QuoteId" Type="SL_QuoteDetail" />
    </Type>
  </Table>
  <Table Name="dbo.SL_QuoteDetail" Member="SL_QuoteDetails">
    <Type Name="SL_QuoteDetail">
      <Column Name="SL_QuoteDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_QuoteId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Association Name="SL_Quote_SL_QuoteDetail" Member="SL_Quote" ThisKey="SL_QuoteId" OtherKey="SL_QuoteId" Type="SL_Quote" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_ReturnArchive" Member="SL_ReturnArchives">
    <Type Name="SL_ReturnArchive">
      <Column Name="archive_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_ReturnId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ReturnCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ReturnDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Is_InTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="VehicleNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="HandingValue" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_ReturnDetailArchive" Member="SL_ReturnDetailArchives">
    <Type Name="SL_ReturnDetailArchive">
      <Column Name="archive_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_ReturnDetailId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SL_ReturnId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.SL_SalesOrder" Member="SL_SalesOrders">
    <Type Name="SL_SalesOrder">
      <Column Name="SL_SalesOrderId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SalesOrderCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SalesOrderDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DeliverDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_SalesInvoice" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="AttnMr_Job" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Is_OutTrns" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SalesEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SlQouteId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseOrderNo" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SalesOrderApproved" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SalesOrderApprovedByEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SalesOrderApprovedDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="TechnicianId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ValidateUser" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="SL_SalesOrder_SL_SalesOrderDetail" Member="SL_SalesOrderDetails" ThisKey="SL_SalesOrderId" OtherKey="SL_SalesOrderId" Type="SL_SalesOrderDetail" />
    </Type>
  </Table>
  <Table Name="dbo.SL_SalesOrderArchive" Member="SL_SalesOrderArchives">
    <Type Name="SL_SalesOrderArchive">
      <Column Name="SL_SalesOrderArchiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_SalesOrderId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="SalesOrderCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SalesOrderDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DeliverDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_SalesInvoice" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="AttnMr_Job" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Is_OutTrns" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SalesEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SlQouteId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseOrderNo" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SalesOrderApproved" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SalesOrderApprovedByEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SalesOrderApprovedDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="TechnicianId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ValidateUser" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_SalesOrderDetail" Member="SL_SalesOrderDetails">
    <Type Name="SL_SalesOrderDetail">
      <Column Name="SL_SalesOrderDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_SalesOrderId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="SL_SalesOrder_SL_SalesOrderDetail" Member="SL_SalesOrder" ThisKey="SL_SalesOrderId" OtherKey="SL_SalesOrderId" Type="SL_SalesOrder" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_SalesOrderDetailArchive" Member="SL_SalesOrderDetailArchives">
    <Type Name="SL_SalesOrderDetailArchive">
      <Column Name="SL_SalesOrderDetailArchiveId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_SalesOrderId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ST_Attachments" Member="ST_Attachments">
    <Type Name="ST_Attachment">
      <Column Name="Attach_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Process_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Object_Id" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Path" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="UplaodDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AttachName" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ST_AttenMchin" Member="ST_AttenMchins">
    <Type Name="ST_AttenMchin">
      <Column Name="AttenMchinId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Description" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="AttenMchinIP" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="AttenMchinPort" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AttenMchinCommKey" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ST_BackupDates" Member="ST_BackupDates">
    <Type Name="ST_BackupDate">
      <Column Name="Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="BackupDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ST_BarcodeMatch" Member="ST_BarcodeMatches">
    <Type Name="ST_BarcodeMatch">
      <Column Name="BarcodeMatchId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="VarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="Barcode" Type="System.String" DbType="VarChar(MAX) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ST_BarcodeTemplate" Member="ST_BarcodeTemplates">
    <Type Name="ST_BarcodeTemplate">
      <Column Name="Template_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Template_Name" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="ColumnsCount" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="RowsCount" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="Currency" Type="System.String" DbType="NVarChar(5) NOT NULL" CanBeNull="false" />
      <Column Name="Line_1" Type="System.String" DbType="NVarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="Line_2" Type="System.String" DbType="NVarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="Line_3" Type="System.String" DbType="NVarChar(10) NOT NULL" CanBeNull="false" />
      <Column Name="Line_1_FontSize" Type="System.Double" DbType="Float NOT NULL" CanBeNull="false" />
      <Column Name="Line_2_FontSize" Type="System.Double" DbType="Float NOT NULL" CanBeNull="false" />
      <Column Name="Line_3_FontSize" Type="System.Double" DbType="Float NOT NULL" CanBeNull="false" />
      <Column Name="Line_4_FontSize" Type="System.Double" DbType="Float NOT NULL" CanBeNull="false" />
      <Column Name="Line_1_Visible" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Line_2_Visible" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Line_3_Visible" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Line_4_Visible" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsDefault" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PageName" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="PaperWidth" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PaperHeight" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Margins_Top" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Margins_Bottom" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Margins_Left" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Margins_Right" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Font" Type="System.String" DbType="NVarChar(300)" CanBeNull="true" />
      <Column Name="QtyPrefix" Type="System.String" DbType="NVarChar(10)" CanBeNull="true" />
      <Column Name="BatchPrefix" Type="System.String" DbType="NVarChar(10)" CanBeNull="true" />
      <Column Name="ShowBacrodeText" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PrintBatchinBarcode" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ST_Cars" Member="ST_Cars">
    <Type Name="ST_Car">
      <Column Name="CarId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Name" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="License" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="PlateNo" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="RevenueAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ST_CurrencyChange" Member="ST_CurrencyChanges">
    <Type Name="ST_CurrencyChange">
      <Column Name="CrncChngId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ChngDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Rate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ST_DuePeriod" Member="ST_DuePeriods">
    <Type Name="ST_DuePeriod">
      <Column Name="DuePeriodId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Name" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="[From]" Member="From" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="[To]" Member="To" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ST_IncomeTaxDiscount" Member="ST_IncomeTaxDiscounts">
    <Type Name="ST_IncomeTaxDiscount">
      <Column Name="TaxDisId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="TaxDisName" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="TaxDisAnnualValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ST_IncomeTaxLevel" Member="ST_IncomeTaxLevels">
    <Type Name="ST_IncomeTaxLevel">
      <Column Name="TaxLevelId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="[From]" Member="From" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="[To]" Member="To" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Ratio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.ST_InvoiceBook" Member="ST_InvoiceBooks">
    <Type Name="ST_InvoiceBook">
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceBookName" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="IsTaxable" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PrintFileName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ST_PrintSample" Member="ST_PrintSamples">
    <Type Name="ST_PrintSample">
      <Column Name="SampleId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SampleName" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PrintFileName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ST_Updates" Member="ST_Updates">
    <Type Name="ST_Update">
      <Column Name="UpdateId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="UpdateDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="DBVer" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ScriptVer" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.Mall_AreaContract" Member="Mall_AreaContracts">
    <Type Name="Mall_AreaContract">
      <Column Name="ContractId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AreaSize" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="Floor" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="CostPerMeter" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="AdvancePayment" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsStopped" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ContractDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="AreaValue" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Column Name="AreaNumber" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.Manufacturing" Member="Manufacturings">
    <Type Name="Manufacturing">
      <Column Name="Manf_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Manf_NO" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="StartDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="EndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ProductSupposedTotalCost" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ProductActualTotalCost" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="IsFinished" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ProductStoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="OperationalWaste" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ProcessType" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ManufacturingBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ST_PrintInvoice" Member="ST_PrintInvoices">
    <Type Name="ST_PrintInvoice">
      <Column Name="PrintInvoice_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PrintReceiptPOS" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PrintReceiptSLI" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="MinValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PrintLogo" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PrintCompName" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PrintInvoiceId" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PrintInvoiceDate" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="FooterLine1" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="FooterLine2" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="NoOfPrint" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="scndPrinter" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Employee" Member="HR_Employees">
    <Type Name="HR_Employee">
      <Column Name="EmpId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="EmpName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="EmpBirthDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="EmpAddress" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="EmpGender" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="NationalId" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="MaritalStatus" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="MilitaryStatus" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="Education" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="PassportNo" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="Email" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="Tel" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="Photo" Type="System.Data.Linq.Binary" DbType="Image" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="PayPeriod" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="SalaryBasic" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalaryVariable" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Hour" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Day" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AbsensePenaltyDay" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AbsensePenaltyValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="JobId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DeptId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="GroupId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AppointmentDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="EmpState" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ContractType" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="ContractPeriod" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CashInvCommission" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="EmpFName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CommissionType" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="AccruedAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DeliveryRep" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SalesRep" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="EmpCode" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="OnCreditInvCommision" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountInvCommision" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ExpensesAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="EnrollNumber" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="BirthCountryId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NathionalityId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ReligionId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="QualificationId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IdCardEndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ResidenceNo" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ResidenceStartDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ResidenceEndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ResidenceAccompany" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="PassportCountryId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PassportStartDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="PassportEndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SponsorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InsuranceNo" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="DrivingLicense" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Shift1Id" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Shift2Id" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DelayRuleId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="OverTimeRuleId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BankName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="BankAccountNum" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="AbsenseNoPermPenaltyDay" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AbsenseNoPermPenaltyValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CalcIncomeTax" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="InsuranceDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ContractEndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="DrivingEndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="OfferInvCommision" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="Target" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="Sales_R" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="Collection_R" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="Sales_Minimum_R" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="Sales_Minimum_V" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="Collection_Minimum_R" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="Collection_Minimum_V" Type="System.Decimal" DbType="Decimal(18,3) NOT NULL" CanBeNull="false" />
      <Column Name="InOutRuleId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Shift3Id" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Tel2" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="WorkTel" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="LandLine" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="RelName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Relation" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="RelTel" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="children" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="HomeAddress" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="WorkEntityId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="branchId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="QualYear" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsInsurance" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="IsNetTax" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="absencebenefit" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="InsuranceAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="HR_SubtractAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="hasIncentive" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BankId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_Lab" Member="ImExp_Labs">
    <Type Name="ImExp_Lab">
      <Column Name="LabId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="NameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="NameEn" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="PreiodTime" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Column Name="PeriodType" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ImExp_HalalCertification" Member="ImExp_HalalCertifications">
    <Type Name="ImExp_HalalCertification">
      <Column Name="CertificationId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CertificationCode" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="Code" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CertificationDate" Type="System.DateTime" DbType="Date NOT NULL" CanBeNull="false" />
      <Column Name="CertificationAmount" Type="System.Decimal" DbType="Decimal(18,6) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="BankOrDrawerId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DealerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Column Name="Payment" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PaymentDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="jornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Statues" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_PayDetail" Member="HR_PayDetails">
    <Type Name="HR_PayDetail">
      <Column Name="PayDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="PayId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="BenefitName" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="BenefitCalculation" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="Amount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="BenefitType" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsLoan" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="LoanDetailId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BenefitId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="IsTax" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsInsurance" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CompanyInsuranceCalculation" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="CompanyInsuranceAmount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SubtractType" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Association Name="HR_Pay_HR_PayDetail" Member="HR_Pay" ThisKey="PayId" OtherKey="PayId" Type="HR_Pay" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_User_Categories" Member="IC_User_Categories">
    <Type Name="IC_User_Category">
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
      <Column Name="CategoryId" Type="System.Int32" DbType="Int NOT NULL" IsPrimaryKey="true" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_UOM" Member="IC_UOMs">
    <Type Name="IC_UOM">
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="UOM" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="ECode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_Item" Member="IC_Items">
    <Type Name="IC_Item">
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ItemCode1" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemCode2" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="ItemNameAr" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="ItemNameEn" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="Description" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Category" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Company" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SmallUOM" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="SmallUOMPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="MediumUOM" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="MediumUOMFactor" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="MediumUOMPrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="LargeUOM" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="LargeUOMFactor" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="LargeUOMPrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="ReorderLevel" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="MaxQty" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="MinQty" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ChangePriceMethod" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ChangeSellPrice" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsExpire" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsDeleted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PicPath" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemType" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="mtrxId1" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="mtrxId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="mtrxId3" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="mtrxCode0" Type="System.String" DbType="VarChar(5)" CanBeNull="true" />
      <Column Name="mtrxSprtr0" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="mtrxSprtr1" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="mtrxSprtr2" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="mtrxSprtr3" Type="System.Char" DbType="Char(1)" CanBeNull="true" />
      <Column Name="mtrxParentItem" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="mtrxAttribute1" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="mtrxAttribute2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="mtrxAttribute3" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseDiscRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchaseTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesDiscRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchaseTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="UsedInMarketing" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DfltSellUomIndx" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="DfltPrchsUomIndx" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="MediumUOMFactorDecimal" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="LargeUOMFactorDecimal" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="WarrantyMonths" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="MediumUOMCode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="LargeUOMCode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="calcTaxBeforeDisc" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsPos" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CustomSalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomPurchasesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AudiencePrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Floor" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Room" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Elec" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
      <Column Name="Water" Type="System.Int64" DbType="BigInt" CanBeNull="true" />
      <Column Name="Unitno" Type="System.String" DbType="NVarChar(5)" CanBeNull="true" />
      <Column Name="is_libra" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="VariableWeight" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="IsOffer" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Expiry" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DfltWeightUnit" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SmallIsStopped" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="MediumIsStopped" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="LargeIsStopped" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="ItemECode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ItemEType" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Association Name="IC_Item_IC_CustomerItem" Member="IC_CustomerItems" ThisKey="ItemId" OtherKey="ItemId" Type="IC_CustomerItem" />
      <Association Name="IC_Item_IC_InternationalCode" Member="IC_InternationalCodes" ThisKey="ItemId" OtherKey="ItemId" Type="IC_InternationalCode" />
      <Association Name="IC_Category_IC_Item" Member="IC_Category" ThisKey="Category" OtherKey="CategoryId" Type="IC_Category" IsForeignKey="true" />
      <Association Name="IC_Company_IC_Item" Member="IC_Company" ThisKey="Company" OtherKey="CompanyId" Type="IC_Company" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_Invoice" Member="SL_Invoices">
    <Type Name="SL_Invoice">
      <Column Name="SL_InvoiceId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="IsArchived" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SalesEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="DeliverDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Shipping" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="PurchaseOrderNo" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DueDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="AttnMr" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ExpensesRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalCostPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Is_OutTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="JobOrderId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="VehicleNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Is_Posted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PostDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IsOffer" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Is_StoreForEachRow" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AdvancePaymentRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AdvancePaymentValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="RetentionRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="RetentionValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="HandingValue" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="TransportationValue" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="ShiftAdd" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Approved_Invoice" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Approved_UserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Delivery" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ValidateUser" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="uuid" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Estatus" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="issuerId" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="lastSyncDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="syncDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="EstatusCode" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Association Name="SL_Invoice_SL_InvoiceDetail" Member="SL_InvoiceDetails" ThisKey="SL_InvoiceId" OtherKey="SL_InvoiceId" Type="SL_InvoiceDetail" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Country" Member="HR_Countries">
    <Type Name="HR_Country">
      <Column Name="CountryId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CountryName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ECode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Association Name="HR_Country_SL_Customer" Member="SL_Customers" ThisKey="CountryId" OtherKey="CountryId" Type="SL_Customer" />
      <Association Name="HR_Country_IC_Store" Member="IC_Stores" ThisKey="CountryId" OtherKey="CountryId" Type="IC_Store" />
    </Type>
  </Table>
  <Table Name="dbo.ST_Currency" Member="ST_Currencies">
    <Type Name="ST_Currency">
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="crncName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CurrencyPound1" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPound2" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPound3" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPiaster1" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPiaster2" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPiaster3" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyDigitsCount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ECode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="IsDefualt" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.E_ActivityType" Member="E_ActivityTypes">
    <Type Name="E_ActivityType">
      <Column Name="E_ActivityTypeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Code" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="DescriptionAr" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="DescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.E_TaxSubtype" Member="E_TaxSubtypes">
    <Type Name="E_TaxSubtype">
      <Column Name="E_TaxSubtypeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Code" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="DescriptionAr" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="DescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="TaxableTypeId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.IC_InTrns" Member="IC_InTrns">
    <Type Name="IC_InTrn">
      <Column Name="InTrnsId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InTrnsCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InTrnsDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsVendor" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TotalQty" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="DetailIdBrcodPrntd" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="IsOpenBalance" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_PurchaseInv" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="BookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="IC_InTrn_IC_InTrnsDetail" Member="IC_InTrnsDetails" ThisKey="InTrnsId" OtherKey="InTrnsId" Type="IC_InTrnsDetail" />
    </Type>
  </Table>
  <Table Name="dbo.IC_OutTrns" Member="IC_OutTrns">
    <Type Name="IC_OutTrn">
      <Column Name="OutTrnsId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="OutTrnsCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="OutTrnsDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="TotalPurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeliveryEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IsVendor" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Is_SellInv" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="BookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Association Name="IC_OutTrn_IC_OutTrnsDetail" Member="IC_OutTrnsDetails" ThisKey="OutTrnsId" OtherKey="OutTrnsId" Type="IC_OutTrnsDetail" />
    </Type>
  </Table>
  <Table Name="dbo.HR_Todo" Member="HR_Todos">
    <Type Name="HR_Todo">
      <Column Name="TaskId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="TaskName" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="TaskDetail" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="TaskStatus" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.SL_Customer" Member="SL_Customers">
    <Type Name="SL_Customer">
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CusCode" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CusNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CusNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Tel" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Mobile" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Address" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="MaxCredit" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PriceLevel" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="City" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="HasSeparateAccount" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Email" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Fax" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Zip" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Shipping" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Manager" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="DueDaysCount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SalesEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Representative" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Representative_Job" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="RepFNAme" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="RepFJob" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="CategoryId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxCardNumber" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="TaxFileNumber" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="TradeRegistry" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="TaxDepartment" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="IsTaxable" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IdNumber" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="BankName" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="BankAccNum" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Is_Blocked" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Is_Active" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="GroupId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Rep_Mobile" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Rep_ID" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Delivery" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="IdRegion" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="csType" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CollectEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Street" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Neighborhood" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CountryId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Governate" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="BuildingNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Association Name="HR_Country_SL_Customer" Member="HR_Country" ThisKey="CountryId" OtherKey="CountryId" Type="HR_Country" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.ST_UserLog" Member="ST_UserLogs">
    <Type Name="ST_UserLog">
      <Column Name="UserLogId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ScreenId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Code" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="NotesAr" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ActionDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
    </Type>
  </Table>
  <Table Name="dbo.SL_Return" Member="SL_Returns">
    <Type Name="SL_Return">
      <Column Name="SL_ReturnId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ReturnCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ReturnDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Is_InTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="VehicleNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="HandingValue" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="uuid" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="issuerId" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="lastSyncDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="syncDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Estatus" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="EstatusCode" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="longId" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Association Name="SL_Return_SL_ReturnDetail" Member="SL_ReturnDetails" ThisKey="SL_ReturnId" OtherKey="SL_ReturnId" Type="SL_ReturnDetail" />
    </Type>
  </Table>
  <Table Name="dbo.SL_Add" Member="SL_Adds">
    <Type Name="SL_Add">
      <Column Name="SL_AddId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ReturnCode" Type="System.String" DbType="VarChar(20) NOT NULL" CanBeNull="false" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CustomerId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ReturnDate" Type="System.DateTime" DbType="DateTime NOT NULL" CanBeNull="false" />
      <Column Name="Notes" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Expenses" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Paid" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Remains" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="JornalId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DrawerAccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PayAccountId2" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PayAcc2_Paid" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Is_InTrans" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DeductTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DeductTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="AddTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesEmpId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CrncRate" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CostCenterId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DriverName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="VehicleNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Destination" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="ProcessId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SourceId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LastUpdateDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ScaleWeightSerial" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CustomTaxValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="HandingValue" Type="System.Decimal" DbType="Decimal(18,0)" CanBeNull="true" />
      <Column Name="uuid" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="issuerId" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="lastSyncDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="syncDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Estatus" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="EstatusCode" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="longId" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Association Name="SL_Add_SL_Add_Detail" Member="SL_Add_Details" ThisKey="SL_AddId" OtherKey="SL_AddId" Type="SL_Add_Detail" />
    </Type>
  </Table>
  <Table Name="dbo.SL_ReturnDetail" Member="SL_ReturnDetails">
    <Type Name="SL_ReturnDetail">
      <Column Name="SL_ReturnDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_ReturnId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LibraQty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="IsOffer" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="VariableWeight" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="bonusDiscount" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Association Name="SL_Return_SL_ReturnDetail" Member="SL_Return" ThisKey="SL_ReturnId" OtherKey="SL_ReturnId" Type="SL_Return" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.SlReturnInvoiceDetailSubTaxValue" Member="SlReturnInvoiceDetailSubTaxValues">
    <Type Name="SlReturnInvoiceDetailSubTaxValue">
      <Column Name="id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="ReturnInvoiceDetailId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="esubTypeId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="value" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TaxRatio" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_Add_Detail" Member="SL_Add_Details">
    <Type Name="SL_Add_Detail">
      <Column Name="SL_Add_DetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_AddId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PurchasePrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="VendorId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LibraQty" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="IsOffer" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PricingWithSmall" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="VariableWeight" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="bonusDiscount" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Association Name="SL_Add_SL_Add_Detail" Member="SL_Add" ThisKey="SL_AddId" OtherKey="SL_AddId" Type="SL_Add" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.Sl_Add_DetailSubTaxValue" Member="Sl_Add_DetailSubTaxValues">
    <Type Name="Sl_Add_DetailSubTaxValue">
      <Column Name="id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Sl_AddDetailId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="esubTypeId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="value" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TaxRatio" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_InvoiceDetailSubTaxValue" Member="SL_InvoiceDetailSubTaxValues">
    <Type Name="SL_InvoiceDetailSubTaxValue">
      <Column Name="invoicedetailSubTaxId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="InvoiceDetailId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="esubTypeId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="value" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TaxRatio" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.HR_User" Member="HR_Users">
    <Type Name="HR_User">
      <Column Name="UserId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="UserName" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Password" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="AccessType" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
      <Column Name="IsDeleted" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ActiveNavBarGroup" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="PR_I_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="PR_I_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="PR_R_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="PR_R_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SL_I_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SL_I_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SL_R_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SL_R_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Man_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Man_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Cash_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Cash_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Jrnl_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Jrnl_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ShowMainChart" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="StyleName" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="SL_Q_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="SL_Q_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IC_InTrns_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IC_InTrns_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IC_OutTrns_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IC_OutTrns_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IC_Damage_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IC_Damage_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IC_Transfer_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="IC_Transfer_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="att_clWeekEnd" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="att_clFormalVacation" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="att_clEmpVacation" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="att_clEmpAbsence" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="att_clDelay" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="HR_vacation_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_vacation_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Absence_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Absence_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Delay_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Delay_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_OverTime_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_OverTime_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Reward_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Reward_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Penality_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Penality_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Pay_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Pay_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_CashTransfer_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_CashTransfer_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_PayNote_Still" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Acc_PayNote_Paid" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Acc_PayNote_Rejected" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Acc_PayNote_Overdue" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Acc_ReceiveNote_Still" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Acc_ReceiveNote_Paid" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Acc_ReceiveNote_Rejected" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Acc_ReceiveNote_Overdue" Type="System.String" DbType="VarChar(20)" CanBeNull="true" />
      <Column Name="Acc_Rev_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_Rev_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_Exp_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_Exp_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_RecNote_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_RecNote_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_PayNote_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_PayNote_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="JO_Reg_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="JO_Reg_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="JO_Due_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="JO_Due_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_DebitNote_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_DebitNote_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_CreditNote_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Acc_CreditNote_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Man_QC_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Man_QC_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Mr_InDrctSl_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Mr_InDrctSl_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Loan_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Loan_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Weight_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="Weight_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Eval_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Eval_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Prom_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Prom_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_SponsrChng_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_SponsrChng_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Train_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="HR_Train_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="FocusGridInInvoices" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UseBarcodeScanner" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UseContainsToSearchItems" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="InvoicesUseSearchItems" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="InvoicesNotes" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="UserChangeStore" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserChangeDrawer" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="DefaultStore" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="DefaultDrawer" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DefaultRawItemsStore" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DefaultCustGrp" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UserChangeCostCenterInInv" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="AccessOtherUserTrns" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="StoresShowSellP" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="StoresShowPurchaseP" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ShowPrintPreview" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SubtotalBackcolor" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="UseAccountsTreeInRevExp" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SellPriceUponQtyAvailable" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ColumnChooserAvailable" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SaveGridFilters" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Sell_ShowCrntQty" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SellReorder" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SellUnderMinQty" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SellWithNoBalance" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SellLessThanBuyPrice" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SellCustomerOverCredit" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SL_Invoice_PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="SL_Return_PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DefaultSalesRep" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DefaultSLInv_InvBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DefaultSLRet_InvBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="HideDeliverDate" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HideAgedReceivables" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HideShipTo" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HidePO" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HideSalesEmp" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HidePurchasePrice" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HideItemDiscount" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HideBatchColumn4User" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserEditSalePrice" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserCanWriteDiscount" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserMakeOnCreditInv" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="BuyOverMaxQty" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="BuyVendorOverCredit" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PR_Invoice_PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="PR_Return_PayMethod" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DefaultPRInv_InvBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DefaultPRRet_InvBookId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UserEditTransactionDate" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserPostToGL" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserEditPostedBills" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="EditInClosedPeriod" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="AccSecurityLevel" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="NotesReceivableAlertDays" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="NotesPayableAlertDays" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UserEditNotesReceivable_RecipientEmp" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UserRegisterKnownScale" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ExportReport" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="AutoShowPrPriceChange" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="AlarmWarrantyItemsDays" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AlarmExpiredItemsDays" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ShiftReplace_FromDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ShiftReplace_ToDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="UserCanEditQty" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="AlarmItemsReorder" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="AlarmItemsMinLimit" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ShowLast_Sell_Prices" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="ShowLast_Purchase_Prices" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="CanApproveSalesOrders" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="CanSave_SL_WithOldDate" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PostJournalToGLbyDefault" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="DefaultNotesReceiver" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CanSave_SL_WithUpcomingDate" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CanApproveSl_Invoices" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserShowBank_or_DrawerBalance_in_Pay_Receive" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="defaultVisa" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="MaxUserId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="MaxSalesValue" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="PR_HidePurchasePrice" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DueSL_InvoicesAlertDays" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Can_Approve_InternalRequest" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="CanApprove_PR_Quote" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="CanApprove_PR_Order" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="JO_Alert" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="JO_AlertDays" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="HR_EmployeeSalary" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UserCanAccessToJournalFromSameForm" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="ShowManufactureExpenses" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="CrncId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="E_InvoiceSetter" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_ItemSubTaxes" Member="IC_ItemSubTaxes">
    <Type Name="IC_ItemSubTax">
      <Column Name="IC_ItemSubTaxesId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SubTaxId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Rate" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.E_TaxableType" Member="E_TaxableTypes">
    <Type Name="E_TaxableType">
      <Column Name="E_TaxableTypeId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="Code" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="DescriptionAr" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="DescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ParentTaxId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AccountId" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.SL_InvoiceDetail" Member="SL_InvoiceDetails">
    <Type Name="SL_InvoiceDetail">
      <Column Name="SL_InvoiceDetailId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SL_InvoiceId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="ItemId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="UOMIndex" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UOMId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Qty" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountValue" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="TotalSellPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Expire" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="Batch" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Height" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Length" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="Width" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="ItemDescription" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CostPrice" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="ItemDescriptionEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio2" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="DiscountRatio3" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="SalesTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="StoreId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ManufactureDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="CustomTaxRatio" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="CustomTax" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="Serial2" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LibraQty" Type="System.Decimal" DbType="Decimal(18,3)" CanBeNull="true" />
      <Column Name="kg_Weight_libra" Type="System.Decimal" DbType="Decimal(18,8)" CanBeNull="true" />
      <Column Name="QC" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="Pack" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="bonusDiscount" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="AudiencePrice" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Association Name="SL_Invoice_SL_InvoiceDetail" Member="SL_Invoice" ThisKey="SL_InvoiceId" OtherKey="SL_InvoiceId" Type="SL_Invoice" IsForeignKey="true" />
    </Type>
  </Table>
  <Table Name="dbo.ST_Store" Member="ST_Stores">
    <Type Name="ST_Store">
      <Column Name="ST_Store_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="SellRawMaterial" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="BuyAssembly" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SerialForAssembly" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ExpireDate" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HrLoansAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="HrSalaryExpensesAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AutoInvSerialForStore" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="AutoPostSales" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="HrAccruedSalaryAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="UseLengthDimension" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UseWidthDimension" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UseHeightDimension" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CurrencyPound1" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPound2" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPound3" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPiaster1" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPiaster2" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyPiaster3" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CurrencyDigitsCount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="MyDocReportPathFolder" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SecondReportPath" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Batch" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="DrawersAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BanksAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CustomersAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="NotesReceivableAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InventoryAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="VendorsAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CapitalAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="NotesPayableAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TaxAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ManufacturingExpAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="MerchandisingAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchasesAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchasesReturnAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SalesAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SalesReturnAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="OpenInventoryAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CloseInventoryAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseDiscountAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SalesDiscountAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="FixedAssets" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseAutoSerialBatch" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCount" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="MultiplyDimensions" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="UseMediumUom" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UseLargeUom" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ManufactureProductsOnly" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SalesDeductTaxAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseDeductTaxAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PriceIncludeSalesTax" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ClosePeriodDate" Type="System.DateTime" DbType="Date" CanBeNull="true" />
      <Column Name="RecieveNotesUnderCollectAccId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseAddTaxAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SalesAddTaxAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PriceIncludePurchaseTax" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="CalcPurchaseTaxPerItem" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="isFutureDueDate" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="InvoicesCodeRedundancy" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="AttendanceIs2Shifts" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UseQC" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UseRegisteredNotes" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="DebitNoteAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CreditNoteAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="MainCurrencyName" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CostOfSoldGoodsAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="LetterOfCreditAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SalesOrderReserveGood" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="DepreciationAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RealStateSellRvnuAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DelayOnAttendTimeOnly" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="GenerateNewInvCodeOnSave" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="ExpensesAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RevenueAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="GulfHRAvailable" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IncomeTaxAvailable" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="EncodeItemsPerCategory" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="InvoiceWorkflow" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="EncodePrInvPerVendor" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PrintBarcodePerInventory" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="BarcodePrefix" Type="System.String" DbType="VarChar(2)" CanBeNull="true" />
      <Column Name="BarcodeItemCodeLength" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BarcodeQtyLength" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BarcodeBatchCodeLength" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="GroupPOSItems" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="UseBarcodeMatchTable" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Serial" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="BatchNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="BatchNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SerialNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SerialNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DescNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DescNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DescEnNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DescEnNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="PrInvoiceWorkflow" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SellAsRestaurant" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="SalesEmpMandatory" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsStoreOnEachSellRecord" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="IsStoreOnEachPurchRecord" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="MustApproveSalesOrder" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="PriorityNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesEmployeeNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DeliveryEmployeeNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DepartmentNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="StatusNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="PriorityNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesEmployeeNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DeliveryEmployeeNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DepartmentNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="StatusNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CustomTaxAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AttachmentPath" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Mtrx_Color" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Mtrx_Liter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Mtrx_Geir" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ExpireDateNameEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ExpireDateNameAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="VisaAccount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AdvancePaymentAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RetentionAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostDistributionMethod" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Serial2NameAr" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="Serial2NameEn" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="bcIdentifier" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="bcItemCount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="bcScaleCount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="hasChkSum" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="bcScalePrice" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="isPrice" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="Libra_UOM_Id" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="KG_PR_UOM_Id" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="KG_SL_UOM_Id" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="KG_PR_Factor" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="KG_SL_Factor" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="LaborRevenue" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="TransferRevenue" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PiecesCountNameAr" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="PiecesCountNameEn" Type="System.String" DbType="NVarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="TotalMonthDays_HR" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SlInvoice_mustB_Approved" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="LastEvaluationDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ch_Authorize" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="ExpireDisplay" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="CapitalProfitLoss" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="chk_CsTypeValidation" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="IsMaxSalesOrder" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="IsMaxSalesInvoice" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="ExcludeLeavesN_GL" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="NetSalaryTax" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Column Name="PackCount" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="OutstandingRecieveNote" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="intermediateInventoryAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="MinInsureSalary" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="MaxInsureSalary" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="companyShare" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="EmpShare" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="ExemptionRatio" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="TotalSalaryType" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="EgyptionLaw" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="intermediate_PR_InventoryAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="InsuranceAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="BusinessGainAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="NetTaxAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="HR_SubtractAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CustodyAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ReturnCostACC" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="MustApprovePurchaseOrder" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="MustApprovePurchaseQuote" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="CompanyAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CompanyEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CategoryAr" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CategoryEn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesOrderforClient" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="UseLastCostPrice" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="TotalMallSize" Type="System.Decimal" DbType="Decimal(18,6)" CanBeNull="true" />
      <Column Name="ACC_VacationAcount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ACC_AbsenceAcount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ACC_PenaltyAcount" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="RoundValue" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="DocumentThreshold" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
      <Column Name="E_AllowMoreThanTax" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="DefaultRoundingPoints" Type="System.Byte" DbType="TinyInt" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.ST_CompanyInfo" Member="ST_CompanyInfos">
    <Type Name="ST_CompanyInfo">
      <Column Name="Company_Id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="CmpNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CmpNameEn" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CmpAddress" Type="System.String" DbType="NVarChar(100) NOT NULL" CanBeNull="false" />
      <Column Name="CmpCity" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CmpCountry" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CmpTel" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="CmpMobile" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="Logo" Type="System.Data.Linq.Binary" DbType="Image" CanBeNull="true" UpdateCheck="Never" />
      <Column Name="CommercialBook" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="TaxCard" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="MngrName" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="MngrAdress" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="MngrTel" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="MngrMobile" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="DoPeriodicBackup" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="BackupPath" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="NumOfDays" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Code" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="startDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ServerName" Type="System.String" DbType="VarChar(15)" CanBeNull="true" />
      <Column Name="IPAddresses" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Manufacturing" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Accounting" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Inventory" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="HR" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="POS" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Processes" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="PriceList" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Tax" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemMatrix" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Contract" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="EgyptPharmacyTax" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="WoodTrade" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="OfflinePostToGL" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="InvoicePostToStore" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="JobOrder" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesOrder" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="FiscalYearStartDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="FiscalYearEndDate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ModelGlobalPOS" Type="System.String" DbType="NVarChar(MAX) NOT NULL" CanBeNull="false" />
      <Column Name="Marketting" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Currencies" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Checks" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="StockIsPeriodic" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="LetterOfCredit" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Bascol" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesMan" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="RealState" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="FpDependOnInOut" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="Payslip" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="ItemsPosting" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="PrInvoicePostToStore" Type="System.String" DbType="VarChar(MAX)" CanBeNull="true" />
      <Column Name="Cars" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Is_Laundry" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Iban" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Libra" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="SalesOnly" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="Mall" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Imp_exp" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Shareholder" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="FA" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="Pay_Rec" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ActivityType" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="ClientId" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ClientSecret" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="DonglePIN" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="ServiceProviderSignature" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="E_invoiceAvailable" Type="System.String" DbType="NVarChar(200)" CanBeNull="true" />
      <Column Name="InvoiceDate_ValidationDays" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PublicKey" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="TokenSerial" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="LibraryPath" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="PIn" Type="System.String" DbType="NVarChar(MAX)" CanBeNull="true" />
      <Column Name="CertificateCompanyType" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="AddItemNameToDescription" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.E_RecievedDocument" Member="E_RecievedDocuments">
    <Type Name="E_RecievedDocument">
      <Column Name="id" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="uuid" Type="System.String" DbType="VarChar(200) NOT NULL" CanBeNull="false" />
      <Column Name="publicUrl" Type="System.String" DbType="VarChar(600)" CanBeNull="true" />
      <Column Name="typeName" Type="System.String" DbType="VarChar(50)" CanBeNull="true" />
      <Column Name="typeNameEn" Type="System.String" DbType="VarChar(400)" CanBeNull="true" />
      <Column Name="typeNameAr" Type="System.String" DbType="NVarChar(400)" CanBeNull="true" />
      <Column Name="issuerName" Type="System.String" DbType="NVarChar(400) NOT NULL" CanBeNull="false" />
      <Column Name="dateTimeReceived" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="dateTimeIssued" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="total" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="net" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="totalDiscount" Type="System.Decimal" DbType="Decimal(20,6) NOT NULL" CanBeNull="false" />
      <Column Name="status" Type="System.String" DbType="VarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="totalSales" Type="System.Decimal" DbType="Decimal(20,6)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.IC_Store" Member="IC_Stores">
    <Type Name="IC_Store">
      <Column Name="StoreId" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsPrimaryKey="true" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="StoreCode" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="StoreNameAr" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="StoreNameEn" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="Address" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="Tel" Type="System.String" DbType="NVarChar(50) NOT NULL" CanBeNull="false" />
      <Column Name="PurchaseAccount" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="PurchaseReturnAccount" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SellAccount" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="SellReturnAccount" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="OpenInventoryAccount" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CloseInventoryAccount" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="CostMethod" Type="System.Byte" DbType="TinyInt NOT NULL" CanBeNull="false" />
      <Column Name="ManagerName" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="ParentId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostOfSoldGoodsAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="SalesDiscountAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="PurchaseDiscountAcc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="CostCenter" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="pricelistId" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="Mobile" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="IsStopped" Type="System.Boolean" DbType="Bit" CanBeNull="true" />
      <Column Name="ECode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="CountryId" Type="System.Int32" DbType="Int NOT NULL" CanBeNull="false" />
      <Column Name="Governate" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="RegionCity" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="Street" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="BuildingNumber" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Association Name="HR_Country_IC_Store" Member="HR_Country" ThisKey="CountryId" OtherKey="CountryId" Type="HR_Country" IsForeignKey="true" />
    </Type>
  </Table>
</Database>