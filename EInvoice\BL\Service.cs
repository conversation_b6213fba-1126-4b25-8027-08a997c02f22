﻿#region Using directives
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
#endregion

namespace EInvoice.BL
{
    public class Service
    {
        public HttpResponseMessage ExecuteClientPost(string BaseUrl, string url, string accessToken, object model = null)
        {
            //accessToken = "eyJhbGciOiJSUzI1NiIsImtpZCI6IjBGOTkyNkZFQTUyOTgxRjZDMjBENUMzNUQ0NjUxMzAzQ0QzQzBFMzIiLCJ0eXAiOiJhdCtqd3QiLCJ4NXQiOiJENWttX3FVcGdmYkNEVncxMUdVVEE4MDhEakkifQ.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SYcbpzBG-G1wIWTJsPvMxDACOjegukVjG2gd3RDXAf5eUCwkf8WcOE7iw5KNIn2xk-2gsyUhXW3eIkvDVMeItxg2hxkXx3fgQRY_a6wQ4yVqn75FEtDqcMn6glJCqLKM-ROzKAf8dD23SpxS1PHGapLNP8ZcPW2903iVewQ3VmsEUNVr-EjDMu6GBfsRYtTmU0SE2teswlm7lPqAuCVjWfE8KAGBCAJzW2VQ77rlln82A7n4MVz6Xuh8_3C5MztVHgZKqEVVqW4KITzNKsbC0bE-4Si4m3q9g6OcwHzvOGAazpXB6oM5scinyZNdbOtsFQZyCh5g_5Cm1-MVVJPl4w";
            //model = "{\"documents\":[{\"issuer\":{\"address\":{\"branchID\":\"0\",\"country\":\"EG\",\"governate\":\"القاهرة\",\"regionCity\":\"القاهرة\",\"street\":\"شرمسيسمبنىبنكالنيل\",\"buildingNumber\":\"35\",\"postalCode\":null,\"floor\":null,\"room\":null,\"landmark\":null,\"additionalInformation\":null},\"type\":\"B\",\"id\":\"*********\",\"name\":\"شركهالنيلللتصنيعالزراعي\"},\"receiver\":{\"address\":{\"branchID\":null,\"country\":\"EG\",\"governate\":\"القليوبية\",\"regionCity\":\"العبور\",\"street\":\"1\",\"buildingNumber\":\"1\",\"postalCode\":null,\"floor\":null,\"room\":null,\"landmark\":null,\"additionalInformation\":null},\"type\":\"P\",\"id\":\"\",\"name\":\"محمد\"},\"documentType\":\"I\",\"documentTypeVersion\":\"1.0\",\"dateTimeIssued\":\"2022-06-06T04:52:34Z\",\"taxpayerActivityCode\":\"1010\",\"internalID\":\"23\",\"purchaseOrderReference\":\"\",\"purchaseOrderDescription\":\"\",\"salesOrderReference\":\"\",\"salesOrderDescription\":\"\",\"proformaInvoiceNumber\":\"\",\"payment\":{\"bankName\":\"\",\"bankAddress\":\"\",\"bankAccountNo\":\"\",\"bankAccountIBAN\":\"\",\"swiftCode\":\"\",\"terms\":\"\"},\"delivery\":{\"approach\":\"\",\"packaging\":\"\",\"dateValidity\":\"\",\"exportPort\":\"\",\"countryOfOrigin\":\"EG\",\"grossWeight\":0.0,\"netWeight\":0.0,\"terms\":\"\"},\"invoiceLines\":[{\"description\":\"تتراباكجوافه\",\"itemType\":\"GS1\",\"itemCode\":\"*************\",\"unitType\":\"BOX\",\"quantity\":3.000,\"internalCode\":\"*************\",\"salesTotal\":300.00000,\"total\":300.00000,\"valueDifference\":0.0,\"totalTaxableFees\":0.0,\"netTotal\":300.00000,\"itemsDiscount\":0.00000,\"unitValue\":{\"currencySold\":\"EGP\",\"amountEGP\":100.00000,\"amountSold\":null,\"currencyExchangeRate\":0.0},\"discount\":{\"rate\":0.0,\"amount\":0.00000},\"taxableItems\":[]}],\"totalDiscountAmount\":0.00000,\"totalSalesAmount\":300.00000,\"netAmount\":300.00000,\"taxTotals\":[],\"totalAmount\":300.00000,\"extraDiscountAmount\":0.00000,\"totalItemsDiscountAmount\":0.00000,\"signatures\":[{\"signatureType\":\"I\",\"value\":\"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\"}]}]}";
            MyHelper.Utilities.writeToFile(Environment.NewLine + "Json Object: "+ model, "");
            MyHelper.Utilities.writeToFile(Environment.NewLine + "Enter ExecuteClientPost: ", "");
            MyHelper.Utilities.writeToFile(Environment.NewLine + "Enter ExecuteClientPost: ", "");
            MyHelper.Utilities.writeToFile(Environment.NewLine + "BaseUrl: "+BaseUrl, "");
            MyHelper.Utilities.writeToFile(Environment.NewLine + "Url: " + url, "");

            HttpClient webApiClient = new HttpClient
            {
                BaseAddress = new Uri(BaseUrl)
            };

            webApiClient.Timeout = TimeSpan.FromHours(2);
            webApiClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);
            webApiClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            var content = new StringContent(model.ToString(), Encoding.UTF8, "application/json");
            UriBuilder builder = new UriBuilder(BaseUrl);
            builder.Path = "/api/v1.0/documentsubmissions";
            HttpResponseMessage response = webApiClient.PostAsync(builder.Uri, content).Result;
            //webApiClient.DefaultRequestHeaders.Clear();
            //var response = webApiClient.PostAsJsonAsync(url, model).Result;
            var jsonString = response.Content.ReadAsStringAsync().Result;



            MyHelper.Utilities.writeToFile(Environment.NewLine + "jsonString: " + jsonString, "");
            return response;
        }

    }
}
