﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{F862CE1A-2DA2-4148-8A18-1DF544113F03}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DAL</RootNamespace>
    <AssemblyName>DAL</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <PlatformTarget>AnyCPU</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>..\..\..\unused code.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\..\..\unused code.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>..\..\..\unused code.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BL\HelperAcc.cs" />
    <Compile Include="BL\HrHelper.cs" />
    <Compile Include="BL\MyHelper.cs" />
    <Compile Include="Config.cs" />
    <Compile Include="ERP.cs">
      <DependentUpon>ERP.dbml</DependentUpon>
    </Compile>
    <Compile Include="ERP1.designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ERP.dbml</DependentUpon>
    </Compile>
    <Compile Include="Res\ResAccAr.Designer.cs">
      <DependentUpon>ResAccAr.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResAccEn.Designer.cs">
      <DependentUpon>ResAccEn.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResAr.Designer.cs">
      <DependentUpon>ResAr.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResEn.Designer.cs">
      <DependentUpon>ResEn.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResHRAr.Designer.cs">
      <DependentUpon>ResHRAr.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResHREn.Designer.cs">
      <DependentUpon>ResHREn.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResICAr.Designer.cs">
      <DependentUpon>ResICAr.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResICEn.Designer.cs">
      <DependentUpon>ResICEn.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResPrAr.Designer.cs">
      <DependentUpon>ResPrAr.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResPrEn.Designer.cs">
      <DependentUpon>ResPrEn.resx</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Res\ResRptAr.Designer.cs">
      <DependentUpon>ResRptAr.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResRptEn.Designer.cs">
      <DependentUpon>ResRptEn.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResRSAr.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ResRSAr.resx</DependentUpon>
    </Compile>
    <Compile Include="Res\ResRSEn.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ResRSEn.resx</DependentUpon>
    </Compile>
    <Compile Include="Res\ResSLAr.Designer.cs">
      <DependentUpon>ResSLAr.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Res\ResSLEn.Designer.cs">
      <DependentUpon>ResSLEn.resx</DependentUpon>
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="Shared.cs" />
    <Compile Include="Utilities.cs" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{3259AA49-8AA1-44D3-9025-A0B520596A8C}" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Res\ResAccAr.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResAccAr.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResAccEn.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResAccEn.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResAr.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResAr.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResEn.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResEn.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResHRAr.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResHRAr.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResHREn.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResHREn.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResICAr.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResICAr.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResICEn.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResICEn.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResPrAr.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResPrAr.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResPrEn.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResPrEn.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResRptAr.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResRptAr.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResRptEn.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResRptEn.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResRSAr.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResRSAr.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResRSEn.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResRSEn.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResSLAr.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResSLAr.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Res\ResSLEn.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>ResSLEn.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="ERP.dbml">
      <SubType>Designer</SubType>
      <Generator>MSLinqToSQLGenerator</Generator>
      <LastGenOutput>ERP1.designer.cs</LastGenOutput>
    </None>
    <None Include="ERP.dbml.layout">
      <DependentUpon>ERP.dbml</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Properties\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>