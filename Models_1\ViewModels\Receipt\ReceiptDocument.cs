﻿using System;
using System.Collections.Generic;

namespace Models_1.ViewModels.Receipt
{

    // Base class representing a receipt
    public abstract class BaseReceipt
    {
     
        // Contains information about the receipt header
        public Header Header { get; set; }

        public DocumentType DocumentType { get; protected set; }

        // Contains information about the seller
        public Seller Seller { get; set; }

        // Contains information about the buyer
        public Buyer Buyer { get; set; }

        // Contains a list of items included in the receipt
        public List<ItemData> ItemData { get; set; }

        // Total sum of all sales on the receipt
        public decimal TotalSales { get; set; }

        // Sum of all discount amounts applied to commercial items
        public decimal? TotalCommercialDiscount { get; set; }

        // Sum of all discount amounts applied to items
        public decimal? TotalItemsDiscount { get; set; }

        // Additional discounts applied at the receipt level
        public List<Discount> ExtraReceiptDiscountData { get; set; }

        // Total net amount after discounts
        public decimal NetAmount { get; set; }

        // Additional fees added to the total amount (optional)
        public decimal? FeesAmount { get; set; }

        // Total amount of the receipt
        public decimal TotalAmount { get; set; }

        // Contains information about the total taxes applied
        public TaxTotals TaxTotals { get; set; }

        // Monetary amount added to the total receipt amount for final adjustments (optional)
        public decimal? Adjustment { get; set; }

        // Contains information about the contractor (optional)
        public Contractor Contractor { get; set; }

        // Contains information about the beneficiary (optional)
        public Beneficiary Beneficiary { get; set; }
    }

    // Class representing a sales receipt (inherits from BaseReceipt)
    public class SalesReceipt : BaseReceipt
    {
        // Sets DocumentType and TypeVersion for a sales receipt
        public SalesReceipt()
        {
            DocumentType = new DocumentType
            {
                ReceiptType = "s", // Indicates it's a sales receipt
                TypeVersion = "1.2"  // Version of the receipt type
            };
        }
    }

    // Class representing a return receipt 
    public class ReturnReceipt : BaseReceipt
    {
        // Reference to the sale receipt
        public string ReferenceUUID { get; set; }

        // Reference to the old UUID for corrected return receipts (optional)
        public string ReferenceOldUUID { get; set; }

        // Sets DocumentType and TypeVersion for a return receipt
        public ReturnReceipt()
        {

            DocumentType = new DocumentType
            {
                ReceiptType = "r", // Indicates it's a return receipt
                TypeVersion = "1.2", // Version of the receipt type
            };
          
        }
    }
    
    public class DocumentType
    {
        // Indicates the type of the receipt (sales or return)
        public string ReceiptType { get; set; }

        // Indicates the version of the receipt type
        public string TypeVersion { get; set; }
    }

    // Contains information about the receipt header
    public class Header
    {
        // Date and time of issuance of the receipt
        public DateTime DateTimeIssued { get; set; }

        // Unique receipt number per branch within the same submission
        public string ReceiptNumber { get; set; }

        // Unique key on the system level added by the taxpayer
        public string UUID { get; set; }

        // Reference to previous receipt
        public string PreviousUUID { get; set; }

        // Reference to the previous sales order for informational purposes (optional)
        public string SOrderNameCode { get; set; }

        // Order Delivery Mode Codes (optional)
        public string OrderDeliveryMode { get; set; }

        // Total weight of the goods delivered in kilograms (optional)
        public decimal? GrossWeight { get; set; }

        // Net weight of the goods delivered in kilograms (optional)
        public decimal? NetWeight { get; set; }

        // Indicates the use reason for the document (optional)
        public string DocumentUseReason { get; set; }

        // Issuance date and time of the sales receipt in UTC (optional)
        public DateTime? SalesIssuedDateTime { get; set; }
    }

    // Contains information about the seller
    public class Seller
    {
        // Registration number of the seller
        public string RIN { get; set; }

        // Registration name of the company
        public string CompanyTradeName { get; set; }

        // Code of the branch as registered with the tax authority
        public string BranchCode { get; set; }

        // Address of the branch
        public BranchAddress BranchAddress { get; set; }

        // POS serial number
        public string DeviceSerialNumber { get; set; }

        // Syndicate license number (optional)
        public string SyndicateLicenseNumber { get; set; }

        // Activity code
        public string ActivityCode { get; set; }
    }

    // Contains information about the buyer
    public class Buyer
    {
        // Type of the buyer
        public string Type { get; set; }

        // ID of the buyer
        public string ID { get; set; }

        // Name of the buyer (optional)
        public string Name { get; set; }

        // Mobile number of the buyer (optional)
        public string MobileNumber { get; set; }

        // Reference to a payment number for which the receipt was issued (optional)
        public string PaymentNumber { get; set; }
    }

    // Contains information about an item on the receipt
    public class ItemData
    {
        // Internal code of the product
        public string InternalCode { get; set; }

        // Description of the item being sold
        public string Description { get; set; }

        // Coding schema used to encode the item type
        public string ItemType { get; set; }

        // Code of the goods or services item being sold
        public string ItemCode { get; set; }

        // Code of the unit type used from the code table of the measures
        public string UnitType { get; set; }

        // Number of units of the defined unit type being sold
        public decimal Quantity { get; set; }

        // Cost per quantity of the product
        public decimal UnitPrice { get; set; }

        // Total amount for the receipt line after applying discount
        public decimal NetSale { get; set; }

        // Total amount for the receipt line considering quantity and unit price
        public decimal TotalSale { get; set; }

        // Total amount for the receipt line after adding all pricing items, taxes, removing discounts
        public decimal Total { get; set; }
    }

    // Contains information about a discount applied to an item
    public class Discount
    {
        // Amount of the discount applied
        public decimal Amount { get; set; }

        // Description of the discount applied
        public string Description { get; set; }

        // Tax rate applied for the invoice line (optional)
        public decimal? Rate { get; set; }
    }

    // Contains information about a taxable item
    public class TaxableItem
    {
        // Type of tax applied
        public string TaxType { get; set; }

        // Amount of the tax applied
        public decimal Amount { get; set; }

        // Subtype of the tax types (optional)
        public string SubType { get; set; }

        // Tax rate applied for the invoice line (optional)
        public decimal? Rate { get; set; }
    }

    // Contains information about the total taxes applied
    public class TaxTotals
    {
        // Type of tax applied
        public string TaxType { get; set; }

        // Sum of all amounts of given tax in all line items
        public decimal Amount { get; set; }
    }

    // Contains information about a contractor
    public class Contractor
    {
        // Name of the Contractor (optional)
        public string Name { get; set; }

        // Amount that the contractor is paying from the receipt total amount (optional)
        public decimal? Amount { get; set; }

        // Rate that the contractor is paying from the receipt total amount (optional)
        public decimal? Rate { get; set; }
    }

    // Contains information about a beneficiary
    public class Beneficiary
    {
        // Amount that the beneficiary or buyer is paying from the receipt total amount (optional)
        public decimal? Amount { get; set; }

        // Rate that the beneficiary or buyer is paying from the receipt total amount (optional)
        public decimal? Rate { get; set; }
    }

    // Contains information about the branch address
    public class BranchAddress
    {
        // Country represented by ISO-3166-2 2 symbol code
        public string Country { get; set; }

        // Governorate information
        public string Governorate { get; set; }

        // Region and city information
        public string RegionCity { get; set; }

        // Street information
        public string Street { get; set; }

        // Building information
        public string BuildingNumber { get; set; }

        // Postal code (optional)
        public string PostalCode { get; set; }

        // The floor number (optional)
        public string Floor { get; set; }

        // The room/flat number in the floor (optional)
        public string Room { get; set; }

        // Nearest landmark to the address (optional)
        public string Landmark { get; set; }

        // Any additional information to the address (optional)
        public string AdditionalInformation { get; set; }
    }
}
