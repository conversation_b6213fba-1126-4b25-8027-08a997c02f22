﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="txtFrom" xml:space="preserve">
    <value> من </value>
  </data>
  <data name="txtFromDate" xml:space="preserve">
    <value> من تاريخ </value>
  </data>
  <data name="MsgNameExist" xml:space="preserve">
    <value>هذا الاسم مسجل من قبل</value>
  </data>
  <data name="MsgIncorrectData" xml:space="preserve">
    <value>تأكد من صحة البيانات</value>
  </data>
  <data name="MsgDataModified" xml:space="preserve">
    <value>لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا</value>
  </data>
  <data name="MsgTError" xml:space="preserve">
    <value>خطأ</value>
  </data>
  <data name="MsgTInfo" xml:space="preserve">
    <value>معلومة</value>
  </data>
  <data name="MsgPrvEdit" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية تعديل هذا البيان</value>
  </data>
  <data name="MsgPrvNew" xml:space="preserve">
    <value>عفوا، انت لاتملك صلاحية اضافة بيان جديد</value>
  </data>
  <data name="MsgTQues" xml:space="preserve">
    <value>سؤال</value>
  </data>
  <data name="MsgSave" xml:space="preserve">
    <value>تم الحفظ بنجاح</value>
  </data>
  <data name="MsgTWarn" xml:space="preserve">
    <value>تحذير</value>
  </data>
  <data name="txtTo" xml:space="preserve">
    <value> الى </value>
  </data>
  <data name="txtToDate" xml:space="preserve">
    <value> الى تاريخ </value>
  </data>
  <data name="MsgDel" xml:space="preserve">
    <value>تم الحذف بنجاح</value>
  </data>
  <data name="txtDrawer" xml:space="preserve">
    <value> خزينه </value>
  </data>
  <data name="MsgDelRow" xml:space="preserve">
    <value>حذف صف ؟</value>
  </data>
  <data name="MsgNumExist" xml:space="preserve">
    <value>هذا الرقم مسجل من قبل</value>
  </data>
  <data name="MsgAskDel" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد حذف</value>
  </data>
  <data name="txtAssembly" xml:space="preserve">
    <value>تام</value>
  </data>
  <data name="no" xml:space="preserve">
    <value>لا</value>
  </data>
  <data name="yes" xml:space="preserve">
    <value>نعم</value>
  </data>
  <data name="MsgEmpNoAccount" xml:space="preserve">
    <value>لايوجد حساب لهذا الموظف</value>
  </data>
  <data name="txtDivorced" xml:space="preserve">
    <value>مطلق</value>
  </data>
  <data name="txtFemale" xml:space="preserve">
    <value>أنثي</value>
  </data>
  <data name="txtFortnightly" xml:space="preserve">
    <value>كل اسبوعين</value>
  </data>
  <data name="txtHourly" xml:space="preserve">
    <value>بالساعة</value>
  </data>
  <data name="txtMale" xml:space="preserve">
    <value>ذكر</value>
  </data>
  <data name="txtMarried" xml:space="preserve">
    <value>متزوج</value>
  </data>
  <data name="txtMonthly" xml:space="preserve">
    <value>شهري</value>
  </data>
  <data name="txtPerTask" xml:space="preserve">
    <value>بالقطعة</value>
  </data>
  <data name="txtSingle" xml:space="preserve">
    <value>أعزب</value>
  </data>
  <data name="txtWeekly" xml:space="preserve">
    <value>بالاسبوع</value>
  </data>
  <data name="txtWidowed" xml:space="preserve">
    <value>أرمل</value>
  </data>
  <data name="txtHour" xml:space="preserve">
    <value> ساعة </value>
  </data>
  <data name="MsgSysAdminDel" xml:space="preserve">
    <value>عفوا, لايمكن حذف مدير النظام</value>
  </data>
  <data name="MsgUserDel" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا المستخدم</value>
  </data>
  <data name="txtFullAccess" xml:space="preserve">
    <value>دخول كامل</value>
  </data>
  <data name="txtLimitedAccess" xml:space="preserve">
    <value>دخول مخصص</value>
  </data>
  <data name="txtNoAccess" xml:space="preserve">
    <value>الدخول ممنوع</value>
  </data>
  <data name="txtOther" xml:space="preserve">
    <value>أخري</value>
  </data>
  <data name="txtSysAdmin" xml:space="preserve">
    <value>مدير النظام</value>
  </data>
  <data name="txtVacAnnual" xml:space="preserve">
    <value>اعتيادي</value>
  </data>
  <data name="txtVacCasual" xml:space="preserve">
    <value>عارضه</value>
  </data>
  <data name="Continue" xml:space="preserve">
    <value> هل تريد الاستمرار </value>
  </data>
  <data name="MsgDelAbsence" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا الغياب</value>
  </data>
  <data name="ValAbsenceDats" xml:space="preserve">
    <value>تاريخ نهاية الغياب يجب ان يكون اكبر من تاريخ بدء الغياب</value>
  </data>
  <data name="ValAbsenceEndDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ نهاية الغياب</value>
  </data>
  <data name="ValAbsenceExist" xml:space="preserve">
    <value>يوجد غياب مسجل للموظف في هذه الفتره</value>
  </data>
  <data name="ValAbsenceStartDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ بدء الغياب</value>
  </data>
  <data name="ValEmp" xml:space="preserve">
    <value>يجب اختيار الموظف</value>
  </data>
  <data name="ValLastPaySlip" xml:space="preserve">
    <value> اخر راتب استلمه الموظف كان حتي تاريخ </value>
  </data>
  <data name="ValVacationExist" xml:space="preserve">
    <value>يوجد اجازات للموظف في هذه الفتره</value>
  </data>
  <data name="MsgBenefitRecheck" xml:space="preserve">
    <value>برجاء مراجعة هذا البند للموظفين</value>
  </data>
  <data name="MsgDelBenefit" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا البند</value>
  </data>
  <data name="MsgDelBenefitDenied" xml:space="preserve">
    <value>عفوا, لايمكن حذف البند, فهو مخصص بالفعل لموظفين</value>
  </data>
  <data name="ValBenefitName" xml:space="preserve">
    <value>يجب تسجيل اسم البند</value>
  </data>
  <data name="MsgAbsenceExistAsk" xml:space="preserve">
    <value>يوجد غياب مسجل للموظف في هذا اليوم, هل تريد الاستمرار</value>
  </data>
  <data name="MsgDelayExist" xml:space="preserve">
    <value>يوجد تأخير مسجل للموظف في هذا اليوم</value>
  </data>
  <data name="MsgDelDelay" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا التأخير</value>
  </data>
  <data name="MsgDelDept" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا القسم</value>
  </data>
  <data name="MsgDelVacation" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذه الاجازه</value>
  </data>
  <data name="MsgDeptDelChilds" xml:space="preserve">
    <value>يجب حذف الأقسام الفرعية اولا</value>
  </data>
  <data name="MsgDeptMoveEmps" xml:space="preserve">
    <value>لايمكن حذف القسم فهو مستخدم بالفعل</value>
  </data>
  <data name="MsgDeptName" xml:space="preserve">
    <value>يرجى إدخال اسم القسم</value>
  </data>
  <data name="MsgFormalExist" xml:space="preserve">
    <value>يوجد اجازات رسميه في هذه الفتره</value>
  </data>
  <data name="MsgVacationExistAsk" xml:space="preserve">
    <value>يوجد اجازه مسجلة للموظف في هذا اليوم, هل تريد الاستمرار</value>
  </data>
  <data name="ValDates" xml:space="preserve">
    <value>تاريخ البدايه يجب ان يكون اصغر من تاريخ النهايه</value>
  </data>
  <data name="ValDate" xml:space="preserve">
    <value>يجب تسجيل التاريخ</value>
  </data>
  <data name="ValDelayPeriod" xml:space="preserve">
    <value>يجب تسجيل مدة التاخير</value>
  </data>
  <data name="ValVacationName" xml:space="preserve">
    <value>يجب تسجيل اسم الأجازة</value>
  </data>
  <data name="MsgDelOvertime" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا الاضافي</value>
  </data>
  <data name="MsgGroupDel" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذه المجموعه</value>
  </data>
  <data name="MsgGroupMoveEmp" xml:space="preserve">
    <value>يجب نقل الموظفين بهذه المجموعه اولا</value>
  </data>
  <data name="MsgJobDel" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذه الوظيفه</value>
  </data>
  <data name="MsgJobDelMoveEmp" xml:space="preserve">
    <value>لايمكن حذف الوظيفة فهي مستخدمة بالفعل</value>
  </data>
  <data name="MsgleaveAsk" xml:space="preserve">
    <value>هل تريد اخراج الموظف من العمل</value>
  </data>
  <data name="MsgLeaveDelAsk" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف استمارة ترك عمل لموظف</value>
  </data>
  <data name="MsgLeaveWork" xml:space="preserve">
    <value>تم اخراج الموظف من العمل بنجاح</value>
  </data>
  <data name="MsgOvertimeExist" xml:space="preserve">
    <value>يوجد اضافي مسجل للموظف في هذا اليوم</value>
  </data>
  <data name="MsgPnltDelAsk" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا الجزاء</value>
  </data>
  <data name="MsgReturnAsk" xml:space="preserve">
    <value>هل تريد اعادة الموظف للعمل</value>
  </data>
  <data name="MsgReturnDelAsk" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف استمارة عودة  موظف للعمل</value>
  </data>
  <data name="MsgReturnWork" xml:space="preserve">
    <value>تم اعادة الموظف للعمل بنجاح</value>
  </data>
  <data name="MsgRwdExist" xml:space="preserve">
    <value>يوجد مكافأه مسجله للموظف في هذا اليوم, هل تريد الاستمرار</value>
  </data>
  <data name="MsgRwrdDelAsk" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذه المكافأه</value>
  </data>
  <data name="MsgVacationAnnualBalance" xml:space="preserve">
    <value>الموظف تجاوز رصيد الأجازات هل تريد الاستمرار</value>
  </data>
  <data name="MsgVacationDelAsk" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذه الاجازه</value>
  </data>
  <data name="ValAmount" xml:space="preserve">
    <value>يجب تسجيل المبلغ</value>
  </data>
  <data name="ValGroupName" xml:space="preserve">
    <value>يجب تسجيل اسم المجموعه</value>
  </data>
  <data name="ValJobName" xml:space="preserve">
    <value>يجب تسجيل اسم الوظيفه</value>
  </data>
  <data name="ValLeaveDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ ترك العمل</value>
  </data>
  <data name="ValLeaveOut" xml:space="preserve">
    <value>الموظف بالفعل تارك للعمل</value>
  </data>
  <data name="ValOvertimeDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ الاضافي</value>
  </data>
  <data name="ValOvertimePeriod" xml:space="preserve">
    <value>يجب تسجيل مدة الاضافي</value>
  </data>
  <data name="ValPnltDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ الجزاء</value>
  </data>
  <data name="ValPnltyExist" xml:space="preserve">
    <value>يوجد جزاء مسجل للموظف في هذا اليوم, هل تريد الاستمرار</value>
  </data>
  <data name="ValreturnDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ  العودة للعمل</value>
  </data>
  <data name="ValReturnIn" xml:space="preserve">
    <value>الموظف بالفعل موجود بالعمل</value>
  </data>
  <data name="ValRwrdDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ المكافأه</value>
  </data>
  <data name="ValVacationDates" xml:space="preserve">
    <value>تاريخ النهاية يجب ان يكون اكبر من تاريخ البدء</value>
  </data>
  <data name="ValVacationEndDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ النهاية</value>
  </data>
  <data name="ValVacationStartDate" xml:space="preserve">
    <value>يجب تسجيل تاريخ البدء</value>
  </data>
  <data name="Absence" xml:space="preserve">
    <value>غياب</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>اضافه</value>
  </data>
  <data name="Annual" xml:space="preserve">
    <value>اعتيادي</value>
  </data>
  <data name="attendance" xml:space="preserve">
    <value>حضور وانصراف</value>
  </data>
  <data name="Casual" xml:space="preserve">
    <value>عارضه</value>
  </data>
  <data name="customizeAccess" xml:space="preserve">
    <value>تخصيص الصلاحيات</value>
  </data>
  <data name="Day" xml:space="preserve">
    <value>اليوم</value>
  </data>
  <data name="Delay" xml:space="preserve">
    <value>تأخير</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>تعديل</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>الموظف</value>
  </data>
  <data name="MsgBenefitName" xml:space="preserve">
    <value>يجب تسجيل اسم البند</value>
  </data>
  <data name="MsgDateError" xml:space="preserve">
    <value>يجب ادخال التاريخ بشكل صحيح</value>
  </data>
  <data name="MsgDelItem" xml:space="preserve">
    <value>حذف بند ؟</value>
  </data>
  <data name="MsgPassword" xml:space="preserve">
    <value>يرجى إدخال الرقم السري</value>
  </data>
  <data name="MsgPay1" xml:space="preserve">
    <value> الموظف استلم راتب من الفترة </value>
  </data>
  <data name="MsgPay2" xml:space="preserve">
    <value>باستماره رقم</value>
  </data>
  <data name="MsgPay3" xml:space="preserve">
    <value>برجاء اكمال بيانات بنود الاستحقاق</value>
  </data>
  <data name="MsgPay4" xml:space="preserve">
    <value>برجاء اكمال بيانات بنود الاستقطاع</value>
  </data>
  <data name="MsgPayDel" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد حذف استمارة المرتب هذه</value>
  </data>
  <data name="MsgPayNum" xml:space="preserve">
    <value>يجب تسجيل رقم الاستماره</value>
  </data>
  <data name="MsgUserName" xml:space="preserve">
    <value>يرجى إدخال اسم المستخدم</value>
  </data>
  <data name="msgYearPeriod" xml:space="preserve">
    <value>الفترة لا يمكن ان تزيد عن سنة</value>
  </data>
  <data name="MsgZeroValue" xml:space="preserve">
    <value>القيمه يجب ان تكون اكبر من الصفر</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>اخري</value>
  </data>
  <data name="OtherVacation" xml:space="preserve">
    <value>اجازة اخري</value>
  </data>
  <data name="Overtime" xml:space="preserve">
    <value>اضافي</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>طباعه</value>
  </data>
  <data name="save" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="Weekend" xml:space="preserve">
    <value>راحة</value>
  </data>
  <data name="FixedAmount" xml:space="preserve">
    <value>مبلغ ثابت</value>
  </data>
  <data name="OfBasic" xml:space="preserve">
    <value>  % من الاساسي</value>
  </data>
  <data name="OfVariable" xml:space="preserve">
    <value> % من المتغير </value>
  </data>
  <data name="Penalty" xml:space="preserve">
    <value>جزاء</value>
  </data>
  <data name="Ratio" xml:space="preserve">
    <value> نسبة  </value>
  </data>
  <data name="Reward" xml:space="preserve">
    <value>مكافأة</value>
  </data>
  <data name="P" xml:space="preserve">
    <value>P--</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>مبلغ</value>
  </data>
  <data name="BasicRatio" xml:space="preserve">
    <value>نسبة من الأساسي</value>
  </data>
  <data name="leftwork" xml:space="preserve">
    <value>ترك العمل</value>
  </data>
  <data name="MsgCodeExist" xml:space="preserve">
    <value>هذا الكود مسجل من قبل</value>
  </data>
  <data name="MsgEmpCode" xml:space="preserve">
    <value>يرجى إدخال كود الموظف</value>
  </data>
  <data name="MsgEmpCodeZero" xml:space="preserve">
    <value>كود الموظف لايمكن أن يساوي صفر</value>
  </data>
  <data name="MsgEmpDel" xml:space="preserve">
    <value>عفوا, لايمكن حذف الموظف</value>
  </data>
  <data name="MsgEmpDelAsk" xml:space="preserve">
    <value>هل انت متأكد انك تريد حذف هذ الموظف؟</value>
  </data>
  <data name="MsgEmpJournal" xml:space="preserve">
    <value>يجب حذف القيود الخاصه بهذا الموظف أولا</value>
  </data>
  <data name="MsgEmpName" xml:space="preserve">
    <value>يرجى إدخال اسم الموظف</value>
  </data>
  <data name="MsgSelectDept" xml:space="preserve">
    <value>يجب اختيار القسم</value>
  </data>
  <data name="MsgSelectJob" xml:space="preserve">
    <value>يجب اختيار الوظيفة</value>
  </data>
  <data name="PicDel" xml:space="preserve">
    <value>هل تريد بالفعل حذف هذه الصوره؟</value>
  </data>
  <data name="PicExist" xml:space="preserve">
    <value>يوجد صوره بهذا الاسم, يجب تغيير اسم الصوره</value>
  </data>
  <data name="PicSelect" xml:space="preserve">
    <value>يرجى اختيار صورة وإدخال وصف مناسب لها</value>
  </data>
  <data name="PicSize" xml:space="preserve">
    <value>لا يمكن تحميل صوره اكبر من 10 ميجابايت</value>
  </data>
  <data name="StillWork" xml:space="preserve">
    <value>مستمر</value>
  </data>
  <data name="VariableRatio" xml:space="preserve">
    <value>نسبه من المتغير</value>
  </data>
  <data name="Payslip" xml:space="preserve">
    <value>استمارة مرتب رقم</value>
  </data>
  <data name="MsgMasterAccount" xml:space="preserve">
    <value>برجاء اختيار حساب الذمم الرئيسي من شاشة الاعدادات</value>
  </data>
  <data name="MsgMasterExpAccount" xml:space="preserve">
    <value>برجاء اختيار حساب الرواتب الرئيسي من شاشة الاعدادات</value>
  </data>
  <data name="loan" xml:space="preserve">
    <value>سلفة</value>
  </data>
  <data name="PayslipPayment" xml:space="preserve">
    <value>سداد استمارة مرتب رقم</value>
  </data>
  <data name="Commission" xml:space="preserve">
    <value>عمولة</value>
  </data>
  <data name="MsgCommission" xml:space="preserve">
    <value>برجاء تسجيل عمولة المبيعات بشكل صحيح</value>
  </data>
  <data name="MsgAccruedAcc" xml:space="preserve">
    <value>برجاء تحديد حساب استحقاق الرواتب للموظفين من شاشة الإعدادات</value>
  </data>
  <data name="MsgEmpTarget" xml:space="preserve">
    <value>برجاء مراجعة بيانات العمولة</value>
  </data>
  <data name="empDue" xml:space="preserve">
    <value>مستحقة للموظف</value>
  </data>
  <data name="empLoadPay" xml:space="preserve">
    <value>سداد سلفة للموظف</value>
  </data>
  <data name="empPay" xml:space="preserve">
    <value>مسددة للموظف</value>
  </data>
  <data name="PicEdit" xml:space="preserve">
    <value>هل تريد بالفعل تعديل هذه الصوره؟</value>
  </data>
  <data name="MsgItemExist" xml:space="preserve">
    <value>لايمكن اختيار الصنف أكثر من مرة</value>
  </data>
  <data name="MsgMissionDelAsk" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذه المأمورية</value>
  </data>
  <data name="ValMissionExist" xml:space="preserve">
    <value>يوجد مأموريات للموظف في هذه الفتره</value>
  </data>
  <data name="Mission" xml:space="preserve">
    <value>مأمورية</value>
  </data>
  <data name="ValVacType" xml:space="preserve">
    <value>برجاء تحديد نوع الأجازة</value>
  </data>
  <data name="MainPayslip" xml:space="preserve">
    <value>الراتب الأساسي</value>
  </data>
  <data name="SecondaryPayslip" xml:space="preserve">
    <value>الراتب الإضافي</value>
  </data>
  <data name="frmPayMain" xml:space="preserve">
    <value>استمارة راتب اساسي لموظف</value>
  </data>
  <data name="frmPaySecond" xml:space="preserve">
    <value>استمارة راتب إضافي لموظف</value>
  </data>
  <data name="LoanDel" xml:space="preserve">
    <value>هل أنت متأكد انك تريد حذف السلفة</value>
  </data>
  <data name="LoanDelDenied" xml:space="preserve">
    <value>لايمكن حذف السلفة بعد سداد أقساط منها</value>
  </data>
  <data name="LoanJrnl" xml:space="preserve">
    <value>سلفة رقم</value>
  </data>
  <data name="OtherBenefits" xml:space="preserve">
    <value>مستحقات أخرى</value>
  </data>
  <data name="enrollNo" xml:space="preserve">
    <value>كود الموظف بماكينة البصمة مستخدم بالفعل لموظف اخر</value>
  </data>
  <data name="OneEmpAtLeast" xml:space="preserve">
    <value>يجب تسجيل موظف واحد على الأقل</value>
  </data>
  <data name="AppDelete" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد حذف البيان ؟</value>
  </data>
  <data name="DelSponsor" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا الكفيل</value>
  </data>
  <data name="DegreeRequired" xml:space="preserve">
    <value>يجب تسجيل الدرجة</value>
  </data>
  <data name="DelEval" xml:space="preserve">
    <value>هل أنت متأكد انك تريد حذف استمارة التققيم هذه</value>
  </data>
  <data name="EvalItem" xml:space="preserve">
    <value>يجب تسجيل بند التقييم</value>
  </data>
  <data name="NeedEvalItem" xml:space="preserve">
    <value>يجب تسجيل بند تقييم واحد على الأقل</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>عدد أيام</value>
  </data>
  <data name="DelRule" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذه اللائحة</value>
  </data>
  <data name="InvalidRuleRow" xml:space="preserve">
    <value>يجب إدخال التوقيتات بشكل سليم</value>
  </data>
  <data name="DelRuleDenied" xml:space="preserve">
    <value>عفوا لايمكن حذف اللائحة فهي مستخدمة بالفعل</value>
  </data>
  <data name="PenaltyValue" xml:space="preserve">
    <value>قيمة المستند غير معروفة، برجاء تسجيل القيمة، او تسجيل قيمة اليوم للموظف ببيانات الموظفين</value>
  </data>
  <data name="SaveEmpFirst" xml:space="preserve">
    <value>يجب حفظ بيانات الموظف اولا</value>
  </data>
  <data name="ShiftsOverlapped" xml:space="preserve">
    <value>الورديات الجديدة متداخلة مع ورديات قديمة للموظفين</value>
  </data>
  <data name="MsgEmpShiftMove" xml:space="preserve">
    <value>يجب نقل الموظفين بهذه الوردية اولا</value>
  </data>
  <data name="ValTimeTable" xml:space="preserve">
    <value>يجب ادخال التوقيتات بشكل سليم</value>
  </data>
  <data name="SelectShift" xml:space="preserve">
    <value>برجاء اختيار الورديات بشكل سليم</value>
  </data>
  <data name="NoVacBefore" xml:space="preserve">
    <value>يجب التأكد من عدم إختيار موظف تم تحرير استمارة اجازه له مسبقا</value>
  </data>
  <data name="hr" xml:space="preserve">
    <value>نظام ادارة الموارد البشرية</value>
  </data>
  <data name="DelFpUsed" xml:space="preserve">
    <value>عفوا، لايمكنك الإستمرار، البصمة مستخدمة بمستند إضافي/تأخير</value>
  </data>
  <data name="hourvalue" xml:space="preserve">
    <value>يجب ادخال قيمة الساعة</value>
  </data>
  <data name="hrVacReqMonth" xml:space="preserve">
    <value>الموظف لم يتجاوز مدة التعيين المطلوبة لإستحقاق الأجازات</value>
  </data>
  <data name="MsgSelectFromTo" xml:space="preserve">
    <value>برجاء اختيار الفترة بشكل صحيح</value>
  </data>
  <data name="incomeTax" xml:space="preserve">
    <value>ضريبة دخل</value>
  </data>
  <data name="Vacations" xml:space="preserve">
    <value>أجازات</value>
  </data>
  <data name="ValCompanyInsurance" xml:space="preserve">
    <value>يجب ادخال نسبة حصة الشركة من التأمينات</value>
  </data>
  <data name="ArchivedVac" xml:space="preserve">
    <value>تم أرشفة الاستمارة، لايمكن التعديل عليها</value>
  </data>
  <data name="ShiftReplaceAdd" xml:space="preserve">
    <value>إضافة بدل وردية</value>
  </data>
  <data name="ShiftReplaceWithDraw" xml:space="preserve">
    <value>صرف بدل وردية</value>
  </data>
  <data name="DeleteConfirm" xml:space="preserve">
    <value>هل أنت متأكد أنك تريد حذف هذا المستند ؟</value>
  </data>
  <data name="ShiftReplaceExist" xml:space="preserve">
    <value>يوجد بدل وردية مسجل في هذا اليوم، هل تريد الاستمرار ؟</value>
  </data>
  <data name="ValDuration" xml:space="preserve">
    <value> يجب اختيار المدة</value>
  </data>
  <data name="DateFromTo" xml:space="preserve">
    <value>يجب تسجيل التاريخ من وإلى</value>
  </data>
  <data name="MsgWDexist" xml:space="preserve">
    <value>يوجد أيام عمل مسجله للموظف في هذه الفترة, هل تريد الاستمرار</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value>من فضلك تأكد من ادخال اعدادات التامينات.</value>
  </data>
  <data name="InsuranceSettings" xml:space="preserve">
    <value>من فضلك تأكد من إدخال إعدادات التأمينات.</value>
  </data>
  <data name="MsgBusinessGainAcc" xml:space="preserve">
    <value>برجاء تحديد حساب ضريبة كسب العمل من شاشة الإعدادات</value>
  </data>
  <data name="MsgInsuranceAcc" xml:space="preserve">
    <value>برجاء تحديد حساب التأمينات من شاشة الإعدادات</value>
  </data>
  <data name="MsgNetTax" xml:space="preserve">
    <value>برجاء تحديد حساب الضريبة التكافلية من شاشة الإعدادات</value>
  </data>
  <data name="Daily" xml:space="preserve">
    <value>يومي</value>
  </data>
</root>