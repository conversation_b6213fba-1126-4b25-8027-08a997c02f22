# SaveDocument API Documentation

## Quick Reference

### Endpoint
```
POST /api/EInvoice/SaveInvoice
```

### Mandatory Fields Summary ⚠️
**Invoice Level:**
- `companyId`, `storeId`, `customerId`, `date`, `documentType`, `invoiceCode`, `invoiceDetails`

**Invoice Detail Level:**
- `itemId`, `quantity`, `currencyId`, `exchangeRate`, `amount`, `uomId`

### Optional Fields Summary ℹ️
**Invoice Level:**
- `invoiceId`, `storeName`, `customerName`, `TotalDiscount`, `TotalTax`, `Net`, `PurchaseOrderNumber`, `ProformaNumber`, `DeliveryDate`, `Uuid`

**Invoice Detail Level:**
- `Id`, `itemName`, `currencyName`, `uomName`, `Description`, `discountAfterTax`, `totalAmount`, `discount`, `discounts`, `taxes`

### Document Types
- **"I"** = Invoice (فاتورة)
- **"C"** = Credit Note (إشعار خصم) - requires `Uuid` field
- **"D"** = Debit Note (إشعار إضافة) - requires `Uuid` field

---

## Overview
The `SaveDocument` method in the EInvoiceController is responsible for validating and saving invoice documents to the database. This endpoint performs comprehensive validation before storing the invoice data.

## Endpoint Details

### HTTP Method & Route
```
POST /api/EInvoice/SaveInvoice
```

### Method Signature
```csharp
public IActionResult SaveDocument(List<Invoice> invoices)
```

## Request Structure

### Invoice Model
```json
{
  "companyId": 1,
  "storeId": 1,
  "storeName": "Main Store",
  "customerId": 123,
  "customerName": "Customer Name",
  "date": "2024-01-15T10:30:00Z",
  "invoiceId": 1001,
  "documentType": "I",
  "TotalDiscount": 50.00,
  "TotalTax": 15.00,
  "Net": 500.00,
  "invoiceCode": "INV-2024-001",
  "PurchaseOrderNumber": "PO-123",
  "ProformaNumber": "PRO-456",
  "DeliveryDate": "2024-01-20",
  "Uuid": ["uuid-string-1", "uuid-string-2"],
  "invoiceDetails": [
    {
      "Id": 1,
      "itemId": 101,
      "itemName": "Product Name",
      "quantity": 2,
      "currencyId": 1,
      "currencyName": "EGP",
      "exchangeRate": 1.0,
      "amount": 100.00,
      "uomId": 1,
      "uomName": "Piece",
      "Description": "Product Description",
      "discountAfterTax": 5.00,
      "totalAmount": 200.00,
      "discount": {
        "type": 1,
        "amount": 10.00,
        "rate": 5.0
      },
      "discounts": [
        {
          "type": 1,
          "amount": 10.00,
          "rate": 5.0
        }
      ],
      "taxes": [
        {
          "taxId": 1,
          "taxName": "T1",
          "subTaxId": 1,
          "subTaxName": "VAT",
          "type": 1,
          "rate": 14.0,
          "amount": 28.00
        }
      ]
    }
  ]
}
```

### Field Descriptions

#### Invoice Level Fields

##### Mandatory Fields ⚠️
- **companyId**: Integer - ID of the company issuing the invoice
- **storeId**: Integer - ID of the store/branch
- **customerId**: Integer - ID of the customer
- **date**: DateTime - Invoice date and time (ISO 8601 format)
- **documentType**: String - Document type ("I" for Invoice, "C" for Credit Note, "D" for Debit Note)
- **invoiceCode**: String - Unique invoice code (must be unique per date)
- **invoiceDetails**: Array - At least one invoice detail item is required

##### Optional Fields ℹ️
- **invoiceId**: Integer - Internal invoice ID (can be null for new invoices)
- **storeName**: String - Name of the store/branch (for display purposes)
- **customerName**: String - Name of the customer (for display purposes)
- **TotalDiscount**: Decimal - Total discount amount (default: 0.00)
- **TotalTax**: Decimal - Total tax amount (calculated automatically)
- **Net**: Decimal - Net amount (calculated automatically)
- **PurchaseOrderNumber**: String - Purchase order reference
- **ProformaNumber**: String - Proforma invoice reference
- **DeliveryDate**: String - Delivery date (YYYY-MM-DD format)
- **Uuid**: Array of strings - UUIDs for document references (required for Credit/Debit notes)

#### Invoice Detail Fields

##### Mandatory Fields ⚠️
- **itemId**: Integer - Product/item ID (must exist in system)
- **quantity**: Decimal - Quantity of items (must be > 0)
- **currencyId**: Integer - Currency ID (must exist in system)
- **exchangeRate**: Decimal - Exchange rate to EGP (must be > 0)
- **amount**: Decimal - Unit price in specified currency (must be > 0)
- **uomId**: Integer - Unit of measure ID (must exist in system)

##### Optional Fields ℹ️
- **Id**: Integer - Detail line ID (auto-generated if 0 or null)
- **itemName**: String - Product/item name (for display purposes)
- **currencyName**: String - Currency name (for display purposes)
- **uomName**: String - Unit of measure name (for display purposes)
- **Description**: String - Item description
- **discountAfterTax**: Decimal - Discount applied after tax calculation (default: 0.00)
- **totalAmount**: Decimal - Total amount for this line item (calculated automatically)
- **discount**: Object - Discount information (see below)
- **discounts**: Array - Multiple discounts (optional)
- **taxes**: Array - Tax information (calculated based on item configuration)

#### Discount Object

##### Mandatory Fields ⚠️
- **type**: Integer - Discount type (1 = percentage, 0 = fixed amount)

##### Optional Fields ℹ️
- **amount**: Decimal - Discount amount (required if type = 0)
- **rate**: Decimal - Discount rate percentage (required if type = 1)

#### Tax Object

##### Mandatory Fields ⚠️
- **taxId**: Integer - Tax type ID
- **taxName**: String - Tax type name (T1, T2, T3, etc.)
- **subTaxId**: Integer - Sub-tax ID
- **type**: Integer - Tax calculation type (1 = percentage, 0 = fixed amount)

##### Optional Fields ℹ️
- **subTaxName**: String - Sub-tax name (for display purposes)
- **rate**: Decimal - Tax rate percentage (required if type = 1)
- **amount**: Decimal - Tax amount (calculated automatically or required if type = 0)

## Response Structure

### Success Response (200 OK)
```json
"Document Saved"
```

### Error Response (400 Bad Request)
```json
{
  "Messages": [
    {
      "invoiceCode": "INV-2024-001",
      "Message": [
        "Validation error message 1",
        "Validation error message 2"
      ],
      "Islocal": true
    }
  ]
}
```

## Validation Rules

### Mandatory Field Validations ⚠️

#### Invoice Level Mandatory Validations
1. **companyId**: Must be a valid integer > 0 and exist in StCompanyInfo table
2. **storeId**: Must be a valid integer > 0 and exist in IcStore table
3. **customerId**: Must be a valid integer > 0 and exist in SlCustomer table
4. **date**: Must be a valid DateTime in ISO 8601 format
5. **documentType**: Must be exactly "I", "C", or "D"
6. **invoiceCode**:
   - Cannot be null or empty
   - Must be unique for the given date and document type
   - Maximum length validation applies
7. **invoiceDetails**: Must contain at least one item

#### Invoice Detail Mandatory Validations
1. **itemId**: Must exist in IcItem table with valid ItemEcode
2. **quantity**: Must be > 0 (decimal precision up to 5 decimal places)
3. **currencyId**: Must exist in StCurrency table
4. **exchangeRate**: Must be > 0 (decimal precision up to 5 decimal places)
5. **amount**: Must be > 0 (unit price in specified currency)
6. **uomId**: Must exist in IcUom table with valid Ecode

### Optional Field Validations ℹ️

#### Invoice Level Optional Validations
1. **PurchaseOrderNumber**: If provided, validates format and length
2. **DeliveryDate**: If provided, must be valid date format (YYYY-MM-DD)
3. **Uuid**: Required for Credit Notes (C) and Debit Notes (D), must reference existing invoices
4. **TotalDiscount**: If provided, must be >= 0
5. **storeName**: Used for display only, no validation
6. **customerName**: Used for display only, no validation

#### Invoice Detail Optional Validations
1. **Description**: If provided, validates maximum length
2. **discountAfterTax**: If provided, must be >= 0
3. **discount.rate**: If discount type = 1, rate must be between 0-100
4. **discount.amount**: If discount type = 0, amount must be >= 0

### Business Rule Validations

#### Tax Calculations
1. **T1 (VAT)**: Calculated as (Net Total + TotalTaxableFees + Value Difference + T2 + T3) × Rate%
2. **T2 (Table Tax)**: Calculated as (Net Total + TotalTaxableFees + Value Difference + T3) × Rate%
3. **T3 (Fixed Amount)**: Rate must be 0 (fixed amount only)
4. **T4 (Withholding Tax)**: Calculated as (Net Total - Items Discount) × Rate%
5. **T5-T20 (Other Taxes)**: Calculated as Net Total × Rate%

#### Amount Calculations
1. **Sales Total**: Must equal Quantity × Unit Amount (in EGP)
2. **Net Total**: Must equal Sales Total - Discount Amount
3. **Total Amount**: Must equal Net Total + All Taxes + Taxable Fees - Items Discount - Withholding Tax
4. **Exchange Rate Validation**: If currency ≠ EGP, Amount EGP = Amount × Exchange Rate

#### Discount Validations
1. **Percentage Discount**: Discount Amount = (Rate × Sales Total) / 100
2. **Fixed Discount**: Rate should be 0 when amount is specified
3. **Multiple Discounts**: Sum of all discounts cannot exceed line total

### Data Integrity Validations

#### Master Data Dependencies
1. **Company**: Must have valid tax registration and activity code
2. **Store**: Must be active and belong to the specified company
3. **Customer**: Must be active and have valid type (B/P)
4. **Items**: Must be active and have valid tax configuration
5. **Currency**: Must be active with valid exchange rate
6. **UOM**: Must be active and valid for the specified item

#### Document Threshold Validations
1. **Person Type Customer (P)**: If total amount >= threshold, customer national ID is required
2. **Business Type Customer (B)**: Tax registration number validation
3. **Document Limits**: Maximum number of line items per document

## Business Logic

1. **Validation Phase**: 
   - Uses `ApiValidation.Validate()` to validate the entire invoice structure
   - Checks business rules and data integrity
   - Maps input data to internal data structures

2. **Save Phase**:
   - If validation passes, calls `InvoiceBL.SaveDocuments()` to persist data
   - Saves invoice header and detail records to database
   - Updates related tax and discount records

## Error Handling

The API returns appropriate HTTP status codes:
- **200 OK**: Document saved successfully
- **400 Bad Request**: Validation errors occurred

Error messages are returned in Arabic for user-friendly display.

## Usage Notes

1. **Document Types**:
   - "I" = Invoice (فاتورة)
   - "C" = Credit Note (إشعار خصم)
   - "D" = Debit Note (إشعار إضافة)

2. **Currency Handling**:
   - All amounts should be provided in the specified currency
   - Exchange rates are used to convert to EGP for tax calculations

3. **Tax Types**:
   - T1: Value Added Tax (VAT)
   - T2: Table Tax
   - T3: Fixed Amount Tax
   - T4: Withholding Tax
   - T5-T20: Various other tax types

4. **Discount Types**:
   - Type 1: Percentage-based discount
   - Type 0: Fixed amount discount

## Database Impact

The API saves data to the following main tables:
- `SlInvoice`: Invoice header information
- `SlInvoiceDetail`: Invoice line items
- `SlInvoiceDetailSubTaxValue`: Tax details for each line item

## Prerequisites

Before using this API:
1. Ensure company information is configured
2. Ensure store information is configured
3. Ensure customer exists in the system
4. Ensure all items exist with proper tax configuration
5. Ensure currencies and UOMs are properly configured
