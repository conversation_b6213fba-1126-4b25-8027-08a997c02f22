# SaveDocument API Documentation

## Overview
The `SaveDocument` method in the EInvoiceController is responsible for validating and saving invoice documents to the database. This endpoint performs comprehensive validation before storing the invoice data.

## Endpoint Details

### HTTP Method & Route
```
POST /api/EInvoice/SaveInvoice
```

### Method Signature
```csharp
public IActionResult SaveDocument(List<Invoice> invoices)
```

## Request Structure

### Invoice Model
```json
{
  "companyId": 1,
  "storeId": 1,
  "storeName": "Main Store",
  "customerId": 123,
  "customerName": "Customer Name",
  "date": "2024-01-15T10:30:00Z",
  "invoiceId": 1001,
  "documentType": "I",
  "TotalDiscount": 50.00,
  "TotalTax": 15.00,
  "Net": 500.00,
  "invoiceCode": "INV-2024-001",
  "PurchaseOrderNumber": "PO-123",
  "ProformaNumber": "PRO-456",
  "DeliveryDate": "2024-01-20",
  "Uuid": ["uuid-string-1", "uuid-string-2"],
  "invoiceDetails": [
    {
      "Id": 1,
      "itemId": 101,
      "itemName": "Product Name",
      "quantity": 2,
      "currencyId": 1,
      "currencyName": "EGP",
      "exchangeRate": 1.0,
      "amount": 100.00,
      "uomId": 1,
      "uomName": "Piece",
      "Description": "Product Description",
      "discountAfterTax": 5.00,
      "totalAmount": 200.00,
      "discount": {
        "type": 1,
        "amount": 10.00,
        "rate": 5.0
      },
      "discounts": [
        {
          "type": 1,
          "amount": 10.00,
          "rate": 5.0
        }
      ],
      "taxes": [
        {
          "taxId": 1,
          "taxName": "T1",
          "subTaxId": 1,
          "subTaxName": "VAT",
          "type": 1,
          "rate": 14.0,
          "amount": 28.00
        }
      ]
    }
  ]
}
```

### Field Descriptions

#### Invoice Level Fields
- **companyId**: Integer - ID of the company issuing the invoice
- **storeId**: Integer - ID of the store/branch
- **storeName**: String - Name of the store/branch
- **customerId**: Integer - ID of the customer
- **customerName**: String - Name of the customer
- **date**: DateTime - Invoice date and time
- **invoiceId**: Integer (optional) - Internal invoice ID
- **documentType**: String - Document type ("I" for Invoice, "C" for Credit Note, "D" for Debit Note)
- **TotalDiscount**: Decimal - Total discount amount
- **TotalTax**: Decimal - Total tax amount
- **Net**: Decimal - Net amount
- **invoiceCode**: String - Unique invoice code
- **PurchaseOrderNumber**: String (optional) - Purchase order reference
- **ProformaNumber**: String (optional) - Proforma invoice reference
- **DeliveryDate**: String (optional) - Delivery date
- **Uuid**: Array of strings (optional) - UUIDs for document references

#### Invoice Detail Fields
- **Id**: Integer - Detail line ID
- **itemId**: Integer - Product/item ID
- **itemName**: String - Product/item name
- **quantity**: Decimal - Quantity of items
- **currencyId**: Integer - Currency ID
- **currencyName**: String - Currency name
- **exchangeRate**: Decimal - Exchange rate to EGP
- **amount**: Decimal - Unit price in specified currency
- **uomId**: Integer - Unit of measure ID
- **uomName**: String - Unit of measure name
- **Description**: String - Item description
- **discountAfterTax**: Decimal - Discount applied after tax calculation
- **totalAmount**: Decimal - Total amount for this line item

#### Discount Object
- **type**: Integer - Discount type (1 = percentage, 0 = amount)
- **amount**: Decimal - Discount amount
- **rate**: Decimal - Discount rate (percentage)

#### Tax Object
- **taxId**: Integer - Tax type ID
- **taxName**: String - Tax type name (T1, T2, T3, etc.)
- **subTaxId**: Integer - Sub-tax ID
- **subTaxName**: String - Sub-tax name
- **type**: Integer - Tax calculation type (1 = percentage, 0 = amount)
- **rate**: Decimal - Tax rate (percentage)
- **amount**: Decimal - Tax amount

## Response Structure

### Success Response (200 OK)
```json
"Document Saved"
```

### Error Response (400 Bad Request)
```json
{
  "Messages": [
    {
      "invoiceCode": "INV-2024-001",
      "Message": [
        "Validation error message 1",
        "Validation error message 2"
      ],
      "Islocal": true
    }
  ]
}
```

## Validation Rules

The API performs comprehensive validation including:

1. **Document Code Validation**: Ensures invoice code is unique for the given date
2. **Company Data Validation**: Validates company information exists
3. **Store Data Validation**: Validates store information exists
4. **Customer Data Validation**: Validates customer information exists
5. **Item Validation**: Ensures all items exist in the system
6. **Currency Validation**: Validates currency codes
7. **Unit of Measure Validation**: Validates UOM codes
8. **Tax Calculation Validation**: Validates tax calculations are correct
9. **Discount Validation**: Validates discount calculations
10. **Amount Validation**: Validates total amounts and calculations

## Business Logic

1. **Validation Phase**: 
   - Uses `ApiValidation.Validate()` to validate the entire invoice structure
   - Checks business rules and data integrity
   - Maps input data to internal data structures

2. **Save Phase**:
   - If validation passes, calls `InvoiceBL.SaveDocuments()` to persist data
   - Saves invoice header and detail records to database
   - Updates related tax and discount records

## Error Handling

The API returns appropriate HTTP status codes:
- **200 OK**: Document saved successfully
- **400 Bad Request**: Validation errors occurred

Error messages are returned in Arabic for user-friendly display.

## Usage Notes

1. **Document Types**:
   - "I" = Invoice (فاتورة)
   - "C" = Credit Note (إشعار خصم)
   - "D" = Debit Note (إشعار إضافة)

2. **Currency Handling**:
   - All amounts should be provided in the specified currency
   - Exchange rates are used to convert to EGP for tax calculations

3. **Tax Types**:
   - T1: Value Added Tax (VAT)
   - T2: Table Tax
   - T3: Fixed Amount Tax
   - T4: Withholding Tax
   - T5-T20: Various other tax types

4. **Discount Types**:
   - Type 1: Percentage-based discount
   - Type 0: Fixed amount discount

## Database Impact

The API saves data to the following main tables:
- `SlInvoice`: Invoice header information
- `SlInvoiceDetail`: Invoice line items
- `SlInvoiceDetailSubTaxValue`: Tax details for each line item

## Prerequisites

Before using this API:
1. Ensure company information is configured
2. Ensure store information is configured
3. Ensure customer exists in the system
4. Ensure all items exist with proper tax configuration
5. Ensure currencies and UOMs are properly configured
