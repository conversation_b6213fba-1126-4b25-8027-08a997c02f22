﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResAccEn {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResAccEn() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResAccEn", typeof(ResAccEn).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit.
        /// </summary>
        public static string acTypeCredit {
            get {
                return ResourceManager.GetString("acTypeCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        public static string acTypeDebit {
            get {
                return ResourceManager.GetString("acTypeDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Undetermined.
        /// </summary>
        public static string acTypeWithout {
            get {
                return ResourceManager.GetString("acTypeWithout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Distributed Amount must be equal to total capital amount..
        /// </summary>
        public static string capitalDisError {
            get {
                return ResourceManager.GetString("capitalDisError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mandatory.
        /// </summary>
        public static string ccTypeMandatory {
            get {
                return ResourceManager.GetString("ccTypeMandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Optional.
        /// </summary>
        public static string ccTypeOptional {
            get {
                return ResourceManager.GetString("ccTypeOptional", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Without.
        /// </summary>
        public static string ccTypeWithout {
            get {
                return ResourceManager.GetString("ccTypeWithout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Note.
        /// </summary>
        public static string CreditNote {
            get {
                return ResourceManager.GetString("CreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes List.
        /// </summary>
        public static string CreditNoteList {
            get {
                return ResourceManager.GetString("CreditNoteList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note.
        /// </summary>
        public static string DebitNote {
            get {
                return ResourceManager.GetString("DebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Notes List.
        /// </summary>
        public static string DebitNoteList {
            get {
                return ResourceManager.GetString("DebitNoteList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this account, its already in use by Manufacturing module.
        /// </summary>
        public static string DelAccBom {
            get {
                return ResourceManager.GetString("DelAccBom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this account, its already used by Inventories.
        /// </summary>
        public static string DelAccContinualStore {
            get {
                return ResourceManager.GetString("DelAccContinualStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this cost center, you have to delete child cost centers first.
        /// </summary>
        public static string delCCChild {
            get {
                return ResourceManager.GetString("delCCChild", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this fixed asset, you&apos;ve to delete it&apos;s journal entries first.
        /// </summary>
        public static string DelFaDenied {
            get {
                return ResourceManager.GetString("DelFaDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this fixed assets group.
        /// </summary>
        public static string DelFaGrp {
            get {
                return ResourceManager.GetString("DelFaGrp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this group there is some assets related to it.
        /// </summary>
        public static string DelFaGrp2 {
            get {
                return ResourceManager.GetString("DelFaGrp2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are about removing this expense!! Confirm?!.
        /// </summary>
        public static string delManfExpWarn {
            get {
                return ResourceManager.GetString("delManfExpWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Difference.
        /// </summary>
        public static string diff {
            get {
                return ResourceManager.GetString("diff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee.
        /// </summary>
        public static string emp {
            get {
                return ResourceManager.GetString("emp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to excess.
        /// </summary>
        public static string excess {
            get {
                return ResourceManager.GetString("excess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expire Date.
        /// </summary>
        public static string ExpireDate {
            get {
                return ResourceManager.GetString("ExpireDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select fixed assets account and depreciation account in settings screen.
        /// </summary>
        public static string FaAccs {
            get {
                return ResourceManager.GetString("FaAccs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this account.
        /// </summary>
        public static string Msg01 {
            get {
                return ResourceManager.GetString("Msg01", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have to delete this account journals first.
        /// </summary>
        public static string Msg02 {
            get {
                return ResourceManager.GetString("Msg02", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have to delete sub-accounts first.
        /// </summary>
        public static string Msg03 {
            get {
                return ResourceManager.GetString("Msg03", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this account.
        /// </summary>
        public static string Msg04 {
            get {
                return ResourceManager.GetString("Msg04", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t create sub-account.
        /// </summary>
        public static string Msg05 {
            get {
                return ResourceManager.GetString("Msg05", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter account name.
        /// </summary>
        public static string Msg06 {
            get {
                return ResourceManager.GetString("Msg06", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you can&apos;t edit  this account.
        /// </summary>
        public static string Msg09 {
            get {
                return ResourceManager.GetString("Msg09", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete Default Drawer.
        /// </summary>
        public static string Msg10 {
            get {
                return ResourceManager.GetString("Msg10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this drawer.
        /// </summary>
        public static string Msg11 {
            get {
                return ResourceManager.GetString("Msg11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this cash payment.
        /// </summary>
        public static string Msg12 {
            get {
                return ResourceManager.GetString("Msg12", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this cash receive.
        /// </summary>
        public static string Msg13 {
            get {
                return ResourceManager.GetString("Msg13", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this cash transfer.
        /// </summary>
        public static string Msg14 {
            get {
                return ResourceManager.GetString("Msg14", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this journal, you have to delete journal source.
        /// </summary>
        public static string Msg15 {
            get {
                return ResourceManager.GetString("Msg15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this journal entry.
        /// </summary>
        public static string Msg16 {
            get {
                return ResourceManager.GetString("Msg16", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter filling number.
        /// </summary>
        public static string Msg17 {
            get {
                return ResourceManager.GetString("Msg17", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t edit this journal, you have to edit journal source.
        /// </summary>
        public static string Msg18 {
            get {
                return ResourceManager.GetString("Msg18", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you have to enter journals.
        /// </summary>
        public static string Msg19 {
            get {
                return ResourceManager.GetString("Msg19", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit side and Credit side should be equal.
        /// </summary>
        public static string Msg20 {
            get {
                return ResourceManager.GetString("Msg20", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closing Period Accounts can&apos;t be included in journals, it&apos;s calculated automatically.
        /// </summary>
        public static string Msg21 {
            get {
                return ResourceManager.GetString("Msg21", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this pay notes.
        /// </summary>
        public static string Msg22 {
            get {
                return ResourceManager.GetString("Msg22", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this receive notes.
        /// </summary>
        public static string Msg23 {
            get {
                return ResourceManager.GetString("Msg23", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to pay all selected pay notes.
        /// </summary>
        public static string Msg24 {
            get {
                return ResourceManager.GetString("Msg24", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All selected pay notes are payed successfully.
        /// </summary>
        public static string Msg25 {
            get {
                return ResourceManager.GetString("Msg25", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select payment drawer, then continue.
        /// </summary>
        public static string Msg26 {
            get {
                return ResourceManager.GetString("Msg26", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Are you sure you want to update selected receive notes.
        /// </summary>
        public static string Msg27 {
            get {
                return ResourceManager.GetString("Msg27", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select payment drawer, then continue.
        /// </summary>
        public static string Msg28 {
            get {
                return ResourceManager.GetString("Msg28", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive notes data updated successfully.
        /// </summary>
        public static string Msg29 {
            get {
                return ResourceManager.GetString("Msg29", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this revenue.
        /// </summary>
        public static string Msg30 {
            get {
                return ResourceManager.GetString("Msg30", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this expense.
        /// </summary>
        public static string Msg31 {
            get {
                return ResourceManager.GetString("Msg31", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this custom accounts list.
        /// </summary>
        public static string Msg32 {
            get {
                return ResourceManager.GetString("Msg32", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter accounts.
        /// </summary>
        public static string Msg33 {
            get {
                return ResourceManager.GetString("Msg33", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have to delete this account from custom accounts lists first.
        /// </summary>
        public static string Msg34 {
            get {
                return ResourceManager.GetString("Msg34", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter custom account list Name.
        /// </summary>
        public static string Msg35 {
            get {
                return ResourceManager.GetString("Msg35", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, we can&apos;t exceed Maximum number of accounts allowed in this level.
        /// </summary>
        public static string MsgAccLevel {
            get {
                return ResourceManager.GetString("MsgAccLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this account, it&apos;s used by accounts settings.
        /// </summary>
        public static string MsgAccSettng {
            get {
                return ResourceManager.GetString("MsgAccSettng", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Are you sure you want to delete .
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Banks, Receivable and Payable Notes accounts in settings screen.
        /// </summary>
        public static string MsgBankAcc {
            get {
                return ResourceManager.GetString("MsgBankAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t create sub-cost center.
        /// </summary>
        public static string MsgCantAddCCC {
            get {
                return ResourceManager.GetString("MsgCantAddCCC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter cost center code.
        /// </summary>
        public static string MsgCCCode {
            get {
                return ResourceManager.GetString("MsgCCCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry you can&apos;t delete this cost center.
        /// </summary>
        public static string MsgccDelDenie {
            get {
                return ResourceManager.GetString("MsgccDelDenie", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter cost center name.
        /// </summary>
        public static string MsgCCName {
            get {
                return ResourceManager.GetString("MsgCCName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You made some changes, do you want to save.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deleted Successfully.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this cost center.
        /// </summary>
        public static string MsgDelCC {
            get {
                return ResourceManager.GetString("MsgDelCC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have to delete this cost center journals first.
        /// </summary>
        public static string MsgDelCCDenied {
            get {
                return ResourceManager.GetString("MsgDelCCDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this Credit Note.
        /// </summary>
        public static string MsgDelCrdtNote {
            get {
                return ResourceManager.GetString("MsgDelCrdtNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to delete this Debit Note.
        /// </summary>
        public static string MsgDelDbtNote {
            get {
                return ResourceManager.GetString("MsgDelDbtNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to delete row ?.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Drawers main account in settings screen.
        /// </summary>
        public static string MsgDrawerAcc {
            get {
                return ResourceManager.GetString("MsgDrawerAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have to register a drawer or bank account first.
        /// </summary>
        public static string MsgDrwrBank {
            get {
                return ResourceManager.GetString("MsgDrwrBank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select fiscal year end date in company information screen.
        /// </summary>
        public static string MsgFiscalYearEndDate {
            get {
                return ResourceManager.GetString("MsgFiscalYearEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this account, it is used by system. go to settings to change that.
        /// </summary>
        public static string MsgHRAccount {
            get {
                return ResourceManager.GetString("MsgHRAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Set Income Statment Accounts in Settings Screen.
        /// </summary>
        public static string MsgIncomAccs {
            get {
                return ResourceManager.GetString("MsgIncomAccs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to some data are incorrect.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Merchendaising account in settings screen.
        /// </summary>
        public static string MsgMerchendaisingAcc {
            get {
                return ResourceManager.GetString("MsgMerchendaisingAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This name already exists.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Payable Notes account in settings screen.
        /// </summary>
        public static string MsgNotesPayableAcc {
            get {
                return ResourceManager.GetString("MsgNotesPayableAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Receivable Notes account in settings screen.
        /// </summary>
        public static string MsgNotesReceivableAcc {
            get {
                return ResourceManager.GetString("MsgNotesReceivableAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Under Collection Notes account in settings screen.
        /// </summary>
        public static string MsgNotesReceivableUnderCollect {
            get {
                return ResourceManager.GetString("MsgNotesReceivableUnderCollect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This number already exists.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to edit record.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you don&apos;t have privilege to add new record.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saved Successfully.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Info.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Question.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This drawer is assigned as default drawer for some users.
        /// </summary>
        public static string msgUserDefault {
            get {
                return ResourceManager.GetString("msgUserDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Visa, Receivable and Payable Notes accounts in settings screen.
        /// </summary>
        public static string MsgVisaAcc {
            get {
                return ResourceManager.GetString("MsgVisaAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code can&apos;t be zero.
        /// </summary>
        public static string MsgZeroCode {
            get {
                return ResourceManager.GetString("MsgZeroCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net Loss.
        /// </summary>
        public static string NetLoss {
            get {
                return ResourceManager.GetString("NetLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Net Profit.
        /// </summary>
        public static string NetProfit {
            get {
                return ResourceManager.GetString("NetProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is neither Loss nor Profit.
        /// </summary>
        public static string NoLossNoProfit {
            get {
                return ResourceManager.GetString("NoLossNoProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales After Cost.
        /// </summary>
        public static string SalesAfterCost {
            get {
                return ResourceManager.GetString("SalesAfterCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Net.
        /// </summary>
        public static string SalesNet {
            get {
                return ResourceManager.GetString("SalesNet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select period correctly.
        /// </summary>
        public static string SelectPeriod {
            get {
                return ResourceManager.GetString("SelectPeriod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to shortage.
        /// </summary>
        public static string shortage {
            get {
                return ResourceManager.GetString("shortage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Batch.
        /// </summary>
        public static string st_Batch {
            get {
                return ResourceManager.GetString("st_Batch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Name.
        /// </summary>
        public static string st_CompName {
            get {
                return ResourceManager.GetString("st_CompName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Tel.
        /// </summary>
        public static string st_CompTel {
            get {
                return ResourceManager.GetString("st_CompTel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Code.
        /// </summary>
        public static string st_ItemCode {
            get {
                return ResourceManager.GetString("st_ItemCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code 2.
        /// </summary>
        public static string st_ItemCode2 {
            get {
                return ResourceManager.GetString("st_ItemCode2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Name.
        /// </summary>
        public static string st_ItemName {
            get {
                return ResourceManager.GetString("st_ItemName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Price.
        /// </summary>
        public static string st_ItemPrice {
            get {
                return ResourceManager.GetString("st_ItemPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Invoice No..
        /// </summary>
        public static string st_PurchaseInvoice {
            get {
                return ResourceManager.GetString("st_PurchaseInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        public static string st_Qty {
            get {
                return ResourceManager.GetString("st_Qty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter number of days.
        /// </summary>
        public static string st_SetDaysCount {
            get {
                return ResourceManager.GetString("st_SetDaysCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor Code.
        /// </summary>
        public static string st_vendorCode {
            get {
                return ResourceManager.GetString("st_vendorCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Loss.
        /// </summary>
        public static string TotalLoss {
            get {
                return ResourceManager.GetString("TotalLoss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Profit.
        /// </summary>
        public static string TotalProfit {
            get {
                return ResourceManager.GetString("TotalProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account.
        /// </summary>
        public static string txt_Account {
            get {
                return ResourceManager.GetString("txt_Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to C.
        /// </summary>
        public static string txt_C {
            get {
                return ResourceManager.GetString("txt_C", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to D.
        /// </summary>
        public static string txt_D {
            get {
                return ResourceManager.GetString("txt_D", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incoming.
        /// </summary>
        public static string txt_incoming {
            get {
                return ResourceManager.GetString("txt_incoming", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Journal Entry Number.
        /// </summary>
        public static string txt_JournalNumber {
            get {
                return ResourceManager.GetString("txt_JournalNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number .
        /// </summary>
        public static string txt_Number {
            get {
                return ResourceManager.GetString("txt_Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outgoing.
        /// </summary>
        public static string txt_outgoing {
            get {
                return ResourceManager.GetString("txt_outgoing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter bank account number.
        /// </summary>
        public static string txt1 {
            get {
                return ResourceManager.GetString("txt1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account created by Revenue Screen.
        /// </summary>
        public static string txt10 {
            get {
                return ResourceManager.GetString("txt10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account created by Expenses Screen.
        /// </summary>
        public static string txt11 {
            get {
                return ResourceManager.GetString("txt11", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Statement - Total Loss.
        /// </summary>
        public static string txt2 {
            get {
                return ResourceManager.GetString("txt2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Statement - Total Profit.
        /// </summary>
        public static string txt3 {
            get {
                return ResourceManager.GetString("txt3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adjusting Entries.
        /// </summary>
        public static string txt4 {
            get {
                return ResourceManager.GetString("txt4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Account is Debit in.
        /// </summary>
        public static string txt5 {
            get {
                return ResourceManager.GetString("txt5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Account is Credit in.
        /// </summary>
        public static string txt6 {
            get {
                return ResourceManager.GetString("txt6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Account is Debit in.
        /// </summary>
        public static string txt7 {
            get {
                return ResourceManager.GetString("txt7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Account is Credit in.
        /// </summary>
        public static string txt8 {
            get {
                return ResourceManager.GetString("txt8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sub-Accounts included.
        /// </summary>
        public static string txt9 {
            get {
                return ResourceManager.GetString("txt9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account.
        /// </summary>
        public static string txtAcc {
            get {
                return ResourceManager.GetString("txtAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account Name.
        /// </summary>
        public static string txtAccName {
            get {
                return ResourceManager.GetString("txtAccName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Bank .
        /// </summary>
        public static string txtBank {
            get {
                return ResourceManager.GetString("txtBank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bank Account.
        /// </summary>
        public static string txtBankAccount {
            get {
                return ResourceManager.GetString("txtBankAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bill .
        /// </summary>
        public static string txtBill {
            get {
                return ResourceManager.GetString("txtBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bill Number.
        /// </summary>
        public static string txtBillNum {
            get {
                return ResourceManager.GetString("txtBillNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bounce Date.
        /// </summary>
        public static string txtBounceDate {
            get {
                return ResourceManager.GetString("txtBounceDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Note.
        /// </summary>
        public static string txtCashNote {
            get {
                return ResourceManager.GetString("txtCashNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Payment.
        /// </summary>
        public static string txtCashPay {
            get {
                return ResourceManager.GetString("txtCashPay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Payment List.
        /// </summary>
        public static string txtCashPayList {
            get {
                return ResourceManager.GetString("txtCashPayList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Pay Notes.
        /// </summary>
        public static string txtCashPayNotes {
            get {
                return ResourceManager.GetString("txtCashPayNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Receive.
        /// </summary>
        public static string txtCashReceive {
            get {
                return ResourceManager.GetString("txtCashReceive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Receive List.
        /// </summary>
        public static string txtCashReceiveList {
            get {
                return ResourceManager.GetString("txtCashReceiveList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Receive Notes.
        /// </summary>
        public static string txtCashReceiveNotes {
            get {
                return ResourceManager.GetString("txtCashReceiveNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cash Transfer.
        /// </summary>
        public static string txtCashTrnsfr {
            get {
                return ResourceManager.GetString("txtCashTrnsfr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check.
        /// </summary>
        public static string txtCheck {
            get {
                return ResourceManager.GetString("txtCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check Number.
        /// </summary>
        public static string txtCheckNum {
            get {
                return ResourceManager.GetString("txtCheckNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Center.
        /// </summary>
        public static string txtCostCenter {
            get {
                return ResourceManager.GetString("txtCostCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit.
        /// </summary>
        public static string txtCredit {
            get {
                return ResourceManager.GetString("txtCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        public static string txtCustomer {
            get {
                return ResourceManager.GetString("txtCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom List.
        /// </summary>
        public static string txtCustomList {
            get {
                return ResourceManager.GetString("txtCustomList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dealer Name.
        /// </summary>
        public static string txtDealerNm {
            get {
                return ResourceManager.GetString("txtDealerNm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        public static string txtDebit {
            get {
                return ResourceManager.GetString("txtDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount.
        /// </summary>
        public static string txtDiscount {
            get {
                return ResourceManager.GetString("txtDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Drawer .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Endorsed.
        /// </summary>
        public static string txtEndorsed {
            get {
                return ResourceManager.GetString("txtEndorsed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Endorse Date.
        /// </summary>
        public static string txtEndorseDate {
            get {
                return ResourceManager.GetString("txtEndorseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expenses.
        /// </summary>
        public static string txtExp {
            get {
                return ResourceManager.GetString("txtExp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expenses Note.
        /// </summary>
        public static string txtExpense {
            get {
                return ResourceManager.GetString("txtExpense", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expenses List.
        /// </summary>
        public static string txtExpList {
            get {
                return ResourceManager.GetString("txtExpList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Expense Number .
        /// </summary>
        public static string txtExpNum {
            get {
                return ResourceManager.GetString("txtExpNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expenses Type.
        /// </summary>
        public static string txtExpType {
            get {
                return ResourceManager.GetString("txtExpType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Expenses Type.
        /// </summary>
        public static string txtExpTypeAdd {
            get {
                return ResourceManager.GetString("txtExpTypeAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  From Date .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Bounced .
        /// </summary>
        public static string txtNoteBounce {
            get {
                return ResourceManager.GetString("txtNoteBounce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Endorse .
        /// </summary>
        public static string txtNoteEndorse {
            get {
                return ResourceManager.GetString("txtNoteEndorse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Payment .
        /// </summary>
        public static string txtNotePay {
            get {
                return ResourceManager.GetString("txtNotePay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Open Balance .
        /// </summary>
        public static string txtOpenBalance {
            get {
                return ResourceManager.GetString("txtOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outstanding.
        /// </summary>
        public static string txtOutstanding {
            get {
                return ResourceManager.GetString("txtOutstanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partial Payment.
        /// </summary>
        public static string txtPartialPayment {
            get {
                return ResourceManager.GetString("txtPartialPayment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay Date.
        /// </summary>
        public static string txtPayDate {
            get {
                return ResourceManager.GetString("txtPayDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payed to Bank.
        /// </summary>
        public static string txtPayedBank {
            get {
                return ResourceManager.GetString("txtPayedBank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payed to Drawer.
        /// </summary>
        public static string txtPayedDrwr {
            get {
                return ResourceManager.GetString("txtPayedDrwr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay Note.
        /// </summary>
        public static string txtPayNote {
            get {
                return ResourceManager.GetString("txtPayNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount.
        /// </summary>
        public static string txtPurDisc {
            get {
                return ResourceManager.GetString("txtPurDisc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Note.
        /// </summary>
        public static string txtReceiveNote {
            get {
                return ResourceManager.GetString("txtReceiveNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenues.
        /// </summary>
        public static string txtRev {
            get {
                return ResourceManager.GetString("txtRev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue Note.
        /// </summary>
        public static string txtRevenue {
            get {
                return ResourceManager.GetString("txtRevenue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue List.
        /// </summary>
        public static string txtRevList {
            get {
                return ResourceManager.GetString("txtRevList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue Number .
        /// </summary>
        public static string txtRevNum {
            get {
                return ResourceManager.GetString("txtRevNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revenue Type.
        /// </summary>
        public static string txtRevType {
            get {
                return ResourceManager.GetString("txtRevType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Revenue Type.
        /// </summary>
        public static string txtRevTypeAdd {
            get {
                return ResourceManager.GetString("txtRevTypeAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discount.
        /// </summary>
        public static string txtSelDisc {
            get {
                return ResourceManager.GetString("txtSelDisc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  To Date .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor.
        /// </summary>
        public static string txtVendor {
            get {
                return ResourceManager.GetString("txtVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry,Can&apos;t Show Archive In More Than Year.
        /// </summary>
        public static string valArchive {
            get {
                return ResourceManager.GetString("valArchive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This code already exists.
        /// </summary>
        public static string valCodeExist {
            get {
                return ResourceManager.GetString("valCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency rate must be greater than 0.
        /// </summary>
        public static string ValCrncRate {
            get {
                return ResourceManager.GetString("ValCrncRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this account from accounts tree.
        /// </summary>
        public static string ValDelAccDenied {
            get {
                return ResourceManager.GetString("ValDelAccDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please make sure to set the intermediate account in settings.
        /// </summary>
        public static string valIntrmdtStorActId {
            get {
                return ResourceManager.GetString("valIntrmdtStorActId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, This Expeses is already used in another Manfucature..
        /// </summary>
        public static string valManfExp {
            get {
                return ResourceManager.GetString("valManfExp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t add a sub-account to a used account.
        /// </summary>
        public static string ValParentAcc {
            get {
                return ResourceManager.GetString("ValParentAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payable Notes.
        /// </summary>
        public static string ValPayableNotes {
            get {
                return ResourceManager.GetString("ValPayableNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t delete this entry, it&apos;s used in purchas invoice No..
        /// </summary>
        public static string valprExp {
            get {
                return ResourceManager.GetString("valprExp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t add this entry, it&apos;s used in purchas invoice No..
        /// </summary>
        public static string valprExp1 {
            get {
                return ResourceManager.GetString("valprExp1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select note account.
        /// </summary>
        public static string ValSelectDbtCrdtAcc {
            get {
                return ResourceManager.GetString("ValSelectDbtCrdtAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You should choose recieving note..
        /// </summary>
        public static string valSourceNoteError {
            get {
                return ResourceManager.GetString("valSourceNoteError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you can&apos;t add this entry, it&apos;s used in strore movement No..
        /// </summary>
        public static string valstoreExp1 {
            get {
                return ResourceManager.GetString("valstoreExp1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter one note.
        /// </summary>
        public static string Valtxt1 {
            get {
                return ResourceManager.GetString("Valtxt1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total amount should equal the sum of all notes.
        /// </summary>
        public static string VAltxt2 {
            get {
                return ResourceManager.GetString("VAltxt2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter revenue name successfully.
        /// </summary>
        public static string ValTxt3 {
            get {
                return ResourceManager.GetString("ValTxt3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter expense name successfully.
        /// </summary>
        public static string ValTxt4 {
            get {
                return ResourceManager.GetString("ValTxt4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select account.
        /// </summary>
        public static string ValtxtAcc {
            get {
                return ResourceManager.GetString("ValtxtAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account already exists.
        /// </summary>
        public static string ValTxtAccExist {
            get {
                return ResourceManager.GetString("ValTxtAccExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Amount.
        /// </summary>
        public static string ValtxtAmount {
            get {
                return ResourceManager.GetString("ValtxtAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select bank.
        /// </summary>
        public static string valtxtbank {
            get {
                return ResourceManager.GetString("valtxtbank", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Drawer to pay the bill to.
        /// </summary>
        public static string ValTxtBillDrwr {
            get {
                return ResourceManager.GetString("ValTxtBillDrwr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter bounce date.
        /// </summary>
        public static string ValtxtBounce {
            get {
                return ResourceManager.GetString("ValtxtBounce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select Pay Account to pay the paper to.
        /// </summary>
        public static string ValTxtCheckDrwr {
            get {
                return ResourceManager.GetString("ValTxtCheckDrwr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please choose sub account.
        /// </summary>
        public static string valtxtChildAcc {
            get {
                return ResourceManager.GetString("valtxtChildAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Code.
        /// </summary>
        public static string valtxtCode {
            get {
                return ResourceManager.GetString("valtxtCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Bank portfolio.
        /// </summary>
        public static string ValTxtCollectBankAcc {
            get {
                return ResourceManager.GetString("ValTxtCollectBankAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter deposit date.
        /// </summary>
        public static string ValTxtCollectDate {
            get {
                return ResourceManager.GetString("ValTxtCollectDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select cost center.
        /// </summary>
        public static string ValTxtCstCntr {
            get {
                return ResourceManager.GetString("ValTxtCstCntr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select custom accounts list.
        /// </summary>
        public static string ValTxtCstmLst {
            get {
                return ResourceManager.GetString("ValTxtCstmLst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select drawer .
        /// </summary>
        public static string ValtxtDrwr {
            get {
                return ResourceManager.GetString("ValtxtDrwr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter maturity date.
        /// </summary>
        public static string ValtxtDueDate {
            get {
                return ResourceManager.GetString("ValtxtDueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Select Dealer to endorse the paper.
        /// </summary>
        public static string ValTxtEndorse {
            get {
                return ResourceManager.GetString("ValTxtEndorse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you have to enter a value in debit or credit columns.
        /// </summary>
        public static string ValtxtJrnl1 {
            get {
                return ResourceManager.GetString("ValtxtJrnl1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit or credit field should equal Zero.
        /// </summary>
        public static string ValtxtJrnl2 {
            get {
                return ResourceManager.GetString("ValtxtJrnl2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Maturity Date.
        /// </summary>
        public static string ValTxtMaturity {
            get {
                return ResourceManager.GetString("ValTxtMaturity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minus Values are not allowed.
        /// </summary>
        public static string ValtxtMinus {
            get {
                return ResourceManager.GetString("ValtxtMinus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter name.
        /// </summary>
        public static string ValTxtName {
            get {
                return ResourceManager.GetString("ValTxtName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter note number.
        /// </summary>
        public static string ValtxtNoteNum {
            get {
                return ResourceManager.GetString("ValtxtNoteNum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter Payment Date.
        /// </summary>
        public static string ValTxtPayDate {
            get {
                return ResourceManager.GetString("ValTxtPayDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter total amount.
        /// </summary>
        public static string ValtxtTotlAmnt {
            get {
                return ResourceManager.GetString("ValtxtTotlAmnt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to you can&apos;t move this account under a used account.
        /// </summary>
        public static string valTxtUsedAccount {
            get {
                return ResourceManager.GetString("valTxtUsedAccount", resourceCulture);
            }
        }
    }
}
