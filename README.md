# LinkIT E-Invoice System

## 📋 Overview

This repository contains the LinkIT E-Invoice system with comprehensive documentation and testing resources for the **SaveDocument API**. The API is responsible for validating and saving invoice documents to the database.

## 📁 Documentation Files

### API Documentation
- **`SaveDocument_API_Documentation.md`** - Complete API documentation with field descriptions, validation rules, and examples
- **`Postman_Testing_Guide.md`** - Step-by-step guide for testing the API using Postman
- **`EInvoice_SaveDocument_Collection.postman_collection.json`** - Ready-to-import Postman collection

## 🚀 SaveDocument API Quick Start

### Endpoint
```
POST /api/EInvoice/SaveInvoice
```

### ⚠️ Mandatory Fields

#### Invoice Level (Required)
```json
{
  "companyId": 1,           // Must exist in StCompanyInfo
  "storeId": 1,             // Must exist in IcStore
  "customerId": 1,          // Must exist in SlCustomer
  "date": "2024-01-15T10:30:00Z",  // ISO 8601 format
  "documentType": "I",      // "I", "C", or "D"
  "invoiceCode": "INV-001", // Unique per date
  "invoiceDetails": [...]   // At least one item required
}
```

#### Invoice Detail Level (Required)
```json
{
  "itemId": 1,              // Must exist in IcItem
  "quantity": 1.0,          // Must be > 0
  "currencyId": 1,          // Must exist in StCurrency
  "exchangeRate": 1.0,      // Must be > 0
  "amount": 100.00,         // Must be > 0
  "uomId": 1               // Must exist in IcUom
}
```

### ℹ️ Optional Fields

#### Invoice Level (Optional)
- `invoiceId` - Internal ID (null for new invoices)
- `storeName`, `customerName` - Display names
- `TotalDiscount`, `TotalTax`, `Net` - Calculated automatically
- `PurchaseOrderNumber`, `ProformaNumber` - Reference numbers
- `DeliveryDate` - Delivery date (YYYY-MM-DD)
- `Uuid` - Required for Credit/Debit notes only

#### Invoice Detail Level (Optional)
- `Id` - Auto-generated if 0/null
- `itemName`, `currencyName`, `uomName` - Display names
- `Description` - Item description
- `discountAfterTax`, `totalAmount` - Calculated automatically
- `discount`, `discounts`, `taxes` - Discount and tax details

## 📊 Document Types

| Type | Description | UUID Required |
|------|-------------|---------------|
| **"I"** | Invoice (فاتورة) | No |
| **"C"** | Credit Note (إشعار خصم) | Yes |
| **"D"** | Debit Note (إشعار إضافة) | Yes |

## 🧪 Testing with Postman

### 1. Import Collection
1. Open Postman
2. Click **Import** → **Upload Files**
3. Select `EInvoice_SaveDocument_Collection.postman_collection.json`
4. Update environment variables:
   - `base_url`: Your API server URL (e.g., `http://localhost:5000`)
   - `company_id`, `store_id`, `customer_id`: Valid IDs from your database

### 2. Test Cases Included
- ✅ **Valid Invoice** - All fields populated
- ✅ **Minimal Invoice** - Only mandatory fields
- ❌ **Missing Invoice Code** - Validation error test
- ❌ **Empty Details** - Validation error test
- ✅ **Credit Note** - With UUID reference

## 🔧 Success Response
```
HTTP 200 OK
"Document Saved"
```

## ❌ Error Response
```json
HTTP 400 Bad Request
{
  "Messages": [
    {
      "invoiceCode": "INV-001",
      "Message": ["Error message in Arabic"],
      "Islocal": true
    }
  ]
}
```

## 🏗️ Prerequisites

Ensure the following master data exists in your database:
1. **Company Information** (`StCompanyInfo`) - Valid company with tax registration
2. **Store Information** (`IcStore`) - Active store belonging to the company
3. **Customer Information** (`SlCustomer`) - Active customer with valid type
4. **Item Information** (`IcItem`) - Active items with valid codes and tax configuration
5. **Currency Information** (`StCurrency`) - Active currencies
6. **Unit of Measure** (`IcUom`) - Active UOMs

## 📖 Detailed Documentation

For complete documentation including:
- Full field descriptions and validation rules
- Business logic and tax calculations
- Comprehensive test scenarios
- Troubleshooting guide

Please refer to:
- `SaveDocument_API_Documentation.md` - Complete API reference
- `Postman_Testing_Guide.md` - Detailed testing instructions

## 🐛 Common Issues

1. **Connection Refused** → Check if API server is running
2. **404 Not Found** → Verify URL and route configuration
3. **500 Internal Server Error** → Check server logs and database connection
4. **Validation Errors** → Ensure all referenced IDs exist in master tables

## 📝 Notes

- All error messages are returned in Arabic
- Invoice codes must be unique per date and document type
- Credit and Debit notes require UUID references
- Tax calculations are performed automatically
- Decimal precision limited to 5 decimal places