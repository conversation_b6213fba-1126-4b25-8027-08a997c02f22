﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace DAL.Res {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "15.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class ResSLAr {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ResSLAr() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("DAL.Res.ResSLAr", typeof(ResSLAr).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كشف حساب تفصيلي.
        /// </summary>
        public static string accDetail {
            get {
                return ResourceManager.GetString("accDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الوضع الإفتراضي لحسابات عملاء الفئة.
        /// </summary>
        public static string defaultCustGroupAcc {
            get {
                return ResourceManager.GetString("defaultCustGroupAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف فئة العميل، فهي مستخدمة من خلال خطط التسويق.
        /// </summary>
        public static string DelCustGrpOnMr {
            get {
                return ResourceManager.GetString("DelCustGrpOnMr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا غير مصرح لك تحديث بيانات عملاء هذه الفئة.
        /// </summary>
        public static string DfltCustGrp {
            get {
                return ResourceManager.GetString("DfltCustGrp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا.
        /// </summary>
        public static string False {
            get {
                return ResourceManager.GetString("False", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الشهر مسجل بالفعل.
        /// </summary>
        public static string MonthExists {
            get {
                return ResourceManager.GetString("MonthExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف خطة تسويق الشهر؟.
        /// </summary>
        public static string MrDelTarget {
            get {
                return ResourceManager.GetString("MrDelTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تحديد الشهر لهذه الخطة التسويقية.
        /// </summary>
        public static string MrEnterDate {
            get {
                return ResourceManager.GetString("MrEnterDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم تحميل بيانات الشهر بنجاح.
        /// </summary>
        public static string MrMonthLoad {
            get {
                return ResourceManager.GetString("MrMonthLoad", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد خطة تسويق مسجلة لهذا الشهر.
        /// </summary>
        public static string MrPlanNotfound {
            get {
                return ResourceManager.GetString("MrPlanNotfound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد الاستمرار بالموافقة على أوامر البيع؟.
        /// </summary>
        public static string MsgAskApproveSalesOrders {
            get {
                return ResourceManager.GetString("MsgAskApproveSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد انشاء اذن اضافة للمخزن الان؟.
        /// </summary>
        public static string MsgAskCreateInTrns {
            get {
                return ResourceManager.GetString("MsgAskCreateInTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد انشاء اذن صرف من المخزن الان ؟.
        /// </summary>
        public static string MsgAskCreateOutTrns {
            get {
                return ResourceManager.GetString("MsgAskCreateOutTrns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف.
        /// </summary>
        public static string MsgAskDel {
            get {
                return ResourceManager.GetString("MsgAskDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف العميل ؟.
        /// </summary>
        public static string MsgAskDeleteCust {
            get {
                return ResourceManager.GetString("MsgAskDeleteCust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد انك تريد الخروج  ؟.
        /// </summary>
        public static string MsgAskExit {
            get {
                return ResourceManager.GetString("MsgAskExit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد الاستمرار بإلغاء الموافقة على أوامر البيع؟.
        /// </summary>
        public static string MsgAskRejectSalesOrders {
            get {
                return ResourceManager.GetString("MsgAskRejectSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء حفظ الفاتورة اولا.
        /// </summary>
        public static string MsgAskToSaveInv {
            get {
                return ResourceManager.GetString("MsgAskToSaveInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to برجاء حفظ امر البيع اولا.
        /// </summary>
        public static string MsgAskToSaveOrder {
            get {
                return ResourceManager.GetString("MsgAskToSaveOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن تغيير فئة العميل، بعد أن تم استخدام الحساب الخاص به.
        /// </summary>
        public static string MsgChangeGroup {
            get {
                return ResourceManager.GetString("MsgChangeGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل تريد تغيير اكواد الفئات الفرعية؟.
        /// </summary>
        public static string MsgChangeSubCode {
            get {
                return ResourceManager.GetString("MsgChangeSubCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود الفئة الفرعية اصغر من كود الفئة الرئيسة.
        /// </summary>
        public static string msgCheckSubCatCode {
            get {
                return ResourceManager.GetString("msgCheckSubCatCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الكود مسجل من قبل.
        /// </summary>
        public static string MsgCodeExist {
            get {
                return ResourceManager.GetString("MsgCodeExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن تغيير كود الفئة.
        /// </summary>
        public static string MsgCustGrpCode {
            get {
                return ResourceManager.GetString("MsgCustGrpCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا.
        /// </summary>
        public static string MsgDataModified {
            get {
                return ResourceManager.GetString("MsgDataModified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحذف بنجاح.
        /// </summary>
        public static string MsgDel {
            get {
                return ResourceManager.GetString("MsgDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لايمكن حذف العميل، يوجد عمليات بالنظام مرتبطة به.
        /// </summary>
        public static string MsgDelCustDenied {
            get {
                return ResourceManager.GetString("MsgDelCustDenied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد انك تريد حذف فئة العملاء هذه.
        /// </summary>
        public static string MsgDelCustGroup {
            get {
                return ResourceManager.GetString("MsgDelCustGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن حذف هذه الفئة، يوجد عملاء مرتبطون بها.
        /// </summary>
        public static string MsgDelCustGroup2 {
            get {
                return ResourceManager.GetString("MsgDelCustGroup2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل أنت متأكد من أنك تريد حذف هذا المستند.
        /// </summary>
        public static string MsgDeleteInv {
            get {
                return ResourceManager.GetString("MsgDeleteInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب حذف القيود الخاصه بهذا العميل أولا.
        /// </summary>
        public static string MsgDeleteJornalsFirst {
            get {
                return ResourceManager.GetString("MsgDeleteJornalsFirst", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكنك حذف هذا الحساب، يوجد حسابات اخرى مرتبطة به.
        /// </summary>
        public static string msgDelLinked {
            get {
                return ResourceManager.GetString("msgDelLinked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حذف صف ؟.
        /// </summary>
        public static string MsgDelRow {
            get {
                return ResourceManager.GetString("MsgDelRow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال كود العميل.
        /// </summary>
        public static string MsgEnterCustomerCode {
            get {
                return ResourceManager.GetString("MsgEnterCustomerCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يرجى إدخال اسم العميل.
        /// </summary>
        public static string MsgEnterCustomerName {
            get {
                return ResourceManager.GetString("MsgEnterCustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تأكد من صحة البيانات.
        /// </summary>
        public static string MsgIncorrectData {
            get {
                return ResourceManager.GetString("MsgIncorrectData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكنك تغيير هذا الحساب، عليك حذف القيود الخاصة به أولا.
        /// </summary>
        public static string msgLinkAcc {
            get {
                return ResourceManager.GetString("msgLinkAcc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، لايمكن انشاء رصيد افتتاحي لهذا الحساب.
        /// </summary>
        public static string msgLinkAccOpen {
            get {
                return ResourceManager.GetString("msgLinkAccOpen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لم يتم اعتماد أمر البيع بعد.
        /// </summary>
        public static string msgMustApproveSalesOrder {
            get {
                return ResourceManager.GetString("msgMustApproveSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الاسم مسجل من قبل.
        /// </summary>
        public static string MsgNameExist {
            get {
                return ResourceManager.GetString("MsgNameExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد حساب لهذا العميل.
        /// </summary>
        public static string MsgNoAccount {
            get {
                return ResourceManager.GetString("MsgNoAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد كميه كافيه من أحد الاصناف.
        /// </summary>
        public static string MsgNoEnoughQty {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لايوجد كميه كافيه من أحد الاصناف, هل تريد الاستمرار  ؟.
        /// </summary>
        public static string MsgNoEnoughQty_continue {
            get {
                return ResourceManager.GetString("MsgNoEnoughQty_continue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا لا يوجد فواتير مبيعات غير مرحلة .
        /// </summary>
        public static string msgnoNonArchivedSLInvoices {
            get {
                return ResourceManager.GetString("msgnoNonArchivedSLInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هذا الرقم مسجل من قبل.
        /// </summary>
        public static string MsgNumExist {
            get {
                return ResourceManager.GetString("MsgNumExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية حذف هذا البيان.
        /// </summary>
        public static string MsgPrvDel {
            get {
                return ResourceManager.GetString("MsgPrvDel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية تعديل هذا البيان.
        /// </summary>
        public static string MsgPrvEdit {
            get {
                return ResourceManager.GetString("MsgPrvEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا، انت لاتملك صلاحية اضافة بيان جديد.
        /// </summary>
        public static string MsgPrvNew {
            get {
                return ResourceManager.GetString("MsgPrvNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ! يوجد أصناف كميتها اقل من او تساوي صفر برجاء مراجعة البيانات.
        /// </summary>
        public static string MsgQtyLessZero {
            get {
                return ResourceManager.GetString("MsgQtyLessZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الموافقة على أوامر البيع بنجاح.
        /// </summary>
        public static string MsgSalesOrdersApprovedSuccessfully {
            get {
                return ResourceManager.GetString("MsgSalesOrdersApprovedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم إلغاء الموافقة على أوامر البيع بنجاح.
        /// </summary>
        public static string MsgSalesOrdersRejectedSuccessfully {
            get {
                return ResourceManager.GetString("MsgSalesOrdersRejectedSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تم الحفظ بنجاح.
        /// </summary>
        public static string MsgSave {
            get {
                return ResourceManager.GetString("MsgSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خطأ.
        /// </summary>
        public static string MsgTError {
            get {
                return ResourceManager.GetString("MsgTError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to معلومة.
        /// </summary>
        public static string MsgTInfo {
            get {
                return ResourceManager.GetString("MsgTInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سؤال.
        /// </summary>
        public static string MsgTQues {
            get {
                return ResourceManager.GetString("MsgTQues", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تحذير.
        /// </summary>
        public static string MsgTWarn {
            get {
                return ResourceManager.GetString("MsgTWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to كود العميل لايمكن أن يساوي صفر.
        /// </summary>
        public static string MsgValidateCustomerCode {
            get {
                return ResourceManager.GetString("MsgValidateCustomerCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لا يمكن تكرار ارقام التليفون.
        /// </summary>
        public static string repeatPhoneNumber {
            get {
                return ResourceManager.GetString("repeatPhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار مندوب البيع.
        /// </summary>
        public static string SalesEmpMandatory {
            get {
                return ResourceManager.GetString("SalesEmpMandatory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لأمر بيع رقم.
        /// </summary>
        public static string soCode {
            get {
                return ResourceManager.GetString("soCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نعم.
        /// </summary>
        public static string True {
            get {
                return ResourceManager.GetString("True", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خصم.
        /// </summary>
        public static string txt_Discount {
            get {
                return ResourceManager.GetString("txt_Discount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سداد.
        /// </summary>
        public static string txt_Paid {
            get {
                return ResourceManager.GetString("txt_Paid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عمولة مبيعات.
        /// </summary>
        public static string txt_SalesCommission {
            get {
                return ResourceManager.GetString("txt_SalesCommission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ضريبة.
        /// </summary>
        public static string txt_Tax {
            get {
                return ResourceManager.GetString("txt_Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تام.
        /// </summary>
        public static string txtAssembly {
            get {
                return ResourceManager.GetString("txtAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد أنك تريد حذف العميل العام.
        /// </summary>
        public static string txtCantDeleteGCustomer {
            get {
                return ResourceManager.GetString("txtCantDeleteGCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to هل انت متأكد أنك تريد تعديل العميل العام.
        /// </summary>
        public static string txtCantEditGCustomer {
            get {
                return ResourceManager.GetString("txtCantEditGCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الفئه: .
        /// </summary>
        public static string txtCategory {
            get {
                return ResourceManager.GetString("txtCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الشركة: .
        /// </summary>
        public static string txtComapny {
            get {
                return ResourceManager.GetString("txtComapny", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to دائن.
        /// </summary>
        public static string txtCredit {
            get {
                return ResourceManager.GetString("txtCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to العميل: .
        /// </summary>
        public static string txtCustomer {
            get {
                return ResourceManager.GetString("txtCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  اسم المتعامل.
        /// </summary>
        public static string txtDealerNm {
            get {
                return ResourceManager.GetString("txtDealerNm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مدين.
        /// </summary>
        public static string txtDebit {
            get {
                return ResourceManager.GetString("txtDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار حساب الخصم  في شاشة الفروع/ المخازن .
        /// </summary>
        public static string txtDiscountAccount {
            get {
                return ResourceManager.GetString("txtDiscountAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خ ن :.
        /// </summary>
        public static string txtDiscRatio {
            get {
                return ResourceManager.GetString("txtDiscRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to خ ق :.
        /// </summary>
        public static string txtDiscValue {
            get {
                return ResourceManager.GetString("txtDiscValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  خزينه .
        /// </summary>
        public static string txtDrawer {
            get {
                return ResourceManager.GetString("txtDrawer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to انتهي.
        /// </summary>
        public static string txtEnded {
            get {
                return ResourceManager.GetString("txtEnded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تجاوز حد الطلب هل تريد الاستمرار ؟.
        /// </summary>
        public static string txtExceedsReorder {
            get {
                return ResourceManager.GetString("txtExceedsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مصاريف :.
        /// </summary>
        public static string txtExpenses {
            get {
                return ResourceManager.GetString("txtExpenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من .
        /// </summary>
        public static string txtFrom {
            get {
                return ResourceManager.GetString("txtFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  من تاريخ .
        /// </summary>
        public static string txtFromDate {
            get {
                return ResourceManager.GetString("txtFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to التاريخ.
        /// </summary>
        public static string txtInvDate {
            get {
                return ResourceManager.GetString("txtInvDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نوع السداد.
        /// </summary>
        public static string txtInvPayMethod {
            get {
                return ResourceManager.GetString("txtInvPayMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الصنف:.
        /// </summary>
        public static string txtItem {
            get {
                return ResourceManager.GetString("txtItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الصنف موجود مسبقا.
        /// </summary>
        public static string txtItemExist {
            get {
                return ResourceManager.GetString("txtItemExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to حركة الاصناف.
        /// </summary>
        public static string txtItemMovement {
            get {
                return ResourceManager.GetString("txtItemMovement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أرصدة الأصناف.
        /// </summary>
        public static string txtItemsBalance {
            get {
                return ResourceManager.GetString("txtItemsBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاصناف الاكثر مبيعا.
        /// </summary>
        public static string txtItemsBestSell {
            get {
                return ResourceManager.GetString("txtItemsBestSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاصناف الاقل مبيعا.
        /// </summary>
        public static string txtItemsLeastSell {
            get {
                return ResourceManager.GetString("txtItemsLeastSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف لم تباع مطلقا.
        /// </summary>
        public static string txtItemsNotSold {
            get {
                return ResourceManager.GetString("txtItemsNotSold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to أصناف وصلت لحد الطلب.
        /// </summary>
        public static string txtItemsReorder {
            get {
                return ResourceManager.GetString("txtItemsReorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مشتريات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalPurchases {
            get {
                return ResourceManager.GetString("txtItemTotalPurchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مردود مشتريات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalPurchasesReturns {
            get {
                return ResourceManager.GetString("txtItemTotalPurchasesReturns", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مبيعات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalSales {
            get {
                return ResourceManager.GetString("txtItemTotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اجمالي مردود مبيعات صنف/أصناف.
        /// </summary>
        public static string txtItemTotalSalesReturn {
            get {
                return ResourceManager.GetString("txtItemTotalSalesReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اخر اسعار بيع لعميل.
        /// </summary>
        public static string txtLastCustomerPPrices {
            get {
                return ResourceManager.GetString("txtLastCustomerPPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to اخر اسعار بيع .
        /// </summary>
        public static string txtLastPPrices {
            get {
                return ResourceManager.GetString("txtLastPPrices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الصافي :.
        /// </summary>
        public static string txtNet {
            get {
                return ResourceManager.GetString("txtNet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ملاحظات.
        /// </summary>
        public static string txtNotes {
            get {
                return ResourceManager.GetString("txtNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  رصيد افتتاحي .
        /// </summary>
        public static string txtOpenBalance {
            get {
                return ResourceManager.GetString("txtOpenBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المدفوع :.
        /// </summary>
        public static string txtPaid {
            get {
                return ResourceManager.GetString("txtPaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المتبقي :.
        /// </summary>
        public static string txtRemains {
            get {
                return ResourceManager.GetString("txtRemains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to استرجاع فاتورة F10.
        /// </summary>
        public static string txtRestore {
            get {
                return ResourceManager.GetString("txtRestore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تسلسل.
        /// </summary>
        public static string txtSerial {
            get {
                return ResourceManager.GetString("txtSerial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فاتورة مبيعات رقم .
        /// </summary>
        public static string txtSLInvNumber {
            get {
                return ResourceManager.GetString("txtSLInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عرض أسعار رقم.
        /// </summary>
        public static string txtSLQuoteNumber {
            get {
                return ResourceManager.GetString("txtSLQuoteNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فاتورة مرتجع مبيعات رقم .
        /// </summary>
        public static string txtSLReturnInvNumber {
            get {
                return ResourceManager.GetString("txtSLReturnInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to مستمر.
        /// </summary>
        public static string txtStanding {
            get {
                return ResourceManager.GetString("txtStanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المخزن: .
        /// </summary>
        public static string txtStore {
            get {
                return ResourceManager.GetString("txtStore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to تعليق F10.
        /// </summary>
        public static string txtSuspend {
            get {
                return ResourceManager.GetString("txtSuspend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  ض ع ن:.
        /// </summary>
        public static string txtTaxRatio {
            get {
                return ResourceManager.GetString("txtTaxRatio", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ض ع ق:.
        /// </summary>
        public static string txtTaxValue {
            get {
                return ResourceManager.GetString("txtTaxValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى .
        /// </summary>
        public static string txtTo {
            get {
                return ResourceManager.GetString("txtTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  الى تاريخ .
        /// </summary>
        public static string txtToDate {
            get {
                return ResourceManager.GetString("txtToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الاجمالي :.
        /// </summary>
        public static string txtTotal {
            get {
                return ResourceManager.GetString("txtTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فواتير المبيعات.
        /// </summary>
        public static string txtTotalSales {
            get {
                return ResourceManager.GetString("txtTotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فئة العميل تخطت حد الائتمان.
        /// </summary>
        public static string txtValidateCustGrpMaxDebit {
            get {
                return ResourceManager.GetString("txtValidateCustGrpMaxDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to فئة العميل تخطت حد الائتمان, هل تريد المتابعة؟.
        /// </summary>
        public static string txtValidateCustGrpMaxDebitWarn {
            get {
                return ResourceManager.GetString("txtValidateCustGrpMaxDebitWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to العميل تخطى حد الائتمان.
        /// </summary>
        public static string txtValidateCustomerMaxDebit {
            get {
                return ResourceManager.GetString("txtValidateCustomerMaxDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to العميل تخطى حد الائتمان, , هل تريد المتابعة؟.
        /// </summary>
        public static string txtValidateCustomerMaxDebitWarn {
            get {
                return ResourceManager.GetString("txtValidateCustomerMaxDebitWarn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تحديد الخصم.
        /// </summary>
        public static string txtValidateDiscount {
            get {
                return ResourceManager.GetString("txtValidateDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل رقم الفاتوره.
        /// </summary>
        public static string txtValidateInvNumber {
            get {
                return ResourceManager.GetString("txtValidateInvNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار الصنف.
        /// </summary>
        public static string txtValidateItem {
            get {
                return ResourceManager.GetString("txtValidateItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to نسبة الخصم لايمكن ان تتجاوز المائة.
        /// </summary>
        public static string txtValidateMaxDiscount {
            get {
                return ResourceManager.GetString("txtValidateMaxDiscount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية الموجودة حاليا في المخزن بعد خصم الكمية المباعة أقل من الحد الأدنى للكمية.
        /// </summary>
        public static string txtValidateMinQty {
            get {
                return ResourceManager.GetString("txtValidateMinQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب تسجيل صنف علي الاقل في الفاتوره.
        /// </summary>
        public static string txtValidateNoRows {
            get {
                return ResourceManager.GetString("txtValidateNoRows", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا ، غير مصرح للمستخدم انشاء فواتير اجلة .
        /// </summary>
        public static string txtValidateOnCreditInv {
            get {
                return ResourceManager.GetString("txtValidateOnCreditInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية يجب ان تكون اكبر من الصفر.
        /// </summary>
        public static string txtValidateQty {
            get {
                return ResourceManager.GetString("txtValidateQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية الموجودة حاليا في المخزن بعد خصم الكمية المباعة أقل من حد الطلب.
        /// </summary>
        public static string txtValidateReorderQty {
            get {
                return ResourceManager.GetString("txtValidateReorderQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سعر البيع يجب أن يكون أكبر من الصفر.
        /// </summary>
        public static string txtValidateSPrice {
            get {
                return ResourceManager.GetString("txtValidateSPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to سعر البيع يجب أن يكون أكبر من سعر الشراء.
        /// </summary>
        public static string txtValidateSpriceLargerPprice {
            get {
                return ResourceManager.GetString("txtValidateSpriceLargerPprice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to الكمية لا يمكن ان تكون اكبر من الرصيدالحالي.
        /// </summary>
        public static string txtValidateStoreQty {
            get {
                return ResourceManager.GetString("txtValidateStoreQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار وحدة القياس.
        /// </summary>
        public static string txtValidateUom {
            get {
                return ResourceManager.GetString("txtValidateUom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to عفوا... يجب اختيار نوع الضريبة.
        /// </summary>
        public static string txtvalidationTaxType {
            get {
                return ResourceManager.GetString("txtvalidationTaxType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to المورد.
        /// </summary>
        public static string txtVendor {
            get {
                return ResourceManager.GetString("txtVendor", resourceCulture);
            }
        }
    }
}
