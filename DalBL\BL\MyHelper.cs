﻿using System;
using System.Data;
using System.Data.Linq;
using System.Collections;
using System.Data.SqlClient;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DAL.Res;
using DAL;

namespace DAL
{
    public static class MyHelper
    {

        public static DateTime Get_Server_DateTime()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            DateTime DateTimeNow = DB.ExecuteQuery<DateTime>("Select GETDATE()").First();
            return DateTimeNow;
        }
        public static DateTime GetDateCurrentTime(DateTime date)
        {
            DateTime today = Get_Server_DateTime();
            return new DateTime(date.Year, date.Month, date.Day, today.Hour, today.Minute, today.Second);
        }

        public static decimal FractionToDouble(string fraction)
        {
            decimal result;

            if (fraction == null || fraction == string.Empty)
                return 1;

            fraction = fraction.EndsWith(" ") ? fraction.Remove(fraction.Length - 1) : fraction;
            fraction = fraction.StartsWith(" ") ? fraction.Remove(0, 1) : fraction;

            if (decimal.TryParse(fraction, out result))
            {
                return decimal.Round(result, 6);
            }

            string[] split = fraction.Split(new char[] { ' ', '/' });

            if (split.Length == 2 || split.Length == 3)
            {
                decimal a, b;

                if (decimal.TryParse(split[0], out a) && decimal.TryParse(split[1], out b))
                {
                    if (split.Length == 2)
                    {
                        return decimal.Round(((decimal)a / b), 6);
                    }

                    decimal c;

                    if (decimal.TryParse(split[2], out c))
                    {
                        return decimal.Round((a + (decimal)b / c), 6);
                    }
                }
            }

            return 1;
        }

        /// <summary>
        /// Load DataTable with item UOMs,Factors, Default Buy and Default Sell UOM
        /// </summary>
        /// <param name="item">the Item</param>
        /// <param name="dt"> dataTable contains UOMs data</param>
        public static void GetUOMs(DAL.IC_Item item, DataTable dt, List<DAL.IC_UOM> uom_list)
        {
            //get UOMs
            dt.Clear();
            var uomName = "";
            //small(Default)
            if (item.SmallIsStopped != true)
            {
             uomName = (from  u in uom_list
                           where u.UOMId == item.SmallUOM
                           select u.UOM).FirstOrDefault();
             if(uomName!=null)
             dt.Rows.Add(0,
                        item.SmallUOM,
                        uomName,
                        1);
            }
           

            //Medium
            if (item.MediumUOM.HasValue&& item.MediumIsStopped != true)
            {
                uomName = (from u in uom_list
                           where u.UOMId == item.MediumUOM 
                           select u.UOM).FirstOrDefault();
                if (uomName != null)
                    dt.Rows.Add(1,
                            item.MediumUOM.Value,
                            uomName,
                            item.MediumUOMFactor);
            }

            //Large
            if (item.LargeUOM.HasValue && item.LargeIsStopped != true)
            {
                uomName = (from u in uom_list
                           where u.UOMId == item.LargeUOM
                           select u.UOM).FirstOrDefault();
                if (uomName != null)
                    dt.Rows.Add(2,
                            item.LargeUOM.Value,
                            uomName,
                            item.LargeUOMFactor);
            }
        }

        /// <summary>
        /// creates a datatable for UOMs, to be used by all forms        
        /// </summary>
        /// <param name="dtUOM">Uom datatable</param>
        /// <returns>Uom datatable</returns>
        public static DataTable GetUomDataTable(DataTable dtUOM)
        {
            dtUOM.Columns.Clear();
            dtUOM.Columns.Add("Index");
            dtUOM.Columns.Add("UomId");
            dtUOM.Columns.Add("Uom");
            dtUOM.Columns.Add("Factor");
            return dtUOM;
        }

        public static DataTable GetBOMDataTable(DataTable dtBOM)
        {
            dtBOM.Columns.Clear();
            dtBOM.Columns.Add("BOMId");
            dtBOM.Columns.Add("BOM_Name");
            dtBOM.Columns.Add("UOM");
            dtBOM.Columns.Add("Qty");

            return dtBOM;
        }

        public static void GetBOMs(int ItemId, DataTable dt, List<IC_BOM> bom_list, List<IC_UOM> uom_list)
        {
            dt.Clear();

            var data = (from d in bom_list
                        where d.ProductItemId == ItemId
                        join u in uom_list on d.UomId equals u.UOMId
                        select new
                        {
                            d.BOMId,
                            d.BOM_Name,
                            u.UOM,
                            d.Qty
                        }).ToList();

            foreach (var d in data)
                dt.Rows.Add(d.BOMId, d.BOM_Name, d.UOM, d.Qty);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="dtExpireQty"></param>
        /// <returns></returns>
        public static DataTable GetExpireQtyDataTable(DataTable dtExpireQty)
        {
            dtExpireQty.Columns.Clear();
            dtExpireQty.Columns.Add("ExpireId");
            dtExpireQty.Columns.Add("Expire");
            dtExpireQty.Columns.Add("Batch");
            dtExpireQty.Columns.Add("Qty");
            return dtExpireQty;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="itemId"></param>
        /// <param name="storeId"></param>
        /// <param name="dt"></param>
        public static void Get_Expire_Qtys(int itemId, int storeId, DateTime till_date, DataTable dt)
        {
            dt.Rows.Clear();
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var itemsInStore = (from i in DB.IC_ItemStores
                                where i.ItemId == itemId
                                where i.StoreId == storeId
                                where i.InsertTime < till_date
                                group i by new { i.Expire, i.Batch } into grp
                                select new
                                {
                                    Qty = grp.Select(x => x.IsInTrns == true ? x.Qty : x.Qty * -1).Sum(),
                                    Batch = grp.Key.Batch,
                                    Expire = grp.Key.Expire,
                                }).ToList().OrderBy(x => x.Expire);

            foreach (var t in itemsInStore)
            {
                if (t.Qty > 0)
                {
                    dt.Rows.Add(t.Expire + t.Batch, t.Expire, t.Batch, decimal.ToDouble(t.Qty));
                }
            }
        }

        public static void Get_Batch_Qtys(int itemId, int storeId, DateTime till_date, DataTable dt)
        {
            dt.Rows.Clear();
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var itemsInStore = (from i in DB.IC_ItemStores
                                where i.ItemId == itemId
                                where i.StoreId == storeId
                                where i.InsertTime < till_date
                                group i by i.Batch into grp
                                let Qty = grp.Select(x => x.IsInTrns == true ? x.Qty : x.Qty * -1).Sum()
                                where Qty > 0
                                select new
                                {
                                    Qty = Qty,
                                    Batch = grp.Key,
                                }).ToList();

            foreach (var t in itemsInStore)
            {
                if (t.Qty > 0)
                {
                    dt.Rows.Add(t.Batch, decimal.ToDouble(t.Qty));
                }
            }
        }


        /// <summary>
        /// Load DataTable with Stores, and return default Store Id
        /// </summary>
        /// <param name="StoresDataTable"> Stores DataTable</param>
        /// <returns>default Store Id</returns>
        //public static int GetStores(DataTable StoresDataTable, bool IsStore)
        //{
        //    StoresDataTable.Columns.Clear();
        //    StoresDataTable.Columns.Add("StoreId");
        //    StoresDataTable.Columns.Add("StoreCode");
        //    StoresDataTable.Columns.Add("StoreNameAr");
        //    StoresDataTable.Columns.Add("IsDefault");

        //    DAL.ERPDataContext DB = new DAL.ERPDataContext();

        //    var storesQuery = (from d in DB.IC_Stores
        //                       where IsStore == false ? d.ParentId.HasValue == false       //Branch
        //                             : DB.IC_Stores.Where(x => x.ParentId == d.StoreId).Count() == 0
        //                       select d).ToList();

        //    //mahmoud:27-1-2013
        //    int DefaultStore = 0;
        //    var userSettings = DB.ST_Stores.Where(s => s.UserId == Utilities.UserId).FirstOrDefault();
        //    if (userSettings != null && userSettings.DefaultStore != 0)
        //        DefaultStore = userSettings.DefaultStore;
        //    else
        //        DefaultStore = storesQuery.FirstOrDefault().StoreId;

        //    if (storesQuery.Count > 0)
        //    {
        //        foreach (var store in storesQuery)
        //        {
        //            if (Shared.st_Store.UserChangeStore == false && store.StoreId != Shared.st_Store.DefaultStore)
        //                continue;//don't add unauthorized stores for user

        //            if (store.StoreId == DefaultStore)
        //                StoresDataTable.Rows.Add(store.StoreId, store.StoreCode, store.StoreNameAr, true);
        //            else
        //                StoresDataTable.Rows.Add(store.StoreId, store.StoreCode, store.StoreNameAr, false);
        //        }
        //        //select default store
        //        return DefaultStore;
        //    }
        //    return 0;//no stores recorded in DB
        //}

        /// <summary>
        /// 
        /// </summary>
        /// <param name="IsStore"> true: load stores, false: load branches</param>
        /// <param name="defaultStoreId"></param>
        /// <returns></returns>
        public static List<IC_Store> Get_Stores(bool? IsStore, out int defaultStoreId, bool UserChangeStore,
            int DefaultStore, int UserId)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            //var strLst = DB.IC_Stores.Where(a => a.IsStopped == false||a.IsStopped==null).ToList();
            var strLst = DB.IC_Stores.ToList();

            if (!IsStore.HasValue)
            {
                defaultStoreId = strLst.FirstOrDefault().StoreId;
                return strLst;
            }

            List<IC_Store> availableStores;

            var query = DB.IC_User_Stores.Where(x => x.UserId == UserId).Select(i => i.StoreId);
            if (query.Count() > 0)
            {
                availableStores = strLst.Where(s => query.Contains(s.StoreId)).ToList();
            }
            else
            {
                availableStores = (from d in strLst
                                   where UserChangeStore ? true :
                                         d.StoreId == DefaultStore
                                   where IsStore == false ? d.ParentId.HasValue == false       //Branch
                                         : strLst.Where(x => x.ParentId == d.StoreId).Count() == 0
                                   select d).ToList();
            }
            if (availableStores.Count == 0)//user default store doesn't exist in level
            {
                IC_Store defStore = strLst.Where(s => s.StoreId == DefaultStore).Select(s => s).FirstOrDefault();
                if (defStore.ParentId.HasValue)//user store not in branches :- get branch
                {
                    availableStores = strLst.Where(s => s.StoreId == defStore.ParentId.Value).ToList();
                    defaultStoreId = availableStores.Select(s => s.StoreId).First();
                }
                else //user branch not in stores :- get child stores
                {
                    availableStores = strLst.Where(s => s.ParentId == defStore.StoreId).ToList();
                    defaultStoreId = availableStores.Select(s => s.StoreId).First();
                }
            }
            else
            {
                //test whether user has a default store, and exists in lsit of available stores
                if (UserId > 0 && DefaultStore != 0
                    && availableStores.Exists(x => x.StoreId == DefaultStore))
                    defaultStoreId = DefaultStore;
                else
                    defaultStoreId = availableStores.FirstOrDefault().StoreId;
            }

            return availableStores;
        }

        /// <summary>
        /// get all branches and stores, and in case user can't change store, it gets this store and its childs only
        /// </summary>
        /// <returns></returns>
        public static List<IC_Store> Get_Stores4Reports(bool UserChangeStore, int DefaultStore)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var strLst = DB.IC_Stores.ToList();


            List<IC_Store> availableStores;
            //var query = DB.IC_User_Stores.Where(x => x.UserId == Shared.UserId).Select(i => i.StoreId);
            //if (query.Count() > 0)
            //{
            //    availableStores = strLst.Where(s => query.Contains(s.StoreId)).ToList();
            //}
            //else
            {
                availableStores = (from d in strLst
                                   where UserChangeStore ? true :
                                         (d.StoreId == DefaultStore ||
                                         d.ParentId == DefaultStore)
                                   select d).ToList();
            }
            return availableStores;
        }

        public static int? GetSalesEmps(DataTable dt_SalesEmps, bool deliveryRep, bool salesRep, int? DefaultSalesRep)
        {
            int defaultEmp = DefaultSalesRep.HasValue ?
                DefaultSalesRep.Value : 0;

            if (dt_SalesEmps.Columns.Count < 1)
            {
                dt_SalesEmps.Columns.Add("EmpId", typeof(Int32));
                dt_SalesEmps.Columns.Add("EmpName");
                dt_SalesEmps.Columns.Add("EmpFName");
                dt_SalesEmps.Columns.Add("AccountId", typeof(Int32));
                dt_SalesEmps.Columns.Add("CashInvCommission");
                dt_SalesEmps.Columns.Add("JobNameAr");
                dt_SalesEmps.Columns.Add("JobNameEn");
                dt_SalesEmps.Columns.Add("Code");
            }

            DAL.ERPDataContext HRDB = new DAL.ERPDataContext();
            var emps = (from d in HRDB.HR_Employees
                        join j in HRDB.HR_Jobs
                        on d.JobId equals j.JobId

                        where (salesRep == true && deliveryRep == false) ? d.SalesRep == true : true
                        where (salesRep == false && deliveryRep == true) ? d.DeliveryRep == true : true
                        where (salesRep == true && deliveryRep == true) ? (d.DeliveryRep == true || d.SalesRep == true) : true
                        where (salesRep == false && deliveryRep == false) ? d.SalesRep == d.DeliveryRep == false : true

                        select new
                        {
                            d.EmpId,
                            d.EmpName,
                            d.AccountId,
                            d.CashInvCommission,
                            d.EmpFName,
                            d.JobId,
                            j.JobNameAr,
                            j.JobNameEn,
                            EmpCode = d.EmpCode
                        }).ToList();

            int count = 0;
            foreach (var em in emps)
            {
                dt_SalesEmps.Rows.Add(em.EmpId, em.EmpName, em.EmpFName, em.AccountId, em.CashInvCommission, em.JobNameAr, em.JobNameEn, em.EmpCode);
                count++;
            }

            if (emps.Exists(x => x.EmpId == defaultEmp))
                return defaultEmp;
            else
                return null;
        }

        public static List<SL_CustomerCategoryInfo> GetCustomersCategories()
        {
            ERPDataContext DB = new ERPDataContext();

            List<SL_CustomerCategoryInfo> groups = (from g in DB.SL_CustomerGroups
                                                    join a in DB.ACC_Accounts
                                                    on g.AccountId equals a.AccountId
                                                    orderby a.AcNumber
                                                    select new SL_CustomerCategoryInfo
                                                    {
                                                        CustomerGroupId = g.CustomerGroupId,
                                                        CustomerGroupCode = g.CustomerGroupCode,
                                                        CGNameAr = g.CGNameAr,
                                                        CGNameEn = g.CGNameEn,
                                                        Desc = g.Desc,
                                                        MaxCredit = g.MaxCredit,
                                                        AccountId = a.AccountId,
                                                        AcNumber = a.AcNumber,
                                                        CustomersDefaultAccount = g.CustomersDefaultAccount,
                                                        CustomersHaveSeparateAccount = g.CustomersHaveSeparateAccount,
                                                    }
                                                 ).ToList();

            groups.Insert(0, new SL_CustomerCategoryInfo
            {
                CustomerGroupId = 0,
                CustomerGroupCode = "",
                CGNameAr = "",
                CGNameEn = "",
                Desc = "",
                MaxCredit = 0,
                AccountId = 0,
                AcNumber = "",
                CustomersDefaultAccount = 0,
                CustomersHaveSeparateAccount = false,
            });

            return groups;
        }
        public static List<SL_CustomerCategoryInfo> GetChildCustomersCategories()
        {
            ERPDataContext DB = new ERPDataContext();
            List<SL_CustomerCategoryInfo> groups =
                (from g in DB.SL_CustomerGroups
                 where DB.SL_CustomerGroups.Where(b => b.ParentGroupId == g.CustomerGroupId).Count() < 1
                 join a in DB.ACC_Accounts
                 on g.AccountId equals a.AccountId

                 orderby a.AcNumber
                 select new SL_CustomerCategoryInfo
                 {
                     CustomerGroupId = g.CustomerGroupId,
                     CustomerGroupCode = g.CustomerGroupCode,
                     CGNameAr = g.CGNameAr,
                     CGNameEn = g.CGNameEn,
                     Desc = g.Desc,
                     MaxCredit = g.MaxCredit,
                     AccountId = a.AccountId,
                     AcNumber = a.AcNumber,
                     CustomersDefaultAccount = g.CustomersDefaultAccount,
                     CustomersHaveSeparateAccount = g.CustomersHaveSeparateAccount,
                 }).ToList();

            groups.Insert(0, new SL_CustomerCategoryInfo
            {
                CustomerGroupId = 0,
                CustomerGroupCode = "",
                CGNameAr = "",
                CGNameEn = "",
                Desc = "",
                MaxCredit = 0,
                AccountId = 0,
                AcNumber = "",
                CustomersDefaultAccount = 0,
                CustomersHaveSeparateAccount = false,
            });

            return groups;
        }

        #region 
        /// <summary>
        /// Get Customer Groups and return them in list<SL_Group_Customer> adding an empty record at 0 index
        /// </summary>
        /// <returns></returns>

        public static List<SL_CustomerCategoryInfo> GetCustomersGroups()
        {
            ERPDataContext DB = new ERPDataContext();

            List<SL_CustomerCategoryInfo> groups = (from g in DB.SL_Group_Customers
                                                    select new SL_CustomerCategoryInfo
                                                    {
                                                        CustomerGroupId = g.GroupId,
                                                        CustomerGroupCode = g.Code,
                                                        CGNameAr = g.NameAr,
                                                        CGNameEn = g.NameEn,
                                                        ParentGroupId = g.ParentId
                                                    }
                                                 ).ToList();

            groups.Insert(0, new SL_CustomerCategoryInfo
            {
                CustomerGroupId = 0,
                CustomerGroupCode = "",
                CGNameAr = "",
                CGNameEn = "",
                ParentGroupId = null
            });

            return groups;
        }

        public static List<SL_CustomerCategoryInfo> GetCustomersChildrenGroups()
        {
            ERPDataContext DB = new ERPDataContext();
            List<SL_CustomerCategoryInfo> groups =
                (from g in DB.SL_Group_Customers
                 where DB.SL_Group_Customers.Where(b => b.ParentId == g.GroupId).Count() < 1

                 select new SL_CustomerCategoryInfo
                 {
                     CustomerGroupId = g.GroupId,
                     CustomerGroupCode = g.Code,
                     CGNameAr = g.NameAr,
                     CGNameEn = g.NameEn,
                     ParentGroupId = g.ParentId
                 }).ToList();

            groups.Insert(0, new SL_CustomerCategoryInfo
            {
                CustomerGroupId = 0,
                CustomerGroupCode = "",
                CGNameAr = "",
                CGNameEn = "",
            });

            return groups;
        }

        #endregion

        public static List<PR_VendorGroupInfo> GetVendorsGroups()
        {
            ERPDataContext DB = new ERPDataContext();
            List<PR_VendorGroupInfo> groups = (from g in DB.PR_VendorGroups
                                               join a in DB.ACC_Accounts
                                               on g.AccountId equals a.AccountId
                                               orderby a.AcNumber
                                               select new PR_VendorGroupInfo
                                               {
                                                   VendorGroupId = g.VendorGroupId,
                                                   VendorGroupCode = g.VendorGroupCode,
                                                   VGNameAr = g.VGNameAr,
                                                   VGNameEn = g.VGNameEn,
                                                   Desc = g.Desc,
                                                   MaxCredit = g.MaxCredit,
                                                   AccountId = a.AccountId,
                                                   AcNumber = a.AcNumber,
                                                   VendorHasSeparateAccount = g.VendorHasSeparateAccount,
                                                   VendorDefaultAccount = g.VendorDefaultAccount,
                                               }).ToList();

            groups.Insert(0, new PR_VendorGroupInfo
            {
                VendorGroupId = 0,
                VendorGroupCode = "",
                VGNameAr = "",
                VGNameEn = "",
                Desc = "",
                MaxCredit = 0,
                AccountId = 0,
                AcNumber = "",
                VendorHasSeparateAccount = false,
                VendorDefaultAccount = 0,
            });

            return groups;
        }
        public static List<PR_VendorGroupInfo> GetChildVendorsGroups()
        {
            ERPDataContext DB = new ERPDataContext();
            List<PR_VendorGroupInfo> groups =
                (from g in DB.PR_VendorGroups
                 where DB.PR_VendorGroups.Where(b => b.ParentGroupId == g.VendorGroupId).Count() < 1
                 join a in DB.ACC_Accounts
                 on g.AccountId equals a.AccountId
                 orderby a.AcNumber
                 select new PR_VendorGroupInfo
                 {
                     VendorGroupId = g.VendorGroupId,
                     VendorGroupCode = g.VendorGroupCode,
                     VGNameAr = g.VGNameAr,
                     VGNameEn = g.VGNameEn,
                     Desc = g.Desc,
                     MaxCredit = g.MaxCredit,
                     AccountId = a.AccountId,
                     AcNumber = a.AcNumber,
                     VendorHasSeparateAccount = g.VendorHasSeparateAccount,
                     VendorDefaultAccount = g.VendorDefaultAccount,
                 }).ToList();

            groups.Insert(0, new PR_VendorGroupInfo
            {
                VendorGroupId = 0,
                VendorGroupCode = "",
                VGNameAr = "",
                VGNameEn = "",
                Desc = "",
                MaxCredit = 0,
                AccountId = 0,
                AcNumber = "",
                VendorHasSeparateAccount = false,
                VendorDefaultAccount = 0,
            });

            return groups;
        }

        /// <summary>
        /// Load DataTable with Vendors
        /// </summary>
        /// <param name="VendorsDataTable"> Vendors DataTable</param>
        /// <returns>return golden vendor or first vendorId</returns>
        /// <summary>
        public static int? GetVendors(out List<VendorInfo> lst_Vendors, out int lastVenId)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            lst_Vendors = (from v in DB.PR_Vendors
                           join a in DB.ACC_Accounts
                           on v.AccountId equals a.AccountId
                           from g in DB.PR_VendorGroups.Where(g => g.VendorGroupId == v.GroupId).DefaultIfEmpty()
                           select new VendorInfo
                           {
                               VendorId = v.VendorId,
                               VenCode = v.VenCode,
                               VenNameAr = v.VenNameAr,
                               VenNameEn = v.VenNameEn,
                               Zip = v.Zip,
                               TradeRegistry = v.TradeRegistry,
                               Tel = v.Tel,
                               TaxFileNumber = v.TaxFileNumber,
                               TaxDepartment = v.TaxDepartment,
                               TaxCardNumber = v.TaxCardNumber,
                               Shipping = v.Shipping,
                               Representative_Job = v.Representative_Job,
                               Representative = v.Representative,
                               RepFNAme = v.RepFNAme,
                               RepFJob = v.RepFJob,
                               PriceLevel = v.PriceLevel,
                               Mobile = v.Mobile,
                               MaxCredit = v.MaxCredit,
                               ManagerName = v.ManagerName,
                               IsTaxable = v.IsTaxable,
                               HasSeparateAccount = v.HasSeparateAccount,
                               GroupId = v.GroupId,
                               Fax = v.Fax,
                               Email = v.Email,
                               DueDaysCount = v.DueDaysCount,
                               AccountId = v.AccountId,
                               VendorAccNumber = a.AcNumber,
                               Address = v.Address,
                               City = v.City,

                           })
                          .OrderBy(x => x.VendorAccNumber).ThenBy(x => x.VenCode).ToList(); 

            if (lst_Vendors.Count > 0)
            {
                lastVenId = lst_Vendors.Select(v => v.VendorId).Max();
                return lst_Vendors.Select(v => v.VendorId).First();
            }
            else
            {
                lastVenId = 0;
                return null;
            }
        }

        public static int? GetVendorsNotStopped(int vendorId ,out List<VendorInfo> lst_Vendors, out int lastVenId)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            lst_Vendors = (from v in DB.PR_Vendors
                           where (v.StoppedVendor==null|| v.StoppedVendor==false)
                           join a in DB.ACC_Accounts
                           on v.AccountId equals a.AccountId
                           from g in DB.PR_VendorGroups.Where(g => g.VendorGroupId == v.GroupId).DefaultIfEmpty()
                           select new VendorInfo
                           {
                               VendorId = v.VendorId,
                               VenCode = v.VenCode,
                               VenNameAr = v.VenNameAr,
                               VenNameEn = v.VenNameEn,
                               Zip = v.Zip,
                               TradeRegistry = v.TradeRegistry,
                               Tel = v.Tel,
                               TaxFileNumber = v.TaxFileNumber,
                               TaxDepartment = v.TaxDepartment,
                               TaxCardNumber = v.TaxCardNumber,
                               Shipping = v.Shipping,
                               Representative_Job = v.Representative_Job,
                               Representative = v.Representative,
                               RepFNAme = v.RepFNAme,
                               RepFJob = v.RepFJob,
                               PriceLevel = v.PriceLevel,
                               Mobile = v.Mobile,
                               MaxCredit = v.MaxCredit,
                               ManagerName = v.ManagerName,
                               IsTaxable = v.IsTaxable,
                               HasSeparateAccount = v.HasSeparateAccount,
                               GroupId = v.GroupId,
                               Fax = v.Fax,
                               Email = v.Email,
                               DueDaysCount = v.DueDaysCount,
                               AccountId = v.AccountId,
                               VendorAccNumber = a.AcNumber,
                               Address = v.Address,
                               City = v.City,

                           })
                          .OrderBy(x => x.VendorAccNumber).ThenBy(x => x.VenCode).ToList();

            if (vendorId != 0)
            {
                var vendorStopped = (from v in DB.PR_Vendors
                                    where (v.StoppedVendor == true && v.VendorId == vendorId)
                                    join a in DB.ACC_Accounts
                                    on v.AccountId equals a.AccountId
                                    from g in DB.PR_VendorGroups.Where(g => g.VendorGroupId == v.GroupId).DefaultIfEmpty()
                                    select new VendorInfo
                                    {
                                        VendorId = v.VendorId,
                                        VenCode = v.VenCode,
                                        VenNameAr = v.VenNameAr,
                                        VenNameEn = v.VenNameEn,
                                        Zip = v.Zip,
                                        TradeRegistry = v.TradeRegistry,
                                        Tel = v.Tel,
                                        TaxFileNumber = v.TaxFileNumber,
                                        TaxDepartment = v.TaxDepartment,
                                        TaxCardNumber = v.TaxCardNumber,
                                        Shipping = v.Shipping,
                                        Representative_Job = v.Representative_Job,
                                        Representative = v.Representative,
                                        RepFNAme = v.RepFNAme,
                                        RepFJob = v.RepFJob,
                                        PriceLevel = v.PriceLevel,
                                        Mobile = v.Mobile,
                                        MaxCredit = v.MaxCredit,
                                        ManagerName = v.ManagerName,
                                        IsTaxable = v.IsTaxable,
                                        HasSeparateAccount = v.HasSeparateAccount,
                                        GroupId = v.GroupId,
                                        Fax = v.Fax,
                                        Email = v.Email,
                                        DueDaysCount = v.DueDaysCount,
                                        AccountId = v.AccountId,
                                        VendorAccNumber = a.AcNumber,
                                        Address = v.Address,
                                        City = v.City,

                                    }).OrderBy(x => x.VendorAccNumber).ThenBy(x => x.VenCode).ToList().FirstOrDefault(); 
                 
                                 if (vendorStopped != null)
                                 lst_Vendors.Add(vendorStopped);
            }
            if (lst_Vendors.Count > 0)
            {
                lastVenId = lst_Vendors.Select(v => v.VendorId).Max();
                return lst_Vendors.Select(v => v.VendorId).First();
            }
            else
            {
                lastVenId = 0;
                return null;
            }
        }
        /// <summary>
        /// Load all Customers, or customers in mandatory user's customer group
        /// </summary>
        /// <param name="lst_Customers"> SL_Customer_Info List</param>
        /// <param name="DefaultCustGrp"> user's mandatory customer group</param>
        /// <returns>return general customerId</returns>
        /// <summary>        
        public static int? GetCustomers(out List<SL_Customer_Info> lst_Customers, int DefaultCustGrp, out int LastCustId, params bool[] JstActvCstmrs)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            string custGrpAccNum = string.Empty;
            if (DefaultCustGrp > 0)
                custGrpAccNum = (from s in DB.SL_CustomerGroups
                                 join a in DB.ACC_Accounts
                                 on s.AccountId equals a.AccountId
                                 where s.CustomerGroupId == DefaultCustGrp
                                 select a.AcNumber).First();

            try
            {
                bool? _jstActvCstmrs = null;
                if (JstActvCstmrs.Length > 0)
                    _jstActvCstmrs = JstActvCstmrs[0];

                lst_Customers = (from c in DB.SL_Customers
                                 //join a in DB.ACC_Accounts
                                 //on c.AccountId equals a.AccountId
                                 //from p in DB.IC_PriceLevels.Where(p => p.PriceLevelId == c.PriceLevel).DefaultIfEmpty()
                                 from g in DB.SL_CustomerGroups.Where(g => g.CustomerGroupId == c.CategoryId).DefaultIfEmpty()
                                 where DefaultCustGrp == 0 ? true : true//a.AcNumber.StartsWith(custGrpAccNum)
                                 where _jstActvCstmrs != null ? c.Is_Active == _jstActvCstmrs : true
                                 //let CustGroup = DB.SL_Group_Customers
                                 //let CustRegion = DB.SL_CustomerRegions
                    
                                 select new SL_Customer_Info
                                 {
                                     CustomerId = c.CustomerId,
                                     CusCode = c.CusCode,
                                     AccountId = c.AccountId,
                                     //PriceLevelId = c.PriceLevel,
                                     DueDaysCount = c.DueDaysCount,
                                     SalesEmpId = c.SalesEmpId,
                                     HasSeparateAccount = c.HasSeparateAccount,
                                     MaxCredit = decimal.ToDouble(c.MaxCredit),
                                     DiscountRatio = decimal.ToDouble(c.DiscountRatio),
                                     CusNameAr = c.CusNameAr,
                                     CusNameEn = c.CusNameEn,
                                     Tel = c.Tel,
                                     Mobile = c.Mobile,
                                     Address = c.Address,
                                     //PriceLevel = p.PLName,
                                     City = c.City,
                                     Email = c.Email,
                                     Fax = c.Fax,
                                     Zip = c.Zip,
                                     Shipping = c.Shipping,
                                     Manager = c.Manager,
                                     Representative = c.Representative,
                                     Representative_Job = c.Representative_Job,
                                     RepFNAme = c.RepFNAme,
                                     RepFJob = c.RepFJob,
                                     IsTaxable = c.IsTaxable,
                                     IdNumber = c.IdNumber,

                                     GrpId = c.CategoryId,
                                     Delivery = c.Delivery,
                                     CGNameAr = g == null ? string.Empty : g.CGNameAr,
                                     //AccNumber = g == null ? null : a.AcNumber,

                                     GroupMaxCredit = g == null ? 0 : g.MaxCredit,
                                     GroupAccountId = g == null ? (int?)null : g.AccountId,
                                     Is_Blocked = c.Is_Blocked,
                                     //Mohammad 10-11-2019
                                     //CustGroup = CustGroup.Where(x => x.GroupId == c.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault(),
                                     Country= DB.HR_Countries.Where(a=>a.CountryId==c.CountryId).FirstOrDefault().CountryName,
                                     Rep_ID = c.Rep_ID,
                                     Rep_Mobile = c.Rep_Mobile,
                                     Street=c.Street,
                                     BuildingNumber=c.BuildingNumber,
                                     Governate=c.Governate,
                                     csType=c.csType==0?"طبيعي": c.csType==1?"اعتباري":"اجنبي",
                                     TaxCardNumber=c.TaxCardNumber,

                                     //Region = DB.SL_CustomerRegions.Where(x=>x.IdRegion == c.IdRegion).Select(x=>x.RegionName).FirstOrDefault()
                                 }).OrderBy(x => x.CusCode).ToList();

                if (lst_Customers.Count > 0)
                {
                    LastCustId = lst_Customers.Select(v => v.CustomerId).Max();
                    return lst_Customers.Select(v => v.CustomerId).First();
                }
                else
                {
                    LastCustId = 0;
                    return null;
                }
            }
            catch (Exception excep)
            {
                LastCustId = 0;
                lst_Customers = null;
                return null;
            }
        }

        /// <summary>
        /// Load DataTable with Companies,
        /// </summary>
        /// <param name="StoresDataTable"> Companies DataTable</param>        
        public static void GetCompanies(DataTable CompaniesDataTable)
        {
            CompaniesDataTable.Columns.Clear();
            CompaniesDataTable.Columns.Add("CompanyId");
            CompaniesDataTable.Columns.Add("CompanyCode");
            CompaniesDataTable.Columns.Add("CompanyNameAr");
            CompaniesDataTable.Columns.Add("CompanyNameEn");
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var companiesQuery = DB.IC_Companies.Select(i => i).ToList();
            if (companiesQuery.Count > 0)
            {
                foreach (var company in companiesQuery)
                {
                    CompaniesDataTable.Rows.Add(company.CompanyId, company.CompanyCode, company.CompanyNameAr, company.CompanyNameEn);
                }
            }
        }

        /// <summary>
        /// Load DataTable with Categories,
        /// </summary>
        /// <param name="CategoriesDataTable"> Categories DataTable</param>        
        public static void GetAllCategoriesDt(DataTable CategoriesDataTable)
        {
            CategoriesDataTable.Columns.Clear();
            CategoriesDataTable.Columns.Add("CategoryId");
            CategoriesDataTable.Columns.Add("CatNumber");
            CategoriesDataTable.Columns.Add("CategoryNameAr");
            CategoriesDataTable.Columns.Add("CategoryNameEn");
            CategoriesDataTable.Columns.Add("Level");
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            List<IC_Category> cats = DB.IC_Categories.OrderBy(x => x.CatNumber).Select(d => d).ToList();

            foreach (var cat in cats)
            {
                CategoriesDataTable.Rows.Add(cat.CategoryId, cat.CatNumber, cat.CategoryNameAr, cat.CategoryNameEn, cat.Level);
            }
        }

        public static List<IC_Category> GetAllCategoriesList()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            List<IC_Category> cats = DB.IC_Categories.OrderBy(x => x.CatNumber).Select(d => d).ToList();
            return cats;
        }

        /// <summary>
        /// Get Categories for Perpetual Item Posting
        /// </summary>
        /// <returns></returns>
        public static List<IC_Category> GetChildCategoriesList()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            List<IC_Category> cats = DB.IC_Categories.OrderBy(x => x.CatNumber).Select(d => d).ToList();

            List<IC_Category> lst = new List<IC_Category>();

            foreach (var d in cats)
            {
                if (cats.Where(c => c.ParentId == d.CategoryId).Count() > 0)
                    continue;

                lst.Add(
                    new IC_Category
                    {
                        CategoryId = d.CategoryId,
                        CatNumber = d.CatNumber,
                        Code2 = d.Code2,
                        CategoryNameAr = d.CategoryNameAr,
                        CategoryNameEn = d.CategoryNameEn,
                        Level = d.Level,
                        SellAcc = d.SellAcc,
                        SellReturnAcc = d.SellReturnAcc,
                        COGSAcc = d.COGSAcc,
                        InvAcc = d.InvAcc
                    });
            }

            return lst;
        }

        /// <summary>
        /// Get Categories for Periodic Item Posting
        /// </summary>
        /// <returns></returns>
        public static List<IC_Category> GetChildCategoriesList_Periodic()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            List<IC_Category> cats = DB.IC_Categories.OrderBy(x => x.CatNumber).Select(d => d).ToList();

            List<IC_Category> lst = new List<IC_Category>();

            foreach (var d in cats)
            {
                if (cats.Where(c => c.ParentId == d.CategoryId).Count() > 0)
                    continue;

                lst.Add(
                    new IC_Category
                    {
                        CategoryId = d.CategoryId,
                        CatNumber = d.CatNumber,
                        CategoryNameAr = d.CategoryNameAr,
                        CategoryNameEn = d.CategoryNameEn,
                        Level = d.Level,
                        SellAcc = d.SellAcc,
                        SellReturnAcc = d.SellReturnAcc,
                        CloseInventoryAcc = d.CloseInventoryAcc,
                        OpenInventoryAcc = d.OpenInventoryAcc,
                        PurchaseAcc = d.PurchaseAcc,
                        PurchaseReturnAcc = d.PurchaseReturnAcc
                    });
            }
            return lst;
        }

        /// <summary>
        /// get store settings, to validate your transaction
        /// </summary>
        /// <returns></returns>
        public static DAL.ST_Store GetStoreSettings()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            DAL.ST_Store stStore = DB.ST_Stores.First();

            return stStore;
        }
        public static DAL.ST_Store GetStoreSettings(out List<ST_Currency> lstCrnc, out List<ST_IncomeTaxDiscount> lstIncomeTaxDiscount,
            out List<ST_IncomeTaxLevel> lstIncomeTaxLevel)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            DAL.ST_Store stStore = DB.ST_Stores.First();

            //load currencies
            lstCrnc = (from g in DB.ST_Currencies
                       orderby g.CrncId
                       select g).ToList();

            lstCrnc.Insert(0, new ST_Currency
            {
                CrncId = 0,
                crncName = stStore.MainCurrencyName,
                CurrencyDigitsCount = stStore.CurrencyDigitsCount.HasValue ? stStore.CurrencyDigitsCount.Value : 0,
                CurrencyPiaster1 = stStore.CurrencyPiaster1,
                CurrencyPiaster2 = stStore.CurrencyPiaster2,
                CurrencyPiaster3 = stStore.CurrencyPiaster3,
                CurrencyPound1 = stStore.CurrencyPound1,
                CurrencyPound2 = stStore.CurrencyPound2,
                CurrencyPound3 = stStore.CurrencyPound3,
                LastRate = 1
            });

            lstIncomeTaxDiscount = null;// DB.ST_IncomeTaxDiscounts.ToList();
            lstIncomeTaxLevel = null;// DB.ST_IncomeTaxLevels.ToList();

            return stStore;
        }



        /// <summary>
        /// calculate store Qty from provided Qty and UOM
        /// </summary>
        /// <param name="qty"></param>
        /// <param name="uomIndex">large =0, medium=1, small=2 </param>
        /// <param name="MediumUOMFactor"></param>
        /// <param name="SmallUOMFactor"></param>
        /// <returns></returns>
        public static decimal CalculateUomQty(decimal qty, byte uomIndex, decimal MediumUOMFactor, decimal LargeUOMFactor)
        {
            decimal storeQty = 0;
            if (uomIndex == 0)// small uom
                storeQty = qty;
            else if (uomIndex == 1)// medium uom            
                storeQty = qty * MediumUOMFactor;
            else if (uomIndex == 2)// large uom
                storeQty = qty * LargeUOMFactor;
            return storeQty;
        }

        /// <summary>
        /// get store qty, and return qty for selected UOM
        /// </summary>
        /// <param name="qty">store qty, recorded in smallest uom</param>
        /// <param name="uomIndex">current uom index</param>
        /// <param name="MediumUOMFactor"></param>
        /// <param name="SmallUOMFactor"></param>
        /// <returns></returns>
        public static decimal getCalculatedUomQty(decimal smallQty, byte uomIndex, decimal MediumUOMFactor, decimal LargeUOMFactor)
        {
            decimal uomQty = 0;
            if (uomIndex == 0)// small uom
                uomQty = smallQty;
            else if (uomIndex == 1)// medium uom
                uomQty = smallQty / MediumUOMFactor;
            else if (uomIndex == 2)// large uom            
                uomQty = smallQty / LargeUOMFactor;
            return uomQty;
        }


        public static decimal getCalculatedUomQty(decimal smallQty, byte uomIndex, string MediumUOMFactor, string LargeUOMFactor)
        {
            decimal uomQty = 0;
            if (uomIndex == 0)// small uom
                uomQty = smallQty;
            else if (uomIndex == 1)// medium uom
                uomQty = smallQty / FractionToDouble(MediumUOMFactor);
            else if (uomIndex == 2)// large uom            
                uomQty = smallQty / FractionToDouble(LargeUOMFactor);
            return uomQty;
        }

        /// <summary>
        /// get the pieces count of the smallest uom of item, in given store.
        /// </summary>
        /// <param name="itemId"></param>
        /// <param name="storeId"></param>
        /// <returns></returns>
        public static decimal GetItemPieces(DateTime _until_date, int itemId, int storeId, params string[] batch/*,string batch*/)
        {
            DateTime until_date = _until_date;
            if (_until_date <= DateTime.MinValue)
                until_date = DateTime.Now;
            bool valid = false;
            if (batch != null && batch.Any())
            {
                valid = true;
            }
            var batchString = string.IsNullOrEmpty(batch.FirstOrDefault()) ? null : batch.FirstOrDefault();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            decimal val = 0;

            ////update 30/8/2018 alaa
            //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0)
            ////if (storeId > 0)

            //    val = (from s in DB.IC_Stores
            //           where s.ParentId == storeId || s.StoreId == storeId
            //           join i in DB.IC_ItemStores on s.StoreId equals i.StoreId
            //           where i.ItemId == itemId
            //           && i.InsertTime < until_date
            //            where valid?( Shared.st_Store.Batch==true? (batchString==null? true: i.Batch== batchString) :true):true
            //           //where i.Batch==null
            //           select new
            //           {
            //               Qty = i.IsInTrns ? i.Qty : (i.Qty * -1)
            //           }).Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();
            //else

            val = (from i in DB.IC_ItemStores
                   where Shared.st_Store.SalesOrderReserveGood ? true : i.ProcessId != (int)Process.SalesOrder
                   where i.ItemId == itemId
                   //3/9/2018
                   && i.StoreId == storeId
                   && i.InsertTime < until_date
                   where valid ? (Shared.st_Store.Batch == true ? (batchString == null ? true : i.Batch == batchString) : true) : true

                   select new
                   {
                       PiecesCount = i.IsInTrns ? i.PiecesCount : (i.PiecesCount * -1)
                   }).Select(x => x.PiecesCount).ToList().DefaultIfEmpty(0).Sum();

            return decimal.Round(val, 4);
        }


        public static decimal GetItemPieces(DateTime _until_date, int itemId, int storeId, int? SalesOrderId, params string[] batch/*,string batch*/)
        {
            DateTime until_date = _until_date;
            if (_until_date <= DateTime.MinValue)
                until_date = DateTime.Now;
            bool valid = false;
            if (batch != null && batch.Any())
            {
                valid = true;
            }
            var batchString = string.IsNullOrEmpty(batch.FirstOrDefault()) ? null : batch.FirstOrDefault();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            decimal val = 0;

            ////update 30/8/2018 alaa
            //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0)
            ////if (storeId > 0)

            //    val = (from s in DB.IC_Stores
            //           where s.ParentId == storeId || s.StoreId == storeId
            //           join i in DB.IC_ItemStores on s.StoreId equals i.StoreId
            //           where i.ItemId == itemId
            //           && i.InsertTime < until_date
            //            where valid?( Shared.st_Store.Batch==true? (batchString==null? true: i.Batch== batchString) :true):true
            //           //where i.Batch==null
            //           select new
            //           {
            //               Qty = i.IsInTrns ? i.Qty : (i.Qty * -1)
            //           }).Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();
            //else

            val = (from i in DB.IC_ItemStores
                   where Shared.st_Store.SalesOrderReserveGood ? true : i.ProcessId != (int)Process.SalesOrder
                   where i.ItemId == itemId
                   //3/9/2018
                   && i.StoreId == storeId
                   && i.InsertTime < until_date
                   where valid ? (Shared.st_Store.Batch == true ? (batchString == null ? true : i.Batch == batchString) : true) : true

                   select new
                   {
                       PiecesCount = i.IsInTrns ? i.PiecesCount : (i.PiecesCount * -1)
                   }).Select(x => x.PiecesCount).ToList().DefaultIfEmpty(0).Sum();

            if (SalesOrderId.HasValue)
            {
                var val2 = (from i in DB.IC_ItemStores
                            join sld in DB.SL_SalesOrderDetails on i.SourceId equals sld.SL_SalesOrderDetailId
                            where i.ProcessId == (int)Process.SalesOrder

                            where sld.SL_SalesOrderId == SalesOrderId
                            where i.ItemId == itemId

                            //3/9/2018
                            && i.StoreId == storeId
                            //&& i.InsertTime < until_date
                            where valid ? (Shared.st_Store.Batch == true ? (batchString == null ? true : i.Batch == batchString) : true) : true

                            select new
                            {
                                i.PiecesCount
                            }).Select(x => x.PiecesCount).ToList().DefaultIfEmpty(0).Sum();
                val = val + val2;
            }
            return decimal.Round(val, 4);
        }

        /// <summary>
        /// get the qty of the smallest uom of item, in given store.
        /// </summary>
        /// <param name="itemId"></param>
        /// <param name="storeId"></param>
        /// <returns></returns>
        public static decimal GetItemQty(DateTime _until_date, int itemId, int storeId, params string[] batch/*,string batch*/)
        {
            DateTime until_date = _until_date;
            if (_until_date <= DateTime.MinValue)
                until_date = DateTime.Now;
            bool valid = false;
            if (batch != null && batch.Any())
            {
                valid = true;
            }
            var batchString = string.IsNullOrEmpty(batch.FirstOrDefault()) ? null : batch.FirstOrDefault();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            decimal val = 0;

            ////update 30/8/2018 alaa
            //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0)
            ////if (storeId > 0)

            //    val = (from s in DB.IC_Stores
            //           where s.ParentId == storeId || s.StoreId == storeId
            //           join i in DB.IC_ItemStores on s.StoreId equals i.StoreId
            //           where i.ItemId == itemId
            //           && i.InsertTime < until_date
            //            where valid?( Shared.st_Store.Batch==true? (batchString==null? true: i.Batch== batchString) :true):true
            //           //where i.Batch==null
            //           select new
            //           {
            //               Qty = i.IsInTrns ? i.Qty : (i.Qty * -1)
            //           }).Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();
            //else
            val = (from i in DB.IC_ItemStores
                   where Shared.st_Store.SalesOrderReserveGood ? true : i.ProcessId != (int)Process.SalesOrder
                   where i.ItemId == itemId
                   //3/9/2018
                   && i.StoreId == storeId
                   && i.InsertTime < until_date
                   where valid ? (Shared.st_Store.Batch == true ? (batchString == null ? true : i.Batch == batchString) : true) : true

                   select new
                   {
                       Qty = i.IsInTrns ? i.Qty : (i.Qty * -1)
                   }).Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();

            return decimal.Round(val, 4);
        }

        public static decimal GetItemQty(DateTime _until_date, int itemId, int storeId, decimal length, decimal width, decimal height, params string[] batch/*,string batch*/)
        {
            DateTime until_date = _until_date;
            if (_until_date <= DateTime.MinValue)
                until_date = DateTime.Now;
            bool valid = false;
            if (batch != null && batch.Any())
            {
                valid = true;
            }
            var batchString = string.IsNullOrEmpty(batch.FirstOrDefault()) ? null : batch.FirstOrDefault();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            return (from i in DB.IC_ItemStores
                    where i.ItemId == itemId
                    && i.StoreId == storeId
                    && i.Length == length
                    && i.Width == width
                    && i.Height == height
                    //&& Shared.st_Store.Batch == true ? i.Batch == batch : true
                    where valid ? (Shared.st_Store.Batch == true ? (batchString == null ? i.Batch == null : i.Batch == batchString) : true) : true

                    where until_date == null ? true : i.InsertTime < until_date
                    select new
                    {
                        Qty = i.IsInTrns ? i.Qty : (i.Qty * -1)
                    }).Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();
        }

        public static decimal GetItemQty(DateTime _until_date, int itemId, int storeId, int? SalesOrderId, params string[] batch/*,string batch*/)
        {
            DateTime until_date = _until_date;
            if (_until_date <= DateTime.MinValue)
                until_date = DateTime.Now;
            bool valid = false;
            if (batch != null && batch.Any())
            {
                valid = true;
            }
            var batchString = string.IsNullOrEmpty(batch.FirstOrDefault()) ? null : batch.FirstOrDefault();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            decimal val = 0;

            ////update 30/8/2018 alaa
            //if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0)
            ////if (storeId > 0)

            //    val = (from s in DB.IC_Stores
            //           where s.ParentId == storeId || s.StoreId == storeId
            //           join i in DB.IC_ItemStores on s.StoreId equals i.StoreId
            //           where i.ItemId == itemId
            //           && i.InsertTime < until_date
            //            where valid?( Shared.st_Store.Batch==true? (batchString==null? true: i.Batch== batchString) :true):true
            //           //where i.Batch==null
            //           select new
            //           {
            //               Qty = i.IsInTrns ? i.Qty : (i.Qty * -1)
            //           }).Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();
            //else
            val = (from i in DB.IC_ItemStores
                   where Shared.st_Store.SalesOrderReserveGood ? true : i.ProcessId != (int)Process.SalesOrder
                   where i.ItemId == itemId
                   //3/9/2018
                   && i.StoreId == storeId
                   && i.InsertTime < until_date
                   where valid ? (Shared.st_Store.Batch == true ? (batchString == null ? true : i.Batch == batchString) : true) : true

                   select new
                   {
                       Qty = i.IsInTrns ? i.Qty : (i.Qty * -1)
                   }).Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();

            if (SalesOrderId.HasValue)
            {
                var val2 = (from i in DB.IC_ItemStores
                            join sld in DB.SL_SalesOrderDetails on i.SourceId equals sld.SL_SalesOrderDetailId
                            where i.ProcessId == (int)Process.SalesOrder

                            where sld.SL_SalesOrderId == SalesOrderId
                            where i.ItemId == itemId
                            //3/9/2018
                            && i.StoreId == storeId
                            where valid ? (Shared.st_Store.Batch == true ? (batchString == null ? true : i.Batch == batchString) : true) : true

                            select new
                            {
                                i.Qty
                            }).Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();
                val = val + val2;
            }

            return decimal.Round(val, 4);
        }

        /// <summary>
        /// get the qty of the smallest uom of the item, in all stores
        /// </summary>
        /// <param name="itemId"></param>        
        /// <returns></returns>
        public static decimal GetItemQty4AllStores(int itemId)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var Qty = (from i in DB.IC_ItemStores
                       where i.ItemId == itemId
                       group i by i.ItemId into grp
                       select
                        grp.Where(c => c.IsInTrns == true).Count() > 0 ? grp.Where(c => c.IsInTrns == true).Sum(x => x.Qty) : 0
                         -
                        grp.Where(c => c.IsInTrns == false).Count() > 0 ? grp.Where(c => c.IsInTrns == false).Sum(x => x.Qty) : 0
                           ).FirstOrDefault();
            return Qty;
        }


        /// <summary>
        /// Return the price of an item depending on its default Sell UOM or its Default Purchase UOM
        /// </summary>
        /// <param name=></param>
        /// <returns>the item price</returns>
        public static decimal GetItemPrice(byte DefaultUOM, int? MedFactor, int? LargeFactor, decimal SellPrice)
        {
            int Factor = 1;
            if (DefaultUOM == 1)//Medium
            {
                if (MedFactor.HasValue)
                    Factor = MedFactor.Value;
            }
            else if (DefaultUOM == 2)//large
            {
                if (LargeFactor.HasValue)
                    Factor = LargeFactor.Value;
            }
            return (decimal)(SellPrice / Factor);
        }


        public static DataTable Get_Available_Itemstore_FIFO(int itemId, int storeId, DateTime Until_Date, DateTime? expire_date, string batch,
            decimal Length, decimal Width, decimal Height, string QC, string Serial)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            DataTable dt = new DataTable();
            dt.Columns.Add("InTrns_Id");
            dt.Columns.Add("AvailableQty");
            dt.Columns.Add("PurchasePrice");
            dt.Columns.Add("VendorId");
            dt.Columns.Add("Expire");
            dt.Columns.Add("Batch");
            dt.Columns.Add("Serial");

            var itemQty = (from i in DB.IC_ItemStores
                           where i.ProcessId != (int)Process.SalesOrder
                           && i.ItemId == itemId
                           && i.StoreId == storeId
                           && i.InsertTime <= Until_Date
                           && i.Qty > 0
                           where expire_date == null ? true : i.Expire == expire_date
                           where (batch == null || batch == string.Empty) ? true : i.Batch == batch
                           where (QC == null || QC == string.Empty) ? true : i.QC == QC
                           where i.Length > 0 && i.Width > 0 && i.Height > 0 ? i.Length == Length && i.Width == Width && i.Height == Height : true
                           orderby i.InsertTime ascending
                           orderby i.Expire ascending
                           group i by new
                           {
                               Cost = decimal.Round(i.PurchasePrice / i.Qty, 4),
                               i.VendorId,
                               i.Expire,
                               i.Batch,
                               i.Serial
                           } into grp
                           let TransDate = grp.Where(x => x.IsInTrns).Select(x => x.InsertTime).Min()
                           let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                           where Available > 0
                           orderby TransDate ascending
                           orderby grp.Key.Expire ascending
                           select new
                           {
                               AvailableQty = Available,
                               PurchasePrice = grp.Key.Cost * Available,
                               VendorId = grp.Key.VendorId,
                               grp.Key.Expire,
                               grp.Key.Batch,
                               grp.Key.Serial,
                           }).ToList();

            foreach (var d in itemQty)
            {
                DataRow dr = dt.NewRow();
                dr["InTrns_Id"] = 0;
                dr["AvailableQty"] = d.AvailableQty;
                dr["PurchasePrice"] = d.PurchasePrice;
                dr["VendorId"] = d.VendorId;
                dr["Expire"] = d.Expire;
                dr["Batch"] = d.Batch;
                dr["Serial"] = d.Serial;
                dt.Rows.Add(dr);
            }
            return dt;
        }


        public static DataTable Get_Available_Itemstore_LIFO(int itemId, int storeId, DateTime Until_Date, DateTime? expire_date, string batch,
                decimal Length, decimal Width, decimal Height, string QC, string Serial)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            DataTable dt = new DataTable();
            dt.Columns.Add("InTrns_Id");
            dt.Columns.Add("AvailableQty");
            dt.Columns.Add("PurchasePrice");
            dt.Columns.Add("VendorId");
            dt.Columns.Add("Expire");
            dt.Columns.Add("Batch");
            dt.Columns.Add("Serial");

            var itemQty = (from i in DB.IC_ItemStores
                           where i.ProcessId != (int)Process.SalesOrder
                           && i.ItemId == itemId
                           && i.StoreId == storeId
                           && i.InsertTime <= Until_Date
                           && i.Qty > 0
                           where expire_date == null ? true : i.Expire == expire_date
                           where (batch == null || batch == string.Empty) ? true : i.Batch == batch
                           where (Serial == null || Serial == string.Empty) ? true : i.Serial == Serial
                           where (QC == null || QC == string.Empty) ? true : i.QC == QC
                           where i.Length == Length && i.Width == Width && i.Height == Height
                           orderby i.InsertTime descending
                           orderby i.Expire descending
                           group i by new { Cost = decimal.Round(i.PurchasePrice / i.Qty, 6), i.VendorId, i.Expire, i.Batch, i.Serial } into grp
                           let TransDate = grp.Where(x => x.IsInTrns).Select(x => x.InsertTime).Min()
                           let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                           where Available > 0
                           orderby TransDate descending
                           orderby grp.Key.Expire descending
                           select new
                           {
                               AvailableQty = Available,
                               PurchasePrice = grp.Key.Cost * Available,
                               VendorId = grp.Key.VendorId,
                               grp.Key.Expire,
                               grp.Key.Batch,
                               grp.Key.Serial
                           }).ToList();

            foreach (var d in itemQty)
            {
                DataRow dr = dt.NewRow();
                dr["InTrns_Id"] = 0;
                dr["AvailableQty"] = d.AvailableQty;
                dr["PurchasePrice"] = d.PurchasePrice;
                dr["VendorId"] = d.VendorId;
                dr["Expire"] = d.Expire;
                dr["Batch"] = d.Batch;
                dr["Serial"] = d.Serial;
                dt.Rows.Add(dr);
            }
            return dt;
        }

        public static DataTable Get_Available_Itemstore_Average(int itemId, int storeId, DateTime Until_Date, DateTime? expire_date, string batch,
           decimal Length, decimal Width, decimal Height, string QC, string Serial)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            DataTable dt = new DataTable();
            dt.Columns.Add("InTrns_Id");
            dt.Columns.Add("AvailableQty");
            dt.Columns.Add("PurchasePrice");
            dt.Columns.Add("VendorId");
            dt.Columns.Add("Expire");
            dt.Columns.Add("Batch");
            dt.Columns.Add("Serial");

            var itemQty = (from i in DB.IC_ItemStores
                           where i.ProcessId != (int)Process.SalesOrder
                           && i.ItemId == itemId
                           && i.StoreId == storeId
                           && i.InsertTime <= Until_Date
                           && i.Qty > 0
                           //where expire_date == null ? true : i.Expire == expire_date
                           where (batch == null || batch == string.Empty) ? true : i.Batch == batch
                           where (QC == null || QC == string.Empty) ? true : i.QC == QC
                           where i.Length > 0 && i.Width > 0 && i.Height > 0 ? i.Length == Length && i.Width == Width && i.Height == Height : true
                           //orderby i.InsertTime ascending
                           //orderby i.Expire ascending
                           group i by new
                           {
                               //Cost = decimal.Round(i.PurchasePrice / i.Qty, 4),
                               //i.VendorId,
                               i.Expire,
                               i.Batch,
                               i.Serial
                           } into grp
                           let TransDate = grp.Where(x => x.IsInTrns).Select(x => x.InsertTime).Min()
                           let TotalCost = grp.Sum(x => x.IsInTrns ? x.PurchasePrice : x.PurchasePrice * -1)
                           let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                           where Available > 0
                           orderby TransDate ascending
                           orderby grp.Key.Expire ascending
                           select new
                           {
                               AvailableQty = Available,
                               PurchasePrice = TotalCost,
                               VendorId = grp.Select(x => x.VendorId).FirstOrDefault(),
                               grp.Key.Expire,
                               grp.Key.Batch,
                               grp.Key.Serial,
                           }).ToList();

            foreach (var d in itemQty)
            {
                DataRow dr = dt.NewRow();
                dr["InTrns_Id"] = 0;
                dr["AvailableQty"] = d.AvailableQty;
                dr["PurchasePrice"] = d.PurchasePrice;
                dr["VendorId"] = d.VendorId;
                dr["Expire"] = d.Expire;
                dr["Batch"] = d.Batch;
                dr["Serial"] = d.Serial;
                dt.Rows.Add(dr);
            }
            return dt;
        }

        public static List<IC_ItemStore> Get_Available_Itemstore_FIFO(int storeId, DateTime Until_Date, List<StoreItem> lst)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var data = (from l in lst
                        join i in DB.IC_ItemStores on l.ItemId equals i.ItemId
                        where i.ProcessId != (int)Process.SalesOrder
                        && i.StoreId == storeId
                        && i.ItemId == l.ItemId
                        && i.InsertTime <= Until_Date
                        && i.Qty > 0
                        where l.ExpireDate == null ? true : i.Expire == l.ExpireDate
                        where (l.Batch == null || l.Batch == string.Empty) ? true : i.Batch == l.Batch
                        where (l.Serial == null || l.Serial == string.Empty) ? true : i.Serial == l.Serial
                        where (l.QC == null || l.QC == string.Empty) ? true : i.QC == l.QC
                        where i.Length == l.Length && i.Width == l.Width && i.Height == l.Height
                        group i by i into grp
                        group grp.Key by new
                        {
                            grp.Key.ItemId,
                            Cost = decimal.Round(grp.Key.PurchasePrice / grp.Key.Qty, 4),
                            grp.Key.VendorId,
                            grp.Key.Expire,
                            grp.Key.Batch,
                            grp.Key.Serial,
                            grp.Key.QC,
                            grp.Key.ParentItemId,
                            grp.Key.M1,
                            grp.Key.M2,
                            grp.Key.M3,
                            grp.Key.Height,
                            grp.Key.Length,
                            grp.Key.Width,
                            grp.Key.Pack
                        } into grp
                        let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                        where Available > 0

                        select new
                        {
                            TotalOut = grp.Where(x => x.IsInTrns == false).Select(x => x == null ? 0 : x.Qty).Sum(),
                            Available,
                            lst_ins = grp.Where(x => x.IsInTrns),
                        }).ToList();

            var xsx = from l in lst
                      join i in DB.IC_ItemStores on l.ItemId equals i.ItemId
                      where i.ProcessId != (int)Process.SalesOrder
                      && i.StoreId == storeId
                      && i.ItemId == l.ItemId
                      && i.InsertTime <= Until_Date
                      && i.Qty > 0
                      where l.ExpireDate == null ? true : i.Expire == l.ExpireDate
                      where (l.Batch == null || l.Batch == string.Empty) ? true : i.Batch == l.Batch
                      where (l.Serial == null || l.Serial == string.Empty) ? true : i.Serial == l.Serial
                      where (l.QC == null || l.QC == string.Empty) ? true : i.QC == l.QC
                      where i.Length == l.Length && i.Width == l.Width && i.Height == l.Height
                      orderby i.InsertTime

                      select i;
            var vv = from g in xsx
                     group g by g into grp
                     group grp by new
                     {
                         grp.Key.ItemId,
                         Cost = decimal.Round(grp.Key.PurchasePrice / grp.Key.Qty, 4),
                         grp.Key.VendorId,
                         grp.Key.Expire,
                         grp.Key.Batch,
                         grp.Key.Serial,
                         grp.Key.QC,
                         grp.Key.ParentItemId,
                         grp.Key.M1,
                         grp.Key.M2,
                         grp.Key.M3,
                         grp.Key.Height,
                         grp.Key.Length,
                         grp.Key.Width,
                     } into grp
                     select grp;

            List<IC_ItemStore> lst_allins = new List<IC_ItemStore>();
            foreach (var d in data)
            {
                decimal totalout = d.TotalOut;
                foreach (var dd in d.lst_ins.OrderBy(x => x.InsertTime).ThenBy(x => x.ItemStoreId).ToList())
                {
                    decimal unitcost = dd.PurchasePrice / dd.Qty;
                    if (totalout >= dd.Qty)
                    {
                        totalout -= dd.Qty;
                        continue;
                    }
                    else
                    {
                        dd.Qty -= totalout;
                        dd.PurchasePrice = decimal.Round(dd.Qty * unitcost, 4);
                        totalout = 0;
                        lst_allins.Add(dd);
                    }
                }
            }

            return lst_allins.OrderBy(x => x.InsertTime).ThenBy(x => x.Expire).ThenBy(x => x.ItemStoreId).ToList();
        }

        public static List<IC_ItemStore> Get_Available_Itemstore_LIFO(int storeId, DateTime Until_Date, List<StoreItem> lst)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var data = (from l in lst
                        join i in DB.IC_ItemStores on l.ItemId equals i.ItemId
                        where i.ProcessId != (int)Process.SalesOrder
                        && i.StoreId == storeId
                        && i.ItemId == l.ItemId
                        && i.InsertTime <= Until_Date
                        && i.Qty > 0
                        orderby i.InsertTime descending
                        where l.ExpireDate == null ? true : i.Expire == l.ExpireDate
                        where (l.Batch == null || l.Batch == string.Empty) ? true : i.Batch == l.Batch
                        where (l.Serial == null || l.Serial == string.Empty) ? true : i.Serial == l.Serial
                        where (l.QC == null || l.QC == string.Empty) ? true : i.QC == l.QC
                        where i.Length == l.Length && i.Width == l.Width && i.Height == l.Height
                        group i by i into grp
                        group grp.Key by new
                        {
                            grp.Key.ItemId,
                            Cost = decimal.Round(grp.Key.PurchasePrice / grp.Key.Qty, 4),
                            grp.Key.VendorId,
                            grp.Key.Expire,
                            grp.Key.Batch,
                            grp.Key.Serial,
                            grp.Key.Serial2,
                            grp.Key.QC,
                            grp.Key.ParentItemId,
                            grp.Key.M1,
                            grp.Key.M2,
                            grp.Key.M3,
                            grp.Key.Height,
                            grp.Key.Length,
                            grp.Key.Width,
                            grp.Key.Pack

                        } into grp
                        let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                        where Available > 0
                        select new
                        {
                            TotalOut = grp.Where(x => x.IsInTrns == false).Select(x => x == null ? 0 : x.Qty).Sum(),
                            Available,
                            lst_ins = grp.Where(x => x.IsInTrns),
                        }).ToList();

            List<IC_ItemStore> lst_allins = new List<IC_ItemStore>();
            foreach (var d in data)
            {
                decimal totalout = d.TotalOut;
                foreach (var dd in d.lst_ins.OrderByDescending(x => x.InsertTime).ThenByDescending(x => x.ItemStoreId).ToList())
                {
                    decimal unitcost = dd.PurchasePrice / dd.Qty;
                    if (totalout >= dd.Qty)
                    {
                        totalout -= dd.Qty;
                        continue;
                    }
                    else
                    {
                        dd.Qty -= totalout;
                        dd.PurchasePrice = Math.Round(dd.Qty * unitcost, 4);
                        totalout = 0;
                        lst_allins.Add(dd);
                    }
                }
            }

            return lst_allins.OrderByDescending(x => x.InsertTime).ThenBy(x => x.Expire).ThenBy(x => x.ItemStoreId).ToList();
        }

        public static List<IC_ItemStore> Get_Available_Itemstore_Average(int storeId, DateTime Until_Date, List<StoreItem> lst)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var data = (from l in lst
                        join i in DB.IC_ItemStores on l.ItemId equals i.ItemId
                        where i.ProcessId != (int)Process.SalesOrder
                        && i.StoreId == storeId// (OLD BEFORE MULTIPLE STORES 13/7/2017 ALAA)
                        //where storeId > 0 ? i.StoreId == storeId : i.StoreId == ((SL_InvoiceDetail)l.Source).StoreId
                        && i.ItemId == l.ItemId
                        && i.InsertTime <= Until_Date
                        && i.Qty > 0
                        //where (l.Batch == null || l.Batch == string.Empty) ? true : i.Batch == l.Batch

                        where i.Length == l.Length && i.Width == l.Width && i.Height == l.Height
                        group i by i into grp
                        select grp.Key).ToList();

            var itemQty = (from i in data
                           group i by new
                           {
                               i.ItemId,
                               i.ParentItemId,
                               i.M1,
                               i.M2,
                               i.M3,
                               i.Length,
                               i.Width,
                               i.Height,
                               i.Batch,

                               //Batch = (string.IsNullOrEmpty(i.Batch) ? "<null>" : i.Batch)
                           } into grp
                           let TotalCost = grp.Sum(x => x.IsInTrns ? x.PurchasePrice : x.PurchasePrice * -1)
                           let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                           where Available > 0 && TotalCost > 0
                           select new IC_ItemStore
                           {
                               ItemId = grp.Key.ItemId,
                               StoreId = storeId,
                               VendorId = null,
                               ProcessId = (int)Process.InTrns,
                               SourceId = 0,
                               IsInTrns = true,
                               Qty = Available,
                               PurchasePrice = TotalCost,
                               InsertTime = Until_Date,
                               Batch = grp.Key.Batch,
                               //Batch =null,

                               Serial = null,
                               Expire = null,
                               Height = grp.Key.Height,
                               Width = grp.Key.Width,
                               Length = grp.Key.Length,
                               PiecesCount = 0,
                               ParentItemId = grp.Key.ParentItemId,
                               M1 = grp.Key.M1,
                               M2 = grp.Key.M2,
                               M3 = grp.Key.M3,
                               SellPrice = 0,
                               QC = null,
                               Pack = 0
                           }).ToList();



            return itemQty;
        }

        /// <summary>
        /// inventory should be added before new year start date, to get open inventory value.
        /// </summary>
        /// <returns></returns>
        public static List<IC_ItemStore> GetAvailableItemsNewYear(DateTime insertTime)
        {
            List<IC_ItemStore> lstNewStore = new List<IC_ItemStore>();
            ERPDataContext DB = new ERPDataContext();
            var data = (from i in DB.IC_ItemStores
                        where i.InsertTime < insertTime
                        where i.ProcessId != (int)Process.SalesOrder
                        group i by new
                        {
                            i.StoreId,
                            i.ItemId,
                            i.Expire,
                            i.Batch,
                            i.Height,
                            i.Length,
                            i.Width,
                            i.ParentItemId,
                            i.M1,
                            i.M2,
                            i.M3,
                            i.QC,
                            i.Serial
                        } into grp
                        select new
                        {
                            StoreId = grp.Key.StoreId,
                            ItemId = grp.Key.ItemId,
                            CurrentQty = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1),
                            PiecesCount = grp.Sum(x => x.IsInTrns ? x.PiecesCount : x.PiecesCount * -1),
                            totalPrice = grp.Sum(x => x.IsInTrns ? x.PurchasePrice : x.PurchasePrice * -1),
                            grp.Key.Expire,
                            grp.Key.Batch,
                            grp.Key.Height,
                            grp.Key.Length,
                            grp.Key.Width,
                            grp.Key.ParentItemId,
                            grp.Key.M1,
                            grp.Key.M2,
                            grp.Key.M3,
                            grp.Key.QC,
                            grp.Key.Serial,
                            Pack = grp.Sum(x => x.IsInTrns ? x.Pack : x.Pack * -1)
                        }).Distinct().ToList();

            foreach (var row in data)
            {
                if (row.CurrentQty == 0)
                    continue;

                IC_ItemStore it = new IC_ItemStore();
                it.ItemId = row.ItemId;
                it.StoreId = row.StoreId;
                it.Qty = Math.Abs(row.CurrentQty);
                it.PurchasePrice = Math.Abs(row.totalPrice);
                it.ProcessId = (int)Process.OpenBalance;
                it.SourceId = -1;
                it.IsInTrns = row.CurrentQty > 0 ? true : false;
                it.InsertTime = insertTime;
                it.Expire = row.Expire;
                it.Batch = row.Batch;
                it.Height = row.Height;
                it.Length = row.Length;
                it.Width = row.Width;
                it.PiecesCount = Math.Abs(row.PiecesCount);
                it.ParentItemId = row.ParentItemId;
                it.M1 = row.M1;
                it.M2 = row.M2;
                it.M3 = row.M3;
                it.QC = row.QC;
                it.Serial = row.Serial;
                it.Pack = row.Pack;

                lstNewStore.Add(it);
            }
            return lstNewStore;
        }

        //public static DataTable GetCloseInventory(DateTime fromDate, DateTime toDate, out decimal closeInvValue, out decimal openInvValue,
        //    int costCenter)
        //{
        //    closeInvValue = openInvValue = 0;
        //    DAL.ERPDataContext DB = new DAL.ERPDataContext();

        //    DataTable dtStoresCloseOpen = new DataTable();
        //    //dtStoresCloseOpen.Columns.Add("StoreId");
        //    dtStoresCloseOpen.Columns.Add("CloseAccountId");
        //    dtStoresCloseOpen.Columns.Add("Value");
        //    dtStoresCloseOpen.Columns.Add("OpenAccountId");
        //    dtStoresCloseOpen.Columns.Add("OpenValue");

        //    //Get Close Inventory && Open Inventory
        //    var stores = DB.IC_Stores.Where(c => costCenter > 0 ? c.CostCenterId == costCenter : true).ToList();
        //    foreach (var str in stores)
        //    {
        //        var itemsOpenCost = (from i in DB.IC_ItemStores
        //                             where i.StoreId == str.StoreId
        //                             && i.InsertTime.Date < fromDate.Date
        //                             group i by i.ItemId into grp
        //                             select new
        //                             {
        //                                 itemId = grp.Key,
        //                                 PurchasePrice = grp.Sum(x => x.IsInTrns ? x.PurchasePrice : x.PurchasePrice * -1)
        //                             }).ToList().Sum(x => x.PurchasePrice);

        //        var itemsCloseCost = (from i in DB.IC_ItemStores
        //                              where i.StoreId == str.StoreId
        //                              && i.InsertTime.Date <= toDate.Date
        //                              group i by i.ItemId into grp
        //                              select new
        //                              {
        //                                  itemId = grp.Key,
        //                                  PurchasePrice = grp.Sum(x => x.IsInTrns ? x.PurchasePrice : x.PurchasePrice * -1)
        //                              }).ToList().Sum(x => x.PurchasePrice);


        //        openInvValue += itemsOpenCost;
        //        closeInvValue += itemsCloseCost;

        //        //set accounts values
        //        var storeAccountsRow = (from DataRow dr in dtStoresCloseOpen.Rows
        //                                where dr["CloseAccountId"].ToString() == str.CloseInventoryAccount.ToString()
        //                                where dr["OpenAccountId"].ToString() == str.OpenInventoryAccount.ToString()

        //                                select dr).FirstOrDefault();

        //        if (storeAccountsRow != null && storeAccountsRow["CloseAccountId"] != DBNull.Value && storeAccountsRow["CloseAccountId"] != null
        //            && storeAccountsRow["OpenAccountId"] != DBNull.Value && storeAccountsRow["OpenAccountId"] != null)
        //        {
        //            storeAccountsRow["Value"] = Convert.ToDouble(storeAccountsRow["Value"]) +
        //                decimal.ToDouble(itemsCloseCost);

        //            storeAccountsRow["OpenValue"] = Convert.ToDouble(storeAccountsRow["OpenValue"]) +
        //                decimal.ToDouble(itemsOpenCost);
        //        }
        //        else
        //            dtStoresCloseOpen.Rows.Add(/*str.StoreId,*/ str.CloseInventoryAccount, decimal.ToDouble(itemsCloseCost),
        //               str.OpenInventoryAccount, decimal.ToDouble(itemsOpenCost));
        //    }
        //    return dtStoresCloseOpen;
        //}

        //public static decimal Get_Store_Evaluation(DateTime fromDate, DateTime toDate, int? storeId, int costcenterId)
        //{
        //    ERPDataContext DB = new ERPDataContext();
        //    return (from d in DB.IC_ItemStores
        //            join s in DB.IC_Stores on d.StoreId equals s.StoreId
        //            where costcenterId == 0 ? true : s.CostCenterId == costcenterId
        //            where storeId.HasValue ? d.StoreId == storeId : true
        //            where d.InsertTime >= fromDate
        //            where d.InsertTime <= toDate
        //            group d by d.ItemId into grp
        //            let qty = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
        //            where qty != 0
        //            select new
        //            {
        //                Total = grp.Sum(d => d.IsInTrns ? d.PurchasePrice : d.PurchasePrice * -1)
        //            }).Select(x => x.Total).ToList().DefaultIfEmpty(0).Sum();

        //}

        #region Add_ItemStore_Methods

        public static void AddItemToStore(int itemId, int storeId,
             decimal qty, byte uom, decimal MediumUOMFactor, decimal LargeUOMFactor,
            int? VendorId, int SourceId, int processId, bool IsInTrns, decimal PurchasePrice, DateTime InsertTime,
            DateTime? expire_date, string batch, decimal Length, decimal Width, decimal Height, decimal PiecesCount,
            int? ParentItemId, int? M1, int? M2, int? M3, decimal? TotalSellPrice, string QC, String Serial, int Pack)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            DAL.IC_ItemStore itmStr = new DAL.IC_ItemStore();
            itmStr.ItemId = itemId;
            itmStr.StoreId = storeId;
            itmStr.VendorId = VendorId;//open balance = null            
            itmStr.ProcessId = processId;
            itmStr.SourceId = SourceId;
            itmStr.IsInTrns = IsInTrns;
            //calculate qty
            itmStr.Qty = CalculateUomQty(qty, uom, MediumUOMFactor, LargeUOMFactor);
            itmStr.PurchasePrice = PurchasePrice;
            itmStr.InsertTime = InsertTime;

            if (batch == null || batch == string.Empty || batch.Trim() == string.Empty)
                itmStr.Batch = null;
            else
                itmStr.Batch = batch;

            if (Serial == null || Serial == string.Empty || Serial.Trim() == string.Empty)
                itmStr.Serial = null;
            else
                itmStr.Serial = Serial;

            itmStr.Expire = expire_date;

            itmStr.Height = Height;
            itmStr.Width = Width;
            itmStr.Length = Length;
            itmStr.PiecesCount = PiecesCount;

            itmStr.Pack = Pack;

            itmStr.ParentItemId = ParentItemId;
            itmStr.M1 = M1;
            itmStr.M2 = M2;
            itmStr.M3 = M3;

            itmStr.SellPrice = TotalSellPrice;
            if (QC == null || QC == string.Empty || QC.Trim() == string.Empty)
                itmStr.QC = null;
            else
                itmStr.QC = QC;

            DB.IC_ItemStores.InsertOnSubmit(itmStr);
            DB.SubmitChanges();
        }

        public static void AddItemToStoreDataContext(ERPDataContext DB, int itemId, int storeId,
             decimal qty, int? VendorId, int SourceId, int processId, bool IsInTrns, decimal PurchasePrice, DateTime InsertTime,
            DateTime? expire_date, string batch, decimal Length, decimal Width, decimal Height, decimal PiecesCount,
            int? ParentItemId, int? M1, int? M2, int? M3, decimal? TotalSellPrice, string QC, byte MultiplyDimensions, string Serial)
        {
            DAL.IC_ItemStore itmStr = new DAL.IC_ItemStore();
            itmStr.ItemId = itemId;
            itmStr.StoreId = storeId;
            itmStr.VendorId = VendorId;//open balance = null            
            itmStr.ProcessId = processId;
            itmStr.SourceId = SourceId;
            itmStr.IsInTrns = IsInTrns;
            //calculate qty
            itmStr.Qty = qty;
            itmStr.PurchasePrice = PurchasePrice;
            itmStr.InsertTime = InsertTime;

            if (batch == null || batch == string.Empty || batch.Trim() == string.Empty)
                itmStr.Batch = null;
            else
                itmStr.Batch = batch;

            if (Serial == null || Serial == string.Empty || Serial.Trim() == string.Empty)
                itmStr.Serial = null;
            else
                itmStr.Serial = Serial;

            itmStr.Expire = expire_date;

            if (MultiplyDimensions != (byte)Dimensions.Multiply)
            {
                itmStr.Height = Height;
                itmStr.Width = Width;
                itmStr.Length = Length;
                itmStr.PiecesCount = PiecesCount;
            }

            itmStr.ParentItemId = ParentItemId;
            itmStr.M1 = M1;
            itmStr.M2 = M2;
            itmStr.M3 = M3;

            itmStr.SellPrice = TotalSellPrice;
            if (QC == null || QC == string.Empty || QC.Trim() == string.Empty)
                itmStr.QC = null;
            else
                itmStr.QC = QC;

            DB.IC_ItemStores.InsertOnSubmit(itmStr);
        }
        #endregion

        #region Subtract_From_Store_Methods
        /// <summary>
        /// Distinguish & Distinguish+Multiply Dimensions
        /// </summary>
        /// <param name="ItemId"></param>
        /// <param name="StoreId"></param>
        /// <param name="SourceId"></param>
        /// <param name="processId"></param>
        /// <param name="UOMIndex"></param>
        /// <param name="Qty"></param>
        /// <param name="MediumUOMFactor"></param>
        /// <param name="LargeUOMFactor"></param>
        /// <param name="cost_method"></param>
        /// <param name="InsertTime"></param>
        /// <param name="expire_date"></param>
        /// <param name="batch"></param>
        /// <param name="Length"></param>
        /// <param name="Width"></param>
        /// <param name="Height"></param>
        /// <param name="PiecesCount"></param>
        /// <param name="ParentItemId"></param>
        /// <param name="M1"></param>
        /// <param name="M2"></param>
        /// <param name="M3"></param>
        /// <param name="TotalSellPrice"></param>
        /// <param name="VendorId"></param>
        /// <param name="QC"></param>
        /// <param name="CalcCostNoInsert">true when salesInvoice doesn't post to store</param>
        /// <returns>cost of sold goods</returns>
        public static decimal Subtract_from_store(int ItemId, int StoreId, int SourceId, int processId,
                    byte UOMIndex, decimal Qty, decimal MediumUOMFactor, decimal LargeUOMFactor, byte cost_method, DateTime InsertTime,
                    DateTime? expire_date, string batch, decimal Length, decimal Width, decimal Height, decimal PiecesCount,
                    int? ParentItemId, int? M1, int? M2, int? M3, decimal TotalSellPrice, int? VendorId, string QC, bool CalcCostNoInsert,
                    string Serial, int Pack)
        {

            decimal ItemCost = 0;
            decimal UOMSellPrice = 0;
            if (Qty > 0)
            {
                UOMSellPrice = TotalSellPrice / Qty;
            }

            if (batch == null || batch == string.Empty || batch.Trim() == string.Empty)
                batch = null;

            if (Serial == null || Serial == string.Empty || Serial.Trim() == string.Empty)
                Serial = null;

            if (QC == null || QC == string.Empty || QC.Trim() == string.Empty)
                QC = null;

            DataTable available_itemstores = new DataTable();
            if (cost_method == (byte)CostMethod.FIFO)
            {
                available_itemstores = MyHelper.Get_Available_Itemstore_FIFO(ItemId, StoreId, InsertTime, expire_date, batch, Length, Width, Height, QC, Serial);
            }
            else if (cost_method == (byte)CostMethod.LIFO)
            {
                available_itemstores = MyHelper.Get_Available_Itemstore_LIFO(ItemId, StoreId, InsertTime, expire_date, batch, Length, Width, Height, QC, Serial);
            }
            else if (cost_method == (byte)CostMethod.AVERAGE)
            {
                available_itemstores = MyHelper.Get_Available_Itemstore_Average(ItemId, StoreId, InsertTime, expire_date, batch, Length, Width, Height, QC, Serial);
            }

            if (available_itemstores.Rows.Count > 0)
            {
                //get out qty by smallest uom 
                decimal out_qty = MyHelper.CalculateUomQty(Qty, UOMIndex, MediumUOMFactor, LargeUOMFactor);
                foreach (DataRow dr_in_store in available_itemstores.Rows)
                {
                    if (out_qty <= 0)
                        break;

                    decimal AvailableQty = Convert.ToDecimal(dr_in_store["AvailableQty"]);
                    decimal PurchasePrice = Convert.ToDecimal(dr_in_store["PurchasePrice"]);

                    if (dr_in_store["VendorId"] != DBNull.Value)
                        VendorId = Convert.ToInt32(dr_in_store["VendorId"]);

                    decimal unit_purchase_price = PurchasePrice / AvailableQty;

                    string current_batch = batch;
                    if (available_itemstores.Columns["Batch"] != null && dr_in_store["Batch"] != DBNull.Value)
                        current_batch = dr_in_store["Batch"].ToString();

                    string current_Serial = Serial;
                    if (available_itemstores.Columns["Serial"] != null && dr_in_store["Serial"] != DBNull.Value)
                        current_Serial = dr_in_store["Serial"].ToString();

                    DateTime? current_expire_date = expire_date;
                    if (available_itemstores.Columns["Expire"] != null && dr_in_store["Expire"] != DBNull.Value)
                        current_expire_date = Convert.ToDateTime(dr_in_store["Expire"]);

                    if (out_qty <= AvailableQty)        //current in record is enough or more than need
                    {
                        //if (CalcCostNoInsert == false)
                        MyHelper.AddItemToStore(ItemId, StoreId, out_qty, 0,
                        MediumUOMFactor, LargeUOMFactor, VendorId, SourceId, processId,
                        false, unit_purchase_price * out_qty, InsertTime, current_expire_date, current_batch, Length, Width, Height, PiecesCount
                        , ParentItemId, M1, M2, M3, UOMSellPrice * out_qty, QC, current_Serial, Pack);

                        ItemCost += (unit_purchase_price * out_qty);
                    }
                    else if (out_qty > AvailableQty)
                    {
                        //if (CalcCostNoInsert == false)
                        MyHelper.AddItemToStore(ItemId, StoreId, AvailableQty, 0,
                        MediumUOMFactor, LargeUOMFactor, VendorId, SourceId, processId,
                        false, unit_purchase_price * AvailableQty, InsertTime, current_expire_date, current_batch, Length, Width, Height, PiecesCount
                        , ParentItemId, M1, M2, M3, UOMSellPrice * AvailableQty, QC, current_Serial, Pack);

                        ItemCost += (unit_purchase_price * AvailableQty);

                        PiecesCount = 0;
                    }
                    out_qty -= AvailableQty;
                }

                if (out_qty > 0)
                {
                    //if (CalcCostNoInsert == false)
                    MyHelper.AddItemToStore(ItemId, StoreId, out_qty, 0,
                    MediumUOMFactor, LargeUOMFactor, null, SourceId, processId,
                    false, 0, InsertTime, expire_date, batch, Length, Width, Height, PiecesCount
                    , ParentItemId, M1, M2, M3, UOMSellPrice * out_qty, QC, Serial, Pack);
                }
            }
            else
            {
                if (cost_method != 2)
                {
                    if (CalcCostNoInsert == false)
                        MyHelper.AddItemToStore(ItemId, StoreId, Qty, UOMIndex,
                            MediumUOMFactor, LargeUOMFactor, null, SourceId, processId,
                            false, 0, InsertTime, expire_date, batch, Length, Width, Height, PiecesCount
                            , ParentItemId, M1, M2, M3, UOMSellPrice * Qty, QC, Serial, Pack);
                    else
                        MyHelper.AddItemToStore(ItemId, StoreId, Qty, UOMIndex,
                            MediumUOMFactor, LargeUOMFactor, null, SourceId, processId,
                            false, ItemCost, InsertTime, expire_date, batch, Length, Width, Height, PiecesCount
                            , ParentItemId, M1, M2, M3, UOMSellPrice * Qty, QC, Serial, Pack);
                }
                else //Average Method
                {
                    decimal avgpurchaseprice = Get_StoreItem_Average_Price(ItemId, Length, Width, Height, StoreId, InsertTime);

                    if (UOMIndex == 1)
                        avgpurchaseprice = avgpurchaseprice * MediumUOMFactor;
                    else if (UOMIndex == 2)
                        avgpurchaseprice = avgpurchaseprice * LargeUOMFactor;

                    if (CalcCostNoInsert == false)
                        MyHelper.AddItemToStore(ItemId, StoreId, Qty, UOMIndex,
                            MediumUOMFactor, LargeUOMFactor, null, SourceId, processId,
                            false, avgpurchaseprice * Qty, InsertTime, expire_date, batch, Length, Width, Height, PiecesCount
                            , ParentItemId, M1, M2, M3, UOMSellPrice * Qty, QC, Serial, Pack);

                    ItemCost += (avgpurchaseprice * Qty);
                }
            }
            return ItemCost;
        }


        public static List<IC_ItemStore> Subtract_from_store(int StoreId, byte cost_method, int processId, DateTime InsertTime, List<StoreItem> lst_storeItems)
        {
            ERPDataContext db = new ERPDataContext();
            if (lst_storeItems.Count() == 0)
                return new List<IC_ItemStore>();

            List<IC_ItemStore> all_available_itemstores = new List<IC_ItemStore>();
            List<IC_ItemStore> all_out_itemstores = new List<IC_ItemStore>();

            //if (StoreId > 0)
            if (lst_storeItems.Where(x => x.StoreId == null).Count() == 0)//in case of storeId on each row
            {


                //var db = new ERPDataContext();
                var CostMethods = from l in lst_storeItems
                                  join s in db.IC_Stores on ((SL_InvoiceDetail)l.Source).StoreId equals s.StoreId
                                  group l by new { s.CostMethod, s.StoreId } into grp
                                  select new
                                  {
                                      grp.Key.CostMethod,
                                      grp.Key.StoreId
                                  };

                foreach (var c in CostMethods)
                {
                    var items = lst_storeItems.Where(x => ((SL_InvoiceDetail)x.Source).StoreId == c.StoreId).ToList();
                    if (c.CostMethod == (byte)CostMethod.FIFO)
                    {
                        all_available_itemstores.AddRange(MyHelper.Get_Available_Itemstore_FIFO(c.StoreId, InsertTime, items));
                    }
                    else if (c.CostMethod == (byte)CostMethod.LIFO)
                    {
                        all_available_itemstores.AddRange(MyHelper.Get_Available_Itemstore_LIFO(c.StoreId, InsertTime, items));
                    }
                    else if (c.CostMethod == (byte)CostMethod.AVERAGE)
                    {
                        all_available_itemstores.AddRange(MyHelper.Get_Available_Itemstore_Average(c.StoreId, InsertTime, items));
                    }
                }




            }
            else
            {
                if (cost_method == (byte)CostMethod.FIFO)
                {
                    all_available_itemstores = MyHelper.Get_Available_Itemstore_FIFO(StoreId, InsertTime, lst_storeItems);
                }
                else if (cost_method == (byte)CostMethod.LIFO)
                {
                    all_available_itemstores = MyHelper.Get_Available_Itemstore_LIFO(StoreId, InsertTime, lst_storeItems);
                }
                else if (cost_method == (byte)CostMethod.AVERAGE)
                {
                    all_available_itemstores = MyHelper.Get_Available_Itemstore_Average(StoreId, InsertTime, lst_storeItems);
                }
            }
            foreach (var outtrans in lst_storeItems)
            {
                var currentStore = 0;
                //if (StoreId == 0)
                if (lst_storeItems.Where(x => x.StoreId == null).Count() == 0) /*db.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0)*/

                    currentStore = ((SL_InvoiceDetail)outtrans.Source).StoreId.Value;
                else
                    currentStore = StoreId;

                if (outtrans.ItemType == (int)ItemType.Service || outtrans.ItemType == (int)ItemType.Subtotal || outtrans.ItemType == (int)ItemType.MatrixParent)
                    continue;

                decimal PiecesCount = outtrans.PiecesCount;
                int Pack = outtrans.Pack;

                var available_itemstores = (from i in all_available_itemstores
                                            where i.ItemId == outtrans.ItemId
                                            where outtrans.VendorId == null ? true : i.VendorId == outtrans.VendorId
                                            where outtrans.ExpireDate == null ? true : i.Expire == outtrans.ExpireDate
                                            //where (outtrans.Batch == null || outtrans.Batch == string.Empty) ? true : i.Batch == outtrans.Batch
                                            // where i.Batch == outtrans.Batch

                                            //14/11/2018 compare batch according to setting
                                            where Shared.st_Store.Batch == true ? i.Batch == outtrans.Batch : true
                                            where (outtrans.Serial == null || outtrans.Serial == string.Empty) ? true : i.Serial == outtrans.Serial
                                            where (outtrans.QC == null || outtrans.QC == string.Empty) ? true : i.QC == outtrans.QC
                                            where i.Length == outtrans.Length && i.Width == outtrans.Width && i.Height == outtrans.Height
                                            select i).ToList();


                if (available_itemstores.Count() == 0)
                {
                    //if (StoreId > 0)//13/7/2017 Mohammad

                    //Adel : get Last Cost price 
                    decimal lastUnitCostPrice = 0;
                    if (Shared.st_Store.UseLastCostPrice == true)
                    {
                        var lastCostquery = (from i in db.IC_ItemStores
                                             where i.ItemId == outtrans.ItemId
                                             where !i.IsInTrns
                                             orderby i.InsertTime descending
                                             select new
                                             {
                                                 i.PurchasePrice,
                                                 i.Qty
                                             }).FirstOrDefault();
                        if(lastCostquery != null)
                        {
                            lastUnitCostPrice = lastCostquery.PurchasePrice / lastCostquery.Qty;
                        }
                    }

                    all_out_itemstores.Add(CreateItemStore(outtrans.ItemId, currentStore, outtrans.TotalQty, 0, 1, 1, null, outtrans.SourceId, processId,
                        false, lastUnitCostPrice * outtrans.TotalQty, InsertTime, outtrans.ExpireDate, outtrans.Batch, outtrans.Length, outtrans.Width, outtrans.Height, PiecesCount
                        , outtrans.ParentItemId, outtrans.M1, outtrans.M2, outtrans.M3, outtrans.SmallUnitSellPrice * outtrans.TotalQty, outtrans.QC, outtrans.Serial, outtrans.Serial2, Pack));

                    //else
                    //{

                    //    all_out_itemstores.Add(CreateItemStore(outtrans.ItemId, ((SL_InvoiceDetail)outtrans.Source).StoreId.Value, outtrans.TotalQty, 0, 1, 1, null, outtrans.SourceId, processId,
                    //       false, 0, InsertTime, outtrans.ExpireDate, outtrans.Batch, outtrans.Length, outtrans.Width, outtrans.Height, PiecesCount
                    //       , outtrans.ParentItemId, outtrans.M1, outtrans.M2, outtrans.M3, outtrans.SmallUnitSellPrice * outtrans.TotalQty, outtrans.QC, outtrans.Serial));

                    //}
                    //else//13/7/2017 Mohammad
                    //{
                    //    foreach (var s in lst_storeItems.Select(x => ((SL_InvoiceDetail)x.Source).StoreId).Distinct())
                    //    {
                    //        all_out_itemstores.Add(CreateItemStore(outtrans.ItemId, s.Value, outtrans.TotalQty, 0, 1, 1, null, outtrans.SourceId, processId,
                    //       false, 0, InsertTime, outtrans.ExpireDate, outtrans.Batch, outtrans.Length, outtrans.Width, outtrans.Height, PiecesCount
                    //       , outtrans.ParentItemId, outtrans.M1, outtrans.M2, outtrans.M3, outtrans.SmallUnitSellPrice * outtrans.TotalQty, outtrans.QC, outtrans.Serial));

                    //    }
                    //}
                }
                else
                {
                    // var db = new ERPDataContext();

                    decimal out_qty = outtrans.TotalQty;
                    foreach (var intrans in available_itemstores)
                    {
                        decimal unit_purchase_price = 0;

                        if (out_qty <= 0 && !Shared.LibraAvailabe)
                            break;

                        if (intrans.Qty > 0)
                            unit_purchase_price = intrans.PurchasePrice / intrans.Qty;

                        if (out_qty < intrans.Qty)        //current in record is enough or more than need
                        {
                            all_out_itemstores.Add(CreateItemStore(intrans.ItemId, currentStore, out_qty, 0, 1, 1, intrans.VendorId, outtrans.SourceId, processId,
                           false, Math.Round(unit_purchase_price * out_qty, 4), InsertTime, intrans.Expire, intrans.Batch, intrans.Length, intrans.Width, intrans.Height, PiecesCount
                           , intrans.ParentItemId, intrans.M1, intrans.M2, intrans.M3, outtrans.SmallUnitSellPrice * out_qty, intrans.QC, intrans.Serial, intrans.Serial2, Pack));


                            outtrans.TotalCost += Math.Round(unit_purchase_price * out_qty, 4);

                            intrans.Qty -= out_qty;
                            intrans.PurchasePrice -= Math.Round(unit_purchase_price * out_qty, 4);
                            out_qty = 0;


                        }
                        else if (out_qty >= intrans.Qty)
                        {
                            all_out_itemstores.Add(CreateItemStore(intrans.ItemId, currentStore, intrans.Qty, 0, 1, 1, intrans.VendorId, outtrans.SourceId, processId,
                            false, decimal.Round(unit_purchase_price * intrans.Qty, 4), InsertTime, intrans.Expire, intrans.Batch, intrans.Length, intrans.Width, intrans.Height, PiecesCount
                            , intrans.ParentItemId, intrans.M1, intrans.M2, intrans.M3, outtrans.SmallUnitSellPrice * intrans.Qty, intrans.QC, intrans.Serial, intrans.Serial2, Pack));
                            PiecesCount = 0;

                            outtrans.TotalCost += decimal.Round(unit_purchase_price * intrans.Qty, 4);

                            all_available_itemstores.Remove(intrans);
                            out_qty -= intrans.Qty;
                        }
                    }

                    if (out_qty > 0)
                    {
                        all_out_itemstores.Add(CreateItemStore(outtrans.ItemId, currentStore, out_qty, 0, 1, 1, outtrans.VendorId, outtrans.SourceId, processId,
                        false, 0, InsertTime, outtrans.ExpireDate, outtrans.Batch, outtrans.Length, outtrans.Width, outtrans.Height, PiecesCount
                        , outtrans.ParentItemId, outtrans.M1, outtrans.M2, outtrans.M3, outtrans.SmallUnitSellPrice * out_qty, outtrans.QC, outtrans.Serial, outtrans.Serial2, Pack));
                    }
                }
            }

            return all_out_itemstores;
        }

        // public static List<IC_ItemStore> Add_to_store(int StoreId, int processId, DateTime InsertTime, List<StoreItem> lst_storeItems)
        public static List<IC_ItemStore> Add_to_store(int processId, DateTime InsertTime, List<StoreItem> lst_storeItems, params int[] StoreId)

        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            List<IC_ItemStore> all_in_itemstores = new List<IC_ItemStore>();
            int count = 0;
            foreach (var intrans in lst_storeItems)
            {
                if (intrans.ItemType == (int)ItemType.Service || intrans.ItemType == (int)ItemType.Subtotal || intrans.ItemType == (int)ItemType.MatrixParent)
                    continue;

                //    all_in_itemstores.Add(CreateItemStore(intrans.ItemId, intrans.st, intrans.TotalQty, 0, 1, 1, intrans.VendorId, intrans.SourceId, processId,
                //        true, intrans.TotalCost, InsertTime, intrans.ExpireDate, intrans.Batch, intrans.Length, intrans.Width, intrans.Height, intrans.PiecesCount
                //        , intrans.ParentItemId, intrans.M1, intrans.M2, intrans.M3, intrans.SmallUnitSellPrice * intrans.TotalQty, intrans.QC, intrans.Serial));


                if (StoreId.Count() > 1)


                    all_in_itemstores.Add(CreateItemStore(intrans.ItemId, StoreId[count], intrans.TotalQty, 0, 1, 1, intrans.VendorId, intrans.SourceId, processId,
               true, intrans.TotalCost, InsertTime, intrans.ExpireDate, intrans.Batch, intrans.Length, intrans.Width, intrans.Height, intrans.PiecesCount
               , intrans.ParentItemId, intrans.M1, intrans.M2, intrans.M3, intrans.SmallUnitSellPrice * intrans.TotalQty, intrans.QC, intrans.Serial, intrans.Serial2, intrans.Pack));

                else
                    all_in_itemstores.Add(CreateItemStore(intrans.ItemId, StoreId[0], intrans.TotalQty, 0, 1, 1, intrans.VendorId, intrans.SourceId, processId,
                    true, intrans.TotalCost, InsertTime, intrans.ExpireDate, intrans.Batch, intrans.Length, intrans.Width, intrans.Height, intrans.PiecesCount
                    , intrans.ParentItemId, intrans.M1, intrans.M2, intrans.M3, intrans.SmallUnitSellPrice * intrans.TotalQty, intrans.QC, intrans.Serial, intrans.Serial2, intrans.Pack));
                count++;

            }

            return all_in_itemstores;
        }
        public static IC_ItemStore CreateItemStore(int itemId, int storeId,
             decimal qty, byte uom, decimal MediumUOMFactor, decimal LargeUOMFactor,
            int? VendorId, int SourceId, int processId, bool IsInTrns, decimal PurchasePrice, DateTime InsertTime,
            DateTime? expire_date, string batch, decimal Length, decimal Width, decimal Height, decimal PiecesCount,
            int? ParentItemId, int? M1, int? M2, int? M3, decimal? TotalSellPrice, string QC, string Serial, string Serial2, int? Pack)
        {
            DAL.IC_ItemStore itmStr = new DAL.IC_ItemStore();
            itmStr.ItemId = itemId;
            itmStr.StoreId = storeId;
            itmStr.VendorId = VendorId;//open balance = null            
            itmStr.ProcessId = processId;
            itmStr.SourceId = SourceId;
            itmStr.IsInTrns = IsInTrns;
            //calculate qty
            itmStr.Qty = CalculateUomQty(qty, uom, MediumUOMFactor, LargeUOMFactor);
            itmStr.PurchasePrice = PurchasePrice;
            itmStr.InsertTime = InsertTime;

            if (batch == null || batch == string.Empty || batch.Trim() == string.Empty)
                itmStr.Batch = null;
            else
                itmStr.Batch = batch;

            if (Serial == null || Serial == string.Empty || Serial.Trim() == string.Empty)
                itmStr.Serial = null;
            else
                itmStr.Serial = Serial;

            if (Serial2 == null || Serial2 == string.Empty || Serial2.Trim() == string.Empty)
                itmStr.Serial2 = null;
            else
                itmStr.Serial2 = Serial2;

            itmStr.Expire = expire_date;

            itmStr.Height = Height;
            itmStr.Width = Width;
            itmStr.Length = Length;
            itmStr.PiecesCount = PiecesCount;

            itmStr.ParentItemId = ParentItemId;
            itmStr.M1 = M1;
            itmStr.M2 = M2;
            itmStr.M3 = M3;

            itmStr.SellPrice = TotalSellPrice;
            if (QC == null || QC == string.Empty || QC.Trim() == string.Empty)
                itmStr.QC = null;
            else
                itmStr.QC = QC;
            itmStr.Pack = Pack;

            return itmStr;
        }
        #endregion


        public static decimal Get_StoreItem_Average_Price(int itemId, decimal Length, decimal Width, decimal Height, int storeId, DateTime Until_Date)
        {
            ERPDataContext DB = new ERPDataContext();

            var data = (from d in DB.IC_ItemStores
                        where d.ProcessId != (int)Process.SalesOrder
                        && d.ItemId == itemId && (d.StoreId == 0 || d.StoreId == storeId) && d.InsertTime <= Until_Date
                        && d.Length == Length && d.Width == Width && d.Height == Height
                        group d by d.ItemId into grp
                        let Total_Pprice = grp.Select(d => d.IsInTrns ? d.PurchasePrice : d.PurchasePrice * -1).Sum()
                        let Total_Qty = grp.Select(d => d.IsInTrns ? d.Qty : d.Qty * -1).Sum()
                        select Total_Pprice <= 0 || Total_Qty <= 0 ? 0 : Total_Pprice / Total_Qty).FirstOrDefault();

            return data;
        }

        /// <summary>
        /// Post Item Stores Journal and archive invoices
        /// </summary>
        public static bool PostNonArchivedSalesInvoices(int? UserId, int? Sl_InvoiceId, bool InvoicePostToStore,
            bool OfflinePostToGL, ST_Store st_Store, bool stockIsPeriodic)
        {
            bool SavedSuccessfuly = false;
            DateTime today = Get_Server_DateTime();
            ERPDataContext DB = new ERPDataContext();
            try
            {
                DB.Connection.Open();
                DB.Transaction = DB.Connection.BeginTransaction();

                var data1 = (from d in DB.SL_InvoiceDetails
                             where UserId == null ? true : d.SL_Invoice.UserId == UserId
                             where Sl_InvoiceId == null ? true : d.SL_InvoiceId == Sl_InvoiceId
                             where d.SL_Invoice.IsArchived == false
                             join t in DB.IC_Items on d.ItemId equals t.ItemId
                             where t.ItemType != (int)ItemType.MatrixParent
                             && t.ItemType != (int)ItemType.Service
                             && t.ItemType != (int)ItemType.Subtotal
                             join s in DB.IC_Stores on d.SL_Invoice.StoreId equals s.StoreId
                             group d by new
                             {
                                 s.CostMethod,
                                 d.SL_Invoice.StoreId,
                                 d.ItemId,
                                 Batch = d.Batch.Trim() == string.Empty ? null : d.Batch,
                                 d.Expire,
                                 d.Serial,
                                 t.IsExpire,
                                 t.MediumUOMFactor,
                                 t.LargeUOMFactor,
                                 t.mtrxAttribute1,
                                 t.mtrxAttribute2,
                                 t.mtrxAttribute3,
                                 t.mtrxParentItem,
                                 Height = (st_Store.MultiplyDimensions != (byte)Dimensions.Multiply) && d.Height.HasValue ? d.Height.Value : 1,
                                 Length = (st_Store.MultiplyDimensions != (byte)Dimensions.Multiply) && d.Length.HasValue ? d.Length.Value : 1,
                                 Width = (st_Store.MultiplyDimensions != (byte)Dimensions.Multiply) && d.Width.HasValue ? d.Width.Value : 1,
                             } into grp
                             select grp).ToList();

                decimal costOfSoldGoods = 0;
                if ((InvoicePostToStore == true && stockIsPeriodic) ||
                    (InvoicePostToStore == true && stockIsPeriodic == false))
                {
                    var stores = data1.Select(x => new { x.Key.StoreId, x.Key.CostMethod }).Distinct().ToList();

                    foreach (var s in stores)
                    {
                        var non_archivedSLInvoiceDetails = (from grp in data1
                                                            where grp.Key.StoreId == s.StoreId
                                                            let dim_qty = grp.Key.Height * grp.Key.Width * grp.Key.Length
                                                            select new
                                                            {
                                                                grp.Key,
                                                                Qty = grp.Count() > 0 ?
                                                                      (from x in grp
                                                                       let factor = x.UOMIndex == 0 ? 1 :
                                                                                    x.UOMIndex == 1 ? FractionToDouble(grp.Key.MediumUOMFactor) :
                                                                                                      FractionToDouble(grp.Key.LargeUOMFactor)
                                                                       select x.Qty * factor * dim_qty).Sum() : 0,

                                                                TotalSellPrice = grp.Select(x => x.TotalSellPrice).Sum(),
                                                                PiecesCount = grp.Count() > 0 ? grp.Select(x => x.PiecesCount).Sum() : 0,
                                                            }).ToList();

                        #region FIFO
                        if (s.CostMethod == (int)CostMethod.FIFO)
                        {
                            var itemQty = (from i in DB.IC_ItemStores
                                           where i.ProcessId != (int)Process.SalesOrder
                                           && i.StoreId == s.StoreId
                                           && i.InsertTime <= today
                                           orderby i.InsertTime ascending
                                           orderby i.Expire ascending
                                           group i by new
                                           {
                                               i.ItemId,
                                               Cost = decimal.Round(i.PurchasePrice / i.Qty, 6),
                                               i.VendorId,
                                               i.Expire,
                                               i.Batch,
                                               i.Serial,
                                               Length = (st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 1 : i.Length,
                                               Width = (st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 1 : i.Width,
                                               Height = (st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 1 : i.Height,
                                           } into grp
                                           let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                                           where Available > 0
                                           select new
                                           {
                                               AvailableQty = Available,
                                               grp.Key.ItemId,
                                               grp.Key.Cost,
                                               grp.Key.VendorId,
                                               grp.Key.Expire,
                                               grp.Key.Batch,
                                               grp.Key.Serial,
                                               grp.Key.Length,
                                               grp.Key.Width,
                                               grp.Key.Height,
                                           }).ToList();

                            var results = (from d in non_archivedSLInvoiceDetails
                                           from i in itemQty.Where(x => x.ItemId == d.Key.ItemId
                                               && (d.Key.Expire == null || x.Expire == d.Key.Expire)
                                               && (d.Key.Batch == null || d.Key.Batch == string.Empty || x.Batch == d.Key.Batch)
                                               && (d.Key.Serial == null || d.Key.Serial == string.Empty || x.Serial == d.Key.Serial)
                                               && d.Key.Height == x.Height
                                               && d.Key.Length == x.Length
                                               && d.Key.Width == x.Width).DefaultIfEmpty()
                                           group i by d into grp
                                           select grp).ToList();


                            foreach (var out_detail in results)
                            {
                                decimal out_qty = out_detail.Key.Qty;
                                decimal PiecesCount = out_detail.Key.PiecesCount;

                                foreach (var in_detail in out_detail)
                                {
                                    if (out_qty <= 0 || in_detail == null)
                                        break;

                                    if (out_qty <= in_detail.AvailableQty)
                                    {
                                        AddItemToStoreDataContext(DB, out_detail.Key.Key.ItemId, s.StoreId, out_qty, in_detail.VendorId, -1, (int)Process.SL_Invoices_Posting,
                                            false, in_detail.Cost * out_qty, today, in_detail.Expire, in_detail.Batch, in_detail.Length, in_detail.Width, in_detail.Height,
                                            out_detail.Key.PiecesCount, out_detail.Key.Key.mtrxParentItem,
                                            out_detail.Key.Key.mtrxAttribute1, out_detail.Key.Key.mtrxAttribute2, out_detail.Key.Key.mtrxAttribute3,
                                            out_detail.Key.TotalSellPrice, null, st_Store.MultiplyDimensions, in_detail.Serial);
                                        costOfSoldGoods += in_detail.Cost * out_qty;
                                    }
                                    else
                                    {
                                        AddItemToStoreDataContext(DB, out_detail.Key.Key.ItemId, s.StoreId, in_detail.AvailableQty, in_detail.VendorId, -1, (int)Process.SL_Invoices_Posting,
                                            false, in_detail.Cost * in_detail.AvailableQty, today, in_detail.Expire, in_detail.Batch, in_detail.Length, in_detail.Width, in_detail.Height,
                                            out_detail.Key.PiecesCount, out_detail.Key.Key.mtrxParentItem,
                                            out_detail.Key.Key.mtrxAttribute1, out_detail.Key.Key.mtrxAttribute2, out_detail.Key.Key.mtrxAttribute3,
                                            in_detail.AvailableQty * out_detail.Key.TotalSellPrice / out_detail.Key.Qty, null, st_Store.MultiplyDimensions,
                                            in_detail.Serial);
                                        costOfSoldGoods += in_detail.Cost * in_detail.AvailableQty;

                                    }
                                    out_qty -= in_detail.AvailableQty;
                                }
                                if (out_qty > 0)
                                {
                                    AddItemToStoreDataContext(DB, out_detail.Key.Key.ItemId, s.StoreId, out_qty, null, -1, (int)Process.SL_Invoices_Posting,
                                            false, 0, today, out_detail.Key.Key.Expire, out_detail.Key.Key.Batch,
                                            out_detail.Key.Key.Length, out_detail.Key.Key.Width, out_detail.Key.Key.Height, out_detail.Key.PiecesCount, out_detail.Key.Key.mtrxParentItem,
                                            out_detail.Key.Key.mtrxAttribute1, out_detail.Key.Key.mtrxAttribute2, out_detail.Key.Key.mtrxAttribute3,
                                            out_qty * out_detail.Key.TotalSellPrice / out_detail.Key.Qty, null, st_Store.MultiplyDimensions,
                                            out_detail.Key.Key.Serial);

                                }
                            }
                            //MessageBox.Show(results.Count() + "\r\n" 
                            //    + DB.GetChangeSet().Inserts.Count());
                        }
                        #endregion

                        #region LIFO
                        if (s.CostMethod == (int)CostMethod.LIFO)
                        {
                            var itemQty = (from i in DB.IC_ItemStores
                                           where i.ProcessId != (int)Process.SalesOrder
                                           && i.StoreId == s.StoreId
                                           && i.InsertTime <= today
                                           orderby i.InsertTime descending
                                           orderby i.Expire descending
                                           group i by new
                                           {
                                               i.ItemId,
                                               Cost = decimal.Round(i.PurchasePrice / i.Qty, 6),
                                               i.VendorId,
                                               i.Expire,
                                               i.Batch,
                                               i.Serial,
                                               Length = (st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 1 : i.Length,
                                               Width = (st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 1 : i.Width,
                                               Height = (st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 1 : i.Height,
                                           } into grp
                                           let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                                           where Available > 0
                                           select new
                                           {
                                               AvailableQty = Available,
                                               grp.Key.ItemId,
                                               grp.Key.Cost,
                                               grp.Key.VendorId,
                                               grp.Key.Expire,
                                               grp.Key.Batch,
                                               grp.Key.Serial,
                                               grp.Key.Length,
                                               grp.Key.Width,
                                               grp.Key.Height,
                                           }).ToList();

                            var results = (from d in non_archivedSLInvoiceDetails
                                           from i in itemQty.Where(x => x.ItemId == d.Key.ItemId
                                               && (d.Key.Expire == null || x.Expire == d.Key.Expire)
                                               && (d.Key.Batch == null || d.Key.Batch == string.Empty || x.Batch == d.Key.Batch)
                                               && (d.Key.Serial == null || d.Key.Serial == string.Empty || x.Serial == d.Key.Serial)
                                               && d.Key.Height == x.Height
                                               && d.Key.Length == x.Length
                                               && d.Key.Width == x.Width).DefaultIfEmpty()
                                           group i by d into grp
                                           select grp).ToList();


                            foreach (var out_detail in results)
                            {
                                decimal out_qty = out_detail.Key.Qty;
                                decimal PiecesCount = out_detail.Key.PiecesCount;

                                foreach (var in_detail in out_detail)
                                {
                                    if (out_qty <= 0 || in_detail == null)
                                        break;

                                    if (out_qty <= in_detail.AvailableQty)
                                    {
                                        AddItemToStoreDataContext(DB, out_detail.Key.Key.ItemId, s.StoreId, out_qty, in_detail.VendorId, -1, (int)Process.SL_Invoices_Posting,
                                            false, in_detail.Cost * out_qty, today, in_detail.Expire, in_detail.Batch, in_detail.Length, in_detail.Width, in_detail.Height,
                                            out_detail.Key.PiecesCount, out_detail.Key.Key.mtrxParentItem,
                                            out_detail.Key.Key.mtrxAttribute1, out_detail.Key.Key.mtrxAttribute2, out_detail.Key.Key.mtrxAttribute3,
                                            out_detail.Key.TotalSellPrice, null, st_Store.MultiplyDimensions, in_detail.Serial);
                                        costOfSoldGoods += in_detail.Cost * out_qty;
                                    }
                                    else
                                    {
                                        AddItemToStoreDataContext(DB, out_detail.Key.Key.ItemId, s.StoreId, in_detail.AvailableQty, in_detail.VendorId, -1, (int)Process.SL_Invoices_Posting,
                                            false, in_detail.Cost * in_detail.AvailableQty, today, in_detail.Expire, in_detail.Batch, in_detail.Length, in_detail.Width, in_detail.Height,
                                            out_detail.Key.PiecesCount, out_detail.Key.Key.mtrxParentItem,
                                            out_detail.Key.Key.mtrxAttribute1, out_detail.Key.Key.mtrxAttribute2, out_detail.Key.Key.mtrxAttribute3,
                                            in_detail.AvailableQty * out_detail.Key.TotalSellPrice / out_detail.Key.Qty,
                                            null, st_Store.MultiplyDimensions, in_detail.Serial);
                                        costOfSoldGoods += in_detail.Cost * in_detail.AvailableQty;
                                    }
                                    out_qty -= in_detail.AvailableQty;
                                }
                                if (out_qty > 0)
                                {
                                    AddItemToStoreDataContext(DB, out_detail.Key.Key.ItemId, s.StoreId, out_qty, null, -1, (int)Process.SL_Invoices_Posting,
                                            false, 0, today, out_detail.Key.Key.Expire, out_detail.Key.Key.Batch,
                                            out_detail.Key.Key.Length, out_detail.Key.Key.Width, out_detail.Key.Key.Height, out_detail.Key.PiecesCount, out_detail.Key.Key.mtrxParentItem,
                                            out_detail.Key.Key.mtrxAttribute1, out_detail.Key.Key.mtrxAttribute2, out_detail.Key.Key.mtrxAttribute3,
                                            out_qty * out_detail.Key.TotalSellPrice / out_detail.Key.Qty, null,
                                            st_Store.MultiplyDimensions, out_detail.Key.Key.Serial);
                                }
                            }
                            //MessageBox.Show(results.Count() + "\r\n"
                            //    + DB.GetChangeSet().Inserts.Count());
                        }
                        #endregion

                        #region Average
                        if (s.CostMethod == (int)CostMethod.AVERAGE)
                        {
                            var itemQty = (from i in DB.IC_ItemStores
                                           where i.ProcessId != (int)Process.SalesOrder
                                           && i.StoreId == s.StoreId
                                           && i.InsertTime <= today
                                           group i by new
                                           {
                                               i.ItemId,
                                               Length = (st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 1 : i.Length,
                                               Width = (st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 1 : i.Width,
                                               Height = (st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 1 : i.Height,
                                           } into grp
                                           let Total_Qty = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                                           let Total_PPrice = grp.Sum(x => x.IsInTrns ? x.PurchasePrice : x.PurchasePrice * -1)
                                           where Total_Qty > 0 && Total_PPrice > 0
                                           select new
                                           {
                                               grp.Key.ItemId,
                                               Cost = Total_PPrice / Total_Qty,
                                               grp.Key.Length,
                                               grp.Key.Width,
                                               grp.Key.Height,
                                           }).ToList();

                            var results = (from d in non_archivedSLInvoiceDetails
                                           from i in itemQty.Where(x => x.ItemId == d.Key.ItemId
                                               && d.Key.Height == x.Height
                                               && d.Key.Length == x.Length
                                               && d.Key.Width == x.Width).DefaultIfEmpty()
                                           group i by d into grp
                                           select grp).ToList();


                            foreach (var out_detail in results)
                            {
                                decimal total_price = 0;
                                if (out_detail.Count() > 0)
                                    total_price = out_detail.FirstOrDefault().Cost * out_detail.Key.Qty;

                                AddItemToStoreDataContext(DB, out_detail.Key.Key.ItemId, s.StoreId, out_detail.Key.Qty, null, -1, (int)Process.SL_Invoices_Posting,
                                        false, total_price, today, out_detail.Key.Key.Expire, out_detail.Key.Key.Batch,
                                        out_detail.Key.Key.Length, out_detail.Key.Key.Width, out_detail.Key.Key.Height, out_detail.Key.PiecesCount, out_detail.Key.Key.mtrxParentItem,
                                        out_detail.Key.Key.mtrxAttribute1, out_detail.Key.Key.mtrxAttribute2, out_detail.Key.Key.mtrxAttribute3,
                                        out_detail.Key.TotalSellPrice, null, st_Store.MultiplyDimensions, out_detail.Key.Key.Serial);
                                costOfSoldGoods += total_price;

                            }
                            //MessageBox.Show(results.Count() + "\r\n"
                            //    + DB.GetChangeSet().Inserts.Count());
                        }
                        #endregion
                    }
                }

                var non_archivedSLInvoices = (from d in DB.SL_Invoices
                                              where UserId == null ? true : d.UserId == UserId
                                              where Sl_InvoiceId == null ? true : d.SL_InvoiceId == Sl_InvoiceId
                                              where d.IsArchived == false
                                              group d by new
                                              {
                                                  d.StoreId,
                                                  d.CustomerId,
                                                  d.DrawerAccountId,
                                                  d.PayAccountId2,
                                                  d.DueDate,
                                                  d.CrncId,
                                                  d.CrncRate,
                                                  d.CostCenterId
                                              } into grp
                                              join s in DB.IC_Stores on grp.Key.StoreId equals s.StoreId
                                              join cst in DB.SL_Customers on grp.Key.CustomerId equals cst.CustomerId
                                              select new
                                              {
                                                  grp.Key,
                                                  CustomerAccountId = cst.AccountId,
                                                  s.SalesDiscountAcc,
                                                  s.PurchaseAccount,
                                                  s.CostOfSoldGoodsAcc,
                                                  s.SellAccount,
                                                  DiscountValue = grp.Count() > 0 ? grp.Select(x => x.DiscountValue).Sum() : 0,
                                                  Expenses = grp.Count() > 0 ? grp.Select(x => x.Expenses).Sum() : 0,
                                                  TaxValue = grp.Count() > 0 ? grp.Select(x => x.TaxValue).Sum() : 0,
                                                  DeductTaxValue = grp.Count() > 0 ? grp.Select(x => x.DeductTaxValue).Sum() : 0,
                                                  AddTaxValue = grp.Count() > 0 ? grp.Select(x => x.AddTaxValue).Sum() : 0,
                                                  Paid = grp.Count() > 0 ? grp.Select(x => x.Paid).Sum() : 0,
                                                  Net = grp.Count() > 0 ? grp.Select(x => x.Net).Sum() : 0,
                                                  PayAcc2_Paid = grp.Where(x => x.PayAcc2_Paid.HasValue && x.PayAcc2_Paid.Value > 0).Count() > 0 ?
                                                     grp.Where(x => x.PayAcc2_Paid.HasValue && x.PayAcc2_Paid.Value > 0)
                                                  .Select(x => x.PayAcc2_Paid.Value).Sum() : 0
                                              }).ToList();


                foreach (var d in non_archivedSLInvoices)
                {
                    #region Save_Jornal
                    DAL.ACC_Journal jornal = new DAL.ACC_Journal();
                    jornal.InsertDate = today;
                    jornal.InsertUser = Shared.UserId;
                    jornal.JCode = (from j in DB.ACC_Journals
                                    select j.JCode).ToList().DefaultIfEmpty(0).Max() + 1;

                    jornal.JNotes = (Shared.IsEnglish ? ResEn.txtSLPosting : ResAr.txtSLPosting) + " " + today.ToShortDateString()
                        + "\r\n" + (Shared.IsEnglish ? ResEn.UserName : ResAr.UserName) + " " + Shared.UserName;
                    jornal.ProcessId = (int)Process.SL_Invoices_Posting;
                    jornal.SourceId = -1; // posting                    
                    jornal.IsPosted = !OfflinePostToGL;
                    jornal.StoreId = d.Key.StoreId;
                    jornal.CrncId = d.Key.CrncId;
                    jornal.CrncRate = d.Key.CrncRate;
                    DB.ACC_Journals.InsertOnSubmit(jornal);
                    DB.SubmitChanges();
                    #endregion

                    decimal total_Sells = d.Net - d.TaxValue + d.DiscountValue - d.Expenses + d.DeductTaxValue - d.AddTaxValue;

                    #region Sell
                    /*قيد البيع*/
                    /*من حســاب كل من*/
                    /* حساب العميل*/
                    DAL.ACC_JournalDetail jornal_Detail_2 = new DAL.ACC_JournalDetail();
                    jornal_Detail_2.JournalId = jornal.JournalId;
                    jornal_Detail_2.AccountId = d.CustomerAccountId.Value;      //حساب العميل
                    jornal_Detail_2.Credit = 0;
                    jornal_Detail_2.Debit = total_Sells + d.Expenses + d.TaxValue - d.DeductTaxValue + d.AddTaxValue;
                    jornal_Detail_2.Notes = jornal.JNotes;
                    jornal_Detail_2.DueDate = d.Key.DueDate;
                    jornal_Detail_2.CrncId = d.Key.CrncId;
                    jornal_Detail_2.CrncRate = d.Key.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_2);

                    /*من حساب ضريبة الخصم  */
                    #region Deduct_Tax_Value
                    if (d.DeductTaxValue > 0)
                    {
                        DAL.ACC_JournalDetail jornal_Detail_deduct_Tax = new DAL.ACC_JournalDetail();
                        jornal_Detail_deduct_Tax.JournalId = jornal.JournalId;
                        jornal_Detail_deduct_Tax.AccountId = st_Store.SalesDeductTaxAccount.Value;
                        jornal_Detail_deduct_Tax.Credit = 0;
                        jornal_Detail_deduct_Tax.Debit = d.DeductTaxValue;
                        jornal_Detail_deduct_Tax.Notes = jornal.JNotes + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                        jornal_Detail_deduct_Tax.CrncId = d.Key.CrncId;
                        jornal_Detail_deduct_Tax.CrncRate = d.Key.CrncRate;
                        DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_deduct_Tax);
                    }
                    #endregion

                    /*الى حساب المبيعات*/
                    DAL.ACC_JournalDetail jornal_Detail_1 = new DAL.ACC_JournalDetail();
                    jornal_Detail_1.JournalId = jornal.JournalId;
                    jornal_Detail_1.AccountId = d.SellAccount;      // حساب مبيعات المخزن
                    jornal_Detail_1.CostCenter = d.Key.CostCenterId;      // تحميل مركز تكلفة المخزن
                    jornal_Detail_1.Credit = total_Sells + d.Expenses;           //اجمالي مبيعات + مصاريف النقل 
                    jornal_Detail_1.Debit = 0;
                    jornal_Detail_1.Notes = jornal.JNotes;
                    jornal_Detail_1.CrncId = d.Key.CrncId;
                    jornal_Detail_1.CrncRate = d.Key.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_1);

                    /*الى حساب ضريبة المبيعات*/
                    if (d.TaxValue > 0)
                    {
                        DAL.ACC_JournalDetail jornal_Detail_tax = new DAL.ACC_JournalDetail();
                        jornal_Detail_tax.JournalId = jornal.JournalId;
                        jornal_Detail_tax.AccountId = st_Store.TaxAcc.Value;//حساب ضريبة المبيعات 
                        jornal_Detail_tax.Credit = d.TaxValue;
                        jornal_Detail_tax.Debit = 0;
                        jornal_Detail_tax.Notes = jornal.JNotes + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                        jornal_Detail_tax.CrncId = d.Key.CrncId;
                        jornal_Detail_tax.CrncRate = d.Key.CrncRate;
                        DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_tax);
                    }

                    /*الى حساب ضريبة الاضافة*/
                    if (d.AddTaxValue > 0)
                    {
                        DAL.ACC_JournalDetail jornal_Detail_addtax = new DAL.ACC_JournalDetail();
                        jornal_Detail_addtax.JournalId = jornal.JournalId;
                        jornal_Detail_addtax.AccountId = st_Store.SalesAddTaxAccount.Value;//حساب ضريبة المبيعات 
                        jornal_Detail_addtax.Credit = d.AddTaxValue;
                        jornal_Detail_addtax.Debit = 0;
                        jornal_Detail_addtax.Notes = jornal.JNotes + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                        jornal_Detail_addtax.CrncId = d.Key.CrncId;
                        jornal_Detail_addtax.CrncRate = d.Key.CrncRate;
                        DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_addtax);
                    }


                    #endregion

                    #region Paid
                    /*قيد السداد*/
                    if (d.Paid > 0 || (d.Key.PayAccountId2.HasValue && d.PayAcc2_Paid > 0))
                    {
                        /* من حساب الخزينة*/
                        if (d.Paid > 0)
                        {
                            DAL.ACC_JournalDetail jornal_Detail_4 = new DAL.ACC_JournalDetail();
                            jornal_Detail_4.JournalId = jornal.JournalId;
                            jornal_Detail_4.AccountId = d.Key.DrawerAccountId.Value;                 // حساب الخزينة
                            jornal_Detail_4.Credit = 0;
                            jornal_Detail_4.Debit = d.Paid;
                            jornal_Detail_4.Notes = jornal.JNotes + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                            jornal_Detail_4.CrncId = d.Key.CrncId;
                            jornal_Detail_4.CrncRate = d.Key.CrncRate;
                            DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_4);
                        }
                        if (d.Key.PayAccountId2.HasValue && d.PayAcc2_Paid > 0)
                        {
                            DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                            jornal_Detail_5.JournalId = jornal.JournalId;
                            jornal_Detail_5.AccountId = d.Key.PayAccountId2.Value;                 // حساب الخزينة2 
                            jornal_Detail_5.Credit = 0;
                            jornal_Detail_5.Debit = d.PayAcc2_Paid;
                            jornal_Detail_5.Notes = jornal.JNotes + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                            jornal_Detail_5.CrncId = d.Key.CrncId;
                            jornal_Detail_5.CrncRate = d.Key.CrncRate;
                            DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);
                        }

                        /*1 الى حساب العميل*/
                        DAL.ACC_JournalDetail jornal_Detail_3 = new DAL.ACC_JournalDetail();
                        jornal_Detail_3.JournalId = jornal.JournalId;
                        jornal_Detail_3.AccountId = d.CustomerAccountId.Value;        // حساب عميل 
                        jornal_Detail_3.Credit = d.Paid + d.PayAcc2_Paid;
                        jornal_Detail_3.Debit = 0;
                        jornal_Detail_3.Notes = jornal.JNotes + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                        jornal_Detail_3.CrncId = d.Key.CrncId;
                        jornal_Detail_3.CrncRate = d.Key.CrncRate;
                        DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_3);
                    }
                    #endregion

                    #region Discount
                    /*قيد الخصم*/
                    /*1 الى حساب العميل*/
                    if (d.DiscountValue > 0)
                    {
                        /* من حساب الخصم المسموح به*/
                        DAL.ACC_JournalDetail jornal_Detail_6 = new DAL.ACC_JournalDetail();
                        jornal_Detail_6.JournalId = jornal.JournalId;
                        jornal_Detail_6.AccountId = d.SalesDiscountAcc.Value;//  حساب الخصم النقدي المسموح به
                        jornal_Detail_6.Credit = 0;
                        jornal_Detail_6.Debit = d.DiscountValue;
                        jornal_Detail_6.Notes = jornal.JNotes + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                        jornal_Detail_6.CrncId = d.Key.CrncId;
                        jornal_Detail_6.CrncRate = d.Key.CrncRate;
                        DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_6);

                        DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                        jornal_Detail_5.JournalId = jornal.JournalId;
                        jornal_Detail_5.AccountId = d.CustomerAccountId.Value;        //حساب عميل
                        jornal_Detail_5.Credit = Convert.ToDecimal(d.DiscountValue);
                        jornal_Detail_5.Debit = 0;
                        jornal_Detail_5.Notes = jornal.JNotes + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                        jornal_Detail_5.CrncId = d.Key.CrncId;
                        jornal_Detail_5.CrncRate = d.Key.CrncRate;
                        DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);
                    }
                    #endregion

                    #region CostOfSoldGoods
                    if (stockIsPeriodic == false)
                    {
                        DAL.ACC_JournalDetail jdCost1 = new DAL.ACC_JournalDetail();
                        jdCost1.JournalId = jornal.JournalId;
                        jdCost1.AccountId = d.CostOfSoldGoodsAcc.Value;//حساب تكلفة البضاعة المباعة
                        jdCost1.Credit = 0;
                        jdCost1.Debit = costOfSoldGoods;
                        jdCost1.Notes = jornal.JNotes;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                        jdCost1.CrncId = d.Key.CrncId;
                        jdCost1.CrncRate = d.Key.CrncRate;
                        jdCost1.CostCenter = d.Key.CostCenterId;
                        DB.ACC_JournalDetails.InsertOnSubmit(jdCost1);

                        DAL.ACC_JournalDetail jdCost2 = new DAL.ACC_JournalDetail();
                        jdCost2.JournalId = jornal.JournalId;
                        jdCost2.AccountId = d.PurchaseAccount;//المخزون
                        jdCost2.Credit = costOfSoldGoods;
                        jdCost2.Debit = 0;
                        jdCost2.Notes = jornal.JNotes;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                        jdCost2.CrncId = d.Key.CrncId;
                        jdCost2.CrncRate = d.Key.CrncRate;
                        DB.ACC_JournalDetails.InsertOnSubmit(jdCost2);
                    }
                    #endregion

                    DB.SubmitChanges();
                }

                var sl_invoices = from sl in DB.SL_Invoices
                                  where sl.IsArchived == false
                                  where UserId == null ? true : sl.UserId == UserId
                                  where Sl_InvoiceId == null ? true : sl.SL_InvoiceId == Sl_InvoiceId
                                  select sl;

                foreach (var s in sl_invoices)
                {
                    s.Is_OutTrans = true;
                    s.IsArchived = true;
                }
                DB.SubmitChanges();
                DB.Transaction.Commit();
                SavedSuccessfuly = true;
            }
            catch
            {
                DB.Transaction.Rollback();
                SavedSuccessfuly = false;
            }
            finally
            {
                DB.Connection.Close();

            }
            return SavedSuccessfuly;
        }

        /// <summary>
        /// Get Item Price from Item Info Qty Equation
        /// </summary>
        /// <param name="itemId"></param>
        /// <param name="Qty"></param>
        /// <param name="uomIndex"></param>
        /// <param name="mediumFactor"></param>
        /// <param name="largeFactor"></param>
        /// <returns></returns>
        public static decimal Get_ItemPricesPerQty(int itemId, decimal Qty, byte uomIndex, decimal mediumFactor, decimal largeFactor)
        {
            decimal factor = 1;
            if (uomIndex == 0)
                factor = 1;
            else if (uomIndex == 1)
                factor = mediumFactor;
            else if (uomIndex == 2)
                factor = largeFactor;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            return (from x in DB.IC_ItemPricesPerQties
                    where x.ItemId == itemId
                    && (Qty * factor) >= x.QtyFrom
                    && (Qty * factor) <= x.QtyTo
                    select x.SellPrice).FirstOrDefault() * factor;
        }

        public static string GetNextNumberInString(string Number, params int[] month)
        {
            if (month.Count() > 0 && (Number == string.Empty || Number == null))
                return month[0] + "-1";
            else if (Number == string.Empty || Number == null)
                return "1";

            string NumPart = "";
            foreach (char c in Number)
            {
                if (char.IsDigit(c))
                    NumPart += c;
                else
                    NumPart = "";
            }
            if (month.Count() > 0 && NumPart == string.Empty)
                return month[0] + "-" + Number + "1";

            else if (NumPart == string.Empty)
                return Number + "1";

            string new_numpart = NumPart.Insert(0, "1");
            new_numpart = (Convert.ToInt64(new_numpart) + 1).ToString();
            if (new_numpart[0] == '1')
                new_numpart = new_numpart.Remove(0, 1);
            else
            {
                new_numpart = new_numpart.Remove(0, 1);
                new_numpart = new_numpart.Insert(0, "1");
            }
            int index = Number.LastIndexOf(NumPart);
            Number = Number.Remove(index);
            Number = Number.Insert(index, new_numpart);
            return Number;
        }


        public static decimal GetAveragePriceForSalesReturn(int uomIndex, decimal MediumUOMFactor, decimal LargeUOMFactor, int ItemId,
                                                            int StoreId, DateTime InsertDate, decimal PurchasePrice)
        {
            decimal factor = 1;
            if (uomIndex == 1)
                factor = MediumUOMFactor;
            else if (uomIndex == 1)
                factor = LargeUOMFactor;

            ERPDataContext DB = new ERPDataContext();

            decimal item_PurchasePrice = DB.IC_ItemStores.Where(z => (z.ProcessId == (int)Process.SellInvoice || z.ProcessId == (int)Process.SL_Invoices_Posting)
                && z.ItemId == ItemId && z.StoreId == StoreId && z.IsInTrns == false
                && z.InsertTime <= InsertDate).Select(z => z.PurchasePrice).ToList().DefaultIfEmpty(0).Sum();
            decimal item_Qty = DB.IC_ItemStores.Where(z => (z.ProcessId == (int)Process.SellInvoice || z.ProcessId == (int)Process.SL_Invoices_Posting)
                && z.ItemId == ItemId && z.StoreId == StoreId && z.IsInTrns == false
                && z.InsertTime <= InsertDate).Select(z => z.Qty).ToList().DefaultIfEmpty(0).Sum();

            if (item_PurchasePrice > 0 && item_Qty > 0)
                return ((item_PurchasePrice / item_Qty) * factor);

            else
                return PurchasePrice;

        }

        public static string GetNextItemBatch(ERPDataContext DB, int ItemId)
        {
            var batch = (from x in DB.IC_ItemStores
                         where x.ItemId == ItemId
                         && x.Batch != null && x.Batch != string.Empty
                         orderby x.Batch descending
                         select x.Batch).FirstOrDefault();

            return GetNextNumberInString(batch);
        }

        public static decimal GetPriceLevelSellPrice(IC_PriceLevel CustomerPriceLevel, IC_Item item, int uomIndex)
        {
            #region Price_Levels
            decimal? uom_price = null;
            if (CustomerPriceLevel != null)
            {
                if (CustomerPriceLevel.IsRatio == false)
                {
                    if (uomIndex == 0)
                        uom_price = CustomerPriceLevel.IC_PriceLevelDetails.Where(x => x.ItemId == item.ItemId).Select(x => x.smallUOMPrice).FirstOrDefault();
                    else if (uomIndex == 1)
                        uom_price = CustomerPriceLevel.IC_PriceLevelDetails.Where(x => x.ItemId == item.ItemId).Select(x => x.MediumUOMPrice).FirstOrDefault();
                    else if (uomIndex == 2)
                        uom_price = CustomerPriceLevel.IC_PriceLevelDetails.Where(x => x.ItemId == item.ItemId).Select(x => x.LargeUOMPrice).FirstOrDefault();
                }
                else if (CustomerPriceLevel.IsRatio == true && CustomerPriceLevel.IsRatioIncrease == true)
                {
                    if (uomIndex == 0)
                        uom_price = item.SmallUOMPrice + item.SmallUOMPrice * (CustomerPriceLevel.Ratio / 100);
                    else if (uomIndex == 1)
                        uom_price = item.MediumUOMPrice + item.MediumUOMPrice * (CustomerPriceLevel.Ratio / 100);
                    else if (uomIndex == 2)
                        uom_price = item.LargeUOMPrice + item.LargeUOMPrice * (CustomerPriceLevel.Ratio / 100);
                }
                else if (CustomerPriceLevel.IsRatio == true && CustomerPriceLevel.IsRatioIncrease == false)
                {
                    if (uomIndex == 0)
                        uom_price = item.SmallUOMPrice - item.SmallUOMPrice * (CustomerPriceLevel.Ratio / 100);
                    else if (uomIndex == 1)
                        uom_price = item.MediumUOMPrice - item.MediumUOMPrice * (CustomerPriceLevel.Ratio / 100);
                    else if (uomIndex == 2)
                        uom_price = item.LargeUOMPrice - item.LargeUOMPrice * (CustomerPriceLevel.Ratio / 100);
                }
            }
            if (uom_price == null)
            {
                if (uomIndex == 0)
                    uom_price = item.SmallUOMPrice;
                else if (uomIndex == 1)
                    uom_price = item.MediumUOMPrice;
                else if (uomIndex == 2)
                    uom_price = item.LargeUOMPrice;
            }

            if (uom_price == null)
                uom_price = 0;

            #endregion

            return uom_price.Value;
        }

        public static decimal GetPriceLevelPurchasePrice(IC_PrPriceLevel VendorPriceLevel, IC_Item item, int uomIndex)
        {
            #region Price_Levels
            decimal? uom_price = null;
            if (VendorPriceLevel != null)
            {
                if (VendorPriceLevel.IsRatio == false)
                {
                    decimal? price = VendorPriceLevel.IC_PrPriceLevelDetails.Where(x => x.ItemId == item.ItemId).Select(x => x.smallUOMPrice).FirstOrDefault();
                    if (uomIndex == 0)
                        uom_price = price;
                    else if (uomIndex == 1)
                        uom_price = price * MyHelper.FractionToDouble(item.MediumUOMFactor);
                    else if (uomIndex == 2)
                        uom_price = price * MyHelper.FractionToDouble(item.LargeUOMFactor);
                }
                else if (VendorPriceLevel.IsRatio == true && VendorPriceLevel.IsRatioIncrease == true)
                {
                    decimal? price = null;
                    if (uomIndex == 0)
                        price = item.PurchasePrice;
                    else if (uomIndex == 1)
                        price = item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor);
                    else if (uomIndex == 2)
                        price = item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor);

                    uom_price = price + price * (VendorPriceLevel.Ratio / 100);
                }
                else if (VendorPriceLevel.IsRatio == true && VendorPriceLevel.IsRatioIncrease == false)
                {
                    decimal? price = null;
                    if (uomIndex == 0)
                        price = item.PurchasePrice;
                    else if (uomIndex == 1)
                        price = item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor);
                    else if (uomIndex == 2)
                        price = item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor);

                    uom_price = price - price * (VendorPriceLevel.Ratio / 100);
                }
            }
            if (!Shared.LibraAvailabe && uom_price == null)
            {
                if (uomIndex == 0)
                    uom_price = item.PurchasePrice;
                else if (uomIndex == 1)
                    uom_price = item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor);
                else if (uomIndex == 2)
                    uom_price = item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor);
            }
            else
            {
                uom_price = item.PurchasePrice;
            }

            if (uom_price == null)
                uom_price = 0;

            #endregion

            return uom_price.Value;
        }

        /// <summary>
        /// used to get batch of item in sales inv when printed
        /// </summary>
        /// <param name="SL_InvoiceDetailId"></param>
        /// <returns></returns>
        public static string GetBatchOfSLInvDetailId(int SL_InvoiceDetailId)
        {
            return new ERPDataContext().IC_ItemStores.Where(x => x.ProcessId == (int)Process.SellInvoice && x.SourceId == SL_InvoiceDetailId).
                Select(x => x.Batch).FirstOrDefault();
        }

        public static List<RS_ProjectGroup> GetChildRS_ProjectsGroups()
        {
            ERPDataContext DB = new ERPDataContext();
            List<RS_ProjectGroup> groups =
                DB.RS_ProjectGroups.Where(a =>
                   DB.RS_ProjectGroups.Where(b => b.ParentGroupId == a.ProjectGroupId).Count() < 1).
                   OrderBy(b => b.CcNumber).ToList();

            groups.Insert(0, new RS_ProjectGroup
            {
                ProjectGroupId = 0,
                ProjectGroupCode = "",
                PGNameAr = "",
                PGNameEn = "",
                Desc = "",
                CCId = 0,
                CcNumber = ""
            });

            return groups;
        }


        public static bool Validate_detailsQty(byte dim, DateTime inserdate, int storeid, DataTable dt, out string msg)
        {
            msg = "";
            ERPDataContext DB = new ERPDataContext();

            // if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)//CANNOT FIND OBJECT with IsStoreOnEachSellRecord value= false
            //if(storeid==0)
            if (DB.ST_Stores.Where(s => s.IsStoreOnEachSellRecord == true).Count() > 0)
            {
                var items1 = (from DataRow dr in dt.Rows
                              where dr.RowState != DataRowState.Deleted
                              && Convert.ToInt32(dr["ItemType"]) != (int)ItemType.Subtotal
                              && Convert.ToInt32(dr["ItemType"]) != (int)ItemType.Service
                              let MUomFactor = FractionToDouble(dr["MediumUOMFactor"].ToString())
                              let LUomFactor = FractionToDouble(dr["LargeUOMFactor"].ToString())
                              let UOMIndex = Convert.ToByte(dr["UomIndex"])
                              let length = Convert.ToDecimal(dr["Length"])
                              let Width = Convert.ToDecimal(dr["Width"])
                              let Height = Convert.ToDecimal(dr["Height"])
                              let Factor = UOMIndex == 0 ? 1 : UOMIndex == 1 ? MUomFactor : LUomFactor
                              let dim_factor = dim == (byte)Dimensions.Distinguish ? 1 : length * Width * Height
                              select new
                              {
                                  ItemCode1 = dr["ItemCode1"] + "",
                                  ItemCode2 = dr["ItemCode2"] + "",
                                  ItemId = Convert.ToInt32(dr["ItemId"]),
                                  Length = dim == (byte)Dimensions.Multiply ? 0 : length,
                                  Width = dim == (byte)Dimensions.Multiply ? 0 : Width,
                                  Height = dim == (byte)Dimensions.Multiply ? 0 : Height,
                                  Qty = Convert.ToDecimal(dr["Qty"]) * Factor * dim_factor,
                                  //StoreId = Convert.ToInt32(dr["StoreId"])//To record the store in case of store on each record Alaa 10-07-2017
                                  StoreId = dt.Columns.Contains("StoreId") == false ? storeid : Convert.ToInt32(dr["StoreId"])//To record the store in case of store on each record Mohammad 04-02-2019
                              }).ToList();

                var items = (from d in items1
                             group d by new { d.ItemId, d.ItemCode1, d.ItemCode2, d.Length, d.Width, d.Height, d.StoreId } into grp
                             select new
                             {
                                 grp.Key.ItemId,
                                 grp.Key.ItemCode1,
                                 grp.Key.ItemCode2,
                                 grp.Key.Length,
                                 grp.Key.Width,
                                 grp.Key.Height,
                                 grp.Key.StoreId,//To record the store in case of store on each record Alaa 10-07-2017
                                 Qty = grp.Sum(x => x.Qty),
                             }).ToList();

                if (items.Count() == 0)
                {
                    msg = "";
                    return true;
                }
                //   ERPDataContext DB = new ERPDataContext();
                // var data = new IQueryable<ST_Store>();

                //if (DB.ST_Stores.FirstOrDefault(s => s.IsStoreOnEachSellRecord == false) == null)//CANNOT FIND OBJECT with IsStoreOnEachSellRecord value= false
                //{
                var data = (from i in items
                            join d in DB.IC_ItemStores on i.ItemId equals d.ItemId
                            where d.StoreId == i.StoreId && d.InsertTime < inserdate
                            group d by new { d.ItemId, d.Length, d.Width, d.Height } into grp
                            let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                            where Available > 0
                            select new
                            {
                                grp.Key.ItemId,
                                grp.Key.Length,
                                grp.Key.Width,
                                grp.Key.Height,

                                CurrentQty = Available,
                            }).ToList();
                foreach (var d in items)
                {
                    decimal currentqty = data.Where(x => x.ItemId == d.ItemId && x.Length == d.Length && x.Width == d.Width && x.Height == d.Height)
                        .Select(x => x.CurrentQty).FirstOrDefault();
                    if (d.Qty > currentqty)
                    {
                        string itm_txt = String.Format("{0,-10} | {1,-10} | {2, -10} | {3,-10}", d.ItemCode1, d.ItemCode2, d.Qty.ToString("n2"), currentqty.ToString("n2"));
                        msg += itm_txt + "\r\n";
                    }
                }
                foreach (var d in items)
                {
                    decimal currentqty = data.Where(x => x.ItemId == d.ItemId && x.Length == d.Length && x.Width == d.Width && x.Height == d.Height)
                        .Select(x => x.CurrentQty).FirstOrDefault();
                    if (d.Qty > currentqty)
                    {
                        string itm_txt = String.Format("{0,-10} | {1,-10} | {2, -10} | {3,-10}", d.ItemCode1, d.ItemCode2, d.Qty.ToString("n2"), currentqty.ToString("n2"));
                        msg += itm_txt + "\r\n";
                    }
                }
            }
            else
            {
                var items1 = (from DataRow dr in dt.Rows
                              where dr.RowState != DataRowState.Deleted
                              && Convert.ToInt32(dr["ItemType"]) != (int)ItemType.Subtotal
                              && Convert.ToInt32(dr["ItemType"]) != (int)ItemType.Service
                              where (dr["IsOffer"] == DBNull.Value || dr["IsOffer"] == null) ? true : (Convert.ToBoolean(dr["IsOffer"]) != true)

                              let MUomFactor = FractionToDouble(dr["MediumUOMFactor"].ToString())
                              let LUomFactor = FractionToDouble(dr["LargeUOMFactor"].ToString())
                              let UOMIndex = Convert.ToByte(dr["UomIndex"])
                              let length = Convert.ToDecimal(dr["Length"])
                              let Width = Convert.ToDecimal(dr["Width"])
                              let Height = Convert.ToDecimal(dr["Height"])
                              let Factor = UOMIndex == 0 ? 1 : UOMIndex == 1 ? MUomFactor : LUomFactor
                              let dim_factor = dim == (byte)Dimensions.Distinguish ? 1 : length * Width * Height
                              select new
                              {
                                  ItemCode1 = dr["ItemCode1"] + "",
                                  ItemCode2 = dr["ItemCode2"] + "",
                                  ItemId = Convert.ToInt32(dr["ItemId"]),
                                  Length = dim == (byte)Dimensions.Multiply ? 0 : length,
                                  Width = dim == (byte)Dimensions.Multiply ? 0 : Width,
                                  Height = dim == (byte)Dimensions.Multiply ? 0 : Height,
                                  Qty = Convert.ToDecimal(dr["Qty"]) * Factor * dim_factor,

                                  //   StoreId = Convert.ToInt32(dr["StoreId"])//To record the store in case of store on each record Alaa 10-07-2017
                              }).ToList();

                var items = (from d in items1
                             group d by new { d.ItemId, d.ItemCode1, d.ItemCode2, d.Length, d.Width, d.Height } into grp
                             select new
                             {
                                 grp.Key.ItemId,
                                 grp.Key.ItemCode1,
                                 grp.Key.ItemCode2,
                                 grp.Key.Length,
                                 grp.Key.Width,
                                 grp.Key.Height,
                                 // grp.Key.StoreId,//To record the store in case of store on each record Alaa 10-07-2017
                                 Qty = grp.Sum(x => x.Qty),
                             }).ToList();

                if (items.Count() == 0)
                {
                    msg = "";
                    return true;
                }
                var data = (from i in items
                            join d in DB.IC_ItemStores on i.ItemId equals d.ItemId
                            where d.StoreId == storeid && d.InsertTime < inserdate
                            group d by new { d.ItemId, d.Length, d.Width, d.Height } into grp
                            let Available = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1)
                            where Available > 0
                            select new
                            {
                                grp.Key.ItemId,
                                grp.Key.Length,
                                grp.Key.Width,
                                grp.Key.Height,

                                CurrentQty = Available,
                            });
                foreach (var d in items)
                {
                    decimal currentqty = data.Where(x => x.ItemId == d.ItemId && x.Length == d.Length && x.Width == d.Width && x.Height == d.Height)
                        .Select(x => x.CurrentQty).FirstOrDefault();
                    if (d.Qty > currentqty)
                    {
                        string itm_txt = String.Format("{0,-10} | {1,-10} | {2, -10} | {3,-10}", d.ItemCode1, d.ItemCode2, d.Qty.ToString("n2"), currentqty.ToString("n2"));
                        msg += itm_txt + "\r\n";
                    }
                }

            }

            //foreach (var d in items)
            //{
            //    decimal currentqty = data.Where(x => x.ItemId == d.ItemId && x.Length == d.Length && x.Width == d.Width && x.Height == d.Height)
            //        .Select(x => x.CurrentQty).FirstOrDefault();
            //    if (d.Qty > currentqty)
            //    {
            //        string itm_txt = String.Format("{0,-10} | {1,-10} | {2, -10} | {3,-10}", d.ItemCode1, d.ItemCode2, d.Qty.ToString("n2"), currentqty.ToString("n2"));
            //        msg += itm_txt + "\r\n";
            //    }
            //}

            if (msg == string.Empty)
                return true;
            else
            {
                string itm_txt = String.Format("{0,-10} | {1,-10} | {2, -10} | {3,-10}", "Code 1", "Code 2", "Qty", "Curr. Qty");
                itm_txt += "\r\n";
                itm_txt += "".PadRight(48, '-');
                itm_txt += "\r\n";

                msg = itm_txt + msg;
                return false;
            }
        }

        public static string CatNumGenerated(IC_Category parent, params byte[] iscode2)
        {
            ERPDataContext DB = new ERPDataContext();
            string NewNumber = string.Empty;
            int level = 0;

            //set level
            level = parent == null ? 1 : parent.Level + 1;

            //set number
            //add parent code
            NewNumber = parent == null ? string.Empty : iscode2.Count() > 0 ? parent.Code2 : parent.CatNumber;
            //add zero decimals, count of zero decimals equal to level+1
            for (int x = 0; x < level; x++)
                NewNumber += "0";

            char[] ccNumberArr = NewNumber.ToCharArray();
            //string numberOfNewAccount = numberOfNewAccount = (DB.IC_Categories.Where(a => a.ParentId == parent.CategoryId).Count() + 1).ToString();
            #region new
            var exitsub = DB.IC_Categories.Where(a => a.ParentId == parent.CategoryId).OrderByDescending(a => iscode2.Count() > 0 ? a.Code2 : a.CatNumber).FirstOrDefault();
            string numberOfNewAccount;

            if (exitsub == null)
            {
                numberOfNewAccount = "1";
            }
            else
            {
                var xCat = (DB.IC_Categories.Where(a => a.ParentId == parent.CategoryId).OrderByDescending(a => iscode2.Count() > 0 ? a.Code2 : a.CatNumber).FirstOrDefault());
                var xx = iscode2.Count() > 0 ? xCat.Code2 : xCat.CatNumber;
                var yy = iscode2.Count() > 0 ? xx.Substring(parent.Code2.Length) : xx.Substring(parent.CatNumber.Length);
                //var z = yy.LastIndexOf("0");
                //if (z == -1 || yy.EndsWith("0"))
                //{
                //    numberOfNewAccount = (Convert.ToInt32(yy) + 1).ToString();
                //}
                //else
                //{
                //numberOfNewAccount = (Convert.ToInt32(yy.Substring(z)) + 1).ToString();
                numberOfNewAccount = (Convert.ToInt32(yy) + 1).ToString();

                //}
            }
            #endregion


            //test that level doesn't exceed digits limits
            #region update
            // if (numberOfNewAccount.Length > (level + 1))
            //update 4/9/2017
            if ((DB.IC_Categories.Where(a => a.ParentId == parent.CategoryId).Count() + 1).ToString().Length > (level + 1))
                return "-1";
            #endregion
            int numbIndex = numberOfNewAccount.Length - 1;

            //add new account number to the number
            for (int x = NewNumber.Length; x > NewNumber.Length - numberOfNewAccount.Length; x--)
            {
                ccNumberArr[x - 1] = numberOfNewAccount[numbIndex];
                numbIndex--;
            }

            //get string from array of char
            string lastNumber = string.Empty;
            foreach (char r in ccNumberArr)
                lastNumber += r;

            NewNumber = lastNumber;
            return NewNumber;
        }

        public static string ItemCatNumGenerated(IC_Category parent)
        {
            ERPDataContext DB = new ERPDataContext();
            string NewNumber = string.Empty;
            int level = 0;

            //set level
            level = parent == null ? 1 : parent.Level + 1;

            //set number
            //add parent code
            NewNumber = parent == null ? string.Empty : parent.Code2;
            //add zero decimals, count of zero decimals equal to level+1
            for (int x = 0; x < level; x++)
                NewNumber += "0";

            char[] ccNumberArr = NewNumber.ToCharArray();
            //string numberOfNewAccount = numberOfNewAccount = (DB.IC_Categories.Where(a => a.ParentId == parent.CategoryId).Count() + 1).ToString();
            #region new
            var exitsub = DB.IC_Items.Where(a => a.Category == parent.CategoryId).OrderByDescending(a => a.ItemCode2).FirstOrDefault();
            string numberOfNewAccount;

            if (exitsub == null)
            {
                numberOfNewAccount = "1";
            }
            else
            {
                var xx = (DB.IC_Items.Where(a => a.Category == parent.CategoryId).OrderByDescending(a => a.ItemCode2).FirstOrDefault().ItemCode2);
                var yy = xx == null ? "0" : xx.Substring(parent.Code2.Length);
                //var z = yy.LastIndexOf("0");
                //if (z == -1 || yy.EndsWith("0"))
                //{
                //    numberOfNewAccount = (Convert.ToInt32(yy) + 1).ToString();
                //}
                //else
                //{
                //numberOfNewAccount = (Convert.ToInt32(yy.Substring(z)) + 1).ToString();
                numberOfNewAccount = (Int32.Parse(yy) + 1).ToString();

                //}
            }
            #endregion


            int numbIndex = numberOfNewAccount.Length - 1;

            //add new account number to the number
            for (int x = NewNumber.Length; x > NewNumber.Length - numberOfNewAccount.Length; x--)
            {
                ccNumberArr[x - 1] = numberOfNewAccount[numbIndex];
                numbIndex--;
            }

            //get string from array of char
            string lastNumber = string.Empty;
            foreach (char r in ccNumberArr)
                lastNumber += r;

            NewNumber = lastNumber;
            return NewNumber;
        }
        public static string ItemNumGenerated(IC_Category parent, params byte[] iscode2)
        {
            ERPDataContext DB = new ERPDataContext();
            string NewNumber = string.Empty;
            int level = 0;

            if (iscode2.Count() > 0 && string.IsNullOrEmpty(parent.Code2)) return string.Empty;
            //set level
            level = parent == null ? 1 : parent.Level + 1;

            //set number
            //add parent code

            NewNumber = parent == null ? string.Empty : iscode2.Count() > 0 ? parent.Code2 : parent.CatNumber;
            //add zero decimals, count of zero decimals equal to level+1
            for (int x = 0; x < level; x++)
                NewNumber += "0";

            char[] ccNumberArr = NewNumber.ToCharArray();
            //string numberOfNewAccount = numberOfNewAccount = (DB.IC_Items.Where(a => a.Category == parent.CategoryId).Count() + 1).ToString();

            string numberOfNewAccount;
            if (iscode2.Count() > 0)
                return ItemCatNumGenerated(parent);
            else
                numberOfNewAccount = (DB.IC_Items.Where(a => a.Category == parent.CategoryId).Select(x => x.ItemCode1).ToList().DefaultIfEmpty(0).Max() + 1).ToString();

            try
            {
                if (numberOfNewAccount.Length > 1)
                    numberOfNewAccount = numberOfNewAccount.Substring(numberOfNewAccount.Length - level);
            }
            catch (Exception ex) { return numberOfNewAccount; }
            //test that level doesn't exceed digits limits
            if (numberOfNewAccount.Length > (level + 1))
                return "-1";

            int numbIndex = numberOfNewAccount.Length - 1;

            //add new account number to the number
            for (int x = NewNumber.Length; x > NewNumber.Length - numberOfNewAccount.Length; x--)
            {
                ccNumberArr[x - 1] = numberOfNewAccount[numbIndex];
                numbIndex--;
            }

            //get string from array of char
            string lastNumber = string.Empty;
            foreach (char r in ccNumberArr)
                lastNumber += r;

            NewNumber = lastNumber;
            return NewNumber;
        }

        public static void GetItemAndQtyFromBarcodeTemplate(ref int barcodeTemplateCode, ref decimal barcodeTemplateQty, ref string barcodeBatch,
            string code, ST_Store st_store)
        {
            //int firstDigit = (int)(code.ToString()[0]);
            if (st_store.bcItemCount.HasValue && st_store.bcScaleCount.HasValue && st_store.bcItemCount.HasValue)
                if (code.Substring(0, 1) == st_store.bcIdentifier.ToString())
                {
                    string Itemcode = code.Substring(1, st_store.bcItemCount.Value);
                    barcodeTemplateCode = Convert.ToInt32(Itemcode);

                    int qtL = st_store.bcScaleCount.Value;
                    string qtyValueStr = code.Substring((1 + st_store.bcItemCount.Value), qtL);
                    if (Shared.st_Store.hasChkSum == true)
                        qtyValueStr = qtyValueStr.Substring(qtyValueStr.Length - 1, qtyValueStr.Length - 1);
                    //decimal qtyValue = st_store.bcScaleCount.GetValueOrDefault(6) == 6 ? Convert.ToDecimal(qtyValueStr) / 1000 : Convert.ToDecimal(qtyValueStr) / 1000;

                    decimal qtyValue = Convert.ToDecimal(qtyValueStr) / 1000;
                    barcodeTemplateQty += qtyValue;
                    return;
                }

            int prfxL = st_store.BarcodePrefix.Length;
            int codeL = st_store.BarcodeItemCodeLength.Value;
            int batchL = st_store.BarcodeBatchCodeLength.Value;
            int qtyL = st_store.BarcodeQtyLength.Value;

            string strItemcode = code.Substring(prfxL, codeL);
            barcodeTemplateCode = Convert.ToInt32(strItemcode);

            barcodeBatch = code.Substring(prfxL + codeL, batchL);
            if (qtyL > 0)
            {
                string strQty = code.Substring(prfxL + codeL + batchL,
                code.Length - prfxL - codeL - batchL);

                decimal intQty = Convert.ToDecimal(strQty.Substring(0, qtyL));

                barcodeTemplateQty += intQty;
            }
            if (code.Length > prfxL + codeL + qtyL + batchL)
            {
                string fraction = code.Substring(prfxL + codeL + qtyL + batchL);
                barcodeTemplateQty += Convert.ToDecimal(code.Substring(prfxL + codeL + qtyL + batchL)) / (decimal)Math.Pow(10, fraction.Length);
            }
        }

        public static IC_Item SearchItem(ERPDataContext DB, string code1, Detail_PR detail_PR,
              ref int barcodeTemplateCode, ref decimal barcodeTemplateQty, ref string barcodeBatch,
            int invoiceId, ST_Store st_Store)
        {
            if (st_Store.PrintBarcodePerInventory == false)
            {
                #region Search Per Barcode Template or Match Table
                if (string.IsNullOrEmpty(st_Store.BarcodePrefix) == false &&
                        st_Store.BarcodePrefix.Length > 0 &&
                        st_Store.BarcodeItemCodeLength.HasValue &&
                        st_Store.BarcodeQtyLength.HasValue)
                {
                    string code = string.Empty;
                    if (code1.StartsWith(st_Store.BarcodePrefix, StringComparison.OrdinalIgnoreCase))
                        code = code1;
                    else
                    {
                        if (st_Store.UseBarcodeMatchTable)
                        {
                            code = DB.ST_BarcodeMatches.Where(x => x.Serial == code1).Select(x => x.Barcode).FirstOrDefault();
                        }
                    }

                    try
                    {
                        MyHelper.GetItemAndQtyFromBarcodeTemplate(ref barcodeTemplateCode, ref barcodeTemplateQty, ref barcodeBatch, code, st_Store);

                        int barcodeTemplateCode1 = barcodeTemplateCode;
                        return DB.IC_Items.Where(x => x.ItemCode1 == barcodeTemplateCode1).FirstOrDefault();
                    }
                    catch (Exception ex)
                    {
                        return null;
                    }
                }
                else
                    return null;
                #endregion
            }
            else
            {

                if (code1.StartsWith("**"))
                {
                    #region Search_Item_With_PR_InvoiceDetail
                    int ProcessId = 0, PR_InvoiceDetailId = 0;
                    Get_PRId_And_PRDetailId_From_BarCode(code1, out ProcessId, out PR_InvoiceDetailId);

                    if (ProcessId == (int)Process.PurchaseInvoice)
                    {
                        detail_PR = (from i in DB.PR_InvoiceDetails
                                     where i.PR_InvoiceDetailId == PR_InvoiceDetailId
                                     select new Detail_PR
                                     {
                                         ItemId = i.ItemId,
                                         Batch = i.Batch,
                                         Expire = i.Expire,
                                         Length = i.Length,
                                         Width = i.Width,
                                         Height = i.Height,
                                         PiecesCount = i.PiecesCount,
                                         PurchasePrice = i.PurchasePrice,
                                         TotalPurchasePrice = i.TotalPurchasePrice,
                                         SellPrice = i.SellPrice,
                                         Qty = i.Qty,
                                         UOMId = i.UOMId,
                                         UOMIndex = i.UOMIndex,
                                         VendorId = (int?)i.PR_Invoice.VendorId,
                                     }).FirstOrDefault();

                    }
                    else
                    {
                        detail_PR = (from i in DB.IC_InTrnsDetails
                                     where i.InTrnsDetailId == PR_InvoiceDetailId
                                     select new Detail_PR
                                     {
                                         ItemId = i.ItemId,
                                         Batch = i.Batch,
                                         Expire = i.Expire,
                                         Length = i.Length,
                                         Width = i.Width,
                                         Height = i.Height,
                                         PiecesCount = i.PiecesCount,
                                         PurchasePrice = i.PurchasePrice,
                                         TotalPurchasePrice = i.TotalPurchasePrice,
                                         SellPrice = i.SellPrice,
                                         Qty = i.Qty,
                                         UOMId = i.UOMId,
                                         UOMIndex = i.UOMIndex,
                                         VendorId = (int?)i.IC_InTrn.VendorId,
                                     }).FirstOrDefault();
                    }

                    if (detail_PR != null)
                        return DB.IC_Items.Where(x => x.ItemId == detail_PR.ItemId).FirstOrDefault();
                    else
                    {
                        return null;
                        //item = DB.IC_Items.Where(x => x.ItemId == ItemId).FirstOrDefault();
                    }
                    #endregion
                }
                else
                {
                    #region Search Code1 Code2 InternationalCode
                    int code1int = 0;
                    Int32.TryParse(code1, out code1int);
                    IC_Item item = null;
                    if (code1int > 0)
                    {
                        item = (from i in DB.IC_Items
                                where i.ItemType != (int)DAL.ItemType.MatrixParent
                                where st_Store.SellRawMaterial == false ?
                                      i.ItemType == (int)DAL.ItemType.Assembly : true
                                where i.ItemCode1 == code1int
                                where invoiceId == 0 ? i.IsDeleted == false : true
                                select i).FirstOrDefault();
                    }
                    //---------
                    if (item == null)
                    {
                        item = (from i in DB.IC_Items
                                where i.ItemType != (int)DAL.ItemType.MatrixParent
                                where st_Store.SellRawMaterial == false ?
                                      i.ItemType == (int)DAL.ItemType.Assembly : true
                                where i.ItemCode2 == code1
                                where invoiceId == 0 ? i.IsDeleted == false : true
                                select i).FirstOrDefault();

                        if (item == null)
                        {
                            item = (from i in DB.IC_Items
                                    where i.ItemType != (int)DAL.ItemType.MatrixParent
                                    where st_Store.SellRawMaterial == false ?
                                          i.ItemType == (int)DAL.ItemType.Assembly : true
                                //    where i.IC_InternationalCodes.Select(x => x.InternationalCode)
                                //.Contains(code1)
                                    where invoiceId == 0 ? i.IsDeleted == false : true
                                    select i).FirstOrDefault();
                        }
                    }

                    return item;
                    #endregion
                }
            }
        }

        public static void Get_PRId_And_PRDetailId_From_BarCode(string BarCode, out int ProcessId, out int PR_Id)
        {
            try
            {
                ProcessId = Convert.ToInt32(BarCode.Split('*')[2]);
                PR_Id = Convert.ToInt32(BarCode.Split('*')[3]);
                //PR_Index = Convert.ToInt32(BarCode.Split('*')[4]);
            }
            catch
            {
                ProcessId = 0;
                PR_Id = 0;
                //PR_Index = 0;
            }
        }


        public static void UpdateST_UserLog(ERPDataContext DB, string code, string NotesAr, int processId, int ScreenId)
        {
            ST_UserLog ul = new ST_UserLog();
            ul.ActionDate = DateTime.Now;
            ul.Code = code;
            ul.NotesAr = NotesAr;
            ul.ProcessId = processId;
            ul.ScreenId = ScreenId;
            ul.UserId = Shared.UserId;

            DB.ST_UserLogs.InsertOnSubmit(ul);
        }

        /// <summary>
        /// get last in trns record's cost
        /// </summary>
        /// <param name="item"></param>
        /// <param name="uomIndex"></param>
        /// <param name="DB"></param>
        /// <returns></returns>
        public static decimal GetLastCostPrice(IC_Item item, byte uomIndex, ERPDataContext DB, DateTime date, int StoreId)
        {
            var lastPrice = (from i in DB.IC_ItemStores
                             where i.InsertTime < date
                             where i.StoreId == StoreId
                             where i.ItemId == item.ItemId
                             where (i.ProcessId == (int)Process.PurchaseInvoice ||
                             i.ProcessId == (int)Process.InTrns ||
                             i.ProcessId == (int)Process.OpenBalance)
                             orderby i.InsertTime descending, i.ItemStoreId descending
                             select new
                             {
                                 i.PurchasePrice,
                                 i.Qty
                             }).FirstOrDefault();

            if (lastPrice == null)
                return 0;

            if (uomIndex == 0)
                return lastPrice.PurchasePrice / lastPrice.Qty;
            else if (uomIndex == 1 && item.MediumUOMFactorDecimal.HasValue)
                return lastPrice.PurchasePrice / lastPrice.Qty * item.MediumUOMFactorDecimal.Value;
            else if (uomIndex == 2 && item.LargeUOMFactorDecimal.HasValue)
                return lastPrice.PurchasePrice / lastPrice.Qty * item.LargeUOMFactorDecimal.Value;
            else
                return 0;
        }

        public static decimal GetLastCostPrice(ItemLkp item, byte uomIndex, ERPDataContext DB, DateTime date, int StoreId)
        {
            var x = DB.IC_ItemStores.Where(xx => xx.ItemId == 3191).ToList();
            var lastPrice = (from i in DB.IC_ItemStores
                             where i.InsertTime < date
                             where i.StoreId == StoreId
                             where i.ItemId == item.ItemId
                             where (i.ProcessId == (int)Process.PurchaseInvoice ||
                             i.ProcessId == (int)Process.InTrns ||
                             i.ProcessId == (int)Process.OpenBalance ||
                             i.ProcessId == (int)Process.Manufacturing)
                             orderby i.InsertTime descending, i.ItemStoreId descending
                             select new
                             {
                                 i.PurchasePrice,
                                 i.Qty
                             }).FirstOrDefault();

            if (lastPrice == null)
                lastPrice = (from i in DB.IC_ItemStores
                             where i.InsertTime < date
                             where i.StoreId == StoreId
                             where i.ItemId == item.ItemId
                             where (i.ProcessId == (int)Process.SellInvoice ||
                             i.ProcessId == (int)Process.OutTrns ||
                             i.ProcessId == (int)Process.PurchaseReturn)
                             orderby i.InsertTime descending, i.ItemStoreId descending
                             select new
                             {
                                 i.PurchasePrice,
                                 i.Qty
                             }).FirstOrDefault();
            if (lastPrice == null)
                return 0;

            if (uomIndex == 0 && lastPrice.Qty > 0)
                return lastPrice.PurchasePrice / lastPrice.Qty;
            else if (uomIndex == 1 && item.MediumUOMFactorDecimal.HasValue)
                return lastPrice.PurchasePrice / lastPrice.Qty * item.MediumUOMFactorDecimal.Value;
            else if (uomIndex == 2 && item.LargeUOMFactorDecimal.HasValue)
                return lastPrice.PurchasePrice / lastPrice.Qty * item.LargeUOMFactorDecimal.Value;
            else
                return 0;
        }

        /// <summary>
        /// get sales order items, in grouped way, to alter reservation
        /// </summary>
        /// <param name="soId"></param>
        /// <returns></returns>
        public static List<StoreItem> GetSoItems(ERPDataContext DB, int soId)
        {
            List<StoreItem> lst_so_Detail = new List<StoreItem>();
            if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply)
                lst_so_Detail = (from s in DB.SL_SalesOrders
                                 join d in DB.SL_SalesOrderDetails
                                 on s.SL_SalesOrderId equals d.SL_SalesOrderId
                                 join t in DB.IC_Items
                                 on d.ItemId equals t.ItemId
                                 where s.SL_SalesOrderId == soId

                                 group new { d, t } by new
                                 {
                                     t.ItemType,
                                     t.Category,
                                     t.mtrxParentItem,
                                     t.mtrxId1,
                                     t.mtrxId2,
                                     t.mtrxId3,
                                     t.MediumUOMFactorDecimal,
                                     t.LargeUOMFactorDecimal,
                                     d.ItemId,

                                     d.UOMIndex,
                                     d.Expire,
                                     d.Batch,

                                 } into grp
                                 select new StoreItem(
                                                       Shared.st_Store.MultiplyDimensions,
                                                       grp.Key.ItemId,
                                                       grp.Key.ItemType,
                                                       DB.SL_SalesOrderDetails.Where(x => x.SL_SalesOrderId == soId).Select(x => x.SL_SalesOrderDetailId).First(),
                                                       grp.Key.UOMIndex,
                                                       grp.Sum(x => x.d.Qty),
                                                       grp.Key.MediumUOMFactorDecimal.HasValue ? grp.Key.MediumUOMFactorDecimal.Value : 0,
                                                       grp.Key.LargeUOMFactorDecimal.HasValue ? grp.Key.LargeUOMFactorDecimal.Value : 0,
                                                       grp.Key.Expire,
                                                       grp.Key.Batch,
                                                       null,
                                                       (int?)null,
                                                       1,
                                                       1,
                                                       1,
                                                       grp.Sum(x => x.d.PiecesCount),
                                                       grp.Key.mtrxParentItem,
                                                      grp.Key.mtrxId1,
                                                      grp.Key.mtrxId2,
                                                      grp.Key.mtrxId3,
                                                      0,
                                                      0,
                                                      null,
                                                      null,
                                                      null,
                                                      grp.Key.Category, 0)).ToList();
            else
                lst_so_Detail = (from s in DB.SL_SalesOrders
                                 join d in DB.SL_SalesOrderDetails
                                 on s.SL_SalesOrderId equals d.SL_SalesOrderId
                                 join t in DB.IC_Items
                                 on d.ItemId equals t.ItemId
                                 where s.SL_SalesOrderId == soId

                                 group new { d, t } by new
                                 {
                                     t.ItemType,
                                     t.Category,
                                     t.mtrxParentItem,
                                     t.mtrxId1,
                                     t.mtrxId2,
                                     t.mtrxId3,
                                     t.MediumUOMFactorDecimal,
                                     t.LargeUOMFactorDecimal,
                                     d.ItemId,
                                     d.UOMIndex,
                                     d.Expire,
                                     d.Batch,
                                     d.Height,
                                     d.Width,
                                     d.Length
                                 } into grp
                                 select new StoreItem(
                                                       Shared.st_Store.MultiplyDimensions,
                                                       grp.Key.ItemId,
                                                       grp.Key.ItemType,
                                                       DB.SL_SalesOrderDetails.Where(x => x.SL_SalesOrderId == soId).Select(x => x.SL_SalesOrderDetailId).First(),
                                                       grp.Key.UOMIndex,
                                                       grp.Sum(x => x.d.Qty),
                                                       grp.Key.MediumUOMFactorDecimal.HasValue ? grp.Key.MediumUOMFactorDecimal.Value : 0,
                                                       grp.Key.LargeUOMFactorDecimal.HasValue ? grp.Key.LargeUOMFactorDecimal.Value : 0,
                                                       grp.Key.Expire,
                                                       grp.Key.Batch,
                                                       null,
                                                       (int?)null,
                                                       grp.Key.Length.HasValue ? grp.Key.Length.Value : 0,
                                                       grp.Key.Width.HasValue ? grp.Key.Width.Value : 0,
                                                       grp.Key.Height.HasValue ? grp.Key.Height.Value : 0,
                                                       grp.Sum(x => x.d.PiecesCount),
                                                       grp.Key.mtrxParentItem,
                                                      grp.Key.mtrxId1,
                                                      grp.Key.mtrxId2,
                                                      grp.Key.mtrxId3,
                                                      0,
                                                      0,
                                                      null,
                                                      null,
                                                      null,
                                                      grp.Key.Category, 0)).ToList();

            return lst_so_Detail;
        }

        /// <summary>
        /// Get List of raw materials for assemly item oftype(C_supposed_raw_Obj)
        /// </summary>
        /// <param name="DB"></param>
        /// <param name="ItemId"></param>
        /// <returns></returns>
        public static List<C_supposed_raw_Obj> LoadManfRaw(ERPDataContext DB, int ItemId)
        {
            var BOMId = DB.IC_BOMs.Where(x => x.ProductItemId == ItemId).Select(x => x.BOMId).First();
            var raws = (from d in DB.IC_BOMDetails
                        where d.BOMId == BOMId
                        join t in DB.IC_Items on d.RawItemId equals t.ItemId
                        select new
                        {
                            Product_Item_Id = ItemId,
                            d.Qty,
                            UOMId = d.UomId,
                            t.SmallUOM,
                            t.MediumUOM,
                            t.LargeUOM,
                            t.MediumUOMFactor,
                            t.LargeUOMFactor,
                            Raw_Item_Id = d.RawItemId,
                            t.ItemNameAr,
                            t.PurchasePrice,
                            t.IsExpire,
                            t.ItemType,
                        }).ToList();

            var supposed_Raws = (from d in raws
                                 let factor = d.UOMId == d.SmallUOM ? 1 :
                                             (d.UOMId == d.MediumUOM.Value ? MyHelper.FractionToDouble(d.MediumUOMFactor) :
                                                                             MyHelper.FractionToDouble(d.LargeUOMFactor))
                                 select new C_supposed_raw_Obj()
                                 {
                                     Raw_Item_ID = d.ItemNameAr,
                                     Qty = Math.Round(decimal.ToDouble(d.Qty * factor), 3),
                                     UomId = d.UOMId,
                                     ItemId = d.Raw_Item_Id,
                                     Factor = factor,
                                     PurchasePrice = d.PurchasePrice,
                                     Total_Price = decimal.ToDouble(factor * (decimal)d.Qty * d.PurchasePrice),
                                     IsExpire = d.IsExpire,
                                     ItemType = d.ItemType,
                                     CurrentQty = d.ItemType == (int)ItemType.Service ? 0 : Math.Round(DB.IC_ItemStores.Where(x => x.ItemId == d.Raw_Item_Id).Select(x => x.IsInTrns ? x.Qty : x.Qty * -1).ToList().DefaultIfEmpty(0).Sum() / factor, 3)
                                 }).ToList();
            var lst_supposedRaws = new List<C_supposed_raw_Obj>();
            foreach (var r in supposed_Raws)
                lst_supposedRaws.Add(r);
            return supposed_Raws;
        }

        /// <summary>
        /// return list of assembly Items' Raw Materials 
        /// </summary>
        /// <param name="DB"></param>
        /// <returns>List<InvBom></returns>
        public static List<InvBom> GetBOM(ERPDataContext DB)
        {
            return (from m in DB.IC_BOMs
                    join d in DB.IC_BOMDetails
                    on m.BOMId equals d.BOMId
                    join i in DB.IC_Items
                    on d.RawItemId equals i.ItemId
                    select new InvBom
                    {
                        BOMId = m.BOMId,
                        ProductItemId = m.ProductItemId,
                        ProductQty = m.Qty,
                        RawItemId = d.RawItemId,
                        UomId = d.UomId,
                        RawQty = d.Qty,
                        UomIndex = d.UomId == i.SmallUOM ? (byte)0 : (d.UomId == i.MediumUOM.Value ? (byte)1 : (byte)2),
                        Factor = d.UomId == i.SmallUOM ? 1 :
                              (d.UomId == i.MediumUOM.Value ? (i.MediumUOMFactorDecimal.HasValue ? i.MediumUOMFactorDecimal.Value : 0) :
                                                             (i.LargeUOMFactorDecimal.HasValue ? i.LargeUOMFactorDecimal.Value : 0)),
                        RawItemType = i.ItemType,
                        mtrxAttribute1 = i.mtrxAttribute1,
                        mtrxAttribute2 = i.mtrxAttribute2,
                        mtrxAttribute3 = i.mtrxAttribute3,
                        mtrxParentItem = i.mtrxParentItem,
                        MediumUOMFactorDecimal = i.MediumUOMFactorDecimal.HasValue ? i.MediumUOMFactorDecimal.Value : 1,
                        LargeUOMFactorDecimal = i.LargeUOMFactorDecimal.HasValue ? i.LargeUOMFactorDecimal.Value : 1
                    }).ToList();
        }

        /// <summary>
        /// get sales order items to be reserved, after subtracting all acheived items
        /// </summary>
        /// <param name="DB"></param>
        /// <param name="SoId"></param>
        /// <param name="lst_outitems"></param>
        /// <returns></returns>
        public static List<StoreItem> PostSoReserve(FormsNames senderForm, ERPDataContext DB, int SoId, List<StoreItem> lst_outitems)
        {
            List<StoreItem> lst_newStoreItem = new List<StoreItem>();

            if (senderForm != FormsNames.SL_SalesOrder)
            {
                //first delete ic_itemStore for Sales Order
                var salesorder_itemstores = (from i in DB.IC_ItemStores
                                             where i.ProcessId == (int)Process.SalesOrder &&
                                             DB.SL_SalesOrderDetails.Where(x => x.SL_SalesOrderId == SoId).Select(x => x.SL_SalesOrderDetailId).Contains(i.SourceId)
                                             select i).ToList();

                DB.IC_ItemStores.DeleteAllOnSubmit(salesorder_itemstores);
            }

            if (Shared.st_Store.SalesOrderReserveGood)
            {
                var itms = DB.IC_Items.Where(x => x.IsOffer == true).ToList();


                if (Shared.LibraAvailabe)
                {
                    var bom = GetBOM(DB);

                    //get offer items to exclude them from Reservation and Reserve Raws
                    var except = (from l in lst_outitems
                                  join i in itms on l.ItemId equals i.ItemId
                                  select l);
                    lst_outitems = lst_outitems.Except(except).ToList();

                    //get Raws for each offer item
                    foreach (var e in except)
                    {
                        foreach (var b in bom.Where(x => x.ProductItemId == e.ItemId))
                        {
                            lst_outitems.Add(new StoreItem(Shared.st_Store.MultiplyDimensions, b.RawItemId, b.RawItemType,
                                               e.SourceId, b.UomIndex, e.TotalQty * b.RawQty /*: detail.Qty * b.RawQty * b.Factor*/,
                                               b.MediumUOMFactorDecimal, b.LargeUOMFactorDecimal, null, null, null, null, 1, 1, 1, e.TotalQty,
                                               b.mtrxParentItem, b.mtrxAttribute1, b.mtrxAttribute2, b.mtrxAttribute3,
                                                0, 0, e.Source, null, null, 0, e.StoreId));
                        }

                    }
                }

                //group sales order items, to avoid item redundancy, when user duplicates his items' data entry
                List<StoreItem> lst_SO = (from s in lst_outitems
                                          group s by new
                                          {
                                              s.ItemType,
                                              s.CategoryId,
                                              s.ParentItemId,
                                              s.M1,
                                              s.M2,
                                              s.M3,
                                              s.ItemId,
                                              s.ExpireDate,
                                              s.Batch,
                                              s.Height,
                                              s.Width,
                                              s.Length
                                          } into grp
                                          select new StoreItem(
                                              grp.Key.ItemId,
                                              grp.Key.ItemType,
                                              lst_outitems.First().SourceId,
                                              grp.Sum(x => x.TotalQty),
                                              grp.Key.ExpireDate,
                                              grp.Key.Batch,
                                              null,
                                              (int?)null,
                                              grp.Key.Length,
                                              grp.Key.Width,
                                              grp.Key.Height,
                                              grp.Sum(x => x.PiecesCount),
                                              grp.Key.ParentItemId,
                                             grp.Key.M1,
                                             grp.Key.M2,
                                             grp.Key.M3,
                                             grp.Sum(x => x.TotalLocalSellPrice),
                                             grp.Sum(x => x.TotalCost),
                                             null,
                                             null,
                                             grp.Key.CategoryId, null,
                                              grp.Sum(x => x.Pack)
                                             )).OrderBy(x => x.ItemId).ToList();

                List<StoreItem> lst_AcheivedItems = new List<StoreItem>();
                if (Shared.InvoicePostToStore)
                    lst_AcheivedItems = (from s in DB.SL_Invoices
                                         join d in DB.SL_InvoiceDetails
                                         on s.SL_InvoiceId equals d.SL_InvoiceId
                                         join i in DB.IC_ItemStores
                                         on d.SL_InvoiceDetailId equals i.SourceId
                                         join t in DB.IC_Items
                                         on i.ItemId equals t.ItemId
                                         where s.ProcessId == (int)Process.SalesOrder &&
                                         s.SourceId == SoId

                                         group new { i, t } by new
                                         {
                                             t.ItemType,
                                             t.Category,
                                             i.ParentItemId,
                                             i.M1,
                                             i.M2,
                                             i.M3,
                                             i.ItemId,
                                             i.Expire,
                                             i.Batch,
                                             i.Height,
                                             i.Width,
                                             i.Length
                                         } into grp
                                         select new StoreItem(
                                              grp.Key.ItemId,
                                              grp.Key.ItemType,
                                              lst_outitems.First().SourceId,
                                              grp.Sum(x => x.i.Qty),
                                              grp.Key.Expire,
                                              grp.Key.Batch,
                                              null,
                                              (int?)null,
                                              grp.Key.Length,
                                              grp.Key.Width,
                                              grp.Key.Height,
                                              grp.Sum(x => x.i.PiecesCount),
                                              grp.Key.ParentItemId,
                                             grp.Key.M1,
                                             grp.Key.M2,
                                             grp.Key.M3,
                                             grp.Sum(x => x.i.SellPrice.HasValue ? x.i.SellPrice.Value : 0),
                                             grp.Sum(x => x.i.PurchasePrice),
                                             null,
                                             null,
                                             grp.Key.Category, null, grp.Sum(x => x.i.Pack))).ToList();
                else
                    lst_AcheivedItems = (from s in DB.IC_OutTrns
                                         join d in DB.IC_OutTrnsDetails
                                         on s.OutTrnsId equals d.OutTrnsId
                                         join i in DB.IC_ItemStores
                                         on d.OutTrnsDetailId equals i.SourceId
                                         join t in DB.IC_Items
                                         on i.ItemId equals t.ItemId
                                         where s.ProcessId == (int)Process.SalesOrder &&
                                         s.SourceId == SoId

                                         group new { i, t } by new
                                         {
                                             t.ItemType,
                                             t.Category,
                                             i.ParentItemId,
                                             i.M1,
                                             i.M2,
                                             i.M3,
                                             i.ItemId,
                                             i.Expire,
                                             i.Batch,
                                             i.Height,
                                             i.Width,
                                             i.Length
                                         } into grp
                                         select new StoreItem(
                                              grp.Key.ItemId,
                                              grp.Key.ItemType,
                                              lst_outitems.First().SourceId,
                                              grp.Sum(x => x.i.Qty),
                                              grp.Key.Expire,
                                              grp.Key.Batch,
                                              null,
                                              (int?)null,
                                              grp.Key.Length,
                                              grp.Key.Width,
                                              grp.Key.Height,
                                              grp.Sum(x => x.i.PiecesCount),
                                              grp.Key.ParentItemId,
                                             grp.Key.M1,
                                             grp.Key.M2,
                                             grp.Key.M3,
                                             grp.Sum(x => x.i.SellPrice.HasValue ? x.i.SellPrice.Value : 0),
                                             grp.Sum(x => x.i.PurchasePrice),
                                             null,
                                             null,
                                             grp.Key.Category, null, grp.Sum(x => x.i.Pack))).ToList();

                foreach (StoreItem i in lst_SO)
                {
                    StoreItem storeItem = lst_AcheivedItems.Where(x => x.ItemId == i.ItemId && x.Batch == i.Batch && x.ExpireDate == i.ExpireDate && x.Height == i.Height &&
                        x.Width == i.Width && x.Length == i.Length).FirstOrDefault();
                    if (storeItem == null)
                        lst_newStoreItem.Add(new StoreItem(i.ItemId, i.ItemType, i.SourceId, i.TotalQty, i.ExpireDate, i.Batch, i.QC,
                            i.VendorId, i.Length, i.Width, i.Height, i.PiecesCount, i.ParentItemId, i.M1, i.M2, i.M3,
                            i.TotalLocalSellPrice, i.TotalCost, i.Serial, i.Serial2, i.CategoryId, null, i.Pack));
                    else
                    {
                        if (i.TotalQty > storeItem.TotalQty)
                            lst_newStoreItem.Add(new StoreItem(i.ItemId, i.ItemType, i.SourceId, i.TotalQty - storeItem.TotalQty, i.ExpireDate, i.Batch, i.QC,
                                i.VendorId, i.Length, i.Width, i.Height, i.PiecesCount - storeItem.PiecesCount, i.ParentItemId, i.M1, i.M2, i.M3,
                            i.TotalLocalSellPrice, i.TotalCost, i.Serial, i.Serial2, i.CategoryId, null, i.Pack));
                    }
                }
            }

            return lst_newStoreItem;
        }

        public static double GetLastPrices(ERPDataContext DB, bool CustomerInvoices, int ItemId, int Customer_Vendor_Id)
        {
            if (ItemId < 1)
                return 0;

            if (CustomerInvoices)
            {
                return (from p in DB.SL_Invoices
                        join d in DB.SL_InvoiceDetails
                        on p.SL_InvoiceId equals d.SL_InvoiceId
                        where d.ItemId == ItemId
                        where p.CustomerId == Customer_Vendor_Id
                        //where Shared.user.UserChangeStore ? true : p.StoreId == Shared.user.DefaultStore
                        select new
                        {
                            p.InvoiceDate,
                            SellPrice = decimal.ToDouble(d.SellPrice),
                        }).OrderByDescending(p => p.InvoiceDate).Select(x => x.SellPrice).FirstOrDefault();
            }
            else
            {
                return (from p in DB.PR_Invoices
                        join d in DB.PR_InvoiceDetails
                        on p.PR_InvoiceId equals d.PR_InvoiceId
                        where d.ItemId == ItemId
                        where p.VendorId == Customer_Vendor_Id
                        //where Shared.user.UserChangeStore ? true : p.StoreId == Shared.user.DefaultStore
                        select new
                        {
                            p.InvoiceDate,
                            SellPrice = decimal.ToDouble(d.PurchasePrice),
                        }).OrderByDescending(p => p.InvoiceDate).Select(x => x.SellPrice).FirstOrDefault();
            }
        }


        public static void FillMultiCC(ref DataTable dt_MultiCC, int SourceId, int ProcessId)
        {
            ERPDataContext DB = new ERPDataContext();
            var multiCC = DB.Acc_Journal_CostCenters.Where(x => x.ProcessId == ProcessId && x.SourceId == SourceId).Select(x => x.CostCenter_Id).Distinct();
            if (multiCC.Count() > 0)
            {
                foreach (var c in multiCC)
                    dt_MultiCC.Rows.Add(Convert.ToInt32(c));
            }
        }
        public static List<IC_Store> Get_StoresNotStopped(int storeid,bool? IsStore, out int defaultStoreId, bool UserChangeStore,
         int DefaultStore, int UserId)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var strLst =new List<IC_Store>();
            if (storeid == 0)
            {
                strLst = DB.IC_Stores.Where(a => a.IsStopped == false || a.IsStopped == null).ToList();
            }

            else
            {
                strLst = DB.IC_Stores.Where(a => a.IsStopped == false || a.IsStopped == null).ToList();
                var storeStopped = DB.IC_Stores.FirstOrDefault(a => a.StoreId == storeid&&a.IsStopped==true);
                if(storeStopped!=null)
                strLst.Add(storeStopped);
            }
            

            if (!IsStore.HasValue)
            {
                defaultStoreId = strLst.FirstOrDefault().StoreId;
                return strLst;
            }

            List<IC_Store> availableStores;

            //var query = DB.IC_User_Stores.Where(x => x.UserId == UserId).Select(i => i.StoreId);
            //if (query.Count() > 0)
            //{
            //    availableStores = strLst.Where(s => query.Contains(s.StoreId)).ToList();
            //}
            //else
            {
                availableStores = (from d in strLst
                                   where UserChangeStore ? true :
                                         d.StoreId == DefaultStore
                                   where IsStore == false ? d.ParentId.HasValue == false       //Branch
                                         : strLst.Where(x => x.ParentId == d.StoreId).Count() == 0
                                   select d).ToList();
            }
            if (availableStores.Count == 0)//user default store doesn't exist in level
            {
                IC_Store defStore = strLst.Where(s => s.StoreId == DefaultStore).Select(s => s).FirstOrDefault();
                if (defStore != null)
                {
                    if (defStore.ParentId.HasValue)//user store not in branches :- get branch
                    {
                        availableStores = strLst.Where(s => s.StoreId == defStore.ParentId.Value).ToList();
                       
                    }
                    else //user branch not in stores :- get child stores
                    {
                        availableStores = strLst.Where(s => s.ParentId == defStore.StoreId).ToList();
                        
                    }
                    
                }
                if (availableStores.Count > 0)
                    defaultStoreId = availableStores.Select(s => s.StoreId).First();
                else
                    defaultStoreId = 0;
            }
            else
            {
                //test whether user has a default store, and exists in lsit of available stores
                if (UserId > 0 && DefaultStore != 0
                    && availableStores.Exists(x => x.StoreId == DefaultStore))
                    defaultStoreId = DefaultStore;
                else
                    defaultStoreId = availableStores.FirstOrDefault().StoreId;
            }

            return availableStores;
        }
        /// <summary>
        /// Get the Default Selling UOMId according to the DfltSellUomIndx in Item
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public static int GetDefaultSellUOMId(IC_Item item)
        {
            if (item.DfltSellUomIndx == 0)
                return item.SmallUOM;
            else
                   if (item.DfltSellUomIndx == 1)
                return item.MediumUOM.Value;
            else
                return item.LargeUOM.Value;
        }
        /// <summary>
        /// Class to calculate the subtaxes 
        /// </summary>
        public static class TaxService
        {
            /// <summary>
            /// Set the sub taxes for the first time or when retriving the invoice
            /// </summary>
            /// <param name="detail">Get the Sub taxes with ratio and and id to calulate the tax value</param>
            /// <param name="row"> Data row to set the taxes on that row </param>
            /// <param name="Dt_Rows">Get the datatable to update on and add the values of the taxes and total price</param>
            /// <param name="rowhandle">row handle</param>
            /// <param name="item">Get the item to add the item id to the new rows  </param>
            /// <param name="discountTaxId"> get the discount tax id  to categorize with parent</param>
            /// <param name="isTaxable">Check if the Book and the customer is taxable or not</param>
            public static void setETaxE_Invoice(List<IC_ItemSubTax> detail, DataRow row, ref DataTable Dt_Rows, int rowhandle, IC_Item item, int discountTaxId, bool isTaxable=true)
            {
                if (detail.Count > 0)
                {
                    ERPDataContext DB = new ERPDataContext();

                    var E_TaxableTypes = DB.E_TaxableTypes.ToList();

                    var customTaxT1 = E_TaxableTypes.FirstOrDefault(a => a.Code == "T2")?.E_TaxableTypeId;
                    var customTaxT2 = E_TaxableTypes.FirstOrDefault(a => a.Code == "T3")?.E_TaxableTypeId;
                    var salesTaxId = E_TaxableTypes.FirstOrDefault(a => a.Code == "T1")?.E_TaxableTypeId;
                    #region Fees

                    #region ServiceChargesFees T09
                    var SubtaxServiceChargesFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "SC01")?.E_TaxableTypeId;
                    var SubtaxServiceChargesFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "SC02")?.E_TaxableTypeId;
                    #endregion

                    #region OtherFees T12
                    var SubtaxotherFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "OF01")?.E_TaxableTypeId;
                    var SubtaxotherFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "OF02")?.E_TaxableTypeId;
                    #endregion

                    #region OtherFees T20
                    var SubtaxOtherFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "OF03")?.E_TaxableTypeId;
                    var SubtaxOtherFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "OF04")?.E_TaxableTypeId;
                    #endregion

                    #region MedicalIsurance
                    var SubtaxMedicalIsuranceFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "MI03")?.E_TaxableTypeId;
                    var SubtaxMedicalIsuranceFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "MI04")?.E_TaxableTypeId;
                    #endregion

                    #region MunicipalityFees
                    var SubtaxMunicipalityFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "Mn03")?.E_TaxableTypeId;
                    var SubtaxMunicipalityFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "Mn04")?.E_TaxableTypeId;
                    #endregion

                    #region ServiceChargesFees T17
                    var SubtaxserviceChargesFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "SC03")?.E_TaxableTypeId;
                    var SubtaxserviceChargesFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "SC04")?.E_TaxableTypeId;
                    #endregion

                    #region ResourceDevelopmentFees
                    var SubtaxResourceDevelopmentFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "RD03")?.E_TaxableTypeId;
                    var SubtaxResourceDevelopmentFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "RD04")?.E_TaxableTypeId;
                    #endregion

                    #endregion
                    decimal totalCustomTaxRatio = 0;

                    var eTaxesWithParentId = from t in detail
                                             join etax in E_TaxableTypes
                                             on t.SubTaxId equals etax.E_TaxableTypeId
                                             let parentTaxPriority = etax.ParentTaxId == 4 ? 1 :
                                                                     etax.ParentTaxId == 5 ? 2 :
                                                                     etax.ParentTaxId == 6 ? 3 :
                                                                     etax.ParentTaxId == 7 ? 4 :
                                                                     etax.ParentTaxId == 8 ? 5 :
                                                                     etax.ParentTaxId == 9 || etax.ParentTaxId == 12 ? 6 :
                                                                     etax.ParentTaxId == 10 ? 7 :
                                                                     etax.ParentTaxId == 11 ? 8 :
                                                                     etax.ParentTaxId == 13 ? 9 :
                                                                     etax.ParentTaxId == 14 ? 10 :
                                                                     etax.ParentTaxId == 15 ? 11 :
                                                                     etax.ParentTaxId == 16 ? 12 :
                                                                     etax.ParentTaxId == 17 ? 13 :
                                                                     etax.ParentTaxId == 18 ? 14 :
                                                                     etax.ParentTaxId == 19 ? 15 :
                                                                     etax.ParentTaxId == 20 ? 16 :
                                                                     etax.ParentTaxId == 2 ? 17 :
                                                                     etax.ParentTaxId == 3 ? 18 :
                                                                     etax.ParentTaxId == 1 ? 19 : 20
                                             orderby parentTaxPriority, etax.ParentTaxId
                                             select t;

                    TaxCalculator taxCalculator = new TaxCalculator();

                    decimal discountValue = Convert.ToDecimal(row["DiscountValue"]);
                    decimal totalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) - discountValue;
                    bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);
                    decimal totalsellPriceBeforeDisc = Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]);

                    foreach (var tax in eTaxesWithParentId)
                    {

                        DataRow dTax = Dt_Rows.NewRow();
                        dTax["SubTax"] = tax.SubTaxId;
                        dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                        dTax["Percentage"] = isTaxable ? decimal.ToDouble(tax.Rate.GetValueOrDefault(0)) : 0;

                        decimal percentage = Convert.ToDecimal(dTax["Percentage"]) / 100;

                        decimal calculatedTax = calcTaxBeforeDisc
                            ? Math.Round(totalsellPriceBeforeDisc * percentage, 4)
                            : Math.Round(totalSellPrice * percentage, 4);

                        SubCustomTaxTypes taxTypeEnum = SubCustomTaxTypes.AddTax;

                        // Assign Discount Tax enum
                        if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
                        {
                            taxTypeEnum = SubCustomTaxTypes.DiscountTax;
                        }
                        else if (Convert.ToInt32(dTax["Tax"]) == salesTaxId)
                        {
                            taxTypeEnum = SubCustomTaxTypes.AddTax;
                            if (calcTaxBeforeDisc) totalsellPriceBeforeDisc += calculatedTax;
                            else totalSellPrice += calculatedTax;
                        }
                        else if (Convert.ToInt32(dTax["Tax"]) == customTaxT1 || Convert.ToInt32(dTax["Tax"]) == customTaxT2)
                        {
                            taxTypeEnum = SubCustomTaxTypes.TableTax;
                            if (calcTaxBeforeDisc) totalsellPriceBeforeDisc += calculatedTax;
                            else totalSellPrice += calculatedTax;
                        }
                        else if (Convert.ToInt32(dTax["SubTax"]) == SubtaxServiceChargesFeesRatio ||
         Convert.ToInt32(dTax["SubTax"]) == SubtaxServiceChargesFeesValue ||
         Convert.ToInt32(dTax["SubTax"]) == SubtaxotherFeesRatio ||
         Convert.ToInt32(dTax["SubTax"]) == SubtaxotherFeesValue
        )
                        {//T09 and T12
                            taxTypeEnum = SubCustomTaxTypes.ServceCharges;
                            if (calcTaxBeforeDisc) totalsellPriceBeforeDisc += calculatedTax;
                            else totalSellPrice += calculatedTax;
                        }
                        else if (Convert.ToInt32(dTax["SubTax"]) == SubtaxOtherFeesRatio ||
                             Convert.ToInt32(dTax["SubTax"]) == SubtaxOtherFeesValue ||
                             Convert.ToInt32(dTax["SubTax"]) == SubtaxMedicalIsuranceFeesRatio ||
                             Convert.ToInt32(dTax["SubTax"]) == SubtaxMedicalIsuranceFeesValue ||
                             Convert.ToInt32(dTax["SubTax"]) == SubtaxMunicipalityFeesRatio ||
                             Convert.ToInt32(dTax["SubTax"]) == SubtaxMunicipalityFeesValue ||
                             Convert.ToInt32(dTax["SubTax"]) == SubtaxServiceChargesFeesRatio ||
                             Convert.ToInt32(dTax["SubTax"]) == SubtaxServiceChargesFeesValue ||
                             Convert.ToInt32(dTax["SubTax"]) == SubtaxResourceDevelopmentFeesRatio ||
                             Convert.ToInt32(dTax["SubTax"]) == SubtaxResourceDevelopmentFeesValue
                            )
                        {
                            taxTypeEnum = SubCustomTaxTypes.OtherFees;
                        }
                        // Add the calculated tax to the TaxCalculator with its type
                        taxCalculator.AddTax(new SubCustomTaxes(rowhandle, percentage, taxTypeEnum));

                        dTax["ItemId"] = item.ItemId;
                        dTax["SellPrice"] = 0;
                        dTax["Qty"] = 0;
                        dTax["TaxValue"] = isTaxable ? Math.Round(decimal.ToDouble(calculatedTax), 4) : 0;
                        dTax["RowHandle"] = rowhandle;

                        Dt_Rows.Rows.Add(dTax);
                    }
                    // Calculate total tax values AddTax, TableTax, DiscountTax and Total of them
                    // Calculate each tax separately
                    decimal originalPrice = Convert.ToBoolean(row["calcTaxBeforeDisc"]) ? (Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"])) : (Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"])) - Convert.ToDecimal(row["DiscountValue"]);
                    //Discount Tax
                    decimal discountTaxValue = taxCalculator.GetDiscountTaxValue(originalPrice);
                    //Table Tax
                    decimal tableTaxValue = taxCalculator.GetTableTaxValue(originalPrice);
                    //Fees As Tax
                    decimal feesAsTaxValue = taxCalculator.GetFeesAsTaxValue(originalPrice);
                    decimal serviceFeesAsTaxValue = taxCalculator.GetServiceFeesAsTaxValue(originalPrice);
                    //Must assign the table tax to the price before the Add Taxes applied
                    decimal priceAfterTableTax = originalPrice + tableTaxValue + serviceFeesAsTaxValue;
                    //Sales Tax
                    decimal addTaxValue = taxCalculator.GetAddTaxValue(priceAfterTableTax);

                    // Set the values in the DataRow
                    row["TotalSubDiscountTax"] = isTaxable ? Math.Round(decimal.ToDouble(discountTaxValue), 4) : 0;
                    row["TotalSubCustomTax"] = isTaxable ? Math.Round(decimal.ToDouble(tableTaxValue), 4) : 0;
                    row["TotalSubAddTax"] = isTaxable ? Math.Round(decimal.ToDouble(addTaxValue), 4) : 0;

                    // Set final total tax value
                    decimal finalTaxValue = tableTaxValue + feesAsTaxValue + serviceFeesAsTaxValue + addTaxValue - discountTaxValue;
                    row["TaxValue"] = isTaxable ? Math.Round(decimal.ToDouble(finalTaxValue), 4) : 0;
                }
            }
            /// <summary>
            /// Calculate the sub taxes after any change in row 
            /// </summary>
            /// <param name="row">Data row to update the taxes in that row</param>
            /// <param name="Dt_Rows">Get the datatable to update on and update  the values of the taxes and total price</param>
            /// <param name="discountTaxId">Get the discount tax id  to categorize with parent</param>
            public static void CalcSubTaxes(DataRow row,ref DataTable Dt_Rows, int discountTaxId)
            {
                ERPDataContext DB = new ERPDataContext();
                int? taxID = 0;
                decimal totalTaxValue = 0;
                var E_TaxableTypes = DB.E_TaxableTypes.ToList();

                var customTaxT1 = E_TaxableTypes.FirstOrDefault(a => a.Code == "T2")?.E_TaxableTypeId;
                var customTaxT2 = E_TaxableTypes.FirstOrDefault(a => a.Code == "T3")?.E_TaxableTypeId;
                var salesTaxId = E_TaxableTypes.FirstOrDefault(a => a.Code == "T1")?.E_TaxableTypeId;
                #region Fees

                #region ServiceChargesFees T09
                var SubtaxServiceChargesFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "SC01")?.E_TaxableTypeId;
                var SubtaxServiceChargesFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "SC02")?.E_TaxableTypeId;
                #endregion

                #region OtherFees T12
                var SubtaxotherFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "OF01")?.E_TaxableTypeId;
                var SubtaxotherFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "OF02")?.E_TaxableTypeId;
                #endregion

                #region OtherFees T20
                var SubtaxOtherFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "OF03")?.E_TaxableTypeId;
                var SubtaxOtherFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "OF04")?.E_TaxableTypeId;
                #endregion

                #region MedicalIsurance
                var SubtaxMedicalIsuranceFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "MI03")?.E_TaxableTypeId;
                var SubtaxMedicalIsuranceFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "MI04")?.E_TaxableTypeId;
                #endregion

                #region MunicipalityFees
                var SubtaxMunicipalityFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "Mn03")?.E_TaxableTypeId;
                var SubtaxMunicipalityFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "Mn04")?.E_TaxableTypeId;
                #endregion

                #region ServiceChargesFees T17
                var SubtaxserviceChargesFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "SC03")?.E_TaxableTypeId;
                var SubtaxserviceChargesFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "SC04")?.E_TaxableTypeId;
                #endregion

                #region ResourceDevelopmentFees
                var SubtaxResourceDevelopmentFeesRatio = E_TaxableTypes.FirstOrDefault(a => a.Code == "RD03")?.E_TaxableTypeId;
                var SubtaxResourceDevelopmentFeesValue = E_TaxableTypes.FirstOrDefault(a => a.Code == "RD04")?.E_TaxableTypeId;
                #endregion

                #endregion

                TaxCalculator taxCalculator = new TaxCalculator();

                
               sortingDT.SortDataTable(ref Dt_Rows);

                bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);
                decimal sellPrice = Convert.ToDecimal(row["SellPrice"]);
                decimal qty = Convert.ToDecimal(row["Qty"]);
                decimal discountValue = Convert.ToDecimal(row["DiscountValue"]);
                decimal totalSellPrice = qty * sellPrice - discountValue;
                decimal totalsellPriceBeforeDisc = Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]);

                //these values ↓ should be considered in calculations, 
                //make sure they ↓ are not null or DBNull.Value

                /*decimal Length = Convert.ToDecimal(row["Length"]);
                decimal Width = Convert.ToDecimal(row["Width"]);
                decimal Height = Convert.ToDecimal(row["Height"]);
                decimal QTYMillion = Convert.ToDecimal(row["QTYMillion"]);
                decimal PiecesCount = Convert.ToDecimal(row["PiecesCount"]);
                decimal Pack = Convert.ToDecimal(row["Pack"]);*/



                // Calculate taxes
                foreach (DataRow m in Dt_Rows.Rows)
                {
                    if (m.RowState == DataRowState.Deleted)
                        continue;

                    if (Convert.ToInt32(m["RowHandle"]) == Convert.ToInt32(row["RowHandle"]))
                    {
                        decimal percentage = Convert.ToDecimal(m["Percentage"]) / 100;
                        decimal calculatedTax = calcTaxBeforeDisc
                            ? Math.Round(totalsellPriceBeforeDisc * percentage, 4)
                            : Math.Round(totalSellPrice * percentage, 4);

                        if (m["SubTax"] != DBNull.Value && m["SubTax"] != null)
                        {
                            var taxType = Convert.ToInt32(m["SubTax"]);
                            taxID = DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == taxType)?.ParentTaxId;
                        }

                        SubCustomTaxTypes taxTypeEnum = SubCustomTaxTypes.AddTax;

                        if (taxID.Value == discountTaxId && taxID != 0)
                        {
                            taxTypeEnum = SubCustomTaxTypes.DiscountTax;
                        }
                        else if (Convert.ToInt32(m["Tax"]) == salesTaxId)
                        {
                            taxTypeEnum = SubCustomTaxTypes.AddTax;
                            if (calcTaxBeforeDisc) totalsellPriceBeforeDisc += calculatedTax;
                            else totalSellPrice += calculatedTax;
                        }
                        else if (Convert.ToInt32(m["Tax"]) == customTaxT1 || Convert.ToInt32(m["Tax"]) == customTaxT2)
                        {
                            taxTypeEnum = SubCustomTaxTypes.TableTax;
                            if (calcTaxBeforeDisc) totalsellPriceBeforeDisc += calculatedTax;
                            else totalSellPrice += calculatedTax;

                        }
                        else if (Convert.ToInt32(m["SubTax"]) == SubtaxServiceChargesFeesRatio ||
                                 Convert.ToInt32(m["SubTax"]) == SubtaxServiceChargesFeesValue ||
                                 Convert.ToInt32(m["SubTax"]) == SubtaxotherFeesRatio ||
                                 Convert.ToInt32(m["SubTax"]) == SubtaxotherFeesValue
                                )
                        {//T09 and T12
                            taxTypeEnum = SubCustomTaxTypes.ServceCharges;
                            if (calcTaxBeforeDisc) totalsellPriceBeforeDisc += calculatedTax;
                            else totalSellPrice += calculatedTax;
                        }
                        else if (Convert.ToInt32(m["SubTax"]) == SubtaxOtherFeesRatio ||
                             Convert.ToInt32(m["SubTax"]) == SubtaxOtherFeesValue ||
                             Convert.ToInt32(m["SubTax"]) == SubtaxMedicalIsuranceFeesRatio ||
                             Convert.ToInt32(m["SubTax"]) == SubtaxMedicalIsuranceFeesValue ||
                             Convert.ToInt32(m["SubTax"]) == SubtaxMunicipalityFeesRatio ||
                             Convert.ToInt32(m["SubTax"]) == SubtaxMunicipalityFeesValue ||
                             Convert.ToInt32(m["SubTax"]) == SubtaxserviceChargesFeesRatio ||
                             Convert.ToInt32(m["SubTax"]) == SubtaxserviceChargesFeesValue ||
                             Convert.ToInt32(m["SubTax"]) == SubtaxResourceDevelopmentFeesRatio ||
                             Convert.ToInt32(m["SubTax"]) == SubtaxResourceDevelopmentFeesValue
                            )
                        {//T16 To T20
                            taxTypeEnum = SubCustomTaxTypes.OtherFees;
                        }

                        // Add the calculated tax to the TaxCalculator with its type
                        taxCalculator.AddTax(new SubCustomTaxes(Convert.ToInt32(m["RowHandle"]), percentage, taxTypeEnum));

                        // Update row tax value
                        m["TaxValue"] = Math.Round(calculatedTax, 4);
                    }
                }

                // Calculate total tax values AddTax, TableTax, DiscountTax and Total of them
                // Calculate each tax separately
                decimal originalPrice = Convert.ToBoolean(row["calcTaxBeforeDisc"]) ? (Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"])) : (Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"])) - Convert.ToDecimal(row["DiscountValue"]);
                //Discount Tax
                decimal discountTaxValue = taxCalculator.GetDiscountTaxValue(originalPrice);
                //Fees As Tax
                decimal feesAsTaxValue = taxCalculator.GetFeesAsTaxValue(originalPrice);
                decimal serviceFeesAsTaxValue = taxCalculator.GetServiceFeesAsTaxValue(originalPrice);
                //Table Tax
                decimal tableTaxValue = taxCalculator.GetTableTaxValue(originalPrice+ serviceFeesAsTaxValue);
                //Must assign the table tax to the price before the Add Taxes applied
                decimal priceAfterTableTax = originalPrice + tableTaxValue + serviceFeesAsTaxValue;
                //Sales Tax
                decimal addTaxValue = taxCalculator.GetAddTaxValue(priceAfterTableTax);
                decimal TotalSellprice = priceAfterTableTax + addTaxValue - discountTaxValue;
                // Set the values in the DataRow
                row["TotalSubDiscountTax"] = Math.Round(decimal.ToDouble(discountTaxValue), 4);
                row["TotalSubCustomTax"] = Math.Round(decimal.ToDouble(tableTaxValue), 4);
                row["TotalSubAddTax"] = Math.Round(decimal.ToDouble(addTaxValue), 4);

                // Set final total tax value
                decimal finalTaxValue = tableTaxValue + feesAsTaxValue + serviceFeesAsTaxValue + addTaxValue - discountTaxValue;
                row["TaxValue"] = Math.Round(decimal.ToDouble(finalTaxValue), 4);

                // Calculate total sell price
                row["TotalSellPrice"] = Math.Round(originalPrice, 4);


            }
        }
    }

    /// <summary>
    /// the model that manage the tax value and tax type
    /// </summary>
    public class SubCustomTaxes
    {
        public int RowHandle { get; set; }
        public decimal TaxValue { get; set; }
        public SubCustomTaxTypes TaxType { get; set; }
        public SubCustomTaxes(int rowHandle, decimal taxValue, SubCustomTaxTypes taxType)
        {
            RowHandle = rowHandle;
            TaxValue = taxValue;
            TaxType = taxType;
        }
    }
    /// <summary>
    /// Calculate the subtaxes for the E-Invoice and the totals of different values of taxes
    /// </summary>
    public class TaxCalculator
    {
        private List<SubCustomTaxes> TaxList;
        public static decimal TotalSellPrice { get; set; }

        public TaxCalculator()
        {
            TaxList = new List<SubCustomTaxes>();
        }

        public void AddTax(SubCustomTaxes tax)
        {
            TaxList.Add(tax);
        }

        public decimal GetTotalTaxValue(SubCustomTaxTypes taxType)
        {
            return TaxList.Where(t => t.TaxType == taxType).Sum(t => t.TaxValue);
        }

        // Method to calculate the price after applying DiscountTax
        public decimal ApplyDiscountTax(decimal totalPrice)
        {
            decimal discountRatio = GetTotalTaxValue(SubCustomTaxTypes.DiscountTax);
            if (discountRatio > 0)
            {
                // Apply DiscountTax: totalPrice * (1 - DiscountTaxRatio)
                totalPrice *= (1 - discountRatio);
            }
            return totalPrice;
        }

        // Method to calculate the price after applying TableTax
        public decimal ApplyTableTax(decimal totalPrice)
        {
            decimal tableTaxRatio = GetTotalTaxValue(SubCustomTaxTypes.TableTax);
            if (tableTaxRatio > 0)
            {
                // Apply TableTax: totalPrice * (1 + TableTaxRatio)
                totalPrice *= (1 + tableTaxRatio);
            }
            return totalPrice;
        }

        // Method to calculate the price after applying AddTax
        public decimal ApplyAddTax(decimal totalPrice)
        {
            decimal addTaxRatio = GetTotalTaxValue(SubCustomTaxTypes.AddTax);
            if (addTaxRatio > 0)
            {
                // Apply AddTax: totalPrice * (1 + AddTaxRatio)
                totalPrice *= (1 + addTaxRatio);
            }
            return totalPrice;
        }

        // Method to calculate the total adjusted price after all taxes
        public decimal GetAdjustedPrice()
        {
            decimal originalPrice = TotalSellPrice;

            //// 1. Apply DiscountTax
            //decimal priceAfterDiscountTax = ApplyDiscountTax(originalPrice);

            // 2. Apply TableTax on the result after DiscountTax
            decimal priceAfterTableTax = ApplyTableTax(originalPrice);

            // 3. Apply AddTax on the result after both DiscountTax and TableTax
            decimal finalPrice = ApplyAddTax(priceAfterTableTax) - GetDiscountTaxValue(originalPrice);

            return finalPrice;
        }

        // To get each tax type's value separately
        public decimal GetDiscountTaxValue(decimal originalPrice)
        {
            decimal discountRatio = GetTotalTaxValue(SubCustomTaxTypes.DiscountTax);
            return discountRatio > 0 ? originalPrice * discountRatio : 0;
        }

        public decimal GetTableTaxValue(decimal priceAfterDiscount)
        {
            decimal tableTaxRatio = GetTotalTaxValue(SubCustomTaxTypes.TableTax);
            return tableTaxRatio > 0 ? priceAfterDiscount * tableTaxRatio : 0;
        }
        public decimal GetServiceFeesAsTaxValue(decimal priceAfterDiscount)
        {
            decimal FeesAsTaxRatio = GetTotalTaxValue(SubCustomTaxTypes.ServceCharges);
            return FeesAsTaxRatio > 0 ? priceAfterDiscount * FeesAsTaxRatio : 0;
        }
        public decimal GetFeesAsTaxValue(decimal priceAfterDiscount)
        {
            decimal FeesAsTaxRatio = GetTotalTaxValue(SubCustomTaxTypes.OtherFees);
            return FeesAsTaxRatio > 0 ? priceAfterDiscount * FeesAsTaxRatio : 0;
        }
        public decimal GetAddTaxValue(decimal priceAfterTableTax)
        {
            decimal addTaxRatio = GetTotalTaxValue(SubCustomTaxTypes.AddTax);
            return addTaxRatio > 0 ? priceAfterTableTax * addTaxRatio : 0;
        }
    }
    public class TaxItem
    {
        public int ItemId { get; set; }
        public decimal SellPrice { get; set; }
        public int Qty { get; set; }
        public int Tax { get; set; }
        public int SubTax { get; set; }
        public decimal Percentage { get; set; }
        public decimal TaxValue { get; set; }
        public int RowHandle { get; set; }
    }
    public static class sortingDT
    {
        public static List<TaxItem> ConvertDataTableToList(DataTable dt)
        {
            return dt.AsEnumerable().Where(x => x.RowState != DataRowState.Deleted).Select(row => new TaxItem
            {
                ItemId = int.TryParse(row["ItemId"].ToString(), out int itemId) ? itemId : 0,
                SellPrice = decimal.TryParse(row["SellPrice"].ToString(), out decimal sellPrice) ? sellPrice : 0m,
                Qty = int.TryParse(row["Qty"].ToString(), out int qty) ? qty : 0,
                Tax = int.TryParse(row["Tax"].ToString(), out int tax) ? tax : 0,
                SubTax = int.TryParse(row["SubTax"].ToString(), out int subTax) ? subTax : 0,
                Percentage = decimal.TryParse(row["Percentage"].ToString(), out decimal percentage) ? percentage : 0m,
                TaxValue = decimal.TryParse(row["TaxValue"].ToString(), out decimal taxValue) ? taxValue : 0m,
                RowHandle = int.TryParse(row["RowHandle"].ToString(), out int rowHandle) ? rowHandle : 0
            }).ToList();
        }

        public static List<TaxItem> SortTaxItems(List<TaxItem> taxItems)
        {
            return taxItems.OrderBy(item =>
            {
                return item.Tax == 4 ? 1 :
                    item.Tax == 5 ? 2 :
                    item.Tax == 6 ? 3 :
                    item.Tax == 7 ? 4 :
                    item.Tax == 8 ? 5 :
                    item.Tax == 9 || item.Tax == 12 ? 6 :
                    item.Tax == 10 ? 7 :
                    item.Tax == 11 ? 8 :
                    item.Tax == 13 ? 9 :
                    item.Tax == 14 ? 10 :
                    item.Tax == 15 ? 11 :
                    item.Tax == 16 ? 12 :
                    item.Tax == 17 ? 13 :
                    item.Tax == 18 ? 14 :
                    item.Tax == 19 ? 15 :
                    item.Tax == 20 ? 16 :
                    item.Tax == 2 ? 17 :
                    item.Tax == 3 ? 18 :
                    item.Tax == 1 ? 19 : 20;
            })
            .ThenBy(item => item.Tax)
            .ToList();
        }

        public static DataTable ConvertListToDataTable(List<TaxItem> taxItems)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("ItemId", typeof(int));
            dt.Columns.Add("SellPrice", typeof(decimal));
            dt.Columns.Add("Qty", typeof(decimal));
            dt.Columns.Add("Tax");
            dt.Columns.Add("SubTax");
            dt.Columns.Add("Percentage");
            dt.Columns.Add("TaxValue");
            dt.Columns.Add("RowHandle", typeof(int));

            foreach (var item in taxItems)
            {
                dt.Rows.Add(item.ItemId, item.SellPrice, item.Qty, item.Tax.ToString(), item.SubTax.ToString(), item.Percentage.ToString(), item.TaxValue.ToString(), item.RowHandle);
            }

            return dt;
        }

        public static void SortDataTable(ref DataTable dt)
        {
            if (dt.Rows.Count <= 0) return;
            // Convert DataTable to List
            List<TaxItem> taxItems = ConvertDataTableToList(dt);
            
            // Sort the List
            List<TaxItem> sortedTaxItems = SortTaxItems(taxItems);

            // Clear the original DataTable
            dt = new DataTable();
            dt = ConvertListToDataTable(sortedTaxItems);
        }
    }
    /// <summary>
    /// List the Sub Custom Tax Types to differentiate between the different taxes 
    /// </summary>
    public enum SubCustomTaxTypes
    {
        AddTax = 1,
        TableTax = 2,
        TableTaxAmount = 3,
        DiscountTax = 4,
        ServceCharges = 9,
        otherfees = 12,
        ResourceDevelopment = 16,
        ServicesCharge = 17,
        MunicipalityFees = 18,
        MedicalInsuranceFees = 19,
        OtherFees = 20
    }
    public class StoreItem
    {
        public int SourceId { get; set; }
        public int ItemId { get; set; }
        public int CategoryId { get; set; }

        public int ItemType { get; set; }
        public decimal TotalQty { get; set; }
        public DateTime? ExpireDate { get; set; }
        public string Batch { get; set; }
        public string QC { get; set; }
        public decimal Length { get; set; }
        public decimal Width { get; set; }
        public decimal Height { get; set; }
        public decimal PiecesCount { get; set; }
        public int? ParentItemId { get; set; }
        public int? M1 { get; set; }
        public int? M2 { get; set; }
        public int? M3 { get; set; }
        public decimal SmallUnitSellPrice { get; set; }
        public decimal SmallUnitCostPrice { get; set; }
        public int? VendorId { get; set; }
        public decimal TotalCost { get; set; }

        public object Source { get; set; }

        public string Serial { get; set; }
        public string Serial2 { get; set; }

        public decimal TotalLocalSellPrice { get; set; }
        //alaa 3/9/2018
        public int? StoreId { get; set; }
        public int Pack { get; set; }


        /// <summary>
        /// do dimensions and uoms calculations
        /// </summary>
        public StoreItem(byte dim, int ItemId, int ItemType, int SourceId,
                    byte UOMIndex, decimal Qty, decimal MediumUOMFactor, decimal LargeUOMFactor,
                   DateTime? ExpireDate, string Batch, string QC, int? VendorId, decimal Length, decimal Width, decimal Height, decimal PiecesCount,
                    int? ParentItemId, int? M1, int? M2, int? M3, decimal TotalLocalSellPrice, decimal TotalCostPrice, object Source, string Serial, string Serial2,
            int CategoryId, int? Pack)
        {

            this.Source = Source;

            this.ItemId = ItemId;
            this.CategoryId = CategoryId;
            this.ItemType = ItemType;
            this.SourceId = SourceId;
            this.ExpireDate = ExpireDate;

            if (Batch == null || Batch == string.Empty || Batch.Trim() == string.Empty)
                this.Batch = null;
            else
                this.Batch = Batch;

            if (Serial == null || Serial == string.Empty || Serial.Trim() == string.Empty)
                this.Serial = null;
            else
                this.Serial = Serial;


            if (Serial2 == null || Serial2 == string.Empty || Serial2.Trim() == string.Empty)
                this.Serial = null;
            else
                this.Serial2 = Serial2;

            if (QC == null || QC == string.Empty || QC.Trim() == string.Empty)
                this.QC = null;
            else
                this.QC = QC;

            if (dim == (byte)Dimensions.Multiply)
            {
                this.Length = 0;
                this.Width = 0;
                this.Height = 0;
                this.TotalQty = MyHelper.CalculateUomQty(Qty, UOMIndex, MediumUOMFactor, LargeUOMFactor)
                     * Height * Width * Length;
            }
            else
            {
                this.Length = Length;
                this.Width = Width;
                this.Height = Height;
                this.TotalQty = MyHelper.CalculateUomQty(Qty, UOMIndex, MediumUOMFactor, LargeUOMFactor);
                if (dim == (byte)Dimensions.DistinguishAndMultiply)
                    this.TotalQty = this.TotalQty * Height * Width * Length;
            }
            this.PiecesCount = PiecesCount;
            this.ParentItemId = ParentItemId;
            this.M1 = M1;
            this.M2 = M2;
            this.M3 = M3;
            this.VendorId = VendorId;
            this.TotalCost = TotalCostPrice;
            if (TotalQty != 0)
            {
                this.SmallUnitSellPrice = TotalLocalSellPrice / this.TotalQty;
                this.SmallUnitCostPrice = TotalCostPrice / this.TotalQty;
            }

            this.TotalLocalSellPrice = TotalLocalSellPrice;
            this.Pack = Pack.HasValue ? Pack.Value : 0;
        }
        /// <summary>
        /// just add data without calculations
        /// </summary>
        public StoreItem(int ItemId, int ItemType, int SourceId, decimal Qty, DateTime? ExpireDate, string Batch, string QC, int? VendorId,
            decimal Length, decimal Width, decimal Height, decimal PiecesCount, int? ParentItemId, int? M1, int? M2, int? M3,
            decimal TotalLocalSellPrice, decimal TotalCostPrice, string Serial, string Serial2,
            int CategoryId)
        {
            this.Source = Source;
            this.ItemId = ItemId;
            this.CategoryId = CategoryId;
            this.ItemType = ItemType;
            this.SourceId = SourceId;
            this.ExpireDate = ExpireDate;

            if (Batch == null || Batch == string.Empty || Batch.Trim() == string.Empty)
                this.Batch = null;
            else
                this.Batch = Batch;

            if (Serial == null || Serial == string.Empty || Serial.Trim() == string.Empty)
                this.Serial = null;
            else
                this.Serial = Serial;


            if (Serial2 == null || Serial2 == string.Empty || Serial2.Trim() == string.Empty)
                this.Serial = null;
            else
                this.Serial2 = Serial2;

            if (QC == null || QC == string.Empty || QC.Trim() == string.Empty)
                this.QC = null;
            else
                this.QC = QC;

            this.Length = Length;
            this.Width = Width;
            this.Height = Height;
            this.TotalQty = Qty;
            this.PiecesCount = PiecesCount;
            this.ParentItemId = ParentItemId;
            this.M1 = M1;
            this.M2 = M2;
            this.M3 = M3;
            this.VendorId = VendorId;
            this.TotalCost = TotalCostPrice;
            if (TotalQty != 0)
            {
                this.SmallUnitSellPrice = TotalLocalSellPrice / this.TotalQty;
                this.SmallUnitCostPrice = TotalCostPrice / this.TotalQty;
            }

            this.TotalLocalSellPrice = TotalLocalSellPrice;
        }
        public StoreItem(byte dim, int ItemId, int ItemType, int SourceId,
                    byte UOMIndex, decimal Qty, decimal MediumUOMFactor, decimal LargeUOMFactor,
                   DateTime? ExpireDate, string Batch, string QC, int? VendorId, decimal Length, decimal Width, decimal Height, decimal PiecesCount,
                    int? ParentItemId, int? M1, int? M2, int? M3, decimal TotalLocalSellPrice, decimal TotalCostPrice, object Source, string Serial, string Serial2,
            int CategoryId, int? StoreId, int? Pack)
        {

            this.Source = Source;

            this.ItemId = ItemId;
            this.CategoryId = CategoryId;
            this.ItemType = ItemType;
            this.SourceId = SourceId;
            this.ExpireDate = ExpireDate;

            if (Batch == null || Batch == string.Empty || Batch.Trim() == string.Empty)
                this.Batch = null;
            else
                this.Batch = Batch;

            if (Serial == null || Serial == string.Empty || Serial.Trim() == string.Empty)
                this.Serial = null;
            else
                this.Serial = Serial;


            if (Serial2 == null || Serial2 == string.Empty || Serial2.Trim() == string.Empty)
                this.Serial2 = null;
            else
                this.Serial2 = Serial2;

            if (QC == null || QC == string.Empty || QC.Trim() == string.Empty)
                this.QC = null;
            else
                this.QC = QC;

            if (dim == (byte)Dimensions.Multiply)
            {
                this.Length = 0;
                this.Width = 0;
                this.Height = 0;
                this.TotalQty = MyHelper.CalculateUomQty(Qty, UOMIndex, MediumUOMFactor, LargeUOMFactor)
                     * Height * Width * Length;
            }
            else
            {
                this.Length = Length;
                this.Width = Width;
                this.Height = Height;
                this.TotalQty = MyHelper.CalculateUomQty(Qty, UOMIndex, MediumUOMFactor, LargeUOMFactor);
                if (dim == (byte)Dimensions.DistinguishAndMultiply)
                    this.TotalQty = this.TotalQty * Height * Width * Length;
            }
            this.PiecesCount = PiecesCount;
            this.ParentItemId = ParentItemId;
            this.M1 = M1;
            this.M2 = M2;
            this.M3 = M3;
            this.VendorId = VendorId;
            this.TotalCost = TotalCostPrice;
            if (TotalQty != 0)
            {
                this.SmallUnitSellPrice = TotalLocalSellPrice / this.TotalQty;
                this.SmallUnitCostPrice = TotalCostPrice / this.TotalQty;
            }

            this.TotalLocalSellPrice = TotalLocalSellPrice;
            this.StoreId = StoreId;
            this.Pack = Pack.HasValue ? Pack.Value : 0;
        }
        /// <summary>
        /// just add data without calculations
        /// </summary>
        public StoreItem(int ItemId, int ItemType, int SourceId, decimal Qty, DateTime? ExpireDate, string Batch, string QC, int? VendorId,
            decimal Length, decimal Width, decimal Height, decimal PiecesCount, int? ParentItemId, int? M1, int? M2, int? M3,
            decimal TotalLocalSellPrice, decimal TotalCostPrice, string Serial, string Serial2,
            int CategoryId, int? StoreId, int? Pack)
        {
            this.Source = Source;
            this.ItemId = ItemId;
            this.CategoryId = CategoryId;
            this.ItemType = ItemType;
            this.SourceId = SourceId;
            this.ExpireDate = ExpireDate;

            if (Batch == null || Batch == string.Empty || Batch.Trim() == string.Empty)
                this.Batch = null;
            else
                this.Batch = Batch;

            if (Serial == null || Serial == string.Empty || Serial.Trim() == string.Empty)
                this.Serial = null;
            else
                this.Serial = Serial;


            if (Serial2 == null || Serial2 == string.Empty || Serial2.Trim() == string.Empty)
                this.Serial2 = null;
            else
                this.Serial2 = Serial2;

            if (QC == null || QC == string.Empty || QC.Trim() == string.Empty)
                this.QC = null;
            else
                this.QC = QC;

            this.Length = Length;
            this.Width = Width;
            this.Height = Height;
            this.TotalQty = Qty;
            this.PiecesCount = PiecesCount;
            this.ParentItemId = ParentItemId;
            this.M1 = M1;
            this.M2 = M2;
            this.M3 = M3;
            this.VendorId = VendorId;
            this.TotalCost = TotalCostPrice;
            if (TotalQty != 0)
            {
                this.SmallUnitSellPrice = TotalLocalSellPrice / this.TotalQty;
                this.SmallUnitCostPrice = TotalCostPrice / this.TotalQty;
            }

            this.TotalLocalSellPrice = TotalLocalSellPrice;
            this.StoreId = StoreId;

            this.Pack = Pack.HasValue ? Pack.Value : 0;
        }


    }

    public class CurrencyLastRate
    {
        int crncyId;


        public int CrncyId
        {
            get { return crncyId; }
            set { crncyId = value; }
        }
        decimal rate;

        public decimal Rate
        {
            get { return rate; }
            set { rate = value; }
        }
    }

    public class ItemUOMs
    {
        private int index;//Large =0, medium=1, small=2
        private int uomId;
        private string uom;
        private int factor;
        private bool isDefault;

        public int Index
        {
            get { return index; }
            set { index = value; }
        }
        public int UomId
        {
            get { return uomId; }
            set { uomId = value; }
        }
        public string Uom
        {
            get { return uom; }
            set { uom = value; }
        }
        public int Factor
        {
            get { return factor; }
            set { factor = value; }
        }
        public bool IsDefault
        {
            get { return isDefault; }
            set { isDefault = value; }
        }
    }

    public class SL_Customer_Info
    {
        public int CustomerId { get; set; }
        public int CusCode { get; set; }
        public int? AccountId { get; set; }
        public int? PriceLevelId { get; set; }
        public int? DueDaysCount { get; set; }
        public int? SalesEmpId { get; set; }
        public bool HasSeparateAccount { get; set; }
        public double MaxCredit { get; set; }
        public double DiscountRatio { get; set; }
        public string CusNameAr { get; set; }
        public string CusNameEn { get; set; }
        public string Tel { get; set; }
        public string Mobile { get; set; }
        public string Address { get; set; }
        public string IdNumber { get; set; }
        public string PriceLevel { get; set; }
        public string City { get; set; }
        public string Email { get; set; }
        public string Fax { get; set; }
        public string Zip { get; set; }
        public string Shipping { get; set; }
        public string Manager { get; set; }
        public string Representative { get; set; }
        public string Representative_Job { get; set; }
        public string RepFNAme { get; set; }
        public string RepFJob { get; set; }
        public string CGNameAr { get; set; }
        public int? GrpId { get; set; }
        public bool IsTaxable { get; set; }
        public string AccNumber { get; set; }
        public decimal GroupMaxCredit { get; set; }
        public int? GroupAccountId { get; set; }
        public bool Is_Blocked { get; set; }
        public string CustGroup { get; set; }
        public string Rep_ID { get; set; }
        public string Rep_Mobile { get; set; }
        public int? Delivery { get; set; }
        public string Region { get; set; }
        public string Street { get; set; }
        public string Governate { get; set; }
        public string BuildingNumber { get; set; }
        public string TaxCardNumber { get; set; }
        public string Country { get; set; }
        public string csType { get; set; }
        

    }

    public class VendorInfo
    {
        public int VendorId { get; set; }
        public int VenCode { get; set; }
        public string VenNameAr { get; set; }
        public string VenNameEn { get; set; }
        public string Zip { get; set; }
        public string TradeRegistry { get; set; }
        public string Tel { get; set; }
        public string TaxFileNumber { get; set; }
        public string TaxDepartment { get; set; }
        public string TaxCardNumber { get; set; }
        public string Shipping { get; set; }
        public string Representative_Job { get; set; }
        public string Representative { get; set; }
        public string RepFNAme { get; set; }
        public string RepFJob { get; set; }
        public int? PriceLevel { get; set; }
        public string Mobile { get; set; }
        public decimal MaxCredit { get; set; }
        public string ManagerName { get; set; }
        public bool IsTaxable { get; set; }
        public bool HasSeparateAccount { get; set; }
        public int? GroupId { get; set; }
        public string Fax { get; set; }
        public string Email { get; set; }
        public int? DueDaysCount { get; set; }
        public int? AccountId { get; set; }
        public string VendorAccNumber { get; set; }
        public string Address { get; set; }
        public string City { get; set; }

    }

    public class PR_VendorGroupInfo
    {
        public int VendorGroupId { get; set; }
        public string VendorGroupCode { get; set; }
        public string VGNameAr { get; set; }
        public string VGNameEn { get; set; }
        public string Desc { get; set; }
        public string AcNumber { get; set; }
        public int AccountId { get; set; }
        public decimal MaxCredit { get; set; }
        public bool VendorHasSeparateAccount { get; set; }
        public int? VendorDefaultAccount { get; set; }
    }

    public class SL_CustomerCategoryInfo
    {
        public int CustomerGroupId { get; set; }
        public string CustomerGroupCode { get; set; }
        public string CGNameAr { get; set; }
        public string CGNameEn { get; set; }
        public string Desc { get; set; }
        public decimal MaxCredit { get; set; }
        public int AccountId { get; set; }
        public string AcNumber { get; set; }

        public int? ParentGroupId { get; set; }
        public bool CustomersHaveSeparateAccount { get; set; }
        public int? CustomersDefaultAccount { get; set; }
    }

    public class DistributionMethod
    {
        public int dId { get; set; }
        public string dName { get; set; }
    }

    public class PrExpenses
    {
        public string Notes { get; set; }
        public decimal Amount { get; set; }
        public int DistributeMethod { get; set; }
    }

    public class ItemLkp
    {
        public int ItemCode1 { get; set; }
        public string ItemCode2 { get; set; }
        public int ItemId { get; set; }
        public string ItemNameAr { get; set; }
        public string ItemNameEn { get; set; }
        public decimal MaxQty { get; set; }
        public decimal MinQty { get; set; }
        public decimal ReorderLevel { get; set; }
        public bool IsExpire { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal Height { get; set; }
        public decimal Width { get; set; }
        public decimal Length { get; set; }
        public decimal SellPrice { get; set; }
        public string PicPath { get; set; }
        public byte? MediumUOM { get; set; }
        public byte? LargeUOM { get; set; }
        public string CategoryNameAr { get; set; }
        public string CompanyNameAr { get; set; }
        public int CategoryId { get; set; }

        public decimal? MediumUOMFactorDecimal { get; set; }
        public decimal? LargeUOMFactorDecimal { get; set; }
        public decimal? SellDiscountRatio { get; set; }


    }

    public class Detail_PR
    {

        public int ItemId { get; set; }
        public string Batch { get; set; }
        public DateTime? Expire { get; set; }
        public decimal? Length { get; set; }
        public decimal? Width { get; set; }
        public decimal? Height { get; set; }
        public decimal PiecesCount { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal TotalPurchasePrice { get; set; }
        public decimal SellPrice { get; set; }
        public decimal Qty { get; set; }
        public int UOMId { get; set; }
        public byte UOMIndex { get; set; }
        public int? VendorId { get; set; }
    }

    public class SumStores
    {
        public int sellAccount { get; set; }
        public decimal TotalSellPrice { get; set; }
        public int CostCenter { get; set; }
    }

    public enum CostDistributeMethod
    {
        NotAllocated = 1,
        PerValue = 2,
        PerQty = 3,
        PerWeight = 4,
        PerPeicesCount = 5,
        PerPrevious = 6
    }

    public enum ChangePurchasePriceMethod
    {
        LastPrice = 1,
        FixedPrice = 2,
        AveragePrice = 3
    }

    public enum AccessType
    {
        Admin = 0,
        FullAccess = 1,
        CustomAccess = 2,
        NoAccess = 3
    }

    public enum Process
    {
        PurchaseInvoice = 1,
        SellInvoice = 2,
        PurchaseReturn = 3,
        SellReturn = 4,
        AdujstIn = 5,
        AdujstOut = 6,
        Damage = 7,
        MoveIn = 8,
        MoveOut = 9,
        OpenBalance = 10,
        DailyJournal = 11,
        Revenue = 12,
        Expenses = 13,
        CashIn = 14,
        CashOut = 15,
        NotesReceivable = 16,
        NotesPayable = 17,
        Manufacturing = 18,
        InTrns = 19,
        OutTrns = 20,
        PaySalary = 21,
        CashTransfer = 22,
        SL_Invoices_Posting = 23,
        SalesOrder = 24,
        DebitNote = 25,
        CreditNote = 26,
        QualityControl = 27,
        Loan = 28,
        sl_Qoute = 29,
        JobOrder = 30,
        Pr_Qoute = 31,
        Pr_Order = 32,
        Pr_Request = 33,
        Assembling = 34,
        Disassembling = 35,
        Scale = 36,
        RealEstateSales = 37,
        VisaNote = 38,
        Stocking = 39,
        Contract = 40,
        FA_Purchase = 41,
        FA_Sell = 42,
        FA_Exclude = 43,
        FA_Evaluate = 44,
        IC_ReceiveGood = 45,
        CurrencyTransfer = 46,
        IC_InternalRequest = 47,
        AccountCashTransfer = 48,
        Pre_Invoice = 49,
        ImExp_Fines=50,
        ImExp_Invoice = 51,
        VendorCommission=52,
        HalalCertifecation=53,
    }

    public enum CostMethod
    {
        FIFO = 0,
        LIFO = 1,
        AVERAGE = 2,
    }
    public enum PreInvoiceTypes
    {
        Advanced = 1,
        WithoutAdvanced = 2,
        WithARelay = 3,
    }
    public enum IsVendor
    {
        Customer = 0,
        Vendor = 1,
        Empolyee = 2,
        Manf = 3,
        ExpenseUnderProd = 4,
        InternalRequest=5,
    }

    public enum Dimensions
    {
        Distinguish = 0,
        Multiply = 1,
        DistinguishAndMultiply = 2,
    }

    public enum InvoiceWorkflow
    {
        Open = 0,
        InvThenBill = 1,
        BillThenInv = 2
    }

    public enum ExpensesType
    {
        PurchaseInvoice = 0,
        Storemove = 1,
        InTrans = 2,
        ImExp_Invoice = 3
    }
    public struct C_supposed_raw_Obj
    {
        public string Raw_Item_ID;
        public double Qty;
        public int UomId;
        public int ItemId;
        public decimal Factor;
        public decimal PurchasePrice;
        public double Total_Price;
        public bool IsExpire;
        public decimal CurrentQty;
        public int ItemType;
        public byte UomIndex;
    }

    public class InvBom
    {
        public int BOMId { get; set; }
        public int ProductItemId { get; set; }
        public decimal ProductQty { get; set; }
        public int RawItemId { get; set; }
        public int UomId { get; set; }
        public decimal RawQty { get; set; }
        public decimal Factor { get; set; }

        public int RawItemType { get; set; }

        public byte UomIndex { get; set; }

        public int? mtrxAttribute1 { get; set; }

        public int? mtrxAttribute2 { get; set; }

        public int? mtrxAttribute3 { get; set; }

        public int? mtrxParentItem { get; set; }

        public decimal MediumUOMFactorDecimal { get; set; }

        public decimal LargeUOMFactorDecimal { get; set; }
    }
}