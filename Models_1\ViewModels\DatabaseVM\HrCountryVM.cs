﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Models_1.ViewModels.DatabaseVM
{
    public class HrCountryVM
    {
        public HrCountryVM()
        {
            IcStore = new HashSet<IcStoreVM>();
            SlCustomer = new HashSet<SlCustomerVM>();
        }

        public int CountryId { get; set; }
        public string CountryName { get; set; }
        public string Ecode { get; set; }
        public string CountryNameEn { get; set; }

        public virtual ICollection<IcStoreVM> IcStore { get; set; }
        public virtual ICollection<SlCustomerVM> SlCustomer { get; set; }
    }
}
