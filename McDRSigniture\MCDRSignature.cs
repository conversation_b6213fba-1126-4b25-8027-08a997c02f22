﻿using Net.Pkcs11Interop.Common;
using Net.Pkcs11Interop.HighLevelAPI;
using Org.BouncyCastle.Asn1;
using Org.BouncyCastle.Asn1.Ess;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.Pkcs;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.IO;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Xml;
namespace McDRSigniture
{
    public class MCDRSignature : IDisposable
    {
        private string DllLibPath = "" /*"eps2003csp11.dll"*/;
        private string TokenPin = "";
        private string TokenCertificate = "";
        private bool flag;

        public static void writeToFile(string msg, string action)
        {
            string path = "c:\\log\\log.txt";
            //if (File.Exists(path))
            //    File.WriteAllText(path, "scmd.DeleteContext" + msg);
            //else
            File.AppendAllText(path, action + " : " + Environment.NewLine + msg);
        }
        public static string GetSigniture(string TokenPin, string TokenCertificate, string DllLibPath, String SourceDocumentJson)
        {
            MCDRSignature tokenSigner = new MCDRSignature();
            tokenSigner.DllLibPath = DllLibPath;
            tokenSigner.TokenPin =TokenPin;
            tokenSigner.TokenCertificate = TokenCertificate;
            String cades = "";
            //    XmlDocument doc = new XmlDocument();
            // var docPath = "";



            ///  XmlNode node = doc.SelectSingleNode($"/configurations/configuration[@id='{1}']");

            //if (node == null)
            //{
            //    writeToFile("Error in configuration file format");
            //    writeToFile("Press Any Key To Exit");
            //    Console.ReadLine();
            //    Environment.Exit(0);
            //}

            //docPath = node.ChildNodes[0]?.InnerText;
            //tokenSigner.TokenPin = node.ChildNodes[1]?.InnerText;
            //tokenSigner.TokenCertificate = node.ChildNodes[2]?.InnerText;


            writeToFile("tokenSigner.TokenCertificate: "+ tokenSigner.TokenCertificate, "");

            if (tokenSigner.TokenPin == null || tokenSigner.TokenCertificate == null)
            {
                writeToFile("Please Insert TokenPin","");
                writeToFile("Press Any Key To Exit","");
                return null;
              
            }


            //if (File.Exists(docPath + @"\SourceDocumentJson.json") == false)
            //{
            //    writeToFile("The file " + docPath + @"\SourceDocumentJson.json is not exist");
            //    writeToFile("Press Any Key To Exit");
            //    Console.ReadLine();
            //    Environment.Exit(0);
            //}

            
            //String SourceDocumentJson = File.ReadAllText(docPath + @"\SourceDocumentJson.json");
            JObject request = JsonConvert.DeserializeObject<JObject>(SourceDocumentJson, new JsonSerializerSettings()
            {
                FloatFormatHandling = FloatFormatHandling.String,
                FloatParseHandling = FloatParseHandling.Decimal,
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                DateParseHandling = DateParseHandling.None
            });

            //Start serialize
            writeToFile("Serializing Json Documents", "");
            String canonicalString = tokenSigner.Serialize(request);
            writeToFile("Serializing Json",canonicalString);
            //File.WriteAllBytes(docPath + @"\CanonicalString.txt", System.Text.Encoding.UTF8.GetBytes(canonicalString));
            // retrieve cades
            if (request["documentTypeVersion"].Value<string>() == "0.9")
            {
                cades = "ANY";
            }
            else
            {
                writeToFile("Signing Invoice", "");
                cades = tokenSigner.SignWithCMS(canonicalString);
            }
            //// File.WriteAllBytes(docPath + @"\Cades.txt", System.Text.Encoding.UTF8.GetBytes(cades));
            //JObject signaturesObject = new JObject(
            //                       new JProperty("signatureType", "I"),
            //                       new JProperty("value", cades));
            //JArray signaturesArray = new JArray();
            //signaturesArray.Add(signaturesObject);
            //request.Add("signatures", signaturesArray);
            //String fullSignedDocument = "{\"documents\":[" + request.ToString() + "]}";
            // File.WriteAllBytes(docPath + @"\FullSignedDocument.json", Encoding.UTF8.GetBytes(fullSignedDocument));
            if (!tokenSigner.flag)
            {
                writeToFile("Please Insert Token", "");
                return "";

            }


            writeToFile("Signed Invoice Created Successfully, check the FullSignedDocument", cades);
            return cades;
            //writeToFile("Press Any Key to Exit");
            //Console.ReadLine();
            //Environment.Exit(0);

        }


        private byte[] Hash(string input)
        {
            using (SHA256 sha = SHA256.Create())
            {
                var output = sha.ComputeHash(Encoding.UTF8.GetBytes(input));
                return output;
            }
        }

        private byte[] HashBytes(byte[] input)
        {
            using (SHA256 sha = SHA256.Create())
            {
                var output = sha.ComputeHash(input);
                return output;
            }
        }
        public string SignWithCMS(String serializedJson)
        {
            byte[] data = Encoding.UTF8.GetBytes(serializedJson);
            Pkcs11InteropFactories factories = new Pkcs11InteropFactories();   
                    writeToFile("b4 SignWithCMS", "");
            writeToFile($"This is the Dll Path : {this.DllLibPath}", "");
            using (IPkcs11Library pkcs11Library = factories.Pkcs11LibraryFactory.LoadPkcs11Library(factories, this.DllLibPath,AppType.MultiThreaded))
            {
                writeToFile("inside SignWithCMS", "");

                ISlot slot = pkcs11Library.GetSlotList(SlotsType.WithTokenPresent).FirstOrDefault();

                if (slot is null)
                {
                    flag = false;
                    writeToFile("No slots found","");
                    return "";
                }

                //ITokenInfo tokenInfo;
                //try
                //{
                //    tokenInfo = slot.GetTokenInfo();
                //    writeToFile("TokenInfo, Label: " + tokenInfo.Label + ", SerialNumber: " + tokenInfo.SerialNumber +
                //        ", ManufacturerId: " + tokenInfo.ManufacturerId, "");
                //}
                //catch (Pkcs11Exception pkcsEx)
                //{
                //    // Handle specific PKCS11 errors
                //    writeToFile("PKCS11 Error reading token info: " + pkcsEx.Message + ", RV: " + pkcsEx.RV, "");
                //    flag = false;
                //    return "";
                //}
                //catch (Exception ex)
                //{
                //    writeToFile("Error reading token info: " + ex.Message, "");
                //    flag = false;
                //    return "";
                //}


                ISlotInfo slotInfo = slot.GetSlotInfo();

                writeToFile("b4 SignWithCMS.slot.OpenSession", "");

                using (var session = slot.OpenSession(SessionType.ReadWrite))
                {
                    writeToFile("inside SignWithCMS.slot.OpenSession", "");

                writeToFile("b4 login", "");
                    session.Login(CKU.CKU_USER, Encoding.UTF8.GetBytes(TokenPin));
                    writeToFile("after login", "");

                    var certificateSearchAttributes = new List<IObjectAttribute>()
                    {
                        session.Factories.ObjectAttributeFactory.Create(CKA.CKA_CLASS, CKO.CKO_CERTIFICATE),
                        session.Factories.ObjectAttributeFactory.Create(CKA.CKA_TOKEN, true),
                        session.Factories.ObjectAttributeFactory.Create(CKA.CKA_CERTIFICATE_TYPE, CKC.CKC_X_509)
                    };

                    IObjectHandle certificate = session.FindAllObjects(certificateSearchAttributes).FirstOrDefault();

                    if (certificate is null)
                    {
                        flag = false;
                        writeToFile( "Certificate not found","");
                        return "";
                    }
                    X509Store store = new X509Store(StoreName.My, StoreLocation.CurrentUser);
                    store.Open(OpenFlags.MaxAllowed);

                    var foundCerts = store.Certificates.Find(X509FindType.FindByIssuerName, TokenCertificate, false);
                    //writeToFile("Certificates.Find.FindBySerialNumber", "");
                    //var foundCerts = store.Certificates.Find(X509FindType.FindBySerialNumber, tokenInfo.SerialNumber, false);

                    

                    if (foundCerts.Count == 0)
                    {
                        flag = false;
                        writeToFile("no device detected, TokenCertificate: "+TokenCertificate, DateTime.Now.ToString());
                        
                        return "";
                    }
                    writeToFile("foundCerts: " + foundCerts.Count, DateTime.Now.ToString());
                    int count = 1;
                    foreach (var cert in foundCerts)
                    {
                        writeToFile("foundCerts no. " + count + ", cert: " + cert, "");
                        writeToFile("foundCerts no. " + count + ", cert.Subject[0]: " + cert.Subject[0], "");
                        writeToFile("SubjectName.Oid.Value " + count + ", cert: " + cert.SubjectName.Oid.Value, "");
                        writeToFile("SubjectName.Name " + count + ", cert: " + cert.SubjectName.Name, "");
                        count++;
                    }
                    var certForSigning = foundCerts[0];
                        writeToFile("foundCerts: "+ certForSigning.GetSerialNumberString()+","+ certForSigning.FriendlyName, "");
                    store.Close();

                    writeToFile($"B4 Content", DateTime.Now.ToString());
                    ContentInfo content = new ContentInfo(new Oid("1.2.840.113549.1.7.5"), data);

                    writeToFile($"B4 Signed CMS", DateTime.Now.ToString());

                    SignedCms cms = new SignedCms(content, true);

                    writeToFile($"B4 bouncyCertificate", DateTime.Now.ToString());

                    EssCertIDv2 bouncyCertificate = new EssCertIDv2(new Org.BouncyCastle.Asn1.X509.AlgorithmIdentifier(new DerObjectIdentifier("1.2.840.113549.1.9.16.2.47")), this.HashBytes(certForSigning.RawData));
                    writeToFile($"B4 signerCertificateV2", DateTime.Now.ToString());

                    SigningCertificateV2 signerCertificateV2 = new SigningCertificateV2(new EssCertIDv2[] { bouncyCertificate });

                    writeToFile($"B4 CmsSigner", DateTime.Now.ToString());

                    CmsSigner signer = new CmsSigner(certForSigning);
                    writeToFile($"B4 DigestAlgorithm", DateTime.Now.ToString());

                    signer.DigestAlgorithm = new Oid("2.16.840.1.101.3.4.2.1");

                    writeToFile($"B4 SignedAttributes", DateTime.Now.ToString());


                    signer.SignedAttributes.Add(new Pkcs9SigningTime(DateTime.UtcNow));
                    signer.SignedAttributes.Add(new AsnEncodedData(new Oid("1.2.840.113549.1.9.16.2.47"), signerCertificateV2.GetEncoded()));

                    writeToFile($"B4 ComputeSignature", DateTime.Now.ToString());
                    try
                    {
                        cms.ComputeSignature(signer);
                    }
                    catch (Exception ex)
                    {
                        writeToFile($"Errorin  CMS ComputeSignature" + ex.Message, DateTime.Now.ToString());
                    }
                    writeToFile($"B4 CMS Encode", DateTime.Now.ToString());

                    var output = cms.Encode();
                    flag = true;
                    writeToFile($"After CMS Encode", DateTime.Now.ToString());

                    return Convert.ToBase64String(output);
                }
            }
        }
        public static bool CheckDLL(string DllLibPath)
        {
            try
            {
                Pkcs11InteropFactories factories = new Pkcs11InteropFactories();
                using (IPkcs11Library pkcs11Library = factories.Pkcs11LibraryFactory.LoadPkcs11Library(factories, DllLibPath,AppType.SingleThreaded))
                {
                    return true;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }
        public string SeeSlots(string DllLibPath)
        {
            try
            {
                Pkcs11InteropFactories factories = new Pkcs11InteropFactories();
                using (IPkcs11Library pkcs11Library = factories.Pkcs11LibraryFactory.LoadPkcs11Library(factories, DllLibPath, AppType.SingleThreaded))
                {
                    var slots = pkcs11Library.GetSlotList(SlotsType.WithTokenPresent);
                    if (slots is null)
                    {
                        flag = false;
                        writeToFile("No slots found", "");
                        return "No slots found";
                    }
                    writeToFile("Slots count: " + slots.Count, "");
                    string slosts = "";
                    string tokenInfos = "";
                    foreach (var s in slots)
                    {
                        try
                        {
                            var slotInfo = s.GetSlotInfo();
                            writeToFile("Slot: " + slotInfo.SlotDescription.Trim(), "");
                            slosts += "Slot: " + slotInfo.SlotDescription.Trim();

                            //Add additional error handling and retry logic for token info
                            try
                                {
                                    var tInfo = s.GetTokenInfo();
                                    tokenInfos += "Token Label: " + tInfo.Label.Trim();
                                    writeToFile("Token Label: " + tInfo.Label.Trim(), "");
                                }
                                catch (Pkcs11Exception pkcsEx)
                                {
                                    // Handle specific PKCS11 errors
                                    tokenInfos += "PKCS11 Error reading token info: " + pkcsEx.Message + ", RV: " + pkcsEx.RV;
                                    writeToFile("PKCS11 Error reading token info: " + pkcsEx.Message + ", RV: " + pkcsEx.RV, "");
                                }
                                catch (Exception ex)
                                {
                                    tokenInfos += "Error reading token info from slot: " + ex.Message;
                                    writeToFile("Error reading token info from slot: " + ex.Message, "");
                                }
                        }
                        catch (Exception ex)
                        {
                            tokenInfos += "Error reading slot info: " + ex.Message;
                            writeToFile("Error reading slot info: " + ex.Message, "");
                        }
                    }
                    return slosts + "\n" + tokenInfos;
                }
            }
            catch (Exception ex)
            {
                return $"Exception:{ex}";
            }
        }

        public string Serialize(JObject request)
        {
            return SerializeToken(request);
        }

        private string SerializeToken(JToken request)
        {
            string serialized = "";
            if (request.Parent is null)
            {
                SerializeToken(request.First);
            }
            else
            {



                if (request.Type == JTokenType.Property)
                {
                    string name = ((JProperty)request).Name.ToUpper();
                    serialized += "\"" + name + "\"";
                    foreach (var property in request)
                    {
                        if (property.Type == JTokenType.Object)
                        {
                            serialized += SerializeToken(property);
                        }
                        if (property.Type == JTokenType.Boolean || property.Type == JTokenType.Integer || property.Type == JTokenType.Float || property.Type == JTokenType.Date)
                        {
                            serialized += "\"" + property.Value<string>() + "\"";
                        }
                        if (property.Type == JTokenType.String)
                        {
                            serialized += JsonConvert.ToString(property.Value<string>());
                        }
                        if (property.Type == JTokenType.Null)
                        {
                            serialized += "\"null\"";
                        }
                        if (property.Type == JTokenType.Array)
                        {
                            foreach (var item in property.Children())
                            {
                                serialized += "\"" + ((JProperty)request).Name.ToUpper() + "\"";
                                serialized += SerializeToken(item);
                            }
                        }
                    }
                }
                // Added to fix "References"
                if (request.Type == JTokenType.String)
                {
                    serialized += JsonConvert.ToString(request.Value<string>());
                }
            }
            if (request.Type == JTokenType.Object)
            {
                foreach (var property in request.Children())
                {

                    if (property.Type == JTokenType.Object || property.Type == JTokenType.Property)
                    {
                        serialized += SerializeToken(property);
                    }
                }
            }

            return serialized;
        }


        public void ListCertificates()
        {

            X509Store store = new X509Store(StoreName.My, StoreLocation.CurrentUser);
            store.Open(OpenFlags.MaxAllowed);
            X509Certificate2Collection collection = (X509Certificate2Collection)store.Certificates;
            X509Certificate2Collection fcollection = (X509Certificate2Collection)collection.Find(X509FindType.FindBySerialNumber, "2b1cdda84ace68813284519b5fb540c2", true);
            foreach (X509Certificate2 x509 in fcollection)
            {
                try
                {
                    byte[] rawdata = x509.RawData;
                    Console.WriteLine("Content Type: {0}{1}", X509Certificate2.GetCertContentType(rawdata), Environment.NewLine);
                    Console.WriteLine("Friendly Name: {0}{1}", x509.FriendlyName, Environment.NewLine);
                    Console.WriteLine("Certificate Verified?: {0}{1}", x509.Verify(), Environment.NewLine);
                    Console.WriteLine("Simple Name: {0}{1}", x509.GetNameInfo(X509NameType.SimpleName, true), Environment.NewLine);
                    Console.WriteLine("Signature Algorithm: {0}{1}", x509.SignatureAlgorithm.FriendlyName, Environment.NewLine);
                    Console.WriteLine("Public Key: {0}{1}", x509.PublicKey.Key.ToXmlString(false), Environment.NewLine);
                    Console.WriteLine("Certificate Archived?: {0}{1}", x509.Archived, Environment.NewLine);
                    Console.WriteLine("Length of Raw Data: {0}{1}", x509.RawData.Length, Environment.NewLine);
                    x509.Reset();
                }
                catch (CryptographicException ex)
                {
                    writeToFile("Information could not be written out for this certificate.", "");
                    throw ex;
                }
            }
            store.Close();
        }

        public void Dispose()
        {
            GC.SuppressFinalize(this);
        }
    }

}