﻿using EInvoice.Models;
using EInvoice.ViewModels.InvoiceVM;
using Models_1.ViewModels.ValidationVM;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace EInvoice.ViewModels.ValidateVM
{
    public class ValidateModel
    {
        public bool IsValid { get; set; }
        public string Message { get; set; }
        public List<InvoiceData> ValidDocumnents { get; set; }
        public ValidationMessages UnValidDocumnents { get; set; }
    }

    public class InvoiceData
    {
        public int invoiceId { get; set; }
        public string invoiceCode { get; set; }
        public StCompanyInfo CompanyData { get; set;}
        public IcStore StoreData { get; set;}
        public SlCustomer CustomerData { get; set;}
        public DateTime InvoiceDate { get; set; }
        public string DocumentType { get; set; } //I - C - D
        public decimal TotalDiscount { get; set; }
        public List<InvoiceDetailData> InvoiceDetailData { get; set; }
        public List<string> Uuid { get; set; }

    }

    public class InvoiceDetailData
    {
        public int SlInvoiceId { get; set; }
        public IcItem ItemData { get; set; }
        public StCurrency CurrencyData { get; set; }
        public IcUom UomData { get; set; }
        public decimal Quantity { get; set; }
        public decimal ExchangeRate { get; set; }
        public decimal Amount { get; set; } // amount with currency entered
        public string Description { get; set; }
        public InvoiceVM.Discount Discount { get; set; }
        public List<Tax> Taxes { get; set; }
        public decimal ItemsDiscount { get; set; }
        public bool IsValid { get; set; }
        public decimal discountAfterTax { get; set; }
    }

    public class Validation<T>
    {
        public bool IsValid { get; set; }
        public string Message { get; set; }
        public List<string> MessagesAr { get; set; }
        public T Data { get; set; }
    }

    //public class ValidationMessage
    //{
    //    public string invoiceCode { get; set; }
    //    public List<string> Message { get; set; }
    //}

    //public class ValidationMessages
    //{
    //    public List<ValidationMessage> Messages { get; set; }
    //}
}
