﻿using DAL;
using System;
using System.Runtime.InteropServices;

namespace DAL.BL
{
    public static class GenerateSignatureBL
    {
        //[DllImport("ChilkatDotNet45.dll",EntryPoint = "ScMinidriver")]
        //public static extern string hello(string json, string donglePIN, string cretSerial);

        public static string GetSigBase64(string json , string donglePIN , string cretSerial)
        {
            try
            {
                //ERPDataContext DB = new ERPDataContext();

                // This example requires the Chilkat API to have been previously unlocked.
                // See Global Unlock Sample for sample code.

                Chilkat.ScMinidriver scmd = new Chilkat.ScMinidriver();

                // Reader names (smart card readers or USB tokens) can be discovered
                // via List Readers or Find Smart Cards
                string readerName = "HYPERSECU USB TOKEN 0";
                bool success = scmd.AcquireContext(readerName);
                if (success == false)
                {
                    string LogMessage = $"Error From Dongle (scmd.AcquireContext) : {scmd.LastErrorText}";
                    //MyHelper.Utilities.save_Log(LogMessage, null);
                    //MyHelper.UpdateST_UserLog(DB, "1", LogMessage, (int)FormAction.Add, 1);
                    //DB.SubmitChanges();
                    //Console.WriteLine(scmd.LastErrorText);
                    return "";
                }

                // If successful, the name of the currently inserted smart card is available:
                Console.WriteLine("Card name: " + scmd.CardName);

                // If desired, perform regular PIN authentication with the smartcard.
                // For more details about smart card PIN authentication, see the Smart Card PIN Authentication Example
                int retval = scmd.PinAuthenticate("user", donglePIN);
                if (retval != 0)
                {
                    //Console.WriteLine("PIN Authentication failed.");
                    string LogMessage = $"Error From Dongle (scmd.PinAuthenticate) : PIN Authentication failed.";
                    //MyHelper.UpdateST_UserLog(DB, "1", LogMessage, (int)FormAction.Add, 1);
                    //DB.SubmitChanges();
                    Utilities.save_Log(LogMessage, null);
                }

                // You can find a cerficate using any of the following certificate parts:
                // "subjectDN" -- The full distinguished name of the cert.
                // "subjectDN_withTags" -- Same as above, but in a format that includes the subject part tags, such as the "CN=" in "CN=something"
                // "subjectCN" -- The common name part (CN) of the certificate's subject.
                // "serial" -- The certificate serial number.
                // "serial:issuerCN" -- The certificate serial number + the issuer's common name, delimited with a colon char.
                // These are the same certificate parts that can be retrieved by listing certificates on the smart card (or USB token).
                // See List Certificates on Smart Card Example
                //string certPart = "subjectCN";
                string certPart = "serial";

                string partValue = cretSerial;

                // If the certificate is found, it is loaded into the cert object.
                // Note: We imported this certificate from a .p12/.pfx using code such as this Example to Import a .pfx/.p12 onto a Smart Card
                Chilkat.Cert cert = new Chilkat.Cert();
                success = scmd.FindCert(certPart, partValue, cert);
                if (success == false)
                {
                    //Console.WriteLine("Failed to find the certificate.");
                    string LogMessage = $"Error From Dongle (scmd.FindCert) : Failed to find the certificate.";
                    //MyHelper.UpdateST_UserLog(DB, "1", LogMessage, (int)FormAction.Add, 1);
                    //DB.SubmitChanges();
                    Utilities.save_Log(LogMessage, null);
                    scmd.DeleteContext();
                    return "";
                }

                Console.WriteLine("Successfully loaded the cert object from the smart card / USB token.");

                // Note: When successful, the cert object is internally linked to the ScMinidriver object's authenticated session.
                // The cert object can now be used to sign or do other cryptographic operations that occur on the smart card / USB token.
                // If your application calls PinDeauthenticate or DeleteContext, the cert will no longer be able to sign on the smart card
                // because the smart card ScMinidriver session will no longer be authenticated or deleted.

                // ------------------------------------------------------------------------------------------------------------

                // Here we have to code to create the CADES-BES signature using Chilkat Crypt2..
                Chilkat.Crypt2 crypt = new Chilkat.Crypt2();

                // Tell the crypt class to use the cert on the ePass2003 token.
                success = crypt.SetSigningCert(cert);
                if (success != true)
                {
                    Console.WriteLine(crypt.LastErrorText);
                    return "";
                }

                Chilkat.JsonObject cmsOptions = new Chilkat.JsonObject();
                // Setting "DigestData" causes OID 1.2.840.113549.1.7.5 (digestData) to be used.
                cmsOptions.UpdateBool("DigestData", true);
                cmsOptions.UpdateBool("OmitAlgorithmIdNull", true);

                // Indicate that we are passing normal JSON and we want Chilkat do automatically
                // do the ITIDA JSON canonicalization:
                cmsOptions.UpdateBool("CanonicalizeITIDA", true);
                cmsOptions.ToString();
                crypt.CmsOptions = cmsOptions.Emit();

                // The CadesEnabled property applies to all methods that create CMS/PKCS7 signatures. 
                // To create a CAdES-BES signature, set this property equal to true. 
                crypt.CadesEnabled = true;

                crypt.HashAlgorithm = "sha256";

                Chilkat.JsonObject jsonSigningAttrs = new Chilkat.JsonObject();
                jsonSigningAttrs.UpdateInt("contentType", 1);
                jsonSigningAttrs.UpdateInt("signingTime", 1);
                jsonSigningAttrs.UpdateInt("messageDigest", 1);
                jsonSigningAttrs.UpdateInt("signingCertificateV2", 1);
                crypt.SigningAttributes = jsonSigningAttrs.Emit();

                // By default, all the certs in the chain of authentication are included in the signature.
                // If desired, we can choose to only include the signing certificate:
                crypt.IncludeCertChain = false;
                // Create the CAdES-BES signature.
                crypt.EncodingMode = "base64";

                // Make sure we sign the utf-8 byte representation of the JSON string
                crypt.Charset = "utf-8";

                //Console.WriteLine("Before Sign:");
                //Console.WriteLine(json.ToString());

                //Chilkat.StringBuilder sb = new Chilkat.StringBuilder();
                //sb.Append(json.ToString());
                //sb.ToCRLF();
                //Console.WriteLine(crypt.HashStringENC(sb.GetAsString()));



                string sigBase64 = crypt.SignStringENC(json.ToString());
                if (crypt.LastMethodSuccess == false)
                {
                    Console.WriteLine(crypt.LastErrorText);
                    string LogMessage = $"Error From Dongle (crypt.SignStringENC) : {crypt.LastErrorText}";
                    //MyHelper.UpdateST_UserLog(DB, "1", LogMessage, (int)FormAction.Add, 1);
                    //DB.SubmitChanges();
                    Utilities.save_Log(LogMessage, null);
                    return "";
                }

                Console.WriteLine("Base64 signature:");
                Console.WriteLine(sigBase64);

                // ------------------------------------------------------------------------------------------------------------
                // Cleanup our ScMinidriver session...

                // When finished with operations that required authentication, you may if you wish, deauthenticate the session.
                success = scmd.PinDeauthenticate("user");
                if (success == false)
                {
                    Console.WriteLine(scmd.LastErrorText);
                    string LogMessage = $"Error From Dongle (scmd.PinDeauthenticate) : {scmd.LastErrorText}";
                    //MyHelper.UpdateST_UserLog(DB, "1", LogMessage, (int)FormAction.Add, 1);
                    //DB.SubmitChanges();
                    Utilities.save_Log(LogMessage, null);

                }

                // Delete the context when finished with the card.
                success = scmd.DeleteContext();
                if (success == false)
                {
                    Console.WriteLine(scmd.LastErrorText);
                    string LogMessage = $"Error From Dongle (scmd.DeleteContext) : {scmd.LastErrorText}";
                    //MyHelper.UpdateST_UserLog(DB, "1", LogMessage, (int)FormAction.Add, 1);
                    //DB.SubmitChanges();
                    Utilities.save_Log(LogMessage, null);

                }

                return sigBase64;
            }
            catch (Exception ex)
            {
                Utilities.save_Log(ex.Message, ex);
                return null;

            }
        }
    }
}
